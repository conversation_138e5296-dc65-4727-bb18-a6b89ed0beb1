# === Cache configuration ===
.node-cache: &node-cache
    key:
        prefix: node
        files:
            - package-lock.json
    paths:
        - node_modules/
    policy: pull

.npm-cache: &npm-cache
    key:
        prefix: npm
        files:
            - package-lock.json
    paths:
        - .npm/
    policy: pull

# === Templates ===
.database: &database
    services:
        - name: public.ecr.aws/docker/library/postgres:17-alpine
          command:
              - -c
              - max_connections=1000
          alias: postgres

.migration: &migration
    stage: migration
    interruptible: true

.test: &test
    stage: test
    interruptible: true
    rules:
        - if: $CI_PIPELINE_SOURCE == 'merge_request_event'

.build: &build
    stage: build
    cache:
        <<: *npm-cache
    image: docker:28.3.0
    parallel:
        matrix:
            - APP_NAME:
                  - api
                  - web
    script:
        - mkdir -p $HOME/.docker
        - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
        - docker login --username $CI_REGISTRY_USER --password $CI_REGISTRY_PASSWORD $CI_REGISTRY
        - TAG=$CI_REGISTRY/$CI_PROJECT_PATH/$APP_NAME-$STAGE:$CI_COMMIT_BRANCH-$CI_COMMIT_SHA
        - docker buildx build --build-arg STAGE=${STAGE} --tag $TAG --file apps/$APP_NAME/Dockerfile --push .
    tags:
        - arm
        - large

.synth: &synth
    stage: build
    cache:
        <<: *node-cache
    script:
        - export $(printf "AWS_ACCESS_KEY_ID=%s AWS_SECRET_ACCESS_KEY=%s AWS_SESSION_TOKEN=%s" $(aws sts assume-role-with-web-identity --role-arn ${ROLE_ARN} --role-session-name "GitLabRunner-${CI_PROJECT_ID}-${CI_PIPELINE_ID}" --web-identity-token ${GITLAB_OIDC_TOKEN} --duration-seconds 3600 --query 'Credentials.[AccessKeyId,SecretAccessKey,SessionToken]' --output text))
        - npx nx run cdk:synth:$STAGE
    artifacts:
        paths:
            - tmp/cdk.out/
        expire_in: 1 day
    tags:
        - x86
        - large

.deploy: &deploy
    stage: deploy
    interruptible: false
    resource_group: deploy:$STAGE:$APP_NAME
    environment:
        name: $STAGE/$APP_NAME
        url: https://$APP_NAME.$STAGE.pocitarna.com
    parallel:
        matrix:
            - APP_NAME:
                  - api
                  - web
                  - currencyRates
                  - housekeeping
                  - mailer
                  - shoptet
    script:
        - if [[ -n $APP_NAME && ! $CI_COMMIT_MESSAGE =~ \[deploy\] ]]; then npx nx show projects --affected --base=$NX_BASE --head=$NX_HEAD --type app --with-target deploy | grep ${APP_NAME} || exit 0; fi
        - export $(printf "AWS_ACCESS_KEY_ID=%s AWS_SECRET_ACCESS_KEY=%s AWS_SESSION_TOKEN=%s" $(aws sts assume-role-with-web-identity --role-arn ${ROLE_ARN} --role-session-name "GitLabRunner-${CI_PROJECT_ID}-${CI_PIPELINE_ID}" --web-identity-token ${GITLAB_OIDC_TOKEN} --duration-seconds 3600 --query 'Credentials.[AccessKeyId,SecretAccessKey,SessionToken]' --output text))
        - npx nx run ${APP_NAME}:deploy:${STAGE}
    tags:
        - x86
        - small

.prod: &prod
    variables:
        ROLE_ARN: 'arn:aws:iam::************:role/Resources-DeploymentRoleC7BBCF2C-2OSrKywyhUKk'
        AWS_DEFAULT_ACCOUNT: ************
        DB_HOST: localhost
        DB_PASS: $RDS_DB_PASS
        STAGE: prod
    id_tokens:
        GITLAB_OIDC_TOKEN:
            aud: https://gitlab.superkoders.com
    rules:
        - if: $CI_COMMIT_BRANCH == 'master'

.staging: &staging
    variables:
        ROLE_ARN: 'arn:aws:iam::************:role/Resources-DeploymentRoleC7BBCF2C-GaJ1wTb9P38D'
        AWS_DEFAULT_ACCOUNT: ************
        DB_HOST: localhost
        DB_PASS: $RDS_DB_PASS
        STAGE: staging
    id_tokens:
        GITLAB_OIDC_TOKEN:
            aud: https://gitlab.superkoders.com
    rules:
        - if: $CI_COMMIT_BRANCH == 'development'

.runtime-migrate: &runtime-migrate
    stage: runtime
    resource_group: runtime:$STAGE:migrate
    script:
        - export $(printf "AWS_ACCESS_KEY_ID=%s AWS_SECRET_ACCESS_KEY=%s AWS_SESSION_TOKEN=%s" $(aws sts assume-role-with-web-identity --role-arn ${ROLE_ARN} --role-session-name "GitLabRunner-${CI_PROJECT_ID}-${CI_PIPELINE_ID}" --web-identity-token ${GITLAB_OIDC_TOKEN} --duration-seconds 3600 --query 'Credentials.[AccessKeyId,SecretAccessKey,SessionToken]' --output text))
        - curl "https://s3.amazonaws.com/session-manager-downloads/plugin/latest/ubuntu_64bit/session-manager-plugin.deb" -o "session-manager-plugin.deb"
        - dpkg -i session-manager-plugin.deb
        - INSTANCE_ID=$(aws ec2 describe-instances --filters "Name=tag:Name,Values=Network*" --query "Reservations[*].Instances[*].InstanceId" --output text)
        - nohup aws ssm start-session --target $INSTANCE_ID --document-name AWS-StartPortForwardingSessionToRemoteHost --parameters "{\"host\":[\"db.${STAGE}.pocitarna.com\"],\"portNumber\":[\"5432\"],\"localPortNumber\":[\"5432\"]}" &
        - SESSION_PID=$!
        - trap 'kill $SESSION_PID' EXIT
        - npx nx run database:migration:migrate
        - npx nx run database:procedure
    environment:
        name: $STAGE/migration
    tags:
        - x86
        - large

image: gitlab.superkoders.com:5050/sk/pocitarna-nx-2023/node-awscli:22.x

stages:
    - install
    - migration
    - test
    - build
    - deploy
    - runtime

variables:
    AWS_REGION: eu-central-1
    CDK_DEFAULT_REGION: eu-central-1
    GIT_DEPTH: 0
    FF_USE_FASTZIP: 1
    CACHE_COMPRESSION_LEVEL: 'fastest'
    DB_HOST: postgres
    POSTGRES_PASSWORD: rootroot
    POSTGRES_USER: postgres
    POSTGRES_DB: pocitarnanx2023
    NX_PARALLEL: 4

workflow:
    rules:
        - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
        - if: $CI_COMMIT_BRANCH == 'master'
        - if: $CI_COMMIT_BRANCH == 'development'

default:
    cache:
        <<: *node-cache
    tags:
        - x86
        - small
    before_script:
        - NX_HEAD=$CI_COMMIT_SHA
        - NX_BASE=${CI_MERGE_REQUEST_DIFF_BASE_SHA:-$CI_COMMIT_BEFORE_SHA}
    retry:
        max: 2
        when:
            - api_failure
            - data_integrity_failure
            - job_execution_timeout
            - runner_system_failure
            - stuck_or_timeout_failure
            - unknown_failure

# === Install stage ===
install:fresh:
    stage: install
    rules:
        - changes:
              - package-lock.json
        - if: $CI_COMMIT_MESSAGE =~ /\[install\]/
    cache:
        - <<: *npm-cache
          policy: pull-push
        - <<: *node-cache
          policy: push
    script:
        - npm ci --cache .npm --prefer-offline --no-audit --ignore-scripts --fund=false
    tags:
        - x86
        - large

# === Migration stage ===
migration:no-user-migrations:
    <<: *migration
    cache: []
    image: alpine
    rules:
        - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && $CI_COMMIT_MESSAGE !~ /^CI/
          changes:
              - modules/database/src/migration/*
    script:
        - exit 1

migration:generate-migrations:
    <<: *database
    <<: *migration
    rules:
        - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && $CI_COMMIT_MESSAGE !~ /^CI/
          changes:
              - modules/database/src/**/*
              - modules/zodios/src/**/*
    script:
        - git config user.email "*******************"
        - git config user.name "CI"
        - git fetch origin
        - git rebase --abort || true
        - git checkout $CI_COMMIT_REF_NAME
        - git reset --hard origin/$CI_COMMIT_REF_NAME
        - MIGRATION_SHA=$(git log --grep='CI - .*-migration' -1 --pretty=format:"%h")
        - if [[ -n "$MIGRATION_SHA" ]]; then git rebase --onto $MIGRATION_SHA^ $MIGRATION_SHA; fi
        - npx nx run database:drop
        - npx nx run database:migration:migrate
        - npx nx run database:migration:generate
        - if [[ ! $(git diff --name-only --cached modules/database/src/migration) ]]; then exit 0; fi
        - eval $(ssh-agent -s)
        - echo "${SSH_PRIVATE_KEY}" | ssh-add -
        - mkdir -p -m 700 ~/.ssh
        - ssh-keyscan $CI_SERVER_HOST >> ~/.ssh/known_hosts
        - chmod 644 ~/.ssh/known_hosts
        - COMMIT_MESSAGE=$(git diff --name-only --cached | grep -e "-migration.ts" | sed "s%modules/database/src/migration/%%" | sed "s%.ts%%")
        - git commit -m "CI - ${COMMIT_MESSAGE}"
        - git remote set-url origin git@$CI_SERVER_HOST:$CI_PROJECT_PATH.git
        - git push --force
    tags:
        - x86
        - large

# === Test stage ===
test:lint:
    <<: *test
    script:
        - npx nx affected --base=$NX_BASE --head=$NX_HEAD --nxBail --target lint
    tags:
        - x86
        - large

test:types:
    <<: *test
    script:
        - npx nx affected --base=$NX_BASE --head=$NX_HEAD --nxBail --target typecheck
    tags:
        - x86
        - large

test:format:
    <<: *test
    script:
        - npx nx format:check --base=$NX_BASE --head=$NX_HEAD

test:migrate:
    <<: *database
    <<: *test
    rules:
        - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && $CI_COMMIT_MESSAGE =~ /^CI/
    script:
        - npx nx run database:drop
        - npx nx run database:migration:migrate
        - npx nx run database:procedure
    tags:
        - x86
        - large

#test:seed:
#    <<: *database
#    <<: *test
#    rules:
#        - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && $CI_COMMIT_MESSAGE =~ /^CI/
#    script:
#        - npx nx run database:reset
#    tags:
#        - x86
#        - large

# === Build staging stage ===
build:staging:
    <<: *staging
    <<: *build

synth:staging:
    <<: *staging
    <<: *synth

# === Deploy staging stage ===
deploy:staging:
    <<: *staging
    <<: *deploy
    dependencies:
        - synth:staging

# === Runtime staging stage ===
runtime:staging:migrate:
    <<: *staging
    <<: *runtime-migrate
    rules:
        - if: $CI_COMMIT_BRANCH == 'development'
          changes:
              - modules/database/src/migration/*
        - if: $CI_COMMIT_BRANCH == 'development'
          when: manual

# === Build prod stage ===
build:prod:
    <<: *prod
    <<: *build

synth:prod:
    <<: *prod
    <<: *synth

# === Deploy prod stage ===
deploy:prod:
    <<: *prod
    <<: *deploy
    dependencies:
        - synth:prod

# === Runtime prod stage ===
runtime:prod:migrate:
    <<: *prod
    <<: *runtime-migrate
    rules:
        - if: $CI_COMMIT_BRANCH == 'master'
          changes:
              - modules/database/src/migration/*
        - if: $CI_COMMIT_BRANCH == 'master'
          when: manual
