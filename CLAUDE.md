# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Key Development Commands

### Start Development Environment
```bash
npm run dev              # Start web frontend (http://localhost:4200) and spin up Docker services
npm run predev           # Start Docker containers only (Postgres, SQS, etc.)
npm run stop             # Stop Docker containers and clean up volumes
```

### Build and Quality Checks
```bash
npm run check            # Run linting and type checking across all projects
nx run-many -t lint      # Run linting on all projects
nx run-many -t typecheck # Run TypeScript type checking on all projects
nx run-many -t build     # Build all projects
```

### Project-Specific Commands
```bash
nx serve web             # Start Next.js web app on port 4200
nx serve api             # Start Express API server on port 3333
nx build <project-name>  # Build specific project (api, web, photo, etc.)
nx lint <project-name>   # Lint specific project
nx typecheck <project-name> # Type check specific project
```

## Architecture Overview

This is an **Nx monorepo** for a computer/electronics refurbishment and retail management system. The architecture consists of:

### Core Applications
- **`apps/web/`** - Next.js frontend with React, Tailwind CSS, and shadcn/ui components
- **`apps/api/`** - Express.js REST API with TypeORM and PostgreSQL
- **`apps/photo/`** - React Native mobile app for product photography and scanning
- **`apps/shoptet/`** - E-commerce integration service for Shoptet platform
- **`apps/mailer/`** - Email service worker for notifications
- **`apps/housekeeping/`** - Background job processors

### Shared Modules
- **`modules/database/`** - TypeORM entities, controllers, and database management
- **`modules/zodios/`** - API schema definitions and validation
- **`modules/zodios-client/`** - Type-safe API client for frontend
- **`modules/ui/`** - Shared UI components and Tailwind configuration
- **`modules/utils/`** - Common utility functions and business logic
- **`modules/config/`** - Configuration management and constants

### Database Architecture
- **PostgreSQL** with TypeORM ORM
- **60+ entities** with comprehensive audit trails using temporal tables
- **Base patterns**: All entities extend `CommonEntity` with UUID, timestamps, and action tracking
- **Product lifecycle management** with status transitions (AT_SUPPLIER → TESTED → SOLD)
- **Complex attribute system** with conditional logic and conflict resolution
- **Multi-currency support** with automatic rate conversion
- **File management** with flexible attachment system

### Key Business Domains
- **Product Management**: Products, categories, envelopes, attribute values
- **Quality Control**: Testing, defects, service cases, warranty claims  
- **Inventory**: Batches, warehouses, positions, tasks
- **E-commerce**: Orders, Shoptet integration, pricing, wholesale
- **User Management**: Authentication, roles, scopes, permissions

## Development Patterns

### Frontend (Next.js)
- **Page structure**: Uses Next.js pages directory (`apps/web/pages/`)
- **Components**: Organized by domain in `apps/web/components/`
- **UI library**: shadcn/ui components with Radix UI primitives
- **Styling**: Tailwind CSS with custom configuration
- **Data fetching**: Zodios client with React Query for API integration
- **Authentication**: NextAuth.js with database sessions

### Backend (Express.js)
- **Route structure**: Domain-based routes in `apps/api/src/routes/`
- **Controllers**: Base controller pattern with middleware composition
- **Database**: TypeORM with repository pattern and query builders
- **Validation**: Zod schemas for request/response validation
- **Authentication**: JWT-based with role/scope authorization
- **File handling**: S3 integration with presigned URLs

### Mobile (React Native)
- **Barcode scanning**: Zebra scanner integration and camera-based scanning
- **Photo capture**: React Native Vision Camera for product photography
- **Navigation**: React Navigation with drawer and tab navigation
- **State management**: Zustand for global state

### Shared Libraries
- **API contracts**: Zodios for type-safe API definitions
- **Business logic**: Centralized in `modules/utils/` with domain-specific utilities
- **UI components**: Shared between web and mobile where possible
- **Configuration**: Environment-based config management

## Important Conventions

### Code Organization
- **Monorepo structure**: Each app/module has its own `project.json` and `tsconfig.json`
- **Import paths**: Use relative imports within projects, absolute imports between modules
- **Shared code**: Place reusable logic in appropriate modules, not in apps

### Database Conventions
- **Entity naming**: CamelCase with corresponding History tables
- **Audit trails**: Every change tracked with user attribution via `Action` entities
- **Status management**: Use enums for status fields with defined transitions
- **Migrations**: Manual migration control, stored in `modules/database/src/migrations/`

### API Conventions
- **REST endpoints**: Domain-based routing with standard HTTP methods
- **Error handling**: Standardized error responses with proper HTTP status codes
- **Validation**: Zod schemas for all request/response validation
- **Authentication**: Bearer token authentication with scope-based authorization

### Testing and Quality
- **Type safety**: Strict TypeScript configuration across all projects
- **Linting**: ESLint with Prettier for code formatting
- **Git hooks**: Husky with lint-staged for pre-commit quality checks
- **Build validation**: All projects must build successfully before deployment

## Development Workflow

1. **Environment setup**: Run `npm run dev` to start all services
2. **Code changes**: Work in feature branches, follow existing patterns
3. **Quality checks**: Run `npm run check` before committing
4. **Database changes**: Create migrations when modifying entities
5. **API changes**: Update Zodios schemas when changing API contracts
6. **Testing**: Test changes in both web and mobile applications

## Testing Commands

### Running Tests
```bash
# Note: Currently no test runner is configured
# Check individual project.json files for project-specific test commands
```

## Deployment and Infrastructure

### AWS CDK Infrastructure
- **`modules/cdk/`** - AWS CDK infrastructure as code
- **CDK Commands**: Infrastructure deployment using AWS CDK
- **Docker Support**: Dockerfile configurations for containerized deployment

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.