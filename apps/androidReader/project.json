{"name": "androidReader", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/androidReader/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/androidReader", "main": "apps/androidReader/src/index.ts", "tsConfig": "apps/androidReader/tsconfig.app.json", "assets": ["apps/androidReader/src/assets"], "webpackConfig": "apps/androidReader/webpack.config.js", "outputFileName": "android.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "androidReader:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "androidReader:build:development"}, "production": {"buildTarget": "androidReader:build:production"}}}, "package": {"cache": true, "executor": "nx:run-commands", "defaultConfiguration": "dev", "dependsOn": ["build"], "options": {"commands": ["cp dist/apps/androidReader/assets/env.${STAGE}.json dist/apps/androidReader/assets/env.json", "pkg dist/apps/androidReader/android.js --targets node20-win-x64,node20-win-arm64,node20-macos-x64,node20-macos-arm64,node20-linux-x64,node20-linux-arm64 --out-path dist/apps/androidReader/bin"], "env": {"PKG_CACHE_PATH": "tmp/apps/androidReader"}, "parallel": false, "dependsOn": ["build"]}, "configurations": {"dev": {"env": {"STAGE": "dev"}}, "staging": {"env": {"STAGE": "staging"}}, "prod": {"env": {"STAGE": "prod"}}}}, "lint": {}, "typecheck": {}}}