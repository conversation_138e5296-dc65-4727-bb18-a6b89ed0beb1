import { execSync } from 'node:child_process';
import { mkdirSync, readFileSync } from 'node:fs';
import { rm } from 'node:fs/promises';
import * as os from 'node:os';
import * as path from 'node:path';
import AdmZip from 'adm-zip';

const isWin32 = process.platform === 'win32';
const LIB_PATH = path.join(__dirname, '/assets/adb-win-x64.zip');
const LIB_TEMP_PATH = path.join(os.tmpdir(), 'adb');

const ADB_PATH = isWin32 ? path.join(LIB_TEMP_PATH, 'adb.exe') : 'adb';
const shell = isWin32 ? 'cmd.exe' : '/bin/sh';

type Keys = 'battery' | 'devicestoragemonitor' | 'diskstats' | 'display' | 'getprop' | 'imei' | 'meminfo';

export const getDeviceInfo = async () => {
	mkdirSync(LIB_TEMP_PATH, { recursive: true });

	if (isWin32) {
		try {
			execSync('taskkill /F /T /IM adb.exe >nul 2>&1', { shell });
			await rm(LIB_TEMP_PATH, { recursive: true, force: true });
		} catch {
			// ignore
		}
		new AdmZip(LIB_PATH).extractAllTo(LIB_TEMP_PATH, true);
	}

	const commands: Record<Keys, string> = {
		battery: `${ADB_PATH} shell "dumpsys battery" > ${path.join(LIB_TEMP_PATH, 'battery')}`,
		devicestoragemonitor: `${ADB_PATH} shell "dumpsys devicestoragemonitor" > ${path.join(LIB_TEMP_PATH, 'devicestoragemonitor')}`,
		diskstats: `${ADB_PATH} shell "dumpsys diskstats" > ${path.join(LIB_TEMP_PATH, 'diskstats')}`,
		display: `${ADB_PATH} shell "dumpsys display" > ${path.join(LIB_TEMP_PATH, 'display')}`,
		getprop: `${ADB_PATH} shell "getprop" > ${path.join(LIB_TEMP_PATH, 'getprop')}`,
		imei: `${ADB_PATH} shell "input keyevent KEYCODE_WAKEUP; sleep 1; input keyevent KEYCODE_CALL; sleep 1; input text '*#06#'; uiautomator dump --compressed /sdcard/imei" && ${ADB_PATH} shell "cat /sdcard/imei" > ${path.join(LIB_TEMP_PATH, 'imei')} && ${ADB_PATH} shell "rm /sdcard/imei; input keyevent KEYCODE_HOME"`,
		meminfo: `${ADB_PATH} shell "dumpsys meminfo" > ${LIB_TEMP_PATH}/meminfo`,
	};

	const result: Record<Keys, string> = {
		battery: '',
		devicestoragemonitor: '',
		diskstats: '',
		display: '',
		getprop: '',
		imei: '',
		meminfo: '',
	};

	execSync(`${ADB_PATH} start-server`, { shell, encoding: 'utf-8' });
	// eslint-disable-next-line no-console
	console.log(execSync(`${ADB_PATH} devices`, { shell, encoding: 'utf-8' }));

	for (const key of Object.keys(commands)) {
		try {
			execSync(commands[key as Keys], { shell, encoding: 'utf-8' });
			result[key as Keys] = readFileSync(path.join(LIB_TEMP_PATH, key)).toString('utf-8').trim().replace(/\n\r/g, '');
		} catch {
			console.warn('Command failed', commands[key as Keys]);
		}
	}

	execSync(`${ADB_PATH} kill-server`, { shell, encoding: 'utf-8' });

	if (isWin32) {
		try {
			await rm(LIB_TEMP_PATH, { recursive: true, force: true });
		} catch {
			// ignore
		}
	}

	return result;
};
