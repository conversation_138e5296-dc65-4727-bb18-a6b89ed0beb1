/* eslint-disable no-console */
import { SYSTEM_USER_AUTH_ID } from '@pocitarna-nx-2023/config';
import * as QRCode from 'qrcode';
import superagent from 'superagent';
import { parseEnv } from './parseEnv';

const { API_ORIGIN, NEXTAUTH_SECRET } = parseEnv();

const blobToBase64 = async (blob: Blob) => {
	const arrayBuffer = await blob.arrayBuffer();
	const buffer = Buffer.from(arrayBuffer);
	return buffer.toString('base64');
};

let isRunning = false;

export const getAllAndUpload = async () => {
	if (isRunning) return;
	isRunning = true;
	try {
		const sessionResponse = await superagent
			.post(`${API_ORIGIN}/auth/session/verification-token`)
			.send({ verificationToken: SYSTEM_USER_AUTH_ID })
			.set('Content-Type', 'application/json')
			.set('x-auth-secret', NEXTAUTH_SECRET);

		if (!sessionResponse.ok) {
			throw new Error(`Failed to create session: ${sessionResponse.status}`);
		}

		const sessionData = sessionResponse.body as { _data: { sessionToken: string } };
		const sessionToken = sessionData?._data?.sessionToken;
		if (!sessionToken) return console.error('Nelze vytvořit sessionToken');

		console.info('Vyčítání produktu...');
		const result: { deviceInfo: object | null } = {
			deviceInfo: null,
		};

		await Promise.all([
			(async () => {
				console.info('Vyčítání Android započalo...');
				const { getDeviceInfo } = await import('./getDeviceInfo');
				result.deviceInfo = await getDeviceInfo();
				console.info('Vyčítání Android dokončeno.');
			})(),
		]);

		console.info('Zpracovávání dat produktu...');
		const { deviceInfo } = result;
		const readers = [
			{
				fileName: 'android.json',
				type: 'application/json',
				data: deviceInfo && new Blob([JSON.stringify(deviceInfo)], { type: 'application/json' }),
			},
		];
		console.info('Vyčtení produktu dokončeno.');

		const fileIds = [];

		console.info('Nahrávání dat do CMP...');
		for (const reader of readers) {
			if (!reader.data) continue;

			try {
				const fileResponse = await superagent
					.post(`${API_ORIGIN}/files/upload/base64`)
					.set('Content-Type', 'application/json')
					.set('Cookie', `next-auth.session-token=${sessionToken}`)
					.send({ name: reader.fileName, data: await blobToBase64(reader.data) });

				const fileData = fileResponse.body as { _data: string };
				fileIds.push(fileData._data);
			} catch {
				throw new Error(`Nepodařio se nahrát soubor ${reader.fileName}`);
			}
		}

		try {
			const productResponse = await superagent
				.post(`${API_ORIGIN}/product/attribute-value-loader`)
				.set('Content-Type', 'application/json')
				.set('Cookie', `next-auth.session-token=${sessionToken}`)
				.send({ fileIds });

			const sn = (productResponse.body as { _data: string })._data;

			console.info('Nahrávání dat do CMP dokončeno.\n');
			console.info(await QRCode.toString(sn, { type: 'terminal' }));
			console.info('Načti QR kód SN do CMP pro dokončení testu produktu');
		} catch (error) {
			if ((<any>error).status === 404) {
				throw new Error(`Nepodařilo se nalézt produkt`);
			}
			throw new Error(`Nepodařilo se přiřadit parametry k produktu: ${(<any>error).status}`);
		}
	} catch (error) {
		console.error(error);
		console.info('Automatické zpracovávání dat zastaveno. Můžete jej spustit znovu pomocí klávesy Enter.');
	} finally {
		isRunning = false;
	}
};
