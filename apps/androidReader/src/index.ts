/* eslint-disable no-console */
import { READER_PORT } from '@pocitarna-nx-2023/config';
import cors from 'cors';
import express from 'express';
import helmet from 'helmet';

const abortController = new AbortController();

const app = express();
app.use(cors({ origin: true, credentials: true }));
app.use(helmet({ crossOriginResourcePolicy: { policy: 'cross-origin' } }));

app.get('/hwinfo', async (_, res) => res.status(204).json());
app.get('/systeminfo', async (_, res) => res.status(204).json());
app.get('/gpuz', async (_, res) => res.status(204).json());
app.get('/pnp', async (_, res) => res.status(204).json());
app.get('/aggreg', async (_, res) => res.status(204).json());
app.get('/ios', async (_, res) => res.status(204).json());

app.get('/android', async (_, res) => {
	console.time('Android');
	console.info('Požadavek na Android se začal zpracovávat...');
	try {
		const { getDeviceInfo } = await import('./getDeviceInfo');
		const data = await getDeviceInfo();
		res.setHeader('Content-Type', 'application/json');
		res.send(data);
		console.info('Android bylo úspěšně zpracováno.');
	} catch (error) {
		console.warn('Android nebylo možné zpracovat.');
		console.error(error);
		res.status(500).send();
	}
	console.timeEnd('Android');
});

const server = app.listen(READER_PORT, () => {
	console.info(`Vyčítač pro Android běží na portu ${READER_PORT}\n`);
});

const abortHandler: NodeJS.SignalsListener = (...args) => {
	console.warn('Aborting...', ...args);
	abortController.abort();
	server.close();
};

process.on('SIGINT', abortHandler).on('SIGTERM', abortHandler).on('uncaughtException', abortHandler);

const stdin = process.stdin;
stdin.setRawMode(true);
stdin.resume();
stdin.setEncoding('utf8');
stdin.on('data', async (key: string) => {
	const keyCode = key.charCodeAt(0);
	// ctrl-c ( end of text )
	if (keyCode === 3) {
		process.exit();
	}
	// enter
	if (keyCode === 10 || keyCode === 13) {
		const { getAllAndUpload } = await import('./getAllAndUpload');
		await getAllAndUpload();
	}
});

console.info('Stiskni ENTER pro vyčtení produktu...');
