import { readFileSync } from 'node:fs';
import * as path from 'node:path';

type Env = { API_ORIGIN: string; NEXTAUTH_SECRET: string };

const ENV_PATH = path.join(__dirname, '/assets/env.json');

const isValidEnv = (env: any): env is Env =>
	env &&
	env.API_ORIGIN &&
	env.NEXTAUTH_SECRET &&
	typeof env.API_ORIGIN === 'string' &&
	typeof env.NEXTAUTH_SECRET === 'string' &&
	env.API_ORIGIN.length > 0 &&
	env.NEXTAUTH_SECRET.length > 0;

export const parseEnv = () => {
	const contents = readFileSync(ENV_PATH);
	const env = JSON.parse(contents.toString());
	if (!isValidEnv(env)) {
		throw new Error('Invalid env file');
	}

	return env;
};
