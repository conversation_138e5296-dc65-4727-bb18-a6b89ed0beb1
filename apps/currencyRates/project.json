{"name": "currencyRates", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/currencyRates/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"platform": "node", "outputPath": "dist/apps/currencyRates", "format": ["cjs"], "bundle": false, "main": "apps/currencyRates/src/main.ts", "tsConfig": "apps/currencyRates/tsconfig.app.json", "assets": ["apps/currencyRates/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {}, "production": {"esbuildOptions": {"sourcemap": false, "outExtension": {".js": ".js"}}}}}, "deploy": {"executor": "nx:run-commands", "defaultConfiguration": "staging", "configurations": {"staging": {"command": "nx run cdk:deploy:staging CurrencyRates"}, "prod": {"command": "nx run cdk:deploy:prod CurrencyRates"}}}, "serve": {"continuous": true, "executor": "nx:run-commands", "options": {"commands": ["nx run cdk:synth", "sam local start-lambda --template ./tmp/cdk.out/CurrencyRates.template.json --env-vars ./lambda.dev.json --invoke-image gitlab.superkoders.com:5050/sk/pocitarna-nx-2023/lambda-runtime:latest", "nx run cdk:clean"], "parallel": false}}, "invoke": {"executor": "nx:run-commands", "options": {"command": "aws lambda invoke --function-name <PERSON><PERSON> --region eu-central-1 --cli-read-timeout 900 --endpoint-url http://localhost:3001 --no-verify-ssl /dev/null"}}, "lint": {}, "typecheck": {}}}