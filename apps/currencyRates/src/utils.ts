import { Batch<PERSON>ontroller } from '@pocitarna-nx-2023/database';
import { isSupportedCurrency } from '@pocitarna-nx-2023/utils';
import { type RatesMap } from './types';

export const parseCurrencyResponse = (textResponse: string): RatesMap => {
	const lines = textResponse.split('\n').slice(2);

	return lines.reduce<RatesMap>((ratesMap, currentLine) => {
		const { code, rate } = extractDataFromLine(currentLine);
		if (code && isSupportedCurrency(code) && rate) {
			ratesMap[code] = parseStringifiedRate(rate);
		}

		return ratesMap;
	}, {} as RatesMap);
};

const extractDataFromLine = (line: string) => {
	const [, , , code, rate] = line.split('|');

	return { code, rate };
};

const parseStringifiedRate = (rate: string) => parseFloat(rate.replace(',', '.'));

export const updateDeliveredBatchesCurrencyRate = async () => {
	const [batchesToUpdate] = await new BatchController().findBatchesNeedingCurrencyRateUpdate();

	await Promise.all(
		batchesToUpdate.map(async (batch) => {
			await new BatchController().applyLatestCurrencyRate(batch.id);
		}),
	);
};
