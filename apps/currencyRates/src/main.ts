import { withSentry } from '@pocitarna-nx-2023/aws';
import { CURRENCY_RATES_LAMBDA_ENDPOINT } from '@pocitarna-nx-2023/config';
import { CurrencyController, CurrencyRateController, withAction, withDatabase } from '@pocitarna-nx-2023/database';
import { generateCurrencyRateUrl } from '@pocitarna-nx-2023/utils';
import { type CurrencyRateBaseInfo } from './types';
import { parseCurrencyResponse, updateDeliveredBatchesCurrencyRate } from './utils';

export const handler = withSentry(
	'https://<EMAIL>/4507849354248272',
	withDatabase(
		withAction(CURRENCY_RATES_LAMBDA_ENDPOINT, async () => {
			const endpoint = generateCurrencyRateUrl(new Date());
			const res = await fetch(endpoint);

			if (!res.ok) return;

			const textResponse = await res.text();
			const ratesMap = parseCurrencyResponse(textResponse);
			const [currencies] = await new CurrencyController().list({});

			const ratesWithCurrency: CurrencyRateBaseInfo[] = Object.entries(ratesMap)
				.map(([code, rate]) => {
					const currency = currencies.find((currency) => currency.code === code);

					if (!currency) return null;

					const currencyRate: CurrencyRateBaseInfo = {
						currency,
						rate,
						source: endpoint,
					};

					return currencyRate;
				})
				.filter((entry): entry is CurrencyRateBaseInfo => entry !== null);

			const promises = ratesWithCurrency.map(async (entity) => {
				const match = await new CurrencyRateController().findRateByCurrencyAndSource(entity.currency, entity.source);

				if (!match) {
					await new CurrencyRateController().create(entity);
				}
			});

			await Promise.all(promises);

			await updateDeliveredBatchesCurrencyRate();
		}),
	),
);
