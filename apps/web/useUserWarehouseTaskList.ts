import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useSession } from 'next-auth/react';
import { useMemo } from 'react';

export const useUserWarehouseTaskList = () => {
	const { data } = useSession();
	const userId = data?.user?.userId;
	const { data: warehouseTasksData, invalidate } = apiHooks.useGetWarehouseTasks(
		{
			queries: {
				filter: { type: { eq: 'SHIFT' }, status: { eq: 'OPEN' }, userId: { eq: userId } },
				page: 1,
				limit: MAX_POSITIVE_INTEGER,
				sort: [
					'warehousePosition.sector',
					'warehousePosition.rack',
					'warehousePosition.shelf',
					'warehousePosition.box',
					'entity.createdAt',
					'entity.id',
				],
			},
		},
		{ enabled: !!userId },
	);

	const warehouseTasks = useMemo(() => warehouseTasksData?._data ?? [], [warehouseTasksData?._data]);

	return { warehouseTasks, invalidateUserWarehouseTasks: invalidate };
};
