import { EMPTY_VALUE } from '@pocitarna-nx-2023/config';
import { checkIfDiskCapacitiesMatch, splitAttributesByDisplayName } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { useMemo } from 'react';
import { useCategoryAttributes } from './useCategoryAttributes';
import { useProductAttributeValues } from './useProductAttributeValues';

type CompareMismatchAttributesType = {
	displayName: string;
	importValue: ApiBody<'getProductAttributeValues'>[number];
	resolvedValue: ApiBody<'getProductAttributeValues'>[number];
};

export const useCompareMismatchAttributes = (product: ApiBody<'getProduct'>) => {
	const { categoryAttributes } = useCategoryAttributes(product.productCategoryId, 'compare');
	const { productAttributeValues } = useProductAttributeValues(product);

	return useMemo(() => {
		const [categoryDiskAttributes, otherCategoryAttributes] = splitAttributesByDisplayName(
			categoryAttributes,
			(item) => item.displayName,
		);

		const [productDiskAttributeValues] = splitAttributesByDisplayName(productAttributeValues, (item) => item.attribute.displayName);

		const diskCapacitiesMatch = checkIfDiskCapacitiesMatch(
			productDiskAttributeValues,
			(item) => item.attributeValueType === 'service',
			(item) => item.attributeValueType === 'import',
			(item) => item.attributeValueType === 'resolved',
		);

		return [
			...findMismatches(otherCategoryAttributes, productAttributeValues),
			...(!diskCapacitiesMatch ? findMismatches(categoryDiskAttributes, productAttributeValues) : []),
		];
	}, [categoryAttributes, productAttributeValues]);
};

const findMismatches = (
	categoryAttributes: ApiBody<'getProductCategoryAttributes'>,
	productAttributeValues: ApiBody<'getProductAttributeValues'>,
) => {
	return categoryAttributes.reduce<CompareMismatchAttributesType[]>((acc, categoryAttribute) => {
		const serviceAttributeValue = productAttributeValues.find(
			(item) => item.attribute.id === categoryAttribute.id && item.attributeValueType === 'service',
		);

		const importAttributeValue = productAttributeValues.find(
			(item) => item.attribute.id === categoryAttribute.id && item.attributeValueType === 'import',
		);

		const resolvedAttributeValue = productAttributeValues.find(
			(item) => item.attribute.id === categoryAttribute.id && item.attributeValueType === 'resolved',
		);

		const compareAgainst = serviceAttributeValue ?? importAttributeValue;

		if (!compareAgainst || !resolvedAttributeValue) return acc;

		const hasPlaceholderValue = compareAgainst.value === EMPTY_VALUE;

		if (hasPlaceholderValue) return acc;

		if (compareAgainst.value === resolvedAttributeValue.value) return acc;
		if (compareAgainst.value === 'SSD' && ['NVMe', 'SATA'].includes(resolvedAttributeValue.value as string)) return acc; // FIXME - is there a way we don't have to hardcode this?

		return [
			...acc,
			{
				displayName: compareAgainst.attribute.displayName,
				importValue: compareAgainst,
				resolvedValue: resolvedAttributeValue,
			},
		];
	}, []);
};
