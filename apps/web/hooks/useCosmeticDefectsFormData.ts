import { MAX_POSITIVE_INTEGER, PRODUCT_TYPES } from '@pocitarna-nx-2023/config';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useMemo } from 'react';
import { z } from 'zod';
import { calculateProductGrade } from '../utils/productGrading';

export const useCosmeticDefectsFormData = (product: ApiBody<'getProduct'>) => {
	const { data: cosmeticAreasData } = apiHooks.useGetCosmeticAreas({
		queries: {
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
			filter: { productCategoryId: { eq: product.productCategoryId } },
			sort: ['name'],
		},
	});
	const cosmeticAreas = useMemo(() => cosmeticAreasData?._data ?? [], [cosmeticAreasData?._data]);

	const { data: categoryGradesData } = apiHooks.useGetProductCategoryGrades(
		{
			params: {
				productCategoryId: product.productCategoryId ?? '',
			},
		},
		{ enabled: Boolean(product.productCategoryId) },
	);

	const categoryGrades = useMemo(() => categoryGradesData?._data ?? [], [categoryGradesData?._data]);

	const { data: categoryCosmeticDefectsData } = apiHooks.useGetCosmeticDefects({
		queries: {
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
			filter: {
				'productCategoryCosmeticDefects.productCategoryId': { eq: product.productCategoryId },
				gradeId: { eq: categoryGrades.map((item) => item.gradeId) },
			},

			sort: ['name'],
		},
	});
	const categoryCosmeticDefects = useMemo(() => {
		return categoryCosmeticDefectsData?._data ?? [];
	}, [categoryCosmeticDefectsData?._data]);

	const schema = z.object({
		gradeId: z.string().uuid(),
		type: z.enum(PRODUCT_TYPES),
		clearCategoryCosmeticAreas: z.record(z.string().uuid(), z.boolean()),
	});

	const { data: productCosmeticDefectsData } = apiHooks.useGetProductCosmeticDefects({
		queries: {
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
			filter: { productId: { eq: product.id } },
			sort: ['cosmeticDefect.name'],
		},
	});
	const productCosmeticDefects = useMemo(() => productCosmeticDefectsData?._data ?? [], [productCosmeticDefectsData?._data]);

	const { data: rankedGradesData, isFetched } = apiHooks.useGetRankedGrades({});

	const rankedGrades = useMemo(() => rankedGradesData?._data ?? [], [rankedGradesData?._data]);

	const calculatedGradeId = useMemo(() => {
		if (!isFetched) return null;
		return calculateProductGrade({ defects: productCosmeticDefects, product, categoryGrades });
	}, [productCosmeticDefects, isFetched, product, categoryGrades]);

	const defaultValues = useMemo(() => {
		return {
			gradeId: calculatedGradeId ?? '',
			type: product.type ?? 'REFURBISHED',
			clearCategoryCosmeticAreas: cosmeticAreas.reduce<Record<string, boolean>>((acc, area) => {
				acc[area.id] = false;
				return acc;
			}, {}),
		};
	}, [calculatedGradeId, product.type, cosmeticAreas]);

	const allRelevantDefectsHaveImage = useMemo(
		() =>
			productCosmeticDefects.every((defect) => {
				if (!defect.cosmeticDefect?.pictureRequired) return true;
				return defect.files.length > 0;
			}),
		[productCosmeticDefects],
	);

	return {
		cosmeticAreas,
		categoryCosmeticDefects,
		productCosmeticDefects,
		rankedGrades,
		calculatedGradeId,
		defaultValues,
		schema,
		allRelevantDefectsHaveImage,
	};
};
