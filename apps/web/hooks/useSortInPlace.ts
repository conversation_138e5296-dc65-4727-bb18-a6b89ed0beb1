import { useMemo } from 'react';
import { useSorting } from '../contexts/SortingContext';

export const useSortInPlace = <T>(rows: T[]) => {
	const { sorting } = useSorting();

	const sortedRows = useMemo(() => {
		if (!sorting || sorting.length === 0) return rows;

		return sorting
			.slice()
			.reverse()
			.reduce((accRows, [key, direction]) => {
				return accRows.toSorted((a, b) => {
					const aVal = a[key as keyof typeof a];
					const bVal = b[key as keyof typeof b];

					if (aVal == null && bVal == null) return 0;
					if (aVal == null) return 1;
					if (bVal == null) return -1;

					if (typeof aVal === 'number' && typeof bVal === 'number') {
						return direction === 'asc' ? aVal - bVal : bVal - aVal;
					}

					if (typeof aVal === 'string' && typeof bVal === 'string') {
						return direction === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
					}

					return 0;
				});
			}, rows);
	}, [rows, sorting]);

	return { sortedRows };
};
