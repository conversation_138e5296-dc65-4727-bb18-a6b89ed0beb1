import { useCallback, useEffect } from 'react';

export const useFormKeyboardHandler = () => {
	const handleKeyDown = useCallback((event: KeyboardEvent) => {
		const target = event.target as HTMLElement;
		const form = target.closest('form');
		if (!form) return;

		if (event.key === 'Enter') {
			const isButton = target.tagName === 'BUTTON';
			const isTextarea = target.tagName === 'TEXTAREA';
			const isCombobox = target.role === 'combobox';
			const isCtrlOrCmdPressed = event.ctrlKey || event.metaKey;
			if ((isButton || isTextarea || isCombobox) && !isCtrlOrCmdPressed) return;

			if (!isCtrlOrCmdPressed) {
				event.preventDefault();
				return;
			}

			if (isCtrlOrCmdPressed) {
				event.preventDefault();
				const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
				form.dispatchEvent(submitEvent);
			}
		}
	}, []);

	useEffect(() => {
		document.addEventListener('keydown', handleKeyDown, true);
		return () => document.removeEventListener('keydown', handleKeyDown, true);
	}, [handleKeyDown]);
};
