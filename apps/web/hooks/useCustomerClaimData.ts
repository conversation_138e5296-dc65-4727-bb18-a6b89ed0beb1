import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { filterUndefined, uniques, uniquesBy } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';

export const useCustomerClaimData = (customerClaim: ApiBody<'getCustomerClaim'>, scope?: 'all-affected-products') => {
	const { data: productDefectsData } = apiHooks.useGetAllProductDefects({
		queries: {
			sort: [['createdAt', 'asc']],
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
			filter: {
				customerClaimId: {
					eq: customerClaim.id,
				},
			},
		},
	});
	const productDefects = productDefectsData?._data ?? [];
	const referenceDefect = productDefects[0];

	const product = customerClaim.ecommerceOrderItem?.product;

	const { data: allAffectedProductsData } = apiHooks.useGetAllProducts(
		{
			queries: {
				page: 1,
				limit: MAX_POSITIVE_INTEGER,
				filter: { id: { eq: uniques(filterUndefined(productDefects.map((item) => item?.productId))) } },
			},
		},
		{ enabled: scope === 'all-affected-products' && productDefects.length > 0 },
	);

	const allAffectedProducts = allAffectedProductsData?._data ?? [];

	return {
		product,
		referenceDefect,
		productDefects,
		allAffectedProducts: uniquesBy([product, ...allAffectedProducts] as ApiBody<'getAllProducts'>, 'id'),
	};
};
