import { type ComboBoxItemType } from '@pocitarna-nx-2023/ui';
import { type ApiAliases, type ListProps } from '@pocitarna-nx-2023/zodios';
import { type UIEvent, useCallback, useMemo, useState } from 'react';
import { useInfiniteQuery } from './useInfiniteQuery';

// TS struggles to infer the type from the ApiBody<Alias>, hence I'm explicitely requiring a generic for the item type

type Params<T> = {
	params?: Record<string, string>;
	queries?: ListProps;
	formatResult: (item: T) => ComboBoxItemType;
	selectedItem?: ComboBoxItemType;
	canRun?: boolean;
};

export const useLazyComboboxProps = <T>(alias: ApiAliases, { selectedItem, params, formatResult, queries, canRun = true }: Params<T>) => {
	const [enabled, setEnabled] = useState(false);

	const { data, isFetching, fetchNextPage, hasNextPage } = useInfiniteQuery(alias, { params, queries }, canRun && enabled);

	const queryResults = useMemo(
		() =>
			(data?.pages ?? []).reduce<T[]>((acc, curr) => {
				acc.push(...(curr._data as T[]));
				return acc;
			}, []),
		[data?.pages],
	);

	const items = useMemo(() => {
		const formatted = queryResults.map((item) => formatResult(item));

		if (selectedItem) return [selectedItem, ...formatted];

		return formatted;
	}, [queryResults, selectedItem, formatResult]);

	const onHover = useCallback(() => setEnabled(true), []);

	const onScroll = useCallback(
		(e: UIEvent<HTMLElement>) => {
			const target = e.target as HTMLElement;
			const scrollHeight = target.scrollHeight;
			const scrollTop = target.scrollTop;
			const clientHeight = target.clientHeight;

			if (scrollTop + clientHeight >= scrollHeight && !isFetching && hasNextPage) {
				fetchNextPage?.();
			}
		},
		[isFetching, fetchNextPage, hasNextPage],
	);

	return useMemo(() => ({ onHover, onScroll, items, queryResults }), [onScroll, items, onHover, queryResults]);
};
