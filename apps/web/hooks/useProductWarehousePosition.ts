import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useMemo } from 'react';

export const useProductWarehousePosition = (product: ApiBody<'getProduct'>) => {
	const { data: warehousePositionData } = apiHooks.useGetWarehousePosition(
		{
			params: { warehousePositionId: product.warehousePositionId ?? '' },
		},
		{ enabled: Boolean(product.warehousePositionId) },
	);
	return useMemo(() => warehousePositionData?._data ?? null, [warehousePositionData?._data]);
};
