import { type ApiBody } from '@pocitarna-nx-2023/zodios';

export const useShouldShowClaimNotice = (product: ApiBody<'getAllProducts'>[number]) => {
	const openProductDefects = product.productDefects.filter(
		(defect) => !defect.serviceTaskId && defect.defectType.name !== 'Neshodující se parametry',
	);

	if (openProductDefects.length > 0) return true;

	return product.productTest?.hasTestMismatches && !product.productTest?.ignoreMismatch;
};
