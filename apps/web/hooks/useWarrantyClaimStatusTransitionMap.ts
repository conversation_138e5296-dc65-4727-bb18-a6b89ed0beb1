import { type WarrantyClaimStatus } from '@pocitarna-nx-2023/config';
import { type Button } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useQueryClient } from '@tanstack/react-query';
import { type ComponentProps, type FC, useState } from 'react';
import { DefaultStatusTransitionForm } from '../components/warrantyClaim/warrantyClaimTransitionHandler/DefaultStatusTransitionForm';
import { FromNewToWaitingForVendorForm } from '../components/warrantyClaim/warrantyClaimTransitionHandler/FromNewToWaitingForVendorForm';
import { FromRepairToClosedForm } from '../components/warrantyClaim/warrantyClaimTransitionHandler/FromRepairToClosedForm';
import { FromVendorToDiscountForm } from '../components/warrantyClaim/warrantyClaimTransitionHandler/FromVendorToDiscountForm';
import { FromVendorToRejectedForm } from '../components/warrantyClaim/warrantyClaimTransitionHandler/FromVendorToRejectedForm';
import { FromVendorToWaitingToRepair } from '../components/warrantyClaim/warrantyClaimTransitionHandler/FromVendorToWaitingToRepair';
import { PieceTradingForm } from '../components/warrantyClaim/warrantyClaimTransitionHandler/PieceTradingForm';
import { ResolveByVendorForm } from '../components/warrantyClaim/warrantyClaimTransitionHandler/ResolveByVendorForm';
import { SentToVendorForm } from '../components/warrantyClaim/warrantyClaimTransitionHandler/SentToVendorForm';
import { WithProductAutopsyForm } from '../components/warrantyClaim/warrantyClaimTransitionHandler/WithProductAutopsyForm';
import { WithProductReturnedForm } from '../components/warrantyClaim/warrantyClaimTransitionHandler/WithProductReturnedForm';
import { WithProductStockForm } from '../components/warrantyClaim/warrantyClaimTransitionHandler/WithProductStockForm';
import { WithServiceCaseForm } from '../components/warrantyClaim/warrantyClaimTransitionHandler/WithServiceCaseForm';

export type ModalForm = FC<ComponentProps<typeof DefaultStatusTransitionForm>>;

export type TargetTransitionInfo = {
	status: WarrantyClaimStatus;
	label: string;
	modalForm?: ModalForm;
	variant?: ComponentProps<typeof Button>['variant'];
};

type Params = {
	warrantyClaimStatus: WarrantyClaimStatus | null;
	productDefects?: ApiBody<'getAllProductDefects'>;
};

export const useWarrantyClaimStatusTransitionMap = ({ productDefects = [], warrantyClaimStatus }: Params) => {
	const [selectedTargetTransition, setSelectedTargetTransition] = useState<TargetTransitionInfo | null>(null);

	if (!warrantyClaimStatus)
		return { possibleTransitions: [], selectedTargetTransition: null, setSelectedTargetTransition, FormToShow: null };

	const hasAccessToProductDefects = productDefects.length > 0;
	const FormToShow: ModalForm = selectedTargetTransition?.modalForm ?? DefaultStatusTransitionForm;
	const productCanGoToStock = productDefects.every((defect) => defect.serviceTask?.status === 'CLOSED');
	const thereAreUnsolvedDefects = productDefects.some((defect) => defect.serviceTask?.status !== 'CLOSED');

	const claimStatusTransitionsMap: Record<WarrantyClaimStatus, TargetTransitionInfo[]> = {
		NEW: [{ status: 'WAITING_FOR_VENDOR', label: 'Nahlásit dodavateli', modalForm: FromNewToWaitingForVendorForm }],
		CLOSED: [],
		SENT_TO_VENDOR: [
			{ status: 'REFUNDED', label: 'Vrácení zboží' },
			{ status: 'WAITING_FOR_DISCOUNT', label: 'Dohodnutá sleva' },
			...(hasAccessToProductDefects
				? [
						{ status: 'REJECTED', label: 'Neuznaná reklamace', modalForm: FromVendorToRejectedForm } as const,
						{
							status: 'RESOLVED_BY_VENDOR',
							label: 'Vyřešeno dodavatelem',
							modalForm: ResolveByVendorForm,
						} as const,
						{ status: 'TRADED_PIECE', label: 'Náhradní kus', modalForm: PieceTradingForm } as const,
					]
				: []),
		],
		WAITING_FOR_RETURN: [],
		WAITING_FOR_VENDOR: [
			{ status: 'SENT_TO_VENDOR', label: 'Odeslat dodavateli', modalForm: SentToVendorForm },
			{ status: 'WAITING_FOR_DISCOUNT', label: 'Dohodnutá sleva' },
			...(hasAccessToProductDefects
				? [
						{
							status: 'WAITING_FOR_REPAIR',
							label: 'Poslat na servis',
							modalForm: FromVendorToWaitingToRepair,
						} as const,
						{ status: 'REJECTED', label: 'Neuznaná reklamace', modalForm: FromVendorToRejectedForm } as const,
						{
							status: 'RESOLVED_BY_VENDOR',
							label: 'Vyřešeno dodavatelem',
							modalForm: ResolveByVendorForm,
						} as const,
					]
				: []),
		],
		WAITING_FOR_DISCOUNT: hasAccessToProductDefects
			? [{ status: 'DISCOUNT', label: 'Zadat slevu', modalForm: FromVendorToDiscountForm }]
			: [],
		REJECTED: [],
		DISCOUNT: [
			...(thereAreUnsolvedDefects
				? [{ status: 'CLOSED', label: 'Uzavřít případ, poslat do interního servisu', modalForm: WithServiceCaseForm } as const]
				: []),
			...(productCanGoToStock
				? [
						{
							status: 'CLOSED',
							label: 'Uzavřít případ, poslat na naskladnění',
							modalForm: WithProductStockForm,
						} as const,
					]
				: []),
			{ status: 'CLOSED', label: 'Uzavřít případ, vráceno dodavateli', modalForm: WithProductReturnedForm },
			{ status: 'CLOSED', variant: 'destructive', label: 'Uzavřít případ, poslat na pitevnu', modalForm: WithProductAutopsyForm },
		],
		WAITING_FOR_REPAIR: [
			...(hasAccessToProductDefects
				? [
						{
							status: 'CLOSED',
							label: 'Potvrdit přijetí zboží a předat na testování',
							modalForm: FromRepairToClosedForm,
						} as const,
					]
				: []),
		],
		REFUNDED: [],
		RESOLVED_BY_VENDOR: [],
		TRADED_PIECE: hasAccessToProductDefects
			? [
					{
						status: 'CLOSED',
						label: 'Potvrdit přijetí zboží a předat na testování',
						modalForm: FromRepairToClosedForm,
					} as const,
				]
			: [],
	};

	const possibleTransitions = claimStatusTransitionsMap[warrantyClaimStatus];

	return { possibleTransitions, selectedTargetTransition, setSelectedTargetTransition, FormToShow };
};

export const useGetWarrantyClaimTransitionInvalidations = () => {
	const queryClient = useQueryClient();

	const getClaimListQueryKey = apiHooks.getKeyByAlias('getWarrantyClaims');
	const invalidateWarrantyClaimList = () => queryClient.invalidateQueries(getClaimListQueryKey);
	const getClaimHistoryQueryKey = apiHooks.getKeyByAlias('getWarrantyClaimHistory');
	const invalidateWarrantyClaimHistory = () => queryClient.invalidateQueries(getClaimHistoryQueryKey);
	const getClaimQueryKey = apiHooks.getKeyByAlias('getWarrantyClaim');
	const invalidateWarrantyClaim = () => queryClient.invalidateQueries(getClaimQueryKey);
	const getProductQueryKey = apiHooks.getKeyByAlias('getProduct');
	const invalidateProduct = () => queryClient.invalidateQueries(getProductQueryKey);
	const getProductDefectsQueryKey = apiHooks.getKeyByAlias('getAllProductDefects');
	const invalidateProductDefects = () => queryClient.invalidateQueries(getProductDefectsQueryKey);
	const getProductHistoryQueryKey = apiHooks.getKeyByAlias('getProductHistory');
	const invalidateProductHistory = () => queryClient.invalidateQueries(getProductHistoryQueryKey);

	const invalidate = () => {
		invalidateWarrantyClaimList();
		invalidateWarrantyClaimHistory();
		invalidateWarrantyClaim();
		invalidateProduct();
		invalidateProductDefects();
		invalidateProductHistory();
	};

	return {
		invalidateWarrantyClaimList,
		invalidateWarrantyClaimHistory,
		invalidateWarrantyClaim,
		invalidateProduct,
		invalidateProductDefects,
		invalidateProductHistory,
		invalidate,
	};
};
