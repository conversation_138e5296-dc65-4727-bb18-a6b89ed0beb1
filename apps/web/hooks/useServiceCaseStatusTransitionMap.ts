import { type ServiceCaseStatus, ServiceCaseStatusMessage } from '@pocitarna-nx-2023/config';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useQueryClient } from '@tanstack/react-query';
import { type ComponentProps, type FC, useState } from 'react';
import { DefaultStatusTransitionForm } from '../components/service/serviceCaseTransitionHandler/DefaultStatusTransitionForm';
import { FromAssignedToServiceCenterForm } from '../components/service/serviceCaseTransitionHandler/FromAssignedToServiceCenterForm';
import { FromBuybackToClosedForm } from '../components/service/serviceCaseTransitionHandler/FromBuybackToClosedForm';
import { FromNewToAssignedForm } from '../components/service/serviceCaseTransitionHandler/FromNewToAssignedForm';
import { FromReceivedToRejectedForm } from '../components/service/serviceCaseTransitionHandler/FromReceivedToRejectedForm';
import { FromRejectedToInternalServiceForm } from '../components/service/serviceCaseTransitionHandler/FromRejectedToInternalServiceForm';
import { FromRepairToClosedForm } from '../components/service/serviceCaseTransitionHandler/FromRepairToClosedForm';
import { FromRepairToNewForm } from '../components/service/serviceCaseTransitionHandler/FromRepairToNewForm';
import { FromReturnToClosedForm } from '../components/service/serviceCaseTransitionHandler/FromReturnToClosedForm';
import { FromServiceCenterToOfferReceivedForm } from '../components/service/serviceCaseTransitionHandler/FromServiceCenterToOfferReceivedForm';
import { WithNewServiceCenterForm } from '../components/service/serviceCaseTransitionHandler/WithNewServiceCenterForm';

type ModalForm = FC<ComponentProps<typeof DefaultStatusTransitionForm>>;

export type TargetTransitionInfo = {
	status: ServiceCaseStatus;
	label: string;
	modalForm?: ModalForm;
	buttonVariant?: 'destructive';
};

type Params = {
	serviceCaseStatus: ServiceCaseStatus | null;
	isBulkAction?: boolean;
};

export const useServiceCaseStatusTransitionMap = ({ serviceCaseStatus, isBulkAction = false }: Params) => {
	const [selectedTargetTransition, setSelectedTargetTransition] = useState<TargetTransitionInfo | null>(null);

	if (!serviceCaseStatus)
		return { possibleTransitions: [], selectedTargetTransition: null, setSelectedTargetTransition, FormToShow: null };

	const serviceStateStatusTransitionsMap: Record<ServiceCaseStatus, TargetTransitionInfo[]> = {
		NEW: [{ status: 'ASSIGNED_TO_INTERNAL_SERVICE', label: 'Přiřadit servis', modalForm: FromNewToAssignedForm }],
		CLOSED: [],
		ASSIGNED_TO_INTERNAL_SERVICE: [],
		ASSIGNED_TO_EXTERNAL_SERVICE: [
			{ status: 'SENT_TO_SERVICE_CENTER', label: 'Odeslat do servisu', modalForm: FromAssignedToServiceCenterForm },
		],
		SENT_TO_SERVICE_CENTER: isBulkAction
			? []
			: [
					{
						status: 'OFFER_RECEIVED',
						label: ServiceCaseStatusMessage['OFFER_RECEIVED'],
						modalForm: FromServiceCenterToOfferReceivedForm,
					},
				],
		OFFER_RECEIVED: [
			{ status: 'WAITING_FOR_REPAIR', label: 'Přijmout nabídku' },
			...(isBulkAction
				? []
				: [
						{
							status: 'OFFER_REJECTED',
							label: 'Odmítnout nabídku',
							buttonVariant: 'destructive',
							modalForm: FromReceivedToRejectedForm,
						} as const,
					]),
		],
		OFFER_REJECTED: [
			{ status: 'WAITING_FOR_RETURN', label: 'Vrátit zboží na Počítárnu' },
			{ status: 'WAITING_FOR_BUYBACK', label: 'Nabídnout odkup' },
			...(isBulkAction
				? []
				: [
						{
							status: 'ASSIGNED_TO_EXTERNAL_SERVICE',
							label: 'Vyberte jiný externí servis',
							modalForm: WithNewServiceCenterForm,
						} as const,
					]),
		],
		WAITING_FOR_PRODUCT: [],
		WAITING_FOR_REPAIR: isBulkAction
			? []
			: [
					{ status: 'CLOSED', label: 'Potvrdit přijetí zboží a předat na testování', modalForm: FromRepairToClosedForm },
					{
						status: 'NEW',
						label: 'Potvrdit přejetí zboží a vybrat nové řešení',
						modalForm: FromRepairToNewForm,
					} as const,
				],
		WAITING_FOR_RETURN: [
			{
				status: 'ASSIGNED_TO_INTERNAL_SERVICE',
				label: 'Převést na interní servis',
				modalForm: FromRejectedToInternalServiceForm,
			} as const,
			...(!isBulkAction
				? [
						{
							status: 'CLOSED',
							label: 'Potvrdit přijetí zboží a předat na pitevnu',
							modalForm: FromReturnToClosedForm,
						} as const,
						{
							status: 'ASSIGNED_TO_EXTERNAL_SERVICE',
							label: 'Vyberte jiný externí servis',
							modalForm: WithNewServiceCenterForm,
						} as const,
					]
				: []),
		],
		WAITING_FOR_BUYBACK: [
			...(isBulkAction ? [] : [{ status: 'CLOSED', label: 'Odkoupeno, zadat cenu', modalForm: FromBuybackToClosedForm } as const]),
			{ status: 'WAITING_FOR_RETURN', label: 'Odmítnuto, čeká na vrácení' },
		],
	};

	const possibleTransitions = serviceStateStatusTransitionsMap[serviceCaseStatus];

	const FormToShow: ModalForm = selectedTargetTransition?.modalForm ?? DefaultStatusTransitionForm;

	return { possibleTransitions, selectedTargetTransition, setSelectedTargetTransition, FormToShow };
};

export const useGetServiceCaseTransitionInvalidations = () => {
	const queryClient = useQueryClient();

	const getServiceCasesQueryKey = apiHooks.getKeyByAlias('getServiceCases');
	const invalidateServiceCases = () => queryClient.invalidateQueries(getServiceCasesQueryKey);
	const getServiceCaseHistoryQueryKey = apiHooks.getKeyByAlias('getServiceCaseHistory');
	const invalidateServiceCaseHistory = () => queryClient.invalidateQueries(getServiceCaseHistoryQueryKey);
	const getServiceCaseQueryKey = apiHooks.getKeyByAlias('getServiceCase');
	const invalidateServiceCase = () => queryClient.invalidateQueries(getServiceCaseQueryKey);
	const getProductQueryKey = apiHooks.getKeyByAlias('getProduct');
	const invalidateProduct = () => queryClient.invalidateQueries(getProductQueryKey);
	const getProductDefectsQueryKey = apiHooks.getKeyByAlias('getAllProductDefects');
	const invalidateProductDefects = () => queryClient.invalidateQueries(getProductDefectsQueryKey);
	const getProductHistoryQueryKey = apiHooks.getKeyByAlias('getProductHistory');
	const invalidateProductHistory = () => queryClient.invalidateQueries(getProductHistoryQueryKey);

	const invalidate = () => {
		invalidateServiceCases();
		invalidateServiceCase();
		invalidateServiceCaseHistory();
		invalidateProduct();
		invalidateProductDefects();
		invalidateProductHistory();
	};

	return {
		invalidateServiceCases,
		invalidateServiceCase,
		invalidateServiceCaseHistory,
		invalidateProduct,
		invalidateProductDefects,
		invalidateProductHistory,
		invalidate,
	};
};
