import { type ServiceTaskResolutionScope } from '@pocitarna-nx-2023/config';
import { useState } from 'react';

export const useServiceTaskResolutionScope = () => {
	const [resolutionScope, setResolutionScope] = useState<ServiceTaskResolutionScope>('serviceTask');

	const handleResolutionScopeChange = (scope: ServiceTaskResolutionScope) => {
		setResolutionScope(scope);
	};

	return { resolutionScope, handleResolutionScopeChange };
};
