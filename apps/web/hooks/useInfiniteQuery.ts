import { type Api, type ApiAliases, type ApiBody, type ApiPagedResponse } from '@pocitarna-nx-2023/zodios';
import { apiClient, apiHooks } from '@pocitarna-nx-2023/zodios-client';
import type { ZodiosRequestOptionsByAlias } from '@zodios/core';

const getEndpoints = apiClient.api.filter((item) => item.method === 'get');

export const useInfiniteQuery = <Alias extends ApiAliases>(
	alias: <PERSON><PERSON>,
	// eslint-disable-next-line @typescript-eslint/ban-ts-comment
	// @ts-ignore
	options: ZodiosRequestOptionsByAlias<Api, Alias>,
	enabled = true,
) => {
	const endpoint = getEndpoints.find((item) => item.alias === alias)?.path;
	if (!endpoint) return { isFetching: false, fetchNextPage: () => [], hasNextPage: false };

	// eslint-disable-next-line @typescript-eslint/ban-ts-comment
	// @ts-ignore - Cannot infer the correct type
	const { data, isFetching, fetchNextPage, hasNextPage } = apiHooks.useInfiniteQuery(endpoint, options, {
		enabled,
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		// @ts-ignore
		getPageParamList: () => ['page'], // Needed for Zodios
		getNextPageParam: (lastPage: ApiPagedResponse<ApiBody<Alias>>) => {
			const nextPage = lastPage?._paging?.nextPage;
			if (!nextPage) return undefined;

			return {
				queries: {
					page: nextPage,
				},
			};
		},
	});

	const castedData = data as { pages: ApiPagedResponse<ApiBody<Alias>>[] } | undefined;

	return { data: castedData, isFetching, fetchNextPage, hasNextPage };
};
