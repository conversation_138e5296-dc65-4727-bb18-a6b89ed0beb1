import { useRouter } from 'next/router';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useConfirm } from '../components/ConfirmProvider';

export const useBeforeUnload = (message: string) => {
	const [unsavedChanges, setUnsavedChanges] = useState<boolean>(false);
	const ref = useRef(unsavedChanges);
	const router = useRouter();
	const confirm = useConfirm();

	useEffect(() => {
		ref.current = unsavedChanges;
		return () => {
			ref.current = false;
		};
	}, [unsavedChanges]);

	const handleBeforeUnload = useCallback(
		async (event: BeforeUnloadEvent) => {
			if (ref.current) {
				event.returnValue = message;
				return message;
			}
		},
		[message],
	);

	const handleBeforeRouteChange = useCallback(
		(url: string) => {
			if (ref.current) {
				(async () => {
					await confirm(message);
					router.events.off('routeChangeStart', handleBeforeRouteChange);
					await router.push(url);
					router.events.on('routeChangeStart', handleBeforeRouteChange);
				})();
				throw 'Prevented page navigation';
			}
		},
		[confirm, message, router],
	);

	const handleRouteChangeComplete = () => setUnsavedChanges(false);

	useEffect(() => {
		window.addEventListener('beforeunload', handleBeforeUnload);
		router.events.on('routeChangeStart', handleBeforeRouteChange);
		router.events.on('routeChangeComplete', handleRouteChangeComplete);
		return () => {
			setUnsavedChanges(false);
			window.removeEventListener('beforeunload', handleBeforeUnload);
			router.events.off('routeChangeStart', handleBeforeRouteChange);
			router.events.off('routeChangeComplete', handleRouteChangeComplete);
		};
	}, [handleBeforeRouteChange, handleBeforeUnload, router.events]);

	return [unsavedChanges, setUnsavedChanges] as const;
};
