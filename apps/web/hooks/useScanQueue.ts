import { type InventoryItemScan } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useCallback, useRef, useState } from 'react';
import { useDebounce } from 'rooks';

const DELAY_BEFORE_BATCH_MS = 1000;
const DELAY_AFTER_REQUEST_MS = 1000;
const GENERIC_ERROR_MESSAGE = 'Chyba při dávkovém skenování';

export const useScanQueue = (inventoryId: string, scannedItems: InventoryItemScan[] = []) => {
	const [queue, setQueue] = useState<InventoryItemScan[]>([]);
	const bufferRef = useRef<InventoryItemScan[]>([]);
	const processingRef = useRef(false);

	const { mutate: scanInventoryItems } = apiHooks.useScanInventoryItem({
		params: { inventoryId },
	});

	const debouncedProcessBuffer = useDebounce(() => {
		if (processingRef.current || bufferRef.current.length === 0) return;

		processingRef.current = true;
		const itemsToScan = [...bufferRef.current];
		bufferRef.current = [];

		scanInventoryItems(
			itemsToScan.map((item) => item.pcn),
			{
				onSuccess: (response) => {
					setQueue((prev) =>
						prev.map((item) => {
							const result = response._data.find((r) => r.pcn === item.pcn);
							if (!result) return item;
							return {
								...item,
								status: result.status,
								error: result.status !== 'ERROR' ? undefined : result.error ?? GENERIC_ERROR_MESSAGE,
							};
						}),
					);
				},
				onError: (error) => {
					console.error('Batch scan error:', error);
					setQueue((prev) =>
						prev.map((item) =>
							itemsToScan.some((b) => b.pcn === item.pcn) ? { ...item, status: 'ERROR', error: GENERIC_ERROR_MESSAGE } : item,
						),
					);
				},
				onSettled: () => {
					processingRef.current = false;
					if (bufferRef.current.length > 0) {
						setTimeout(() => debouncedProcessBuffer(), DELAY_AFTER_REQUEST_MS);
					}
				},
			},
		);
	}, DELAY_BEFORE_BATCH_MS);

	const addToQueue = useCallback(
		(pcn: string) => {
			if ([...queue, ...scannedItems].some((item) => item.pcn === pcn && ['OK', 'PENDING'].includes(item.status))) return;

			const newItem: InventoryItemScan = { pcn, status: 'PENDING' };
			setQueue((prev) => [...prev, newItem]);
			bufferRef.current.push(newItem);

			debouncedProcessBuffer();
		},
		[queue, scannedItems, debouncedProcessBuffer],
	);

	return {
		queue,
		addToQueue,
	};
};
