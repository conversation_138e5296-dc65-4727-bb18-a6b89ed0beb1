import { IS_PROD, IS_STAGING, STAGE } from '@pocitarna-nx-2023/config';
import * as Sentry from '@sentry/nextjs';

export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;

Sentry.init({
	dsn: 'https://<EMAIL>/4507847817625680',
	integrations: [Sentry.replayIntegration()],
	sendDefaultPii: true,
	tracesSampleRate: 0.2,
	replaysSessionSampleRate: 0,
	replaysOnErrorSampleRate: 1.0,
	environment: `${STAGE}-web`,
	enabled: IS_STAGING || IS_PROD,
});
