import { type Grade } from '@pocitarna-nx-2023/database';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';

const GRADE_NOT_FOUND = -1;

export const calculateProductGrade = ({
	product,
	defects,
	categoryGrades,
}: {
	product: ApiBody<'getProduct'>;
	defects: ApiBody<'getProductCosmeticDefects'>;
	categoryGrades: ApiBody<'getProductCategoryGrades'>;
}): string | null => {
	const availableGrades = categoryGrades.map((item) => item.grade);
	const aGrade = availableGrades.find((item) => item.name === 'A');
	const validGrades = product.type === 'REFURBISHED' && aGrade ? availableGrades.slice(availableGrades.indexOf(aGrade)) : availableGrades;

	if (defects.length === 0) return validGrades[0]?.id ?? null;

	const sortedGradeNames = validGrades.map((grade) => grade.name);
	const worstGradeIndex = getWorstGradeIndex(defects, sortedGradeNames);

	if (worstGradeIndex === GRADE_NOT_FOUND) return null;

	const worstGradeName = sortedGradeNames[worstGradeIndex];
	const defectCount = countDefectsAtGrade(defects, worstGradeName);
	const applicableCategoryGrading = categoryGrades.find((item) => item.grade.name === worstGradeName);
	const maxCosmeticDefectsForWorstGrade = applicableCategoryGrading?.maxCosmeticDefects ?? 0;

	if (!applicableCategoryGrading) {
		throw new Error(`Grade ${worstGradeName} not found`);
	}

	const shouldDowngrade = defectCount > maxCosmeticDefectsForWorstGrade;
	const isWorstPossibleGrade = worstGradeIndex === sortedGradeNames.length - 1;

	if (shouldDowngrade && !isWorstPossibleGrade) {
		const downgradedGradeName = sortedGradeNames[worstGradeIndex + 1];
		const downgradedGrade = validGrades.find((item) => item.name === downgradedGradeName); // findGradeConfig(validGrades, downgradedGradeName);

		if (!downgradedGrade) {
			throw new Error(`Downgraded grade ${downgradedGradeName} not found`);
		}

		return downgradedGrade.id;
	}

	return applicableCategoryGrading.grade.id;
};

const getWorstGradeIndex = (defects: ApiBody<'getProductCosmeticDefects'>, sortedGradeNames: Grade['name'][]): number => {
	let worstIndex = GRADE_NOT_FOUND;

	for (const defect of defects) {
		const gradeIndex = sortedGradeNames.indexOf(defect.cosmeticDefect.grade.name);

		if (gradeIndex === GRADE_NOT_FOUND) {
			throw new Error(`Unknown grade: ${defect.cosmeticDefect.grade.name}`);
		}

		worstIndex = Math.max(worstIndex, gradeIndex);
	}

	return worstIndex;
};

const countDefectsAtGrade = (defects: ApiBody<'getProductCosmeticDefects'>, gradeName: string): number => {
	return defects.filter((defect) => defect.cosmeticDefect.grade.name === gradeName).length;
};
