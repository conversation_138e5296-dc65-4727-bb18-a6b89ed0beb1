import type { AttributeValueType } from '@pocitarna-nx-2023/config';

export const transformFilterAttributeValues = (
	attributeValues: string | string[] | number | boolean | { attributeId: string; attributeValueId: string }[] = [],
	type: AttributeValueType = 'resolved',
	temporary = false,
) => {
	if (!Array.isArray(attributeValues) || attributeValues.length === 0) return {};

	return attributeValues.reduce<{
		[key: `attributeValues${number}.type`]: { eq: AttributeValueType } | undefined;
		[key: `attributeValues${number}.attributeValueId`]: { eq: string } | undefined;
		[key: `attributeValues${number}.attributeValue${number}.attributeId`]: { eq: string } | undefined;
		[key: `attributeValues${number}.attributeValue${number}.temporary`]: { eq: boolean } | undefined;
	}>((acc, item, index) => {
		if (typeof item === 'string') return acc;
		const { attributeId, attributeValueId } = item;
		acc[`attributeValues${index}.type`] = { eq: type };
		acc[`attributeValues${index}.attributeValue${index}.temporary`] = { eq: temporary };
		if (attributeValueId.length > 0) {
			acc[`attributeValues${index}.attributeValueId`] = { eq: attributeValueId };
		}
		if (attributeId.length > 0) {
			acc[`attributeValues${index}.attributeValue${index}.attributeId`] = { eq: attributeId };
		}
		return acc;
	}, {});
};
