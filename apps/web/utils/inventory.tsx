import { toast } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import Link from 'next/link';

export const onInventoryItemSuccess = (inventoryItem: ApiBody<'getInventoryItems'>[number], variant?: 'amount-envelope') => {
	const isAmountEnvelope = variant === 'amount-envelope';
	const href = `/inventory/${inventoryItem.inventoryId}?selectedItem=${inventoryItem.productEnvelopeId}${!isAmountEnvelope ? `|${inventoryItem.productId}` : ''}`;
	const linkContent = isAmountEnvelope ? inventoryItem.productEnvelopeCode : inventoryItem.code;

	const message = (
		<>
			Aktualizováno -{' '}
			<Link href={href} className="text-link" target="_blank">
				{linkContent}
			</Link>
		</>
	);

	toast.success(message);
};
