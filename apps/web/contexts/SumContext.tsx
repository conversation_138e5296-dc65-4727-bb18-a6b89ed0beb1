import { createContext, type FC, type PropsWithChildren, useCallback, useContext, useEffect, useMemo, useState } from 'react';

type SumInfo = {
	id: string;
	amount?: number;
};

type SumContext = {
	add: (sumInfo: SumInfo) => void; // FIXME - use two arguments ffs
	deduct: (id: string) => void;
	total: number;
};

const SumContext = createContext<SumContext | undefined>(undefined);

type Props = {
	aggregatorId?: string;
};

export const SumProvider: FC<PropsWithChildren<Props>> = ({ aggregatorId, children }) => {
	const [totalSumInfo, setTotalSumInfo] = useState<SumInfo[]>([]);
	const parentContext = useContext(SumContext);

	const add = useCallback(({ id, amount }: SumInfo) => {
		setTotalSumInfo((prevState) => {
			const existingEntryIndex = prevState.findIndex((item) => item.id === id);

			if (existingEntryIndex >= 0) {
				const newArray = [...prevState];
				newArray[existingEntryIndex] = { id, amount: amount ?? 1 };
				return newArray;
			}

			return [...prevState, { id, amount: amount ?? 1 }];
		});
	}, []);

	const deduct = useCallback((id: string) => {
		setTotalSumInfo((prev) => prev.filter((item) => item.id !== id));
	}, []);

	const total = useMemo(() => {
		return totalSumInfo.reduce((acc, curr) => acc + (curr.amount ?? 1), 0);
	}, [totalSumInfo]);

	// If inside another SumProvider, update the parent with the sub-totals
	useEffect(() => {
		if (!parentContext || !aggregatorId) return;

		parentContext.add({ id: aggregatorId, amount: total });

		return () => {
			if (parentContext && aggregatorId) {
				parentContext.deduct(aggregatorId);
			}
		};
	}, [total, parentContext, aggregatorId]);

	const contextValue = useMemo(
		() => ({
			add,
			deduct,
			total,
		}),
		[add, deduct, total],
	);

	return <SumContext.Provider value={contextValue}>{children}</SumContext.Provider>;
};

export const useSum = () => {
	const context = useContext(SumContext);

	if (!context) throw new Error('useSum must be used within a SumProvider');

	return context;
};
