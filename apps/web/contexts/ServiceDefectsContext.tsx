import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { createContext, type FC, type PropsWithChildren, useCallback, useContext, useMemo, useState } from 'react';

type ServiceDefectsContext = {
	addDefects: (defect: ApiBody<'getProductDefects'>) => void;
	removeDefects: (id: string[]) => void;
	defects: ApiBody<'getProductDefects'>;
};

const ServiceDefectsContext = createContext<ServiceDefectsContext | undefined>(undefined);

export const ServiceDefectsProvider: FC<PropsWithChildren> = ({ children }) => {
	const [defects, setDefects] = useState<ApiBody<'getProductDefects'>>([]);

	const addDefects = useCallback((defects: ApiBody<'getProductDefects'>) => {
		setDefects((prev) => {
			const defectIds = prev.map((defect) => defect.id);
			const newDefects = defects.filter((item) => !defectIds.includes(item.id));
			return [...prev, ...newDefects];
		});
	}, []);

	const removeDefects = useCallback((defectIds: string[]) => {
		setDefects((prev) => prev.filter((item) => !defectIds.includes(item.id)));
	}, []);

	const contextValue = useMemo(
		() => ({
			defects,
			addDefects,
			removeDefects,
		}),
		[defects, addDefects, removeDefects],
	);

	return <ServiceDefectsContext.Provider value={contextValue}>{children}</ServiceDefectsContext.Provider>;
};

export const useServiceDefects = () => {
	const context = useContext(ServiceDefectsContext);

	if (!context) throw new Error('useServiceDefects must be used within a ServiceDefectsProvider');

	return context;
};
