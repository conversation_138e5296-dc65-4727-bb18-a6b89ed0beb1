import { type ProductType } from '@pocitarna-nx-2023/config';
import { createContext, type FC, type PropsWithChildren, useContext, useMemo, useState } from 'react';

type TestDiffContext = {
	gradeId: string | undefined;
	setGradeId: (gradeId: string) => void;
	productType: ProductType | undefined;
	setProductType: (productType: ProductType) => void;
	clearCategoryCosmeticAreas: Record<string, boolean>;
	setclearCategoryCosmeticAreas: (clearCategoryCosmeticAreas: Record<string, boolean>) => void;
};

const TestDiffContext = createContext<TestDiffContext | undefined>(undefined);

export const TestDiffProvider: FC<PropsWithChildren> = ({ children }) => {
	const [gradeId, setGradeId] = useState<string>();
	const [productType, setProductType] = useState<ProductType>();
	const [clearCategoryCosmeticAreas, setclearCategoryCosmeticAreas] = useState<Record<string, boolean>>({});

	const contextValue = useMemo(
		() => ({
			gradeId,
			setGradeId,
			productType,
			setProductType,
			clearCategoryCosmeticAreas,
			setclearCategoryCosmeticAreas,
		}),
		[gradeId, productType, clearCategoryCosmeticAreas],
	);

	return <TestDiffContext.Provider value={contextValue}>{children}</TestDiffContext.Provider>;
};

export const useTestDiffContext = () => {
	const context = useContext(TestDiffContext);

	return context;
};
