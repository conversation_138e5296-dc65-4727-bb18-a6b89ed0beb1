import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';

type Props = {
	product: ApiBody<'getProduct'>;
};

export const PastSnDisplayer: FC<Props> = ({ product }) => {
	const serialNumbers = product.sn.split('|').filter((sn) => sn !== '');
	const pastSerialNumbers = product.pastSn.split('|').filter((sn) => sn !== '' && !serialNumbers.includes(sn));
	return <small>{pastSerialNumbers.join(', ')}</small>;
};
