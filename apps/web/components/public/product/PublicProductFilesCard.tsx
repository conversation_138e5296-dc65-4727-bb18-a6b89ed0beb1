import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { Media } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useMemo } from 'react';

type Props = {
	product: ApiBody<'getProduct'>;
};

export const PublicProductFilesCard: FC<Props> = ({ product }) => {
	const { data: productFilesData } = apiHooks.useGetPublicProductFiles({
		params: { productId: product.id },
		queries: {
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
		},
	});

	const productImages = useMemo(() => (productFilesData?._data ?? []).map(({ file }) => file), [productFilesData?._data]);

	return <Media files={productImages} />;
};
