import { STAGE } from '@pocitarna-nx-2023/config';
import { <PERSON><PERSON>, <PERSON>, PageContent } from '@pocitarna-nx-2023/ui';
import { type PropsWithChildren } from 'react';

export const PublicLayout = ({ children }: PropsWithChildren) => {
	return (
		<Page className="min-h-screen flex flex-col">
			<Header stage={STAGE} />
			<PageContent>{children}</PageContent>
		</Page>
	);
};

PublicLayout.displayName = 'Layout';
