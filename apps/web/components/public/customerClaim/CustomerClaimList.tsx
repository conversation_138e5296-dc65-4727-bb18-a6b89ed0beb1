import { CustomerClaimStatusMessage } from '@pocitarna-nx-2023/config';
import {
	<PERSON>ton,
	Card,
	CardHeader,
	CardTitle,
	MutedText,
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@pocitarna-nx-2023/ui';
import { formatCustomerClaimCode, formatDate, formatProductCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import Link from 'next/link';
import { type FC } from 'react';

type Props = {
	customerClaims: ApiBody<'getPublicCustomerClaims'>;
};

export const CustomerClaimList: FC<Props> = ({ customerClaims }) => {
	if (customerClaims.length === 0) return null;

	return (
		<Card>
			<CardHeader>
				<CardTitle>Reklamace</CardTitle>
			</CardHeader>

			<Table inCard title="Reklamace">
				<TableHeader>
					<TableRow>
						<TableHead>Název produktu</TableHead>
						<TableHead>Objednávka</TableHead>
						<TableHead>Reklamace</TableHead>
						<TableHead>Stav</TableHead>
						<TableHead></TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{customerClaims.map((claim) => (
						<CustomerClaimRow key={claim.id} claim={claim} />
					))}
				</TableBody>
			</Table>
		</Card>
	);
};

const CustomerClaimRow: FC<{ claim: ApiBody<'getPublicCustomerClaims'>[number] }> = ({ claim }) => {
	const orderItem = claim.ecommerceOrderItem;
	const order = claim.ecommerceOrderItem.ecommerceOrder;
	const product = orderItem.product;

	return (
		<TableRow>
			<TableCell>
				{orderItem.name}
				{product && (
					<MutedText>
						<br />
						<Link className="text-link" href={`/public/product/${product.id}`}>
							{formatProductCode(product.code)}
						</Link>
						<br />
						{product.sn.toUpperCase()}
					</MutedText>
				)}
			</TableCell>
			<TableCell>
				{order.code}
				<br />
				<MutedText>{formatDate(order.createdAt)}</MutedText>
			</TableCell>
			<TableCell>
				<div>
					{formatCustomerClaimCode(claim.code)}
					<br />
					<MutedText>{formatDate(claim.createdAt)}</MutedText>
				</div>
			</TableCell>
			<TableCell>{CustomerClaimStatusMessage[claim.status]}</TableCell>
			<TableCell>
				<Button asChild>
					<Link href={`/public/customer-claim/${claim.id}`}>Detail</Link>
				</Button>
			</TableCell>
		</TableRow>
	);
};
