import { Button, FormContext, InputControl, Stack } from '@pocitarna-nx-2023/ui';
import { type FC } from 'react';
import { z } from 'zod';

type Props = {
	onSubmit: (data: { code: string; email: string }) => void;
	isLoading: boolean;
};

export const CustomerClaimSearchForm: FC<Props> = ({ onSubmit, isLoading }) => {
	return (
		<FormContext
			schema={z.object({
				code: z.string(),
				email: z
					.string({
						required_error:
							'Můžete pokračovat online pouze v případě, že máte k objednávce nebo reklamaci přiřazený e-mail.V ostatních případech kontaktujte Počítařnu.',
					})
					.email(),
			})}
			defaultValues={{
				code: '',
				email: '',
			}}
			onSubmit={onSubmit}
		>
			{(control) => (
				<Stack gap={4}>
					<InputControl control={control} name="code" label="<PERSON><PERSON>lo objednávky / reklamace" />
					<InputControl control={control} name="email" label="E-mail" />

					<Button className="w-full" variant="default" type="submit" isLoading={isLoading}>
						Vytvořit
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
