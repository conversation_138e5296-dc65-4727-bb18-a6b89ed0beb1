import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Stack } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { PdfProtocolLink } from '../../customerClaim/PdfProtocolLink';

type Props = {
	customerClaim: ApiBody<'getCustomerClaim'>;
};

export const CustomerClaimActions: FC<Props> = ({ customerClaim }) => {
	return (
		<Card sticky>
			<CardHeader>
				<CardTitle>Akce</CardTitle>
			</CardHeader>
			<CardContent>
				<Stack gap={4}>
					<PdfProtocolLink customerClaim={customerClaim} />
				</Stack>
			</CardContent>
		</Card>
	);
};
