import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>alog<PERSON>ontent,
	<PERSON>alogHeader,
	<PERSON>alogTitle,
	DialogTrigger,
	MutedText,
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
	toast,
} from '@pocitarna-nx-2023/ui';
import { formatCustomerClaimCode, formatDate } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { type FC } from 'react';
import { CustomerClaimSubmissionForm } from '../../customerClaim/CustomerClaimSubmissionForm';

type Props = {
	order: ApiBody<'getPublicEcommerceOrders'>[number];
	email: string;
	customerClaims: ApiBody<'getPublicCustomerClaims'>;
};

export const CustomerClaimOrderProductsList: FC<Props> = ({ order, email, customerClaims }) => {
	const relevantOrderItems = order.items.filter((item) => item.type === 'PRODUCT' && item.productId !== null);

	return (
		<Card>
			<CardHeader>
				<CardTitle>Produkty</CardTitle>
			</CardHeader>

			<Table inCard title="Produkty">
				<TableHeader>
					<TableRow>
						<TableHead>Název produktu</TableHead>
						<TableHead>Objednávka</TableHead>
						<TableHead>Reklamace</TableHead>
						<TableHead></TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{relevantOrderItems.map((item) => (
						<CustomerClaimOrderProductRow
							order={order}
							orderItem={item}
							key={item.id}
							email={email}
							customerClaims={customerClaims}
						/>
					))}
				</TableBody>
			</Table>
		</Card>
	);
};

const CustomerClaimOrderProductRow: FC<Props & { orderItem: ApiBody<'getPublicEcommerceOrders'>[number]['items'][number] }> = ({
	order,
	orderItem,
	email,
	customerClaims,
}) => {
	const relatedCustomerClaims = customerClaims.filter((claim) => claim.ecommerceOrderItemId === orderItem.id);

	const { mutate: submitCustomerClaim, isLoading } = apiHooks.useSubmitCustomerClaim();

	const { invalidate: invalidateCustomerClaims } = apiHooks.useGetPublicCustomerClaims(
		{
			queries: { filter: { 'contact.email': { eq: email } } },
		},
		{ enabled: false },
	);

	return (
		<TableRow>
			<TableCell>{orderItem.name}</TableCell>
			<TableCell>
				{order.code}
				<br />
				<MutedText>{formatDate(order.createdAt)}</MutedText>
			</TableCell>
			<TableCell>
				{relatedCustomerClaims.map((claim) => (
					<div key={claim.id}>
						<Link href={`/public/customer-claim/${claim.id}`} className="text-link">
							{formatCustomerClaimCode(claim.code)}
						</Link>
						<br />
						<MutedText>{formatDate(claim.createdAt)}</MutedText>
					</div>
				))}
			</TableCell>
			<TableCell>
				<Dialog>
					<DialogTrigger asChild>
						<Button>Reklamovat</Button>
					</DialogTrigger>
					<DialogContent>
						<DialogHeader>
							<DialogTitle>Reklamovat produkt</DialogTitle>
						</DialogHeader>
						<CustomerClaimSubmissionForm
							email={email}
							ecommerceOrderItemId={orderItem.id}
							order={order}
							isLoading={isLoading}
							onSubmit={(data) => {
								submitCustomerClaim(data, {
									onSuccess: () => {
										invalidateCustomerClaims();
										// closeDialog();
										toast.success('Reklamace byla vytvořena');
									},
								});
							}}
						/>
					</DialogContent>
				</Dialog>
			</TableCell>
		</TableRow>
	);
};
