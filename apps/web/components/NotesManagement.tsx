import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Card<PERSON><PERSON><PERSON>,
	Dialog,
	Dialog<PERSON>ontent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
	Icon,
	Stack,
} from '@pocitarna-nx-2023/ui';
import { type Note } from '@pocitarna-nx-2023/zodios';
import { useMemo } from 'react';
import { useUserHasScope } from '../hooks/useUserHasScope';
import { CreationDetails } from './CreationDetails';
import { ExpandableText } from './ExpandableText';
import { NoteCreation } from './NoteCreation';
import { NoteDeletion } from './NoteDeletion';

export type WithNotes = {
	notes: Note[];
	id: string;
};

type Props<T extends WithNotes> = {
	entity: T;
	entityType: 'warrantyClaim' | 'serviceCase' | 'customerClaim';
	invalidate: () => void;
	disabled?: boolean;
};

export const NotesManagement = <T extends WithNotes>(props: Props<T>) => {
	const { entity, entityType, invalidate, disabled } = props;
	const isAdmin = useUserHasScope('admin');

	const sortedNotes = useMemo(
		() => entity.notes.toSorted((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()),
		[entity.notes],
	);

	return (
		<Card>
			<CardHeader className="flex flex-col justify-between items-start md:flex-row">
				<CardTitle>Poznámky</CardTitle>
			</CardHeader>
			<CardContent>
				<Stack gap={6}>
					{sortedNotes.map((note) => (
						<Stack gap={2} key={note.id}>
							<ExpandableText text={note.content} />
							<div className="flex justify-between">
								<CreationDetails date={note.createdAt} user={note.createdBy} key={note.id}></CreationDetails>
								{isAdmin && <NoteDeletion noteId={note.id} invalidate={invalidate} />}
							</div>
						</Stack>
					))}
				</Stack>
			</CardContent>
			<CardFooter>
				<Dialog>
					<DialogTrigger asChild>
						<Button className="w-full" variant="secondary">
							<Icon name="plus" />
							Přidat poznámku
						</Button>
					</DialogTrigger>
					<DialogContent>
						<DialogHeader>
							<DialogTitle>Upravit poznámky</DialogTitle>
						</DialogHeader>
						<NoteCreation invalidate={invalidate} entityId={entity.id} entityType={entityType} disabled={disabled} />
					</DialogContent>
				</Dialog>
			</CardFooter>
		</Card>
	);
};
