import { toast, useDialog } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useQueryClient } from '@tanstack/react-query';
import { type FC } from 'react';
import { TesterForm } from './TesterForm';

type Props = {
	productIds: string[];
	invalidate: () => Promise<void>;
};

export const TesterProductForm: FC<Props> = ({ productIds, invalidate }) => {
	const { mutate: bulkUpdateProductTests, isLoading } = apiHooks.useBulkUpdateProductTests();

	const queryClient = useQueryClient();

	const getBatchProductTestsQueryKey = apiHooks.getKeyByAlias('getBatchProductTests');
	const invalidateBatchesTesters = () => queryClient.invalidateQueries(getBatchProductTestsQueryKey);
	const getProductHistoryQueryKey = apiHooks.getKeyByAlias('getProductHistory');
	const invalidateProductHistories = () => queryClient.invalidateQueries(getProductHistoryQueryKey);

	const { closeDialog } = useDialog();

	const onSubmit = async (data: { testedBy: { id: string }; deadlineAt: Date | null }) => {
		const { testedBy, deadlineAt } = data;
		if (productIds.length === 0) return;

		bulkUpdateProductTests(
			{ ids: productIds, data: { testedBy, deadlineAt } },
			{
				onSuccess: () => {
					toast.success('Tester aktualizován');
					invalidate();
					invalidateBatchesTesters();
					invalidateProductHistories();
					closeDialog();
				},
			},
		);
	};

	return <TesterForm onSubmit={onSubmit} isLoading={isLoading} />;
};
