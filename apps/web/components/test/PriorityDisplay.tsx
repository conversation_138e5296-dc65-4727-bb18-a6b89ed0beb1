import { PRIORITY_VALUES, PriorityMessage } from '@pocitarna-nx-2023/config';
import { type FC } from 'react';

type Props = {
	entity: { priority: number };
};

export const PriorityDisplay: FC<Props> = ({ entity }) => {
	const { label, className } = getPriorityDisplayInfo(entity.priority);

	return <span className={className}>{label}</span>;
};

const getPriorityDisplayInfo = (priority: number) => {
	if (priority < PRIORITY_VALUES.MEDIUM) {
		return { label: PriorityMessage.LOW, className: 'opacity-50' };
	} else if (priority >= PRIORITY_VALUES.HIGH) {
		return { label: PriorityMessage.HIGH, className: 'text-destructive' };
	} else {
		return { label: PriorityMessage.MEDIUM };
	}
};
