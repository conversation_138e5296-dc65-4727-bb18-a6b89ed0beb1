import { MAX_POSITIVE_INTEGER, ProductStatusMessage, TEN_SECONDS } from '@pocitarna-nx-2023/config';
import {
	<PERSON><PERSON>,
	Dialog,
	DialogContent,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	Stack,
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
	toast,
} from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useState } from 'react';
import { useTestDiffContext } from '../../contexts/TestDiffContext';
import { useCompareMismatchAttributes } from '../../hooks/useCompareMismatchAttributes';
import { useProductAttributeValues } from '../../hooks/useProductAttributeValues';
import { useUserHasScope } from '../../hooks/useUserHasScope';
import { AttributeValue } from '../AttributeValue';
import { attributeValueTypeMap } from './TestDiffInputs';

type Props = {
	product: ApiBody<'getProduct'>;
	handleValidation?: () => Promise<boolean> | undefined;
	minimumTestPhotosAmount: number;
	allRelevantDefectsHaveImage: boolean;
};

export const TestSubmitButton: FC<Props> = ({ product, handleValidation, minimumTestPhotosAmount, allRelevantDefectsHaveImage }) => {
	const { gradeId, productType, clearCategoryCosmeticAreas } = useTestDiffContext() ?? {};

	const productId = product.id;
	const finishTestDisabled = !useUserHasScope('productTest');
	const envelopeAssignmentDisabled = !useUserHasScope('envelopeWrite');
	const { data: productFilesData } = apiHooks.useGetProductFiles(
		{ params: { productId }, queries: { page: 1, limit: MAX_POSITIVE_INTEGER } },
		{ refetchInterval: TEN_SECONDS },
	);
	const productFiles = productFilesData?._data ?? [];
	const productImages = productFiles.filter(({ file }: ApiBody<'getProductFiles'>[number]) => file.mime.includes('image'));
	const thereAreEnoughImages = productImages.length >= minimumTestPhotosAmount;

	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId } });
	const { invalidate: invalidateBatch } = apiHooks.useGetBatch({ params: { batchId: product.batchId ?? '' } }, { enabled: false });
	const { mutate: finishProductTest, isLoading: isLoadingFinish } = apiHooks.useFinishProductTest({ params: { productId } });
	const { mutate: saveProductTest, isLoading: isLoadingSaving } = apiHooks.useSaveProductTest({ params: { productId } });
	const { invalidate: invalidateProducts } = apiHooks.useGetAllProducts({}, { enabled: false });
	const { invalidate: invalidateBatchProducts } = apiHooks.useGetBatchProducts(
		{ params: { batchId: product.batchId ?? '' }, queries: { page: 1, limit: MAX_POSITIVE_INTEGER } },
		{ enabled: false },
	);
	const { invalidate: invalidateProductsInStock } = apiHooks.useGetProductEnvelopesReadyForStock({}, { enabled: false });
	const { invalidate: invalidateBatches } = apiHooks.useGetBatches({}, { enabled: false });

	const { data: productTasksData } = apiHooks.useGetProductTasks({ params: { productId } });
	const productTasks = productTasksData?._data ?? [];
	const thereAreOpenProductTasks = productTasks.some((task) => task.status !== 'CLOSED');
	const isLoading = isLoadingFinish || isLoadingSaving;

	const [warrantyClaimDialogOpen, setWarrantyClaimDialogOpen] = useState(false);
	const { productResolvedAttributeValues } = useProductAttributeValues(product);

	const hasAutofilledAttributes = productResolvedAttributeValues.some((attributeValue) => attributeValue.autofill);

	const compareMismatchAttributes = useCompareMismatchAttributes(product);

	const handleSubmit = (callback: () => void) => async () => {
		if (!allRelevantDefectsHaveImage) {
			toast.error('Nahrajte prosím fotografii pro všechny relevantní kosmetické vady.');
			return;
		}
		if (finishTestDisabled) return;
		const isFormValid = handleValidation ? await handleValidation() : true;

		if (!isFormValid) {
			toast.error('Nelze dokončit testování, všechny povinné parametry (s hvězdičkou) je potřeba vyplnit.');
			const firstErrorField = document.querySelector('[data-has-error="true"]');
			firstErrorField?.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' });
			firstErrorField?.querySelector('label')?.focus({ preventScroll: true });
			return;
		}

		if (callback === completeProductTest && compareMismatchAttributes.length > 0 && !product.productTest?.ignoreMismatch) {
			setWarrantyClaimDialogOpen(true);
		} else {
			callback();
		}
	};

	const invalidateAll = () => {
		invalidateProduct();
		invalidateProducts();
		invalidateBatch();
		invalidateBatches();
		invalidateBatchProducts();
		invalidateProductsInStock();
	};

	const handleSaveProductTest = () => {
		saveProductTest(
			{ gradeId, productType, clearCategoryCosmeticAreas },
			{
				onSuccess: (data) => {
					toast.success('', { description: `Produkt byl převeden na ${ProductStatusMessage[data?._data.status]}` });
					invalidateAll();
				},
			},
		);
	};

	const completeProductTest = (preventWarrantyClaim = false) => {
		if (!productImages || !thereAreEnoughImages) {
			toast.error(`Nahrajte alespoň ${minimumTestPhotosAmount} obrázků dle pracovního postupu`);
			return;
		}

		finishProductTest(
			{ preventWarrantyClaim, gradeId, productType, clearCategoryCosmeticAreas },
			{
				onSuccess: (data) => {
					toast.success('', { description: `Produkt byl převeden na ${ProductStatusMessage[data?._data.status]}` });
					invalidateAll();
				},
			},
		);
	};

	return (
		<Stack gap={4} direction="row">
			{product.status === 'TO_TEST' && (
				<Button
					type="button"
					className="grow"
					disabled={
						finishTestDisabled ||
						product.code == null ||
						thereAreOpenProductTasks ||
						hasAutofilledAttributes ||
						!thereAreEnoughImages
					}
					onClick={handleSubmit(handleSaveProductTest)}
					isLoading={isLoading}
				>
					Dokončit testování a předat vedoucímu
				</Button>
			)}

			{['TO_TEST', 'TESTED'].includes(product.status) && (
				<Button
					type="button"
					className="grow"
					disabled={
						envelopeAssignmentDisabled ||
						product.code == null ||
						thereAreOpenProductTasks ||
						hasAutofilledAttributes ||
						!thereAreEnoughImages
					}
					onClick={handleSubmit(completeProductTest)}
					isLoading={isLoading}
				>
					Dokončit testování a přiřadit kartu
				</Button>
			)}

			<Dialog open={warrantyClaimDialogOpen} onOpenChange={setWarrantyClaimDialogOpen}>
				<DialogContent size="lg">
					<DialogHeader>
						<DialogTitle>Neshodující se parametry</DialogTitle>
					</DialogHeader>
					<Stack gap={4}>
						<p>Hodnoty následujících parametrů se neshodují s hodnotami z importu.</p>
						<Table className="text-wrap">
							<TableHeader>
								<TableRow>
									<TableHead className="text-foreground font-bold">Parametr</TableHead>
									<TableHead className="text-foreground font-bold">
										{attributeValueTypeMap.import}/{attributeValueTypeMap.service}
									</TableHead>
									<TableHead className="text-foreground font-bold">{attributeValueTypeMap.resolved}</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{compareMismatchAttributes.map((attribute, index) => (
									<TableRow key={index}>
										<TableCell>{attribute.displayName}</TableCell>
										<TableCell>
											<AttributeValue attributeValue={attribute.importValue} />
										</TableCell>
										<TableCell>
											<AttributeValue attributeValue={attribute.resolvedValue} />
										</TableCell>
									</TableRow>
								))}
							</TableBody>
						</Table>
						<p>Produkt můžete poslat na dod. reklamaci nebo ho naskladnit.</p>
					</Stack>
					<DialogFooter>
						<Button
							onClick={() => {
								if (envelopeAssignmentDisabled) return;
								setWarrantyClaimDialogOpen(false);
								completeProductTest(true);
							}}
							variant="secondary"
							disabled={envelopeAssignmentDisabled}
							isLoading={isLoading}
						>
							Naskladnit
						</Button>
						<Button
							autoFocus
							onClick={() => {
								if (envelopeAssignmentDisabled) return;
								setWarrantyClaimDialogOpen(false);
								completeProductTest(false);
							}}
							variant="secondary"
							disabled={envelopeAssignmentDisabled}
							isLoading={isLoading}
						>
							Poslat na dod. reklamaci
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</Stack>
	);
};
