import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { toast, useDialog } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useQueryClient } from '@tanstack/react-query';
import { type FC } from 'react';
import { TesterForm } from './TesterForm';

type Props = {
	batchIds: string[];
};

export const TesterBatchForm: FC<Props> = ({ batchIds }) => {
	const queryClient = useQueryClient();
	const getBatchProductTestsQueryKey = apiHooks.getKeyByAlias('getBatchProductTests');
	const invalidateBatchesTesters = () => queryClient.invalidateQueries(getBatchProductTestsQueryKey);
	const getBatchProductsQueryKey = apiHooks.getKeyByAlias('getBatchProducts');
	const invalidateBatchProducts = () => queryClient.invalidateQueries(getBatchProductsQueryKey);
	const { invalidate: refreshProducts } = apiHooks.useGetAllProducts({}, { enabled: false });
	const { mutate: bulkUpdateBatchProductTests, isLoading } = apiHooks.useBulkUpdateBatchProductTests({
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER, filter: { status: { eq: 'TO_TEST' } } },
	});

	const { closeDialog } = useDialog();

	const onSubmit = async (data: { testedBy: { id: string }; deadlineAt: Date | null }) => {
		bulkUpdateBatchProductTests(
			{ ids: batchIds, data },
			{
				onSuccess: () => {
					toast.success('Tester aktualizován');
					invalidateBatchesTesters();
					invalidateBatchProducts();
					refreshProducts();
					closeDialog();
				},
			},
		);
	};

	return <TesterForm onSubmit={onSubmit} isLoading={isLoading} />;
};
