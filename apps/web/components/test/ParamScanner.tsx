import { READER_DOWNLOAD_LINKS, READER_FILENAMES, READER_LINKS, READER_MIME_TYPES } from '@pocitarna-nx-2023/config';
import {
	Button,
	cn,
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	Icon,
	Input,
	Popover,
	PopoverContent,
	PopoverTrigger,
	Spinner,
	Stack,
} from '@pocitarna-nx-2023/ui';
import { filterUndefined, formatProductCode, formatProducts } from '@pocitarna-nx-2023/utils';
import { apiClient, apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { AxiosError } from 'axios';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { type ChangeEventHandler, type FC, Fragment, type ReactNode, useCallback, useEffect, useState } from 'react';

type Props = {
	productId?: string;
	productCodeId?: string;
};

export const ParamScanner: FC<Props> = ({ productId, productCodeId }) => {
	const router = useRouter();
	const [error, setError] = useState<{
		type: 'SN_MISMATCH' | 'SN_CONFLICT' | 'MODEL_CONFLICT' | 'NOT_FOUND' | 'READER_ERROR';
		message: ReactNode;
		description?: ReactNode;
	} | null>(null);
	const [fileIds, setFileIds] = useState<string[]>([]);
	const [isLoadingApi, setIsLoadingApi] = useState(false);
	const [isLoadingFile, setIsLoadingFile] = useState(false);
	const [completeDialogIsOpen, setCompleteDialogIsOpen] = useState(false);
	const [forcedParams, setForcedParams] = useState({ forceAppendSn: false, forceModelOverride: false });

	const { invalidate: invalidateProductCodeProduct } = apiHooks.useGetProductCodeProduct(
		{ params: { productCodeId: productCodeId ?? '' } },
		{ enabled: false },
	);
	const { invalidate: invalidateProductAttributeValues, isFetching: isFetchingProductAttributeValues } =
		apiHooks.useGetProductAttributeValues({ params: { productId: productId ?? '' } }, { enabled: false });
	const { invalidate: invalidateProductFiles } = apiHooks.useGetProductFiles(
		{ params: { productId: productId ?? '' } },
		{ enabled: false },
	);
	const { invalidate: invalidateProductHistory } = apiHooks.useGetProductHistory(
		{ params: { productId: productId ?? '' } },
		{ enabled: false },
	);
	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId: productId ?? '' } }, { enabled: false });

	const invalidateAll = useCallback(() => {
		invalidateProductFiles();
		invalidateProductHistory();
		invalidateProductAttributeValues();
		invalidateProduct();
		invalidateProductCodeProduct();
	}, [
		invalidateProduct,
		invalidateProductAttributeValues,
		invalidateProductCodeProduct,
		invalidateProductFiles,
		invalidateProductHistory,
	]);

	const handleReaderError = useCallback((error: unknown) => {
		if (error instanceof AxiosError && error.response) {
			const { status, data } = error.response;
			if (status === 300) {
				const matchingProducts = (data.matchingProducts ?? []) as Array<{ id: string; code: number | null }>;

				setError({
					type: 'MODEL_CONFLICT',
					message: (
						<>
							Vyčítač načetl model &apos;{data.readerModel}&apos;, ale tento produkt je modelu &apos;{data.productModel}
							&apos;.
						</>
					),
					description:
						matchingProducts.length > 0 ? (
							<>
								{formatProducts(matchingProducts.length)} v této várce odpovídá tomuto modelu:{' '}
								{matchingProducts.map(({ id: productId, code }, idx) => (
									<Fragment key={productId}>
										<span>
											<Link className="text-link" href={`/test/product/${productId}`}>
												{formatProductCode(code ? { code } : null)}
											</Link>
										</span>
										{idx < matchingProducts.length - 1 ? ', ' : ''}
									</Fragment>
								))}
							</>
						) : null,
				});
				setCompleteDialogIsOpen(true);
			}

			if (data.sn) {
				if (status === 500) {
					setError({
						type: 'SN_MISMATCH',
						message: `SN z vyčítače (${data.sn.new}) se neshoduje se SN produktu (${data.sn.current}).`,
					});
					setCompleteDialogIsOpen(true);
				} else if (status === 404) {
					setError({ type: 'NOT_FOUND', message: `Produkt se SN ${data.sn} ve várce nebyl nalezen.` });
					setCompleteDialogIsOpen(true);
				} else if (status === 409) {
					setError({
						type: 'SN_CONFLICT',
						message: (
							<>
								SN {data.sn} je jiný produkt v této várce.{' '}
								<Link className="text-link" href={`/product/${data.conflictingProductId}`}>
									Přejít na produkt
								</Link>
							</>
						),
					});
					setCompleteDialogIsOpen(true);
				}
			}
		} else {
			setError({ type: 'READER_ERROR', message: 'Chyba vyčítání dat. Zkontrolujte si, že vám běží API, a zkuste to znovu.' });
			setCompleteDialogIsOpen(true);
		}

		setIsLoadingApi(false);
		setIsLoadingFile(false);
		return [];
	}, []);

	const handleReaderSuccess = useCallback(() => {
		invalidateAll();
		setIsLoadingFile(false);
		setIsLoadingApi(false);
		setError(null);
		setCompleteDialogIsOpen(true);
	}, [invalidateAll]);

	const { mutateAsync: loadAttributeValuesFromReader } = apiHooks.useLoadAttributeValuesFromReader(
		{ params: { productId: productId ?? '' } },
		{ onError: handleReaderError, onSuccess: handleReaderSuccess },
	);
	const { mutateAsync: loadAttributeValuesFromReaderForProductCode } = apiHooks.useLoadAttributeValuesForProductCodeFromReader(
		{ params: { productCodeId: productCodeId ?? '' } },
		{ onError: handleReaderError, onSuccess: handleReaderSuccess },
	);

	const handleLoadAttributeValues = useCallback(
		(fileIds: string[], forceAppendSn = false, forceModelOverride = false) => {
			setCompleteDialogIsOpen(false);
			setIsLoadingApi(true);
			setIsLoadingFile(true);
			const handler = productId
				? loadAttributeValuesFromReader
				: productCodeId
					? loadAttributeValuesFromReaderForProductCode
					: () => Promise.resolve({ _data: false });
			handler({ fileIds, forceAppendSn, forceModelOverride });
		},
		[loadAttributeValuesFromReader, loadAttributeValuesFromReaderForProductCode, productCodeId, productId],
	);

	useEffect(() => {
		if (!isFetchingProductAttributeValues) {
			setIsLoadingApi(false);
			setIsLoadingFile(false);
		}
	}, [isFetchingProductAttributeValues]);

	useEffect(() => {
		if (router.query.readerComplete === 'true') {
			setCompleteDialogIsOpen(true);
		}
	}, [router.query]);

	useEffect(() => {
		if (fileIds.length > 0) {
			handleLoadAttributeValues(fileIds);
		}
	}, [fileIds, handleLoadAttributeValues]);

	const loadFile: ChangeEventHandler<HTMLInputElement> = async (e) => {
		setError(null);
		setIsLoadingFile(true);

		const files = e.currentTarget.files;
		const fileToUpload = files?.[0];

		if (!fileToUpload) return;

		const fileUpload = await apiClient.uploadFile(fileToUpload, { queries: { fileName: fileToUpload.name } });
		const fileId = fileUpload._data;

		setFileIds([fileId]);
	};

	const loadApi = async () => {
		setError(null);
		setIsLoadingApi(true);

		const fileIds = await Promise.all(
			READER_LINKS.map(async (link) => {
				const data = await fetch(link); // FIXME - use axios

				if (data.status !== 200 || !data.body) return;

				const fileName = READER_FILENAMES[link];

				const reader = data.body.getReader();
				const chunks = [];
				let done = false;

				while (!done) {
					const { value, done: streamDone } = await reader.read();
					if (value) {
						chunks.push(new Blob([value], { type: READER_MIME_TYPES[link] }));
					}
					done = streamDone;
				}

				const fileToUpload = new File(chunks, fileName, { type: READER_MIME_TYPES[link] });

				const file = await apiClient.uploadFile(fileToUpload, {
					queries: { fileName },
				});

				return file._data;
			}),
		).catch((error) => handleReaderError(error));

		setFileIds(filterUndefined(fileIds));
	};

	return (
		<>
			<div className="flex flex-wrap items-center md:justify-between">
				<label>Načíst parametry zařízení z vyčítače</label>
				<Popover>
					<PopoverTrigger asChild>
						<Button variant="link" className="text-link p-0 h-auto font-normal max-md:order-1 max-md:mt-4">
							Stáhnout vyčítač
						</Button>
					</PopoverTrigger>
					<PopoverContent variant="tooltip">
						<ul>
							{READER_DOWNLOAD_LINKS.map(({ label, url }) => (
								<li key={url} className="py-1">
									<a href={url} target="_blank" rel="noreferrer noopener" className="underline hover:no-underline">
										{label}
									</a>
								</li>
							))}
						</ul>
					</PopoverContent>
				</Popover>
				<div className="mt-2 flex flex-col gap-4 xl:flex-row w-full">
					<div className="grow">
						<Input onChange={loadFile} type="file" className="text-stone-500 dark:text-stone-400" disabled={isLoadingApi} />
						{isLoadingFile && (
							<p className="text-sm mt-1">
								Načítání ze souboru <Spinner />
							</p>
						)}
					</div>
					<Button onClick={loadApi} disabled={isLoadingApi || isLoadingFile} isLoading={isLoadingApi}>
						Vyčíst parametry z API vyčítače
					</Button>
				</div>
			</div>

			<Dialog open={completeDialogIsOpen} onOpenChange={setCompleteDialogIsOpen}>
				<DialogContent>
					<DialogHeader>
						<Stack direction="column" gap={4} className="items-center text-center">
							<Icon
								name={error ? 'close' : 'check'}
								className={cn('w-20 h-20', error ? 'text-destructive' : 'text-success')}
							/>
							<DialogTitle>{error ? error.message : 'Vyčítání parametrů dokončeno'}</DialogTitle>
						</Stack>
					</DialogHeader>
					{error?.type === 'SN_MISMATCH' && (
						<DialogFooter>
							<Button
								onClick={() => {
									const next = { ...forcedParams, forceAppendSn: true };
									setForcedParams(next);
									handleLoadAttributeValues(fileIds, next.forceAppendSn, next.forceModelOverride);
								}}
							>
								Přiřadit vyčtené SN
							</Button>
						</DialogFooter>
					)}
					{error?.type === 'MODEL_CONFLICT' && (
						<>
							{error.description != null && <DialogDescription>{error.description}</DialogDescription>}
							<DialogFooter>
								<Button
									onClick={() => {
										const next = { ...forcedParams, forceModelOverride: true };
										setForcedParams(next);
										handleLoadAttributeValues(fileIds, next.forceAppendSn, next.forceModelOverride);
									}}
								>
									Použít model z vyčítače
								</Button>
							</DialogFooter>
						</>
					)}
				</DialogContent>
			</Dialog>
		</>
	);
};
