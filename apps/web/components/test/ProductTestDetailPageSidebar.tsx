import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, <PERSON><PERSON>itle, Stack } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { CosmeticDefectsCard } from '../cosmeticDefects/CosmeticDefectsCard';
import { ProductLabelPrint } from '../product/ProductLabelPrint';
import { ProductLabelPrintSn } from '../product/ProductLabelPrintSn';
import { ReturnProductToTestButton } from '../product/ReturnProductToTestButton';
import { ProductEnvelopeLabelPrint } from '../productEnvelope/ProductEnvelopeLabelPrint';
import { ImportedProductAttributesCard } from './ImportedProductAttributesCard';
import { ProductTaskResolve } from './ProductTaskResolve';
import { ProductTemplateActions } from './ProductTemplateActions';
import { ProductTestDetailOverview } from './ProductTestDetailOverview';
import { TestActionButtons } from './TestActionButtons';

type Props = {
	batch: ApiBody<'getBatch'>;
	product: ApiBody<'getProduct'>;
	productWasAlreadyTested: boolean;
	hasTestingRights: boolean;
};

export const ProductTestDetailPageSidebar: FC<Props> = ({ batch, product, productWasAlreadyTested, hasTestingRights }) => {
	const commonComponents = (
		<>
			<ProductTestDetailOverview batch={batch} product={product} />
			<ImportedProductAttributesCard product={product} />
		</>
	);

	if (!productWasAlreadyTested && !hasTestingRights) return commonComponents;

	return (
		<>
			{commonComponents}

			{hasTestingRights && <ProductTaskResolve product={product} />}

			{!productWasAlreadyTested ? (
				<TestActionButtons product={product} />
			) : (
				<>
					<CosmeticDefectsCard product={product} />

					<Card sticky>
						<CardHeader>
							<CardTitle>Akce</CardTitle>
						</CardHeader>
						<CardContent>
							<Stack gap={4}>
								{product.status === 'TESTED' && (
									<ReturnProductToTestButton productId={product.id} disabled={!hasTestingRights} />
								)}
								<ProductLabelPrint product={product} />
								<ProductLabelPrintSn product={product} />
								{product.productEnvelope && (
									<>
										<ProductEnvelopeLabelPrint product={product} productEnvelope={product.productEnvelope} />
										<ProductTemplateActions product={product} disabled={!hasTestingRights} />
									</>
								)}
							</Stack>
						</CardContent>
					</Card>
				</>
			)}
		</>
	);
};
