import { REQUIRED_FIELD } from '@pocitarna-nx-2023/config';
import { Card, CardContent, FormContext, type FormContextRef, Stack, Table, TableBody } from '@pocitarna-nx-2023/ui';
import { filterUndefined, removeDigitsFromText, uniques } from '@pocitarna-nx-2023/utils';
import { type ApiBody, primitive } from '@pocitarna-nx-2023/zodios';
import { type FC, memo, type PropsWithChildren, useEffect, useMemo, useRef, useState } from 'react';
import { z } from 'zod';
import { TestDiffProvider } from '../../contexts/TestDiffContext';
import { useCosmeticDefectsFormData } from '../../hooks/useCosmeticDefectsFormData';
import { useProductAttributeValues } from '../../hooks/useProductAttributeValues';
import { CosmeticDefectsHandling } from '../cosmeticDefects/CosmeticDefectsHandling';
import { AttributesForm } from './AttributesForm';
import { ParamScanner } from './ParamScanner';
import { TestDiffInputPCN } from './TestDiffInputPCN';
import { TestDiffInputSN } from './TestDiffInputSN';
import { TestSubmitButton } from './TestSubmitButton';

// Attributes that have related attributes, like 'Pevný disk 1 - kapacita' -> 'Pevný disk 2 - kapacita' etc.
// and differ only in number.
const ATTRIBUTES_WITH_OTHER_RELATED_ATTRIBUTES = ['Pevný disk 1 - kapacita', 'Pevný disk 1 - rychlost otáček', 'Pevný disk 1 - typ'];

export type NextRelatedAttributeType = {
	attributeName: string;
	nextAttributeName: string;
};

type Props = {
	product: ApiBody<'getProduct'>;
	minimumTestPhotosAmount: number;
};

export const TestDiff: FC<PropsWithChildren<Props>> = memo(({ product, minimumTestPhotosAmount, children }) => {
	const formRef = useRef<FormContextRef<typeof testDiffSchema>>(null);

	const {
		schema: cosmetiDefectsSchema,
		defaultValues: cosmeticDefectsDefaultValues,
		allRelevantDefectsHaveImage,
	} = useCosmeticDefectsFormData(product);

	const {
		productAttributeValues,
		productImportAttributeValues,
		productResolvedAttributeValues,
		categoryTestAttributes,
		categoryCompareAttributes,
		categoryMandatoryAttributes,
	} = useProductAttributeValues(product, true);

	const [nextRelatedAttributes, setNextRelatedAttributes] = useState<NextRelatedAttributeType[]>([]);
	const [displayedRelatedAttributes, setDisplayedRelatedAttributes] = useState<string[]>([]);

	// We need to go through all category attributes and treat related attributes differently.
	// Related attributes should be displayed if they have a value or are mandatory or
	// the user clicked 'Přidat dalši' to display them.
	useEffect(() => {
		if (categoryTestAttributes.length === 0) {
			return;
		}
		const newNextRelatedAttributes: NextRelatedAttributeType[] = [];
		const newDisplayedRelatedAttributes: string[] = [];

		ATTRIBUTES_WITH_OTHER_RELATED_ATTRIBUTES.forEach((item) => {
			const unifiedAttributeName = removeDigitsFromText(item);

			const relatedAttributes = categoryTestAttributes.filter(
				(attribute) => unifiedAttributeName === removeDigitsFromText(attribute.displayName),
			);
			relatedAttributes.sort((a, b) => a.displayName.localeCompare(b.displayName));

			const relatedAttributesReverseOrder = relatedAttributes.toReversed();

			const lastAttributeWithValueOrMandatory = relatedAttributesReverseOrder.find(
				(attribute) =>
					productAttributeValues.map((productAttributeValue) => productAttributeValue.attribute.id).includes(attribute.id) ||
					categoryMandatoryAttributes.some((mandatoryAttribute) => mandatoryAttribute.id === attribute.id),
			);
			const lastAttributeWithValueOrMandatoryIndex = relatedAttributes.findIndex(
				(attribute) => attribute.displayName === lastAttributeWithValueOrMandatory?.displayName,
			);

			for (let i = 0; i <= lastAttributeWithValueOrMandatoryIndex; i++) {
				newDisplayedRelatedAttributes.push(relatedAttributes[i].displayName);
			}

			for (
				let i = lastAttributeWithValueOrMandatoryIndex > -1 ? lastAttributeWithValueOrMandatoryIndex : 0;
				i < relatedAttributes.length - 1;
				i++
			) {
				newNextRelatedAttributes.push({
					attributeName: relatedAttributes[i].displayName,
					nextAttributeName: relatedAttributes[i + 1].displayName,
				});
			}
		});

		setNextRelatedAttributes(newNextRelatedAttributes);
		setDisplayedRelatedAttributes((displayedRelatedAttributes) =>
			uniques([...displayedRelatedAttributes, ...newDisplayedRelatedAttributes]),
		);
	}, [categoryMandatoryAttributes, categoryTestAttributes, productAttributeValues]);

	const filteredCategoryTestAttributes = useMemo(() => {
		const allNextAttributeNames = nextRelatedAttributes.map((item) => item.nextAttributeName);
		return categoryTestAttributes.filter(
			(attribute) =>
				!allNextAttributeNames.includes(attribute.displayName) || displayedRelatedAttributes.includes(attribute.displayName),
		);
	}, [categoryTestAttributes, nextRelatedAttributes, displayedRelatedAttributes]);

	const displayNextRelatedAttribute = (currentAttributeName: string) => {
		setNextRelatedAttributes((nextRelatedAttributes) =>
			nextRelatedAttributes.filter((item) => item.attributeName !== currentAttributeName),
		);
		setDisplayedRelatedAttributes((displayedRelatedAttributes) =>
			filterUndefined([
				...displayedRelatedAttributes,
				nextRelatedAttributes.find((attribute) => attribute.attributeName === currentAttributeName)?.nextAttributeName,
			]),
		);
	};

	const testDiffSchema = z.object({
		attributeValues: z.object(
			filteredCategoryTestAttributes.reduce<
				Record<string, z.ZodObject<{ attributeValueId: z.ZodString; value: z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodBoolean]> }>>
			>((acc, attribute) => {
				const hasImportedAttributeValue = productImportAttributeValues.some(
					(importAttributeValue) => importAttributeValue.attribute.id === attribute.id,
				);
				const isMandatoryAttribute = categoryMandatoryAttributes.some(
					(mandatoryAttribute) => mandatoryAttribute.id === attribute.id,
				);
				const isCompareAttribute = categoryCompareAttributes.some((compareAttribute) => compareAttribute.id === attribute.id);

				const isRequiredAttribute = isMandatoryAttribute || (isCompareAttribute && hasImportedAttributeValue);

				acc[attribute.id] = z.object({
					attributeValueId:
						!['text', 'multiselect'].includes(attribute.dataType) && isRequiredAttribute
							? z.string().min(1, REQUIRED_FIELD)
							: z.string(),
					value:
						['text', 'multiselect'].includes(attribute.dataType) && isRequiredAttribute
							? z.union([z.string().min(1, REQUIRED_FIELD), z.number().min(1, REQUIRED_FIELD), z.boolean()])
							: primitive,
				});
				return acc;
			}, {}),
		),

		...cosmetiDefectsSchema.shape,
	});

	type TestDiffSchema = z.infer<typeof testDiffSchema>;

	const defaultValues: TestDiffSchema = useMemo(
		() => ({
			attributeValues: getDefaultAttributeValues(filteredCategoryTestAttributes, productResolvedAttributeValues),
			...cosmeticDefectsDefaultValues,
		}),
		[cosmeticDefectsDefaultValues, filteredCategoryTestAttributes, productResolvedAttributeValues],
	);

	const [formSubmitted, setFormSubmitted] = useState(false);

	useEffect(() => {
		formRef?.current?.reset(defaultValues);
		if (formSubmitted) {
			formRef?.current?.trigger();
		}
	}, [defaultValues, formSubmitted]);

	return (
		<>
			<Card>
				<CardContent>
					<ParamScanner productId={product.id} />
				</CardContent>
			</Card>

			<Stack>
				<Table>
					<TableBody>
						<TestDiffInputPCN product={product} />
						<TestDiffInputSN product={product} />
					</TableBody>
				</Table>

				<TestDiffProvider>
					<Stack gap={4}>
						<h2 className="h3">Parametry</h2>

						<FormContext ref={formRef} schema={testDiffSchema} defaultValues={defaultValues}>
							{() => (
								<Stack>
									<Table>
										<TableBody>
											{product?.productCategoryId && (
												<AttributesForm
													product={product}
													productCategoryId={product.productCategoryId}
													attributeValuesToTest={productAttributeValues}
													categoryAttributes={filteredCategoryTestAttributes}
													displayNextRelatedAttribute={displayNextRelatedAttribute}
													nextRelatedAttributes={nextRelatedAttributes.filter(
														(attribute) => !displayedRelatedAttributes.includes(attribute.nextAttributeName),
													)}
												/>
											)}
										</TableBody>
									</Table>

									<CosmeticDefectsHandling product={product} />
								</Stack>
							)}
						</FormContext>
					</Stack>
					{children}
					<TestSubmitButton
						product={product}
						minimumTestPhotosAmount={minimumTestPhotosAmount}
						allRelevantDefectsHaveImage={allRelevantDefectsHaveImage}
						handleValidation={() => {
							setFormSubmitted(true);
							return formRef.current?.trigger();
						}}
					/>
				</TestDiffProvider>
			</Stack>
		</>
	);
});

TestDiff.displayName = 'TestDiff';

// FIXME - should also support multiple values
const resolveAttributeValue = ([attributeValue]: ApiBody<'getProductAttributeValues'>) => {
	if (!attributeValue || attributeValue.temporary) return { attributeValueId: '', value: '' };
	if (attributeValue.attribute.dataType === 'text') return { attributeValueId: '', value: String(attributeValue.value) };
	return { attributeValueId: attributeValue.id, value: String(attributeValue.value) };
};

const getDefaultAttributeValues = (
	attributes: ApiBody<'getProductCategoryAttributes'>,
	attributeValues: ApiBody<'getProductAttributeValues'>,
) => {
	return attributes.reduce<Record<string, { value: string | number | boolean; attributeValueId: string }>>((acc, attribute) => {
		acc[attribute.id] = resolveAttributeValue(attributeValues.filter((attributeValue) => attributeValue.attribute.id === attribute.id));
		return acc;
	}, {});
};
