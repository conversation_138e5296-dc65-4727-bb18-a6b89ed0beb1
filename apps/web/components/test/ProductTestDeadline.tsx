import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { SensitiveDate } from '../SensitiveDate';

type Props = {
	productTest?: ApiBody<'getProduct'>['productTest'];
};

export const ProductTestDeadline: FC<Props> = ({ productTest }) => {
	if (!productTest || !productTest.deadlineAt || !productTest.testedBy) return null;

	return (
		<>
			<br />
			{productTest.testedBy.name}: <SensitiveDate date={productTest.deadlineAt} />
		</>
	);
};
