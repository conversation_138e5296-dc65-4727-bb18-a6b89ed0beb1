import { LOADING_MESSAGE, MAX_POSITIVE_INTEGER, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { Button, Checkbox, cn, type DataTable, TableCell, TableRow } from '@pocitarna-nx-2023/ui';
import { formatBatchCode, formatProductCode, uniquesBy } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { type FC, Fragment } from 'react';
import { useCategoryAttributes } from '../../hooks/useCategoryAttributes';
import { AttributeValue } from '../AttributeValue';
import { SplitCodeDisplay } from '../SplitCodeDisplay';
import { BatchTestDeadline } from './BatchTestDeadline';
import { PriorityDisplay } from './PriorityDisplay';
import { ProductTestDeadline } from './ProductTestDeadline';

type Props = {
	product: ApiBody<'getAllProducts'>[number];
	batch?: ApiBody<'getBatch'>;
	batchDetail?: boolean;
	table: DataTable;
	isArchive?: boolean;
};

export const TestProductRow: FC<Props> = ({ product, batch, batchDetail, table, isArchive = false }) => {
	const { categoryAttributes } = useCategoryAttributes(product.productCategoryId, 'config');

	const { data: productTaskData } = apiHooks.useGetProductTasks({
		params: { productId: product.id },
		...(isArchive ? {} : { filter: { status: { eq: 'NEW' } } }),
	});
	const productTasks = productTaskData?._data ?? [];
	const serviceTaskTypes = productTasks.map((task) => task.serviceTaskType) ?? [];
	const uniqueServiceTaskTypes = uniquesBy(serviceTaskTypes, 'id');

	const { data: attributeValuesData, isLoading: isLoadingAttributeValues } = apiHooks.useGetProductAttributeValues({
		params: { productId: product.id },
		queries: {
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
			filter: {
				'productAttributeValue.type': { eq: 'resolved' },
				'attribute.id': { eq: categoryAttributes.map(({ id }) => id) },
			},
		},
	});

	const attributeValues = (attributeValuesData?._data ?? []).toSorted((a, b) => {
		const aSequence = categoryAttributes.find((attribute) => attribute.id === a.attribute.id)?.categoryAttributes.at(0)?.sequence ?? -1;
		const bSequence = categoryAttributes.find((attribute) => attribute.id === b.attribute.id)?.categoryAttributes.at(0)?.sequence ?? -1;
		return aSequence - bSequence;
	});
	const manufacturer = attributeValues.find((item) => item.attribute.displayName === 'Značka (výrobce)');
	const model = attributeValues.find((item) => item.attribute.displayName === 'Model');

	return (
		<TableRow selected={table.isSelected(product.id)}>
			{!isArchive && (
				<TableCell sticky="left">
					<Checkbox checked={table.isSelected(product.id)} onCheckedChange={(checked) => table.selectRow(checked, product.id)} />
				</TableCell>
			)}
			<TableCell>
				{product.productCategory ? (
					<Link className="text-link" href={`/product/envelope/category/${product.productCategory.id}`}>
						{product.productCategory.name}
					</Link>
				) : (
					NOT_AVAILABLE
				)}
			</TableCell>
			{!batchDetail && (
				<TableCell>
					{batch ? (
						<Link className="text-link whitespace-normal" href={`/batch/${batch.id}`}>
							{formatBatchCode(batch.code)}
							<br />
							<span className="line-clamp-1">{batch.name}</span>
						</Link>
					) : (
						NOT_AVAILABLE
					)}
				</TableCell>
			)}
			<TableCell>
				{batch ? <PriorityDisplay entity={batch} /> : NOT_AVAILABLE}
				<br />
				<BatchTestDeadline batch={batch} />
				<ProductTestDeadline productTest={product.productTest} />
			</TableCell>
			<TableCell>{product.productTest?.testedBy?.name ?? NOT_AVAILABLE}</TableCell>
			<TableCell>
				<Link className="text-link" href={`/product/${product.id}`}>
					{formatProductCode(product.code)}
				</Link>
				<br />
				<Link className="text-link" href={`/product/${product.id}`}>
					<SplitCodeDisplay code={product.sn} />
				</Link>
			</TableCell>
			<TableCell>
				{isLoadingAttributeValues ? (
					LOADING_MESSAGE
				) : (
					<>
						{manufacturer ? <AttributeValue attributeValue={manufacturer} /> : NOT_AVAILABLE}
						<br />
						{model ? <AttributeValue attributeValue={model} /> : NOT_AVAILABLE}
					</>
				)}
			</TableCell>
			<TableCell>
				{isLoadingAttributeValues ? (
					LOADING_MESSAGE
				) : (
					<p className="text-wrap">
						{attributeValues
							.filter((item) => item !== model && item !== manufacturer)
							.map((attributeValue, index) => (
								<Fragment key={attributeValue.id}>
									{index > 0 && ', '}
									<AttributeValue attributeValue={attributeValue} />
								</Fragment>
							))}
					</p>
				)}
			</TableCell>
			<TableCell className={cn(!isArchive && 'text-destructive')}>
				{uniqueServiceTaskTypes.map((serviceTaskType, index) => (
					<Fragment key={serviceTaskType.id}>
						{index > 0 && <br />}
						{serviceTaskType.name}
					</Fragment>
				))}
			</TableCell>
			<TableCell className="text-right" sticky="right">
				<Button asChild size="sm">
					<Link href={`/test/product/${product.id}`}>{isArchive ? 'Detail' : 'Testovat'}</Link>
				</Button>
			</TableCell>
		</TableRow>
	);
};
