import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Typo<PERSON>, useDialog } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { useScan } from '../../hooks/useScan';

type Props = {
	setProductId: (productId: string) => void;
};

export const FindByScanningSN: FC<Props> = ({ setProductId }) => {
	const { closeDialog } = useDialog();
	const [scanCode, reset] = useScan();
	const { data, isFetching } = apiHooks.useGetByScanCode({ params: { scanCode } }, { cacheTime: 0, keepPreviousData: false });
	const fetchedProduct = data?._data?.type === 'product' ? data?._data.product : null;
	const isCodeAlreadyAssigned = !!fetchedProduct?.codeId;

	const handleAssignSN = () => {
		if (!fetchedProduct) return;
		setProductId(fetchedProduct.id);
		closeDialog();
	};

	const resetScan = () => {
		reset();
	};

	if (scanCode && fetchedProduct && !isCodeAlreadyAssigned && !isFetching) {
		return (
			<>
				<Typography>
					<p>Načtený kód: {scanCode}</p>
				</Typography>
				<DialogFooter>
					<Button onClick={handleAssignSN}>Přiřadit sériové číslo</Button>
					<Button onClick={resetScan}>Skenovat znovu</Button>
					<DialogClose asChild>
						<Button variant="outline">Zavřít</Button>
					</DialogClose>
				</DialogFooter>
			</>
		);
	}

	return (
		<Typography>
			{isCodeAlreadyAssigned && (
				<>
					<p>Načtený kód: {scanCode}</p>
					<Alert variant="destructive">Kód je již obsazen</Alert>
				</>
			)}
			<p>Čekám na načtení kódu...</p>
			<Spinner />
		</Typography>
	);
};
