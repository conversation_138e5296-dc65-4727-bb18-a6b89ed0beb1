import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { Button, Icon, Print, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@pocitarna-nx-2023/ui';
import { filterUndefined, formatBatchCode, formatEnvelopeCode, formatProductCode, uniques } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import type { FC } from 'react';

type Props = {
	productEnvelopeMap: [string, string, string][];
};

const cellStyle = { border: '1px solid #000', paddingLeft: '8px', paddingRight: '16px', textAlign: 'left' } as const;

export const ProductEnvelopeMapTable: FC<Props> = ({ productEnvelopeMap }) => {
	const productIds = uniques(productEnvelopeMap.map(([productId]) => productId));
	const productEnvelopeIds = uniques(productEnvelopeMap.map(([, productEnvelopeId]) => productEnvelopeId));
	const batchIds = uniques(productEnvelopeMap.map(([, , batchId]) => batchId));
	const { data: productsData } = apiHooks.useGetAllProducts(
		{ queries: { filter: { id: { eq: productIds } }, page: 1, limit: MAX_POSITIVE_INTEGER } },
		{ enabled: productIds.length > 0 },
	);
	const products = productsData?._data ?? [];
	const { data: productEnvelopesData } = apiHooks.useGetAllProductEnvelopes(
		{ queries: { filter: { id: { eq: productEnvelopeIds } }, page: 1, limit: MAX_POSITIVE_INTEGER } },
		{ enabled: productEnvelopeIds.length > 0 },
	);
	const productEnvelopes = productEnvelopesData?._data ?? [];
	const { data: batchesData } = apiHooks.useGetBatches(
		{ queries: { filter: { id: { eq: batchIds } }, page: 1, limit: MAX_POSITIVE_INTEGER } },
		{ enabled: batchIds.length > 0 },
	);
	const batches = batchesData?._data ?? [];
	const categoryIds = filterUndefined(productEnvelopes.map(({ productCategory }) => productCategory?.id));
	const { data: categoriesData } = apiHooks.useGetProductCategoriesList(
		{ queries: { filter: { id: { eq: categoryIds } }, page: 1, limit: MAX_POSITIVE_INTEGER } },
		{ enabled: categoryIds.length > 0 },
	);
	const categories = categoriesData?._data ?? [];

	if (productEnvelopeMap.length === 0) return null;

	const data = filterUndefined(
		productEnvelopeMap.map(([productId, productEnvelopeId, batchId]) => {
			const product = products.find(({ id }) => id === productId);
			const productEnvelope = productEnvelopes.find(({ id }) => id === productEnvelopeId);
			const batch = batches.find(({ id }) => id === batchId);
			const category = categories.find(({ id }) => id === productEnvelope?.productCategoryId);
			if (!product || !productEnvelope || !category || !batch) return null;
			return [product, productEnvelope, category, batch] as const;
		}),
	).toSorted((a, b) => (a[0].code?.code ?? -1) - (b[0].code?.code ?? -1));

	return (
		<Print
			handle={(handlePrint) => (
				<Button variant="secondary" type="button" onClick={handlePrint}>
					<Icon name="print" />
					Vytisknout přehled přiřazení
				</Button>
			)}
		>
			<Table style={{ borderCollapse: 'collapse' }}>
				<TableHeader>
					<TableRow>
						<TableHead style={cellStyle}>Várka</TableHead>
						<TableHead style={cellStyle}>PCN</TableHead>
						<TableHead style={cellStyle}>Kód karty</TableHead>
						<TableHead style={cellStyle}>Název karty</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{data.map(([product, productEnvelope, category, batch]) => (
						<TableRow key={product.id}>
							<TableCell style={cellStyle}>{formatBatchCode(batch.code)}</TableCell>
							<TableCell style={cellStyle}>{formatProductCode(product.code)}</TableCell>
							<TableCell style={cellStyle}>{formatEnvelopeCode(category.codePrefix)(productEnvelope.code)}</TableCell>
							<TableCell style={cellStyle}>{productEnvelope.name}</TableCell>
						</TableRow>
					))}
				</TableBody>
			</Table>
		</Print>
	);
};
