import { NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import {
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	Card<PERSON>ooter,
	CardHeader,
	CardTitle,
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
	FormContext,
	InputControl,
	ParamItem,
	ParamList,
	Stack,
	toast,
	useDialog,
} from '@pocitarna-nx-2023/ui';
import { z } from 'zod';

export type WithTrackingCode = {
	trackingCode: string;
	vendorRMAIdentifier?: string;
};

type Props<T extends WithTrackingCode> = {
	entity: T;
	onSubmit: (data: WithTrackingCode) => void;
	isLoading: boolean;
	withVendorRMAIdentifier?: boolean;
	disabled?: boolean;
};

export const TrackingCard = <T extends WithTrackingCode>(props: Props<T>) => {
	const { entity, withVendorRMAIdentifier, disabled } = props;

	return (
		<Card>
			<CardHeader className="flex flex-col justify-between items-start md:flex-row">
				<CardTitle>Tracking kód</CardTitle>
			</CardHeader>
			<CardContent>
				<ParamList>
					<ParamItem label="Tracking kód" align="center">
						{entity.trackingCode ?? NOT_AVAILABLE}
					</ParamItem>
					{withVendorRMAIdentifier && (
						<ParamItem label="ID RMA dodavatele" align="center">
							{entity.vendorRMAIdentifier ?? NOT_AVAILABLE}
						</ParamItem>
					)}
				</ParamList>
			</CardContent>
			<CardFooter>
				<Dialog>
					<DialogTrigger asChild>
						<Button className="w-full" variant="secondary" disabled={disabled}>
							Upravit
						</Button>
					</DialogTrigger>
					<DialogContent>
						<DialogHeader>
							<DialogTitle>Upravit kod</DialogTitle>
						</DialogHeader>
						<TrackingForm {...props} />
					</DialogContent>
				</Dialog>
			</CardFooter>
		</Card>
	);
};

const TrackingForm = <T extends WithTrackingCode>({
	entity,
	onSubmit,
	isLoading,
	withVendorRMAIdentifier = false,
	disabled = false,
}: Props<T>) => {
	const { closeDialog } = useDialog();

	return (
		<FormContext
			schema={z.object({
				trackingCode: z.string(),
				vendorRMAIdentifier: z.string().optional(),
			})}
			defaultValues={{
				trackingCode: entity.trackingCode ?? '',
				...(withVendorRMAIdentifier && { vendorRMAIdentifier: entity.vendorRMAIdentifier ?? '' }),
			}}
			onSubmit={(data) => {
				if (disabled) return;
				onSubmit(data);
				toast.success('Zaktualizováno');
				closeDialog();
			}}
		>
			{(control) => {
				return (
					<Stack gap={4}>
						<InputControl control={control} name="trackingCode" label="Tracking kód" disabled={disabled} />
						{withVendorRMAIdentifier && (
							<InputControl control={control} name="vendorRMAIdentifier" label="ID RMA dodavatele" disabled={disabled} />
						)}

						<Button className="w-full" variant="default" type="submit" isLoading={isLoading} disabled={disabled}>
							Uložit
						</Button>
					</Stack>
				);
			}}
		</FormContext>
	);
};
