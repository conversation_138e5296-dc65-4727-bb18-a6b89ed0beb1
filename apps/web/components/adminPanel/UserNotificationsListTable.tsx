import { MAX_POSITIVE_INTEGER, NOT_AVAILABLE, type NotificationDeliveryMethod } from '@pocitarna-nx-2023/config';
import {
	Alert,
	AlertDescription,
	AlertTitle,
	type FilterRules,
	Icon,
	Stack,
	Switch,
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
	toast,
} from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, memo, useCallback, useMemo } from 'react';
import { useSorting } from '../../contexts/SortingContext';
import { usePaging } from '../../hooks/usePaging';
import { Pagination } from '../Pagination/Pagination';
import { SortableColumn } from '../sorting/SortableColumn';

type Props = {
	filters: FilterRules;
};

export const ListTable: FC<Props> = ({ filters }) => {
	const [page, limit, setLimit] = usePaging();

	const filter = {
		...(filters['name'] ? { name: { eq: `%${filters['name']}%` } } : {}),
		...(filters['email'] ? { email: { eq: `%${filters['email']}%` } } : {}),
	};

	const { sorting } = useSorting();

	const { data: usersData, isSuccess } = apiHooks.useGetUsers({
		queries: { page, filter, sort: sorting, limit },
	});
	const users = useMemo(() => usersData?._data ?? [], [usersData?._data]);

	const { data: userNotificationTypesData } = apiHooks.useGetUserNotificationTypes(
		{
			queries: {
				page: 1,
				limit: MAX_POSITIVE_INTEGER,
				filter: { userId: { eq: users.map((user) => user.id) } },
			},
		},
		{ enabled: users.length > 0 },
	);

	const userNotificationTypes = userNotificationTypesData?._data ?? [];

	const { data: notificationTypesData } = apiHooks.useGetNotificationTypes({
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER },
	});
	const notificationTypes = notificationTypesData?._data ?? [];

	if (isSuccess && !users.length) {
		return (
			<Alert variant="info">
				<Icon name="circle-info" />
				<AlertTitle>Žádní uživatelé.</AlertTitle>
				<AlertDescription>Pro zobrazení uživatelů zkuste upravit filtr.</AlertDescription>
			</Alert>
		);
	}

	return (
		<>
			<Table>
				<TableHeader>
					<TableRow>
						<SortableColumn property="name">Uživatel</SortableColumn>
						{notificationTypes.map((type) => (
							<TableHead key={type.id}>{type.label}</TableHead>
						))}
					</TableRow>
				</TableHeader>
				<TableBody>
					{users.map((user) => (
						<UserNoficationListRow
							key={user.id}
							user={user}
							notificationTypes={notificationTypes}
							userNotificationTypes={userNotificationTypes.filter(
								(userNotificationType) => userNotificationType.userId === user.id,
							)}
						/>
					))}
				</TableBody>
			</Table>

			<Pagination paging={usersData?._paging} rows={limit} setRows={setLimit} />
		</>
	);
};

export const UserNotificationsListTable = memo(ListTable);
UserNotificationsListTable.displayName = 'UserNotificationsListTable';

const UserNoficationListRow: FC<{
	user: ApiBody<'getUsers'>[number];
	userNotificationTypes: ApiBody<'getUserNotificationTypes'>;
	notificationTypes: ApiBody<'getNotificationTypes'>;
}> = ({ user, userNotificationTypes, notificationTypes }) => {
	return (
		<TableRow>
			<TableCell>
				{user.name} ({user.email ?? NOT_AVAILABLE})
			</TableCell>
			{notificationTypes.map((type) => (
				<TableCell key={type.id}>
					<Stack direction="row" gap={2}>
						<UserNotificationSwitch
							user={user}
							notificationType={type}
							userNotificationTypes={userNotificationTypes}
							deliveryMethod="push"
						/>
						{user.email && (
							<UserNotificationSwitch
								user={user}
								notificationType={type}
								userNotificationTypes={userNotificationTypes}
								deliveryMethod="email"
							/>
						)}
					</Stack>
				</TableCell>
			))}
		</TableRow>
	);
};

const UserNotificationSwitch: FC<{
	user: ApiBody<'getUsers'>[number];
	notificationType: ApiBody<'getNotificationTypes'>[number];
	userNotificationTypes: ApiBody<'getUserNotificationTypes'>;
	deliveryMethod: NotificationDeliveryMethod;
}> = ({ user, notificationType, userNotificationTypes, deliveryMethod }) => {
	const { invalidate } = apiHooks.useGetUserNotificationTypes({}, { enabled: false });

	const onSuccess = () => {
		invalidate();
		toast.success('Zaktualizováno');
	};

	const { mutate: createUserNotificationType } = apiHooks.useCreateUserNotificationType(
		{
			params: { userId: user.id, notificationTypeId: notificationType.id },
		},
		{ onSuccess },
	);
	const { mutate: deleteUserNotificationType } = apiHooks.useDeleteUserNotificationType(
		{
			params: { userId: user.id, notificationTypeId: notificationType.id },
		},
		{ onSuccess },
	);

	const exists = useMemo(
		() =>
			userNotificationTypes.some(
				(userNotificationType) =>
					userNotificationType.notificationTypeId === notificationType.id &&
					userNotificationType.deliveryMethod === deliveryMethod,
			),
		[userNotificationTypes, notificationType.id, deliveryMethod],
	);

	const handleCheck = useCallback(
		(deliveryMethod: NotificationDeliveryMethod) => async () => {
			if (exists) {
				deleteUserNotificationType({ deliveryMethod });
			} else {
				createUserNotificationType({ deliveryMethod });
			}
		},
		[createUserNotificationType, deleteUserNotificationType, exists],
	);

	return (
		<Stack direction="row" gap={2} className="items-center">
			<Icon name={deliveryMethod === 'push' ? 'bell' : 'envelope'} />
			<Switch checked={exists} onCheckedChange={handleCheck(deliveryMethod)} />
		</Stack>
	);
};
