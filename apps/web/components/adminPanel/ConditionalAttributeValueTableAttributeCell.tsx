import { <PERSON><PERSON>, <PERSON>ete<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ell, Toolt<PERSON> } from '@pocitarna-nx-2023/ui';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import type { FC } from 'react';
import { useFormContext } from 'react-hook-form';
import { useUserHasScope } from '../../hooks/useUserHasScope';

type Props = {
	parentAttribute: ApiBody<'getAttribute'>;
	attribute: ApiBody<'getAttribute'>;
};

export const ConditionalAttributeValueTableAttributeCell: FC<Props> = ({ attribute, parentAttribute }) => {
	const isAdmin = useUserHasScope('admin');
	const { watch, setValue } = useFormContext<{ attributeIds: string[] }>();
	const attributeIds = watch('attributeIds');
	const { invalidate } = apiHooks.useGetConditionalAttributesForAttribute(
		{ params: { attributeId: parentAttribute.id } },
		{ enabled: false },
	);
	const { mutate: deleteConditionalAttributeAttribute, isLoading } = apiHooks.useDeleteConditionalAttributeAttribute(
		{
			params: { attributeId: parentAttribute.id, childAttributeId: attribute.id },
		},
		{
			onSuccess: () => {
				invalidate();
				setValue(
					'attributeIds',
					attributeIds.filter((id) => id !== attribute.id),
				);
			},
		},
	);

	return (
		<TableCell className="min-w-64">
			<Stack direction="row" className="justify-between items-center">
				<Tooltip tooltip={attribute.name}>
					<span>
						{attribute.displayName}
						{attribute.unit && <> ( {attribute.unit} )</>}
					</span>
				</Tooltip>
				<DeleteDialog
					title="Odstranit parametr"
					description={
						<p>
							<strong>POZOR</strong>: Tímto se odstraní parametr ze všech šablon!
						</p>
					}
					trigger={
						<Button variant="outline" width="icon" disabled={!isAdmin}>
							<Icon name="trash" className="h-4 w-4" />
						</Button>
					}
					onDelete={(callback) => {
						deleteConditionalAttributeAttribute(undefined);
						callback();
					}}
					isLoading={isLoading}
				/>
			</Stack>
		</TableCell>
	);
};
