import { Stack } from '@pocitarna-nx-2023/ui';
import { formatProductCode } from '@pocitarna-nx-2023/utils';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import type { FC } from 'react';

type Props = {
	attribute: ApiBody<'getFullAttribute'>;
	attributeValue: ApiBody<'getFullAttribute'>['values'][number];
};

export const AttributeValueDeleteDialog: FC<Props> = ({ attribute, attributeValue }) => {
	const { data: productsData } = apiHooks.useGetAttributeValueProducts({
		params: { attributeId: attribute.id, attributeValueId: attributeValue.id },
	});
	const products = productsData?._data ?? [];

	return (
		<Stack gap={4}>
			<p>Opravdu si přejete odstranit položku?</p>
			{products.length > 0 && (
				<>
					<p>
						<strong>Pozor!</strong> Tato hodnota je přiřazena u těchto produktů:
					</p>
					<ul className="gap-1 flex flex-wrap">
						{products.map((product) => (
							<li key={product.id}>
								<Link href={`/product/${product.id}`} className="text-link">
									{formatProductCode(product.code)}
								</Link>
							</li>
						))}
					</ul>
				</>
			)}
		</Stack>
	);
};
