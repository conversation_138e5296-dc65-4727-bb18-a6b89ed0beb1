import { Table, TableBody, TableHead, TableHeader, TableRow } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type ComponentProps, type FC, memo } from 'react';
import { AdminCosmeticDefectRow } from './AdminCosmeticDefectRow';

type Props = ComponentProps<typeof Table> & {
	cosmeticDefects: ApiBody<'getCosmeticDefects'>;
};

const ListTable: FC<Props> = ({ cosmeticDefects, ...props }) => {
	return (
		<Table {...props}>
			<TableHeader>
				<TableRow>
					<TableHead>Název</TableHead>
					<TableHead>Stav produktu</TableHead>
					<TableHead>Lokality závad</TableHead>
					<TableHead>Požadováno foto</TableHead>
					<TableHead />
				</TableRow>
			</TableHeader>
			<TableBody>
				{cosmeticDefects.map((cosmeticDefect) => (
					<AdminCosmeticDefectRow key={cosmeticDefect.id} cosmeticDefect={cosmeticDefect} />
				))}
			</TableBody>
		</Table>
	);
};

export const CosmeticDefectListTable = memo(ListTable);
CosmeticDefectListTable.displayName = 'CosmeticDefectListTable';
