import { Button, FormContext, toast, useDialog } from '@pocitarna-nx-2023/ui';
import { type ApiBody, userUpdate } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';

type Props = {
	user: ApiBody<'getUsers'>[number];
};

export const ReactivateUser: FC<Props> = ({ user }) => {
	const { closeDialog } = useDialog();

	const { invalidate } = apiHooks.useGetUsers(undefined, { enabled: false });
	const { mutate, isLoading } = apiHooks.useUpdateUserAdmin({ params: { userId: user.id } });

	return (
		<FormContext
			schema={userUpdate}
			defaultValues={{ deletedAt: null }}
			onSubmit={(data) =>
				mutate(data, {
					onSuccess: () => {
						invalidate();
						closeDialog();
						toast.success('Uživatel byl znovu aktivován.');
					},
				})
			}
		>
			{() => (
				<Button className="w-full" variant="default" type="submit" isLoading={isLoading}>
					Aktivovat
				</Button>
			)}
		</FormContext>
	);
};
