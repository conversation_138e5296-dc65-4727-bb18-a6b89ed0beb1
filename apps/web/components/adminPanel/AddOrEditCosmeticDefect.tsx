import { MAX_POSITIVE_INTEGER, REQUIRED_FIELD } from '@pocitarna-nx-2023/config';
import {
	Button,
	ComboboxControl,
	DialogFooter,
	FormContext,
	InputControl,
	MultiSelectControl,
	Stack,
	SwitchControl,
	useDialog,
} from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useEffect, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { z } from 'zod';

const schema = z.object({
	name: z.string().min(1, { message: REQUIRED_FIELD }),
	pictureRequired: z.boolean(),
	productCategories: z.array(z.string().uuid()).min(1, { message: REQUIRED_FIELD }),
	cosmeticAreas: z.array(z.string().uuid()).min(1, { message: REQUIRED_FIELD }),
	grade: z.string().uuid(),
});

type Props = {
	cosmeticDefect?: ApiBody<'getCosmeticDefects'>[number];
	gradeId?: string;
};

export const AddOrEditCosmeticDefect: FC<Props> = ({ cosmeticDefect, gradeId }) => {
	const { closeDialog } = useDialog();
	const { invalidate } = apiHooks.useGetCosmeticDefects({}, { enabled: false });

	const { data: gradesData } = apiHooks.useGetRankedGrades({});
	const grades = useMemo(() => gradesData?._data ?? [], [gradesData?._data]);

	const { data: productCategoriesData } = apiHooks.useGetProductCategoriesList({
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER, sort: ['name'] },
	});
	const productCategories = useMemo(() => productCategoriesData?._data ?? [], [productCategoriesData?._data]);

	const { mutate: createCosmeticDefect, isLoading } = apiHooks.useCreateCosmeticDefect(undefined);
	const { mutate: updateCosmeticDefect, isLoading: isUpdating } = apiHooks.useUpdateCosmeticDefect({
		params: { cosmeticDefectId: cosmeticDefect?.id ?? '' },
	});

	const onSuccess = () => {
		invalidate();
		closeDialog();
	};

	const onSubmit = (data: z.input<typeof schema>) => {
		const dataToSubmit = {
			...data,
			grade: { id: data.grade },
		};
		if (cosmeticDefect) {
			updateCosmeticDefect(dataToSubmit, {
				onSuccess,
			});
		} else {
			createCosmeticDefect(dataToSubmit, {
				onSuccess,
			});
		}
	};

	return (
		<FormContext
			schema={schema}
			defaultValues={{
				name: cosmeticDefect?.name ?? '',
				pictureRequired: cosmeticDefect?.pictureRequired ?? false,
				productCategories:
					(cosmeticDefect?.productCategoryCosmeticDefects ?? []).map((category) => category.productCategoryId) ?? [],
				cosmeticAreas: (cosmeticDefect?.cosmeticAreaCosmeticDefects ?? []).map((area) => area.cosmeticAreaId) ?? [],
				grade: cosmeticDefect?.grade?.id ?? gradeId ?? '',
			}}
			onSubmit={onSubmit}
		>
			{(control) => (
				<Stack gap={4}>
					<InputControl control={control} name="name" placeholder="Name" label="Popis vady" />
					<ComboboxControl
						control={control}
						name="grade"
						items={grades.map((grade) => ({ value: grade.id, label: grade.name }))}
						label="Cílový stav"
						hideSearch={true}
						disabled={cosmeticDefect != null}
					/>
					<MultiSelectControl
						control={control}
						name="productCategories"
						label="Vhodné kategorie"
						options={productCategories.map((category) => ({ value: category.id, label: category.name }))}
					/>
					<CosmeticAreasSelector />
					<SwitchControl control={control} name="pictureRequired" label="Vyžadovat fotografii" />

					<DialogFooter>
						<Button className="w-full" variant="default" type="submit" isLoading={isLoading || isUpdating}>
							Vytvořit
						</Button>
					</DialogFooter>
				</Stack>
			)}
		</FormContext>
	);
};

const CosmeticAreasSelector: FC = () => {
	const { watch, control, setValue } = useFormContext<z.input<typeof schema>>();

	const productCategoryIds = watch('productCategories');
	const cosmeticAreasIds = watch('cosmeticAreas');

	const { data: cosmeticAreasData } = apiHooks.useGetCosmeticAreas(
		{
			queries: {
				page: 1,
				limit: MAX_POSITIVE_INTEGER,
				sort: ['name'],
				filter: { productCategoryId: { eq: productCategoryIds } },
			},
		},
		{ enabled: productCategoryIds.length > 0 },
	);

	const cosmeticAreas = useMemo(() => cosmeticAreasData?._data ?? [], [cosmeticAreasData?._data]);

	useEffect(() => {
		const relevantAreaIds = cosmeticAreas.filter((area) => productCategoryIds.includes(area.productCategoryId)).map((area) => area.id);
		const areaIdsToShow = cosmeticAreasIds.filter((id) => relevantAreaIds.includes(id));

		const areEqual = areaIdsToShow.length === cosmeticAreasIds.length && areaIdsToShow.every((id) => cosmeticAreasIds.includes(id));

		if (!areEqual) {
			setValue('cosmeticAreas', areaIdsToShow);
		}
	}, [productCategoryIds, cosmeticAreas, setValue, cosmeticAreasIds]);

	return (
		<MultiSelectControl
			control={control}
			name="cosmeticAreas"
			label="Lokality závad"
			options={cosmeticAreas.map((area) => ({ value: area.id, label: `${area.productCategory.name} - ${area.name}` }))}
			disabled={productCategoryIds.length === 0}
		/>
	);
};
