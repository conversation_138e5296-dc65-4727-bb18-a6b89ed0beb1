import {
	<PERSON><PERSON>,
	<PERSON>ete<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>alog<PERSON><PERSON>nt,
	<PERSON>alog<PERSON>eader,
	DialogTitle,
	DialogTrigger,
	Icon,
	<PERSON>ack,
	TableCell,
	TableRow,
	toast,
} from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { AddOrEditCosmeticDefect } from './AddOrEditCosmeticDefect';

type Props = {
	cosmeticDefect: ApiBody<'getCosmeticDefects'>[number];
};

export const AdminCosmeticDefectRow: FC<Props> = ({ cosmeticDefect }) => {
	const { invalidate } = apiHooks.useGetCosmeticDefects({}, { enabled: false });
	const { mutate, isLoading } = apiHooks.useDeleteCosmeticDefect({ params: { cosmeticDefectId: cosmeticDefect.id } });

	const onDefectDelete = (callback: () => void) =>
		mutate(undefined, {
			onSuccess: () => {
				callback();
				toast.success('Kosmetická vada byla smazána.');
				invalidate();
			},
		});

	const areas = cosmeticDefect.cosmeticAreaCosmeticDefects?.map(
		(area) => `${area.cosmeticArea.productCategory.name} - ${area.cosmeticArea.name}`,
	);

	return (
		<TableRow>
			<TableCell>{cosmeticDefect.name}</TableCell>
			<TableCell>{cosmeticDefect.grade.name}</TableCell>
			<TableCell>{(areas ?? []).join(', ')}</TableCell>
			<TableCell>{cosmeticDefect.pictureRequired ? 'Ano' : 'Ne'}</TableCell>

			<TableCell>
				<Stack direction="row" gap={2} className="justify-end">
					<Dialog>
						<DialogTrigger asChild>
							<Button size="sm" variant="outline" width="icon">
								<Icon name="pencil" className="h-4 w-4" />
							</Button>
						</DialogTrigger>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>Upravit kosmetickou vadu</DialogTitle>
							</DialogHeader>
							<AddOrEditCosmeticDefect cosmeticDefect={cosmeticDefect} />
						</DialogContent>
					</Dialog>
					<DeleteDialog
						title="Odstranit kosmetickou vadu"
						description="Opravdu si přejete odstranit položku?"
						triggerVariant="icon"
						onDelete={onDefectDelete}
						isLoading={isLoading}
					/>
				</Stack>
			</TableCell>
		</TableRow>
	);
};
