import { type FilterRules, Table, TableBody, TableHead, TableHeader, TableRow } from '@pocitarna-nx-2023/ui';
import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, memo, useMemo } from 'react';
import { useSorting } from '../../contexts/SortingContext';
import { usePaging } from '../../hooks/usePaging';
import { Pagination } from '../Pagination/Pagination';
import { SortableColumn } from '../sorting/SortableColumn';
import { ServiceTaskTypeRow } from './ServiceTaskTypeRow';

type Props = {
	tabFilter: ListProps['filter'];
	filters: FilterRules;
};

export const ListTable: FC<Props> = ({ filters }) => {
	const [page, limit, setLimit] = usePaging();

	const filter = {
		...(filters['name'] ? { name: { eq: `%${filters['name']}%` } } : {}),
	};
	const { sorting } = useSorting();

	const { data } = apiHooks.useGetServiceTaskTypes({
		queries: { page, filter, sort: sorting, limit },
	});

	const serviceTaskTypes = useMemo(() => data?._data ?? [], [data?._data]);

	return (
		<>
			<Table>
				<TableHeader>
					<TableRow>
						<SortableColumn property="name">Název</SortableColumn>
						<TableHead>Cena</TableHead>
						<TableHead>Typ</TableHead>
						<TableHead />
					</TableRow>
				</TableHeader>
				<TableBody>
					{serviceTaskTypes.map((serviceTaskType) => (
						<ServiceTaskTypeRow key={serviceTaskType.id} serviceTaskType={serviceTaskType} />
					))}
				</TableBody>
			</Table>

			<Pagination paging={data?._paging} rows={limit} setRows={setLimit} />
		</>
	);
};

export const ServiceTaskTypeListTable = memo(ListTable);
ServiceTaskTypeListTable.displayName = 'ServiceTaskTypeListTable';
