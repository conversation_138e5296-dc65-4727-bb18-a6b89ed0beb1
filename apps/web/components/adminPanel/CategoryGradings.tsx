import { EMPTY_VALUE } from '@pocitarna-nx-2023/config';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	ComboboxControl,
	FormContext,
	type FormContextRef,
	Stack,
	toast,
} from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useEffect, useMemo, useRef } from 'react';
import { z } from 'zod';

type Props = {
	productCategoryId: string;
};

const schema = z.object({
	data: z.record(z.string().uuid(), z.string().pipe(z.coerce.number().min(0))),
});

export const CategoryGradings: FC<Props> = ({ productCategoryId }) => {
	const formRef = useRef<FormContextRef<typeof schema>>(null);
	const { data: gradesData } = apiHooks.useGetRankedGrades({});
	const grades = useMemo(() => gradesData?._data ?? [], [gradesData?._data]);

	const { data: categoryGradesData, invalidate } = apiHooks.useGetProductCategoryGrades({
		params: { productCategoryId },
	});
	const categoryGrades = useMemo(() => categoryGradesData?._data ?? [], [categoryGradesData?._data]);

	const { mutate: handleCategoryGrade, isLoading } = apiHooks.useHandleProductCategoryGrade({ params: { productCategoryId } });

	const defaultValues = useMemo(() => {
		return {
			data: grades.reduce<Record<string, string>>((acc, curr) => {
				const match = categoryGrades.find((item) => item.gradeId === curr.id);
				acc[curr.id] = match?.maxCosmeticDefects?.toString() ?? EMPTY_VALUE;
				return acc;
			}, {}),
		};
	}, [grades, categoryGrades]);

	useEffect(() => {
		formRef?.current?.reset(defaultValues);
	}, [defaultValues]);

	return (
		<Card>
			<CardHeader>
				<CardTitle>Stavy produktů a počet kosmetických vad</CardTitle>
			</CardHeader>
			<CardContent>
				<Stack gap={4}>
					<FormContext ref={formRef} schema={schema} defaultValues={defaultValues}>
						{(control) => (
							<Stack gap={4}>
								{grades.map((grade) => {
									const value = formRef.current?.watch(`data.${grade.id}`);
									return (
										<Stack direction="row" className="items-center" gap={4} key={grade.id}>
											<h5>{grade.name}</h5>
											<ComboboxControl
												key={grade.id}
												control={control}
												name={`data.${grade.id}`}
												items={[
													{ value: EMPTY_VALUE, label: EMPTY_VALUE },
													...Array.from({ length: 10 }).map((_, idx) => ({
														value: (idx + 1).toString(),
														label: (idx + 1).toString(),
													})),
												]}
												disabled={isLoading}
												onSelect={(val) => {
													handleCategoryGrade(
														{ gradeId: grade.id, maxCosmeticDefects: val === EMPTY_VALUE ? 0 : Number(val) },
														{
															onSuccess: () => {
																invalidate();
																toast.success('Zaktualizováno');
															},
														},
													);
												}}
												hideSearch={true}
											/>
											{value === EMPTY_VALUE && (
												<p className="text-warning text-xs">
													Tento stav nebude pro tuto kategorii použitelný. V případě potřeby zvýšete množství.
												</p>
											)}
										</Stack>
									);
								})}
							</Stack>
						)}
					</FormContext>
				</Stack>
			</CardContent>
		</Card>
	);
};
