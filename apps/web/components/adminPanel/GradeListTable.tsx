import { DndContext, type DragEndEvent } from '@dnd-kit/core';
import { SortableContext } from '@dnd-kit/sortable';
import { Stack, Table, TableBody, TableHead, TableHeader, TableRow } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useCallback } from 'react';
import { AdminGradeRow } from './AdminGradeRow';

type Props = {
	grades: ApiBody<'getRankedGrades'>;
	invalidate: () => void;
};

export const GradeListTable: FC<Props> = ({ grades, invalidate }) => {
	const { mutate: changeSequence } = apiHooks.useUpdateGradeSequence({}, { onSuccess: invalidate });

	const handleSort = useCallback(
		(event: DragEndEvent) => {
			const { active, over } = event;
			if (active.id !== over?.id) {
				const newIndex = grades.findIndex(({ id }) => id === over?.id);
				changeSequence({ gradeId: active.id as string, sequence: newIndex });
			}
		},
		[changeSequence, grades],
	);

	return (
		<DndContext onDragEnd={handleSort}>
			<Stack gap={4}>
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead />
							<TableHead>Název stavu</TableHead>
							<TableHead>Sleva z doporučené ceny</TableHead>
							<TableHead>Způsob zaokrouhlování ceny</TableHead>
							<TableHead />
						</TableRow>
					</TableHeader>
					<TableBody>
						<SortableContext items={grades}>
							{grades.map((grade) => (
								<AdminGradeRow key={grade.id} grade={grade} />
							))}
						</SortableContext>
					</TableBody>
				</Table>
			</Stack>
		</DndContext>
	);
};
