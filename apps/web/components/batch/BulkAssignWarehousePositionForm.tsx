import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { Button, ComboboxControl, type ComboBoxItemType, FormContext, Stack, toast, useDialog } from '@pocitarna-nx-2023/ui';
import { formatWarehousePositionName } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useEffect, useMemo, useState } from 'react';
import { z } from 'zod';
import { useMultiTableSelection } from '../../contexts/MultiTableSelectionContext';

type Props = {
	invalidateProductsInBatch: () => void;
};

const schema = z.object({
	warehouseId: z.string().uuid(),
	warehousePositionId: z.string().uuid(),
});

export const BulkAssignWarehousePositionForm: FC<Props> = ({ invalidateProductsInBatch }) => {
	const [selectedWarehouseId, setSelectedWarehouseId] = useState<string | null>(null);
	const [positionsToShow, setPositionsToShow] = useState<ComboBoxItemType[]>([]);

	const { closeDialog } = useDialog();
	const { selectedElements: productIds } = useMultiTableSelection();

	const { data: warehousesData } = apiHooks.useGetWarehouses({
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER, sort: ['name'] },
	});
	const { data: warehousePositionsData } = apiHooks.useGetWarehousePositions(
		{
			queries: { page: 1, limit: MAX_POSITIVE_INTEGER, filter: { 'warehouse.id': { eq: selectedWarehouseId || '' } } },
		},
		{ enabled: Boolean(selectedWarehouseId) },
	);
	const { mutate: bulkUpdateProducts, isLoading } = apiHooks.useBulkUpdateProduct();

	const warehouses = useMemo(() => warehousesData?._data ?? [], [warehousesData?._data]);
	const warehousePositions = useMemo(() => warehousePositionsData?._data ?? [], [warehousePositionsData?._data]);

	useEffect(() => {
		const positions = preparePositionForCombobox(warehousePositions.filter((position) => position.warehouseId === selectedWarehouseId));
		setPositionsToShow(positions);
	}, [warehousePositions, selectedWarehouseId]);

	const onPositionSearchChange = (value: string) => {
		setPositionsToShow(
			preparePositionForCombobox(
				warehousePositions.filter(
					(position) =>
						position.warehouseId === selectedWarehouseId &&
						formatWarehousePositionName(position).toLocaleLowerCase().includes(value.toLocaleLowerCase()),
				),
			),
		);
	};

	return (
		<FormContext
			schema={schema}
			defaultValues={{
				warehouseId: '',
				warehousePositionId: '',
			}}
			onSubmit={(data) => {
				bulkUpdateProducts(
					{ ids: productIds, data: { warehousePosition: { id: data.warehousePositionId } } },
					{
						onSuccess: () => {
							toast.success('Pozice byla aktualizována');
							invalidateProductsInBatch();
							closeDialog();
						},
					},
				);
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<ComboboxControl
						control={control}
						name="warehouseId"
						items={warehouses.map((warehouse) => ({
							value: warehouse.id,
							label: warehouse.name,
						}))}
						label="Sklad"
						onSelect={(value) => setSelectedWarehouseId(value)}
						hideSearch={true}
					/>
					<ComboboxControl
						control={control}
						name="warehousePositionId"
						items={positionsToShow}
						label="Pozice"
						onSearchChange={onPositionSearchChange}
					/>

					<Button className="w-full" variant="default" type="submit" isLoading={isLoading}>
						Pokračovat
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};

const preparePositionForCombobox = (positions: ApiBody<'getWarehousePositions'>) =>
	positions
		.map((position) => ({
			value: position.id,
			label: formatWarehousePositionName(position),
		}))
		.toSorted((a, b) => a.label.localeCompare(b.label));
