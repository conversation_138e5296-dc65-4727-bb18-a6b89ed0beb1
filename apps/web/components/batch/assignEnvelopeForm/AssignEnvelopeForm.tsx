import { Tabs, toast, useDialog } from '@pocitarna-nx-2023/ui';
import { type SingleOrArray } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useQueryClient } from '@tanstack/react-query';
import { type FC, useState } from 'react';
import { useMultiTableSelection } from '../../../contexts/MultiTableSelectionContext';
import { CreateOrEditNewEnvelope } from './CreateOrEditNewEnvelope';
import { SearchAndSelectEnvelope } from './SearchAndSelectEnvelope';

type Props = {
	productId: SingleOrArray<string>;
	batchId: string;
	productCategory?: string;
	productEnvelope?: ApiBody<'getBatchProducts'>[number]['productEnvelope'];
};

export const AssignEnvelopeForm: FC<Props> = ({ productId, batchId, productCategory, productEnvelope }) => {
	const [selectedTab, setSelectedTab] = useState<'edit' | 'search' | 'create'>(productEnvelope ? 'edit' : 'search');

	const queryClient = useQueryClient();

	const { invalidate: invalidateProductsInBatch } = apiHooks.useGetBatchProducts({ params: { batchId } }, { enabled: false });
	const getProductQueryKey = apiHooks.getKeyByAlias('getProduct');
	const invalidateProduct = () => queryClient.invalidateQueries(getProductQueryKey);
	const { closeDialog } = useDialog();

	const onSuccess = () => {
		invalidateProductsInBatch();
		invalidateProduct();
		toast.success('Aktualizováno');
		closeDialog();
	};

	return (
		<>
			<Tabs
				tabs={[
					...(productEnvelope ? [{ label: 'Použít existující kartu', key: 'edit' }] : []),
					{ label: 'Hledat kartu', key: 'search' },
					{ label: 'Vytvořit novou kartu', key: 'create' },
				]}
				activeTab={selectedTab}
				onChange={(key) => {
					setSelectedTab(key as 'search' | 'create');
				}}
			/>
			{selectedTab === 'edit' && (
				<CreateOrEditNewEnvelope
					productId={productId}
					productEnvelope={productEnvelope}
					productCategory={productCategory}
					onSuccess={onSuccess}
					variant="edit"
				/>
			)}
			{selectedTab === 'search' && <SearchAndSelectEnvelope productId={productId} onSuccess={onSuccess} />}
			{selectedTab === 'create' && (
				<CreateOrEditNewEnvelope productId={productId} onSuccess={onSuccess} productCategory={productCategory} variant="create" />
			)}
		</>
	);
};

export const BulkAssignEnvelopeForm: FC<Pick<Props, 'batchId'>> = ({ batchId }) => {
	const { selectedElements: productIds } = useMultiTableSelection();

	return <AssignEnvelopeForm productId={productIds} batchId={batchId} />;
};
