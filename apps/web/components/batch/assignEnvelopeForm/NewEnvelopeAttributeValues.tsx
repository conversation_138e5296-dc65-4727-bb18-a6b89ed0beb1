import { Stack, Table, TableBody, Typography } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useEffect, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { CategoryAttributeInputs } from '../../CategoryAttributeInputs';
import { AttributeFormRow } from '../../test/AttributeFormRow';
import { type RawAttributeValue, transformAttributeValues } from './CreateOrEditNewEnvelope';

type Props = {
	attributesToShow: ApiBody<'getProductCategoryAttributes'>;
	categoryId: string;
	onMatchFound: (envelope: ApiBody<'getProductEnvelope'> | null) => void;
	productEnvelope?: ApiBody<'getBatchProducts'>[number]['productEnvelope'];
};

type QueryParams = {
	attributeValues: RawAttributeValue & { attributeId: string }[];
};

export const NewEnvelopeAttributeValues: FC<Props> = ({ attributesToShow, categoryId, onMatchFound, productEnvelope }) => {
	const [queryParams, setQueryParams] = useState<QueryParams>({ attributeValues: [] });

	const { watch } = useFormContext();
	const envelopeType = watch('type');

	const { data: matchingEnvelopeData } = apiHooks.useGetProductEnvelopeByAttributeValues(
		{
			queries: {
				...queryParams,
				filter: {
					productCategoryId: { eq: categoryId },
					type: { eq: envelopeType },
					...(productEnvelope ? { id: { ne: productEnvelope.id } } : {}),
				},
			},
		},
		{ enabled: queryParams.attributeValues.length > 0 },
	);
	const matchingEnvelope = useMemo(() => matchingEnvelopeData?._data, [matchingEnvelopeData?._data]);

	useEffect(() => {
		const subscription = watch((value, { name, type }) => {
			const attributeValuesChanged = name?.includes('attributeValues') && type === 'change';

			if (attributeValuesChanged) {
				setQueryParams({ attributeValues: transformAttributeValues(value.attributeValues) });
			}
		});

		return () => subscription.unsubscribe();
	}, [watch]);

	useEffect(() => {
		if (matchingEnvelope) {
			onMatchFound(matchingEnvelope);
		} else {
			onMatchFound(null);
		}
	}, [matchingEnvelope, onMatchFound]);

	return (
		<Stack gap={4}>
			<Typography>Parametry</Typography>
			<Table>
				<TableBody>
					{attributesToShow.map((item) => (
						<AttributeFormRow key={item.id} attribute={item} productCategoryId={categoryId}>
							<CategoryAttributeInputs categoryAttribute={item} productCategoryId={categoryId} />
						</AttributeFormRow>
					))}
				</TableBody>
			</Table>
		</Stack>
	);
};
