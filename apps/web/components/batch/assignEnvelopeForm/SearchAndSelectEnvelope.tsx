import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>er,
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuTrigger,
	FormContext,
	Input,
	Stack,
} from '@pocitarna-nx-2023/ui';
import { type SingleOrArray, uniques } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type ChangeEvent, type FC, useEffect, useMemo, useRef, useState } from 'react';
import { useDebounce, useToggle } from 'rooks';
import { z } from 'zod';
import { EnvelopeRecap } from '../../productEnvelope/EnvelopeRecap';

type Props = {
	productId: SingleOrArray<string>;
	onSuccess?: () => void;
};

const schema = z.object({
	productEnvelopeId: z.string().uuid(),
});

export const SearchAndSelectEnvelope: FC<Props> = ({ productId, onSuccess }) => {
	const isBulkUpdate = Array.isArray(productId);
	const productIds = isBulkUpdate ? productId : [productId];
	const idsToProcess = uniques(productIds);

	const [selectedEnvelope, setSelectedEnvelope] = useState<ApiBody<'getProductEnvelope'> | null>(null);

	const { mutate: updateProduct, isLoading } = apiHooks.useUpdateProduct({ params: { productId: productIds[0] } });
	const { mutate: bulkUpdateProduct, isLoading: isBulkLoading } = apiHooks.useBulkUpdateProduct();

	return (
		<>
			<EnvelopeSearcher selectEnvelope={setSelectedEnvelope} />

			{selectedEnvelope && (
				<FormContext
					schema={schema}
					defaultValues={{
						productEnvelopeId: selectedEnvelope.id,
					}}
					onSubmit={(data) => {
						if (isBulkUpdate) {
							bulkUpdateProduct(
								{ ids: idsToProcess, data: { productEnvelope: { id: data.productEnvelopeId } } },
								{ onSuccess },
							);
						} else {
							updateProduct({ productEnvelope: { id: data.productEnvelopeId } }, { onSuccess });
						}
					}}
				>
					{(_control) => (
						<Stack gap={4}>
							<div className="pb-4">
								<EnvelopeRecap productEnvelope={selectedEnvelope} />
							</div>

							<DialogFooter>
								<Button type="submit" isLoading={isLoading || isBulkLoading}>
									Potvrdit
								</Button>
							</DialogFooter>
						</Stack>
					)}
				</FormContext>
			)}
		</>
	);
};

const EnvelopeSearcher: FC<{ selectEnvelope: (envelope: ApiBody<'getProductEnvelope'>) => void }> = ({ selectEnvelope }) => {
	const [isOpen, toggle] = useToggle(false);
	const inputRef = useRef<HTMLInputElement>(null);

	const [searchQuery, setSearchQuery] = useState<string>('');
	const [startQueryRequests, setStartQueryRequests] = useState(false);

	const { data } = apiHooks.useSearchProductEnvelope(
		{ queries: { query: searchQuery, filter: { type: { eq: ['NEW', 'AMOUNT'] } } } },
		{ enabled: startQueryRequests },
	);

	useEffect(() => {
		if (data?._data && !isOpen) {
			toggle();
			setTimeout(() => {
				inputRef.current?.focus();
			}, 200);
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [data?._data]);

	useEffect(() => {
		if (isOpen && !searchQuery) {
			toggle();
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [searchQuery]);

	const handleInputChange = useDebounce((e: ChangeEvent<HTMLInputElement>) => {
		setSearchQuery(e.target.value);
		setStartQueryRequests(true);
	}, 500);

	const results = useMemo(() => data?._data ?? [], [data?._data]);

	return (
		<div className="flex">
			<Input
				ref={inputRef}
				icon="magnifying-glass"
				placeholder="Vyhledat"
				type="search"
				onChange={handleInputChange}
				onFocus={() => {
					if (searchQuery && !isOpen) {
						toggle();
						setTimeout(() => {
							inputRef.current?.focus();
						}, 200);
					}
				}}
				className="pointer-events-auto"
			/>

			<DropdownMenu open={isOpen} modal={false}>
				<DropdownMenuTrigger asChild>
					<span />
				</DropdownMenuTrigger>
				<DropdownMenuContent
					align="end"
					style={{ width: inputRef.current?.offsetWidth }}
					// eslint-disable-next-line @typescript-eslint/no-explicit-any
					onInteractOutside={(e: any) => {
						if (inputRef.current && e.target === inputRef.current) return;
						if (isOpen) toggle();
					}}
					onEscapeKeyDown={toggle}
					onCloseAutoFocus={(e) => e.preventDefault()}
				>
					<div className="flex gap-4 flex-col p-2">
						{results.length === 0 ? (
							<div>Nebyly nalezeny žádné výsledky.</div>
						) : (
							<ul>
								{results.map((productEnvelope: ApiBody<'getProductEnvelope'>) => (
									<li
										key={productEnvelope.id}
										className=" hover:bg-amber-50 cursor-pointer py-4"
										onClick={() => {
											toggle();
											selectEnvelope(productEnvelope);
										}}
									>
										<EnvelopeRecap productEnvelope={productEnvelope} />
									</li>
								))}
							</ul>
						)}
					</div>
				</DropdownMenuContent>
			</DropdownMenu>
		</div>
	);
};
