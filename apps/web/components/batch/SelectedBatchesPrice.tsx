import { TableHead, TableRow } from '@pocitarna-nx-2023/ui';
import { type FC } from 'react';
import { useSum } from '../../contexts/SumContext';
import { PriceDisplayer } from '../PriceDisplayer';

export const SelectedBatchesPrice: FC = () => {
	const { total } = useSum();

	return (
		<TableRow>
			<TableHead colSpan={6} />
			<TableHead className="text-right text-black">∑</TableHead>
			<TableHead className="text-black">
				<PriceDisplayer price={total} rounded vatIncluded={false} />
			</TableHead>
			<TableHead colSpan={4} />
		</TableRow>
	);
};
