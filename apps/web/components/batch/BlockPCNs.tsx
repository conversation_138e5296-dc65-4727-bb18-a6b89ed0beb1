import { CURRENCIES_SYMBOLS, type CurrencyCode } from '@pocitarna-nx-2023/config';
import {
	Button,
	ComboboxControl,
	FormContext,
	type FormContextRef,
	NumericInputControl,
	Stack,
	toast,
	useDialog,
} from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useRef } from 'react';
import { z } from 'zod';
import { useSearchFilter } from '../../hooks/useSearchFilter';

type Props = {
	batchId: string;
	currencyCode: CurrencyCode;
	disabled: boolean;
};

const schema = z.object({
	productCategoryId: z.string(),
	productsCount: z.number().min(1),
	totalPrice: z.number(),
});

export const BlockPCNs: FC<Props> = ({ batchId, currencyCode, disabled }) => {
	const formRef = useRef<FormContextRef<typeof schema>>(null);
	const { updateSearchTerm, filter } = useSearchFilter('name');

	const { data: productCategoriesData } = apiHooks.useGetProductCategoriesList({ queries: { filter } });
	const productCategories = productCategoriesData?._data ?? [];

	const { mutate: addProductsToBatch, isLoading } = apiHooks.useCreateBatchProducts({ params: { batchId } });

	const { invalidate: invalidateProductsInBatch } = apiHooks.useGetBatchProducts({ params: { batchId } }, { enabled: false });

	const { closeDialog } = useDialog();

	return (
		<FormContext
			ref={formRef}
			schema={schema}
			defaultValues={{
				productCategoryId: '',
				productsCount: 0,
				totalPrice: 0.0,
			}}
			onSubmit={(data) => {
				if (disabled) return;

				addProductsToBatch(data, {
					onSuccess: () => {
						toast.success('PCN zablokovány');
						invalidateProductsInBatch();
						closeDialog();
					},
				});
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<ComboboxControl
						label="Kategorie produktu"
						control={control}
						name="productCategoryId"
						onSearchChange={updateSearchTerm}
						items={productCategories.map((category) => ({ label: category.name, value: category.id }))}
						disabled={disabled}
						onValueDelete={() => formRef.current?.setValue('productCategoryId', '', { shouldDirty: true })}
					/>

					<NumericInputControl
						control={control}
						name="productsCount"
						label="Počet poduktů"
						numberFormat="integer"
						disabled={disabled}
					/>

					<NumericInputControl
						control={control}
						name="totalPrice"
						placeholder="0.0"
						label={`Celková cena bez DPH (${CURRENCIES_SYMBOLS[currencyCode]})`}
						numberFormat="decimal"
						disabled={disabled}
					/>

					<Button className="w-full" variant="default" type="submit" disabled={disabled} isLoading={isLoading}>
						Pokračovat
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
