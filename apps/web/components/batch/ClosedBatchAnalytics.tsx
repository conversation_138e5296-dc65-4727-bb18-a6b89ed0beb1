import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, ParamI<PERSON>, ParamList } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useMemo } from 'react';

type Props = {
	batchId: string;
};

export const ClosedBatchAnalytics: FC<Props> = ({ batchId }) => {
	const { data: batchAnalyticsData } = apiHooks.useGetClosedBatchAnalytics({
		params: { batchId },
	});

	const batchAnalytics = useMemo(() => batchAnalyticsData?._data, [batchAnalyticsData?._data]);

	if (!batchAnalytics) return null;

	return (
		<Card>
			<CardHeader>
				<h3 className="font-semibold leading-none tracking-tight">Statistiky várky</h3>
			</CardHeader>
			<CardContent>
				<ParamList>
					<ParamItem label="Otestované produkty">{batchAnalytics.testedProducts}</ParamItem>
					<ParamItem label="Do naskladnění">{batchAnalytics.straightToStock}</ParamItem>
					<ParamItem label="Se servisem">{batchAnalytics.withServiceCase}</ParamItem>
					<ParamItem label="S dod. reklamací">{batchAnalytics.withWarrantyClaim}</ParamItem>
				</ParamList>
			</CardContent>
		</Card>
	);
};
