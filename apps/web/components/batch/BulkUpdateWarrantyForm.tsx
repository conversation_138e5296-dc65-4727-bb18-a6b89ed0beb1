import { WARRANTY_TYPES, WarrantyTypesLabels } from '@pocitarna-nx-2023/config';
import { Button, ComboboxControl, DatepickerControl, FormContext, Stack, toast, useDialog } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { z } from 'zod';
import { useMultiTableSelection } from '../../contexts/MultiTableSelectionContext';

type Props = {
	invalidateProductsInBatch: () => void;
};

const schema = z.object({
	type: z.enum(WARRANTY_TYPES),
	expiredAt: z.coerce.date(),
});

export const BulkUpdateWarrantyForm: FC<Props> = ({ invalidateProductsInBatch }) => {
	const { selectedElements: productIds } = useMultiTableSelection();
	const { mutate: bulkUpdateWarranties, isLoading } = apiHooks.useBulkUpdateProductWarranty();

	const { closeDialog } = useDialog();

	return (
		<FormContext
			schema={schema}
			defaultValues={{
				type: 'BUY',
				expiredAt: new Date(),
			}}
			onSubmit={(data) => {
				bulkUpdateWarranties(
					{ productIds, data },
					{
						onSuccess: () => {
							toast.success('Záruka aktualizována');
							invalidateProductsInBatch();
							closeDialog();
						},
					},
				);
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<ComboboxControl
						control={control}
						name="type"
						items={WARRANTY_TYPES.map((warrantyType) => ({ value: warrantyType, label: WarrantyTypesLabels[warrantyType] }))}
						label="Typ"
						hideSearch={true}
					/>
					<DatepickerControl control={control} name="expiredAt" label="Datum expirace" />

					<Button className="w-full" variant="default" type="submit" isLoading={isLoading}>
						Pokračovat
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
