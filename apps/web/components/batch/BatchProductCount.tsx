import { Tooltip } from '@pocitarna-nx-2023/ui';
import { formatNumber } from '@pocitarna-nx-2023/utils';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import type { FC } from 'react';

type Props = {
	batch: ApiBody<'getBatch'>;
};

export const BatchProductCount: FC<Props> = ({ batch }) => {
	const { data: productsCountData } = apiHooks.useGetBatchProductsCount({ params: { batchId: batch.id } });
	const productsCount = productsCountData?._data;

	return (
		<Tooltip
			tooltip={
				<span>
					Otestované produkty <br />
					Produkty ke schválení karty <br />
					Všechny produkty <br />
				</span>
			}
		>
			<span>
				{formatNumber(productsCount?.tested ?? 0)}
				{' | '}
				{formatNumber(productsCount?.notVerified ?? 0)}
				{' | '}
				{formatNumber(productsCount?.total ?? 0)}
			</span>
		</Tooltip>
	);
};
