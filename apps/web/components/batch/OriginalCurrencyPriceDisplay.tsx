import { MutedText } from '@pocitarna-nx-2023/ui';
import { formatDate, formatPrice } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';

type Props = {
	currencyRate: ApiBody<'getBatch'>['currencyRate'];
	amount?: number;
};
export const OriginalCurrencyPriceDisplay: FC<Props> = ({ currencyRate, amount = 0 }) => (
	<p>
		<strong>{formatPrice(amount / currencyRate.rate, currencyRate.currency.code, true)}</strong>
		<br />
		<MutedText>z {formatDate(currencyRate.createdAt)}</MutedText>
	</p>
);
