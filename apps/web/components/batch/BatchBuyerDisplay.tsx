import { NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { cn } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';

type Props = {
	batch: ApiBody<'getBatch'>;
};

export const BatchBuyerDisplay: FC<Props> = ({ batch }) => {
	return <span className={cn(batch.boughtBy?.deletedAt && 'text-foreground/70')}>{batch.boughtBy?.name ?? NOT_AVAILABLE}</span>;
};
