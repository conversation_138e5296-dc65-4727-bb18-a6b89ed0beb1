import {
	AFTER_WARRANTY_CLAIM_PRODUCT_TARGET_STATUSES,
	PRODUCT_STATUS_NAMES,
	ProductStatusMessage,
	WarrantyClaimStatusMessage,
} from '@pocitarna-nx-2023/config';
import {
	Button,
	ComboboxControl,
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
	FormContext,
	Stack,
	TextControl,
	toast,
	useDialog,
} from '@pocitarna-nx-2023/ui';
import { filterUndefined } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useMemo } from 'react';
import { z } from 'zod';

type Props = {
	warrantyClaimId: string;
	disabled: boolean;
};

export const WarrantyClaimCloseDialog: FC<Props> = ({ warrantyClaimId, disabled }) => {
	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button disabled={disabled}>U<PERSON><PERSON><PERSON><PERSON><PERSON> reklamace</Button>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Uzavřít reklamace</DialogTitle>
				</DialogHeader>
				<WarrantyClaimCloser warrantyClaimId={warrantyClaimId} disabled={disabled} />
			</DialogContent>
		</Dialog>
	);
};

const WarrantyClaimCloser: FC<Props> = ({ warrantyClaimId, disabled }) => {
	const { invalidate: invalidateProductDefects } = apiHooks.useGetAllProductDefects(undefined, { enabled: false });
	const { invalidate: invalidateWarrantyClaimHistory } = apiHooks.useGetWarrantyClaimHistory(
		{ params: { warrantyClaimId } },
		{ enabled: false },
	);
	const { invalidate: invalidateWarrantyClaim } = apiHooks.useGetWarrantyClaim({ params: { warrantyClaimId } }, { enabled: false });
	const { invalidate: invalidateServiceCases } = apiHooks.useGetServiceCases({}, { enabled: false });

	const { mutateAsync: updateWarrantyClaim, isLoading } = apiHooks.useUpdateWarrantyClaim({
		params: { warrantyClaimId: warrantyClaimId },
	});

	const { closeDialog } = useDialog();

	const { data: warrantyClaimServiceTasksData } = apiHooks.useGetWarrantyClaimServiceTasks({ params: { warrantyClaimId } });

	const warrantyClaimServiceTasks = useMemo(() => warrantyClaimServiceTasksData?._data ?? [], [warrantyClaimServiceTasksData?._data]);

	const productsNextStatus = useMemo(() => {
		if (warrantyClaimServiceTasks.length === 0) return null;

		const statusSet = new Set(
			filterUndefined(warrantyClaimServiceTasks.map((task) => task.serviceTaskType.afterWarrantyClaimProductTargetStatus)),
		);

		return AFTER_WARRANTY_CLAIM_PRODUCT_TARGET_STATUSES.find((status) => statusSet.has(status)) ?? null;
	}, [warrantyClaimServiceTasks]);

	return (
		<FormContext
			schema={z.object({ actionDescription: z.string(), productStatus: z.enum(PRODUCT_STATUS_NAMES) })}
			defaultValues={{
				actionDescription: '',
				productStatus: productsNextStatus ?? 'TO_TEST',
			}}
			onSubmit={async (data) => {
				if (disabled) return;

				updateWarrantyClaim(
					{
						status: 'CLOSED',
						actionDescription: data.actionDescription,
						warrantyClaimClosingParams: {
							productStatus: productsNextStatus ?? data.productStatus,
							...(data.productStatus === 'SERVICE' ? { shouldCreateServiceCase: true } : {}),
						},
					},
					{
						onSuccess: () => {
							toast.success(`Upraveno do stavu ${WarrantyClaimStatusMessage['CLOSED']}`);
							closeDialog();
							invalidateWarrantyClaimHistory();
							invalidateProductDefects();
							invalidateWarrantyClaim();
							data.productStatus === 'SERVICE' && invalidateServiceCases();
						},
					},
				);
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<TextControl control={control} name="actionDescription" label="Poznámka" />
					{!productsNextStatus && (
						<ComboboxControl
							control={control}
							name="productStatus"
							items={AFTER_WARRANTY_CLAIM_PRODUCT_TARGET_STATUSES.map((status) => ({
								value: status,
								label: ProductStatusMessage[status],
							}))}
							label="Cílový stav produktu"
							hideSearch={true}
						/>
					)}

					<Button disabled={disabled} isLoading={isLoading}>
						Uzavřít reklamace
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
