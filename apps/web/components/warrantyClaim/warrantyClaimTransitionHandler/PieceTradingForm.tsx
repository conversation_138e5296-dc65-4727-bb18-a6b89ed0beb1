import { <PERSON>ton, FormContext, InputControl, Stack, TextControl, toast, useDialog } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { z } from 'zod';
import { useGetWarrantyClaimTransitionInvalidations } from '../../../hooks/useWarrantyClaimStatusTransitionMap';
import { type TransitionFormProps } from './DefaultStatusTransitionForm';

const schema = z.object({
	actionDescription: z.string(),
	productSN: z.string().min(1, 'Zadejte platné sériové číslo'),
});

export const PieceTradingForm: FC<TransitionFormProps> = (props) => {
	const isBulkUpdate = Array.isArray(props.warrantyClaimId);

	if (isBulkUpdate) return <p>Does not support bulk actions</p>;

	return <Content {...props} />;
};

const Content: FC<TransitionFormProps> = ({ warrantyClaimId, productDefects }) => {
	const { closeDialog } = useDialog();
	const { invalidate, invalidateProductDefects } = useGetWarrantyClaimTransitionInvalidations();

	const { data: serviceTaskTypes } = apiHooks.useGetServiceTaskTypes({ queries: { filter: { name: { eq: 'Výměna kusu' } } } });
	const serviceTaskType = serviceTaskTypes?._data?.[0];
	const { mutateAsync: updateWarrantyClaim, isLoading: isLoadingWarrantyClaim } = apiHooks.useUpdateWarrantyClaim({
		params: { warrantyClaimId: warrantyClaimId as string },
	});
	const { invalidate: invalidateServiceCases } = apiHooks.useGetServiceCases({}, { enabled: false });
	const { mutateAsync: createServiceTask, isLoading: isLoadingServiceTask } = apiHooks.useCreateServiceTask();

	const onSubmit = async (data: z.infer<typeof schema>) => {
		const { productSN, actionDescription } = data;

		if (serviceTaskType) {
			await createServiceTask(
				{
					note: data.actionDescription,
					serviceTaskTypeId: serviceTaskType.id,
					productDefectsIds: productDefects.map(({ id }) => id),
					status: 'CLOSED',
					resolutionScope: 'full',
				},
				{
					onSuccess: () => {
						invalidateProductDefects();
					},
				},
			);

			await updateWarrantyClaim(
				{
					status: 'TRADED_PIECE',
					actionDescription: actionDescription ?? '',
					productSN: productSN,
				},
				{
					onSuccess: () => {
						invalidateServiceCases();
						invalidate();
						toast.success('Zaktualizováno');
						closeDialog();
					},
				},
			);
		}
	};

	return (
		<FormContext
			schema={schema}
			defaultValues={{
				actionDescription: '',
				productSN: '',
			}}
			onSubmit={onSubmit}
		>
			{(control) => (
				<Stack gap={4}>
					<InputControl control={control} name="productSN" label="Nové sériové číslo" />

					<TextControl control={control} name="actionDescription" label="Poznámka" />

					<Button className="w-full" variant="default" type="submit" isLoading={isLoadingWarrantyClaim || isLoadingServiceTask}>
						Uložit
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
