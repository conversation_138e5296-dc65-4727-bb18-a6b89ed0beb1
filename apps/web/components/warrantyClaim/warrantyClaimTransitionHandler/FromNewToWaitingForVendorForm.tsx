import { <PERSON><PERSON>, FormContext, <PERSON>ack, TextControl, useDialog } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { z } from 'zod';
import { useGetWarrantyClaimTransitionInvalidations } from '../../../hooks/useWarrantyClaimStatusTransitionMap';
import type { TransitionFormProps } from './DefaultStatusTransitionForm';

export const FromNewToWaitingForVendorForm: FC<TransitionFormProps> = ({ warrantyClaimId, selectedTargetTransition }) => {
	const isBulkUpdate = Array.isArray(warrantyClaimId);
	const { closeDialog } = useDialog();
	const { invalidate } = useGetWarrantyClaimTransitionInvalidations();
	const { mutate, isLoading } = apiHooks.useUpdateWarrantyClaim({
		params: { warrantyClaimId: Array.isArray(warrantyClaimId) ? warrantyClaimId[0] : warrantyClaimId },
	});
	const { mutate: bulkUpdateWarrantyClaims, isLoading: isBulkLoading } = apiHooks.useBulkUpdateWarrantyClaims();

	return (
		<FormContext
			schema={z.object({
				actionDescription: z.string().min(1, 'Vyplňte prosím poznámku'),
			})}
			defaultValues={{
				actionDescription: '',
			}}
			onSubmit={(data) => {
				const onSuccess = () => {
					invalidate();
					closeDialog();
				};

				if (isBulkUpdate) {
					bulkUpdateWarrantyClaims(
						{ ids: warrantyClaimId, data: { ...data, status: selectedTargetTransition.status } },
						{ onSuccess },
					);
				} else {
					mutate({ ...data, status: selectedTargetTransition.status }, { onSuccess });
				}
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<TextControl control={control} name="actionDescription" label="Nahlášení problému dodavateli" />

					<Button className="w-full" variant="default" type="submit" isLoading={isLoading || isBulkLoading}>
						Uložit
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
