import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, TextControl, toast, useDialog } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { z } from 'zod';
import { useGetWarrantyClaimTransitionInvalidations } from '../../../hooks/useWarrantyClaimStatusTransitionMap';
import { type TransitionFormProps } from './DefaultStatusTransitionForm';

const schema = z.object({
	actionDescription: z.string(),
});

export const ResolveByVendorForm: FC<TransitionFormProps> = (props) => {
	const isBulkUpdate = Array.isArray(props.warrantyClaimId);

	if (isBulkUpdate) return <p>Does not support bulk actions</p>;

	return <Content {...props} />;
};

const Content: FC<TransitionFormProps> = ({ warrantyClaimId }) => {
	const { closeDialog } = useDialog();
	const { invalidate } = useGetWarrantyClaimTransitionInvalidations();

	const { mutateAsync: updateWarrantyClaim, isLoading: isLoadingWarrantyClaim } = apiHooks.useUpdateWarrantyClaim({
		params: { warrantyClaimId: warrantyClaimId as string },
	});
	const { invalidate: invalidateServiceCases } = apiHooks.useGetServiceCases({}, { enabled: false });

	const onSubmit = async (data: z.infer<typeof schema>) => {
		const { actionDescription } = data;

		await updateWarrantyClaim(
			{
				status: 'RESOLVED_BY_VENDOR',
				actionDescription: actionDescription ?? '',
			},
			{
				onSuccess: () => {
					invalidateServiceCases();
					invalidate();
					toast.success('Zaktualizováno');
					closeDialog();
				},
			},
		);
	};

	return (
		<FormContext
			schema={schema}
			defaultValues={{
				actionDescription: '',
			}}
			onSubmit={onSubmit}
		>
			{(control) => (
				<Stack gap={4}>
					<TextControl control={control} name="actionDescription" label="Poznámka" />

					<Button className="w-full" variant="default" type="submit" isLoading={isLoadingWarrantyClaim}>
						Uložit
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
