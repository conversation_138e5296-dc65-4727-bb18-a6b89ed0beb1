import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Text<PERSON>ontrol, toast, useDialog } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { z } from 'zod';
import { useGetWarrantyClaimTransitionInvalidations } from '../../../hooks/useWarrantyClaimStatusTransitionMap';
import { type TransitionFormProps } from './DefaultStatusTransitionForm';

const schema = z.object({
	actionDescription: z.string(),
});

export const SentToVendorForm: FC<TransitionFormProps> = ({ selectedTargetTransition, warrantyClaimId }) => {
	const isBulkUpdate = Array.isArray(warrantyClaimId);

	const { invalidate } = useGetWarrantyClaimTransitionInvalidations();

	const { mutateAsync: updateWarrantyClaim, isLoading: isLoadingWarrantyClaim } = apiHooks.useUpdateWarrantyClaim({
		params: { warrantyClaimId: isBulkUpdate ? warrantyClaimId[0] : warrantyClaimId },
	});
	const { mutate: bulkUpdateWarrantyClaims, isLoading: isBulkLoading } = apiHooks.useBulkUpdateWarrantyClaims();
	const { invalidate: invalidateServiceCases } = apiHooks.useGetServiceCases({}, { enabled: false });

	const { closeDialog } = useDialog();

	const onSuccess = () => {
		invalidateServiceCases();
		invalidate();
		toast.success('Zaktualizováno');
		closeDialog();
	};

	const onSubmit = async (data: z.infer<typeof schema>) => {
		const { actionDescription } = data;

		const dataToSend = {
			status: selectedTargetTransition.status,
			actionDescription: actionDescription ?? '',
		};

		if (isBulkUpdate) {
			bulkUpdateWarrantyClaims(
				{ ids: warrantyClaimId, data: dataToSend },
				{
					onSuccess,
				},
			);
		} else {
			updateWarrantyClaim(dataToSend, {
				onSuccess,
			});
		}
	};

	return (
		<FormContext
			schema={schema}
			defaultValues={{
				actionDescription: '',
			}}
			onSubmit={onSubmit}
		>
			{(control) => (
				<Stack gap={4}>
					<TextControl control={control} name="actionDescription" label="Poznámka (tracking kód)" />

					<Button className="w-full" variant="default" type="submit" isLoading={isLoadingWarrantyClaim || isBulkLoading}>
						Uložit
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
