import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { ProductDefectSolverDialog } from '../productDefect/ProductDefectSolverDialog';
import { ServiceCaseCreationDialog } from './ServiceCaseCreationDialog';
import { WarrantyClaimCloseDialog } from './WarrantyClaimCloseDialog';

type Props = {
	productDefects: ApiBody<'getAllProductDefects'>;
	warrantyClaimId: string;
	disabled: boolean;
};

export const WarrantyClaimDefectSolver: FC<Props> = ({ productDefects, warrantyClaimId, disabled }) => {
	const unsolvedDefects = productDefects.filter((defect) => defect.serviceTask?.status !== 'CLOSED');
	const unsolvedVendorTasks = productDefects.filter((defect) => defect.vendorTaskId == null);

	if (unsolvedVendorTasks.length > 0) {
		return <ProductDefectSolverDialog unsolvedDefects={unsolvedDefects} disabled={disabled} source="WARRANTY_CLAIM" />;
	}

	if (unsolvedDefects.length > 0) {
		return <ServiceCaseCreationDialog warrantyClaimId={warrantyClaimId} productDefects={productDefects} disabled={disabled} />;
	}

	return <WarrantyClaimCloseDialog warrantyClaimId={warrantyClaimId} disabled={disabled} />;
};
