import { But<PERSON>, <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { WithServiceCaseForm } from './warrantyClaimTransitionHandler/WithServiceCaseForm';

type Props = {
	warrantyClaimId: string;
	productDefects: ApiBody<'getAllProductDefects'>;
	disabled: boolean;
};

export const ServiceCaseCreationDialog: FC<Props> = ({ warrantyClaimId, productDefects, disabled }) => {
	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button disabled={disabled}>Vyřešit pomocí interního servisu</Button>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Vyřešit pomocí interního servisu</DialogTitle>
				</DialogHeader>
				<WithServiceCaseForm
					warrantyClaimId={warrantyClaimId}
					productDefects={productDefects}
					selectedTargetTransition={{ status: 'CLOSED', label: 'Vyřešit pomocí interního servisu' }}
					productId={productDefects[0].productId}
				/>
			</DialogContent>
		</Dialog>
	);
};
