import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { TrackingCard, type WithTrackingCode } from '../TrackingCard';

type Props = {
	warrantyClaim: ApiBody<'getWarrantyClaim'>;
	disabled: boolean;
	invalidate: () => void;
};

export const WarrantyClaimTrackingCard: FC<Props> = ({ warrantyClaim, disabled, invalidate }) => {
	const { mutate: updateWarrantyClaim, isLoading } = apiHooks.useUpdateWarrantyClaim({ params: { warrantyClaimId: warrantyClaim.id } });

	const onSubmit = (data: WithTrackingCode) => {
		if (disabled) return;

		updateWarrantyClaim(data, {
			onSuccess: () => {
				invalidate();
			},
		});
	};

	return <TrackingCard entity={warrantyClaim} onSubmit={onSubmit} isLoading={isLoading} disabled={disabled} withVendorRMAIdentifier />;
};
