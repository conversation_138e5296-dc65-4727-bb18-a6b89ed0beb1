import {
	But<PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON>Close,
	DialogContent,
	DialogDescription,
	<PERSON><PERSON><PERSON>ooter,
	<PERSON>alogHeader,
	DialogTitle,
} from '@pocitarna-nx-2023/ui';
import { createContext, type FC, type PropsWithChildren, useCallback, useContext, useRef, useState } from 'react';

type ConfirmContextValue = (message: string) => Promise<unknown>;

const ConfirmContext = createContext<ConfirmContextValue>(async (message) => void message);
export const useConfirm = () => useContext(ConfirmContext);

export const ConfirmProvider: FC<PropsWithChildren> = ({ children }) => {
	const [message, setMessage] = useState<string | null>(null);
	const awaitingPromiseRef = useRef<{ resolve: (value?: unknown) => void; reject: () => void } | null>(null);

	const openConfirmDialog = useCallback((message: string) => {
		setMessage(message);
		return new Promise((resolve, reject) => {
			awaitingPromiseRef.current = { resolve, reject };
		});
	}, []);

	const handleConfirm = useCallback(() => {
		if (awaitingPromiseRef.current) {
			awaitingPromiseRef.current.resolve();
		}
		setMessage(null);
	}, []);

	const handleCancel = useCallback(() => {
		if (awaitingPromiseRef.current) {
			awaitingPromiseRef.current.reject();
		}
		setMessage(null);
	}, []);

	return (
		<>
			<ConfirmContext.Provider value={openConfirmDialog}>{children}</ConfirmContext.Provider>

			<Dialog open={!!message} onOpenChange={handleCancel}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Odcházíte ze stránky</DialogTitle>
					</DialogHeader>
					<DialogDescription>{message}</DialogDescription>
					<DialogFooter>
						<DialogClose asChild>
							<Button variant="ghost">Zpět</Button>
						</DialogClose>
						<Button autoFocus onClick={handleConfirm}>
							Pokračovat
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
};
