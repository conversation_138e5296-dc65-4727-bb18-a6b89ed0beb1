import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ack, TextControl, useDialog } from '@pocitarna-nx-2023/ui';
import { noteCreation } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';

type Props = {
	invalidate: () => void;
	entityId: string;
	entityType: 'warrantyClaim' | 'serviceCase' | 'customerClaim';
	disabled?: boolean;
};

export const NoteCreation: FC<Props> = ({ invalidate, entityId, entityType, disabled }) => {
	const { closeDialog } = useDialog();
	const { mutate, isLoading } = apiHooks.useCreateNote(undefined);

	return (
		<FormContext
			schema={noteCreation}
			defaultValues={{
				content: '',
				warrantyClaim: entityType === 'warrantyClaim' ? { id: entityId } : null,
				serviceCase: entityType === 'serviceCase' ? { id: entityId } : null,
				customerClaim: entityType === 'customerClaim' ? { id: entityId } : null,
			}}
			onSubmit={(data) => {
				mutate(data, {
					onSuccess: () => {
						invalidate();
						closeDialog();
					},
				});
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<TextControl control={control} name="content" label="Poznámka" disabled={disabled} />

					<DialogFooter>
						<Button className="w-full" variant="default" type="submit" disabled={disabled} isLoading={isLoading}>
							Vytvořit
						</Button>
					</DialogFooter>
				</Stack>
			)}
		</FormContext>
	);
};
