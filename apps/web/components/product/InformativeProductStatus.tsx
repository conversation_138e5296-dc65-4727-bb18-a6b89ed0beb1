import { ProductStatusMessage, ServiceCaseStatusMessage, WarrantyClaimStatusMessage } from '@pocitarna-nx-2023/config';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { type FC } from 'react';

type Props = {
	product: ApiBody<'getAllProducts'>[number];
};

export const InformativeProductStatus: FC<Props> = ({ product }) => {
	const isInOrderRelevantStatus = ['RESERVED', 'SOLD'].includes(product.status);
	const isInServiceCase = product.status === 'SERVICE';
	const isInWarrantyClaim = product.status === 'WARRANTY_CLAIM';

	const { data: orderData } = apiHooks.useGetProductOrder({ params: { productId: product.id } }, { enabled: isInOrderRelevantStatus });

	const { data: serviceCaseData } = apiHooks.useGetProductServiceCase(
		{ params: { productId: product.id } },
		{ enabled: isInServiceCase },
	);

	const { data: warrantyClaimData } = apiHooks.useGetProductWarrantyClaim(
		{ params: { productId: product.id } },
		{ enabled: isInWarrantyClaim },
	);

	const order = orderData?._data;
	const serviceCase = serviceCaseData?._data;
	const warrantyClaim = warrantyClaimData?._data;

	if (isInOrderRelevantStatus && order) {
		return (
			<>
				<Link href={`/order/${order.id}`} className="text-link">
					{ProductStatusMessage[product.status]}
				</Link>
				<br />
				{order.code}
			</>
		);
	}

	if (isInServiceCase && serviceCase) {
		return (
			<>
				<Link href={`/service/${serviceCase.id}`} className="text-link">
					{ProductStatusMessage[product.status]}
				</Link>
				<br />
				{ServiceCaseStatusMessage[serviceCase.status]}
			</>
		);
	}

	if (isInWarrantyClaim && warrantyClaim) {
		return (
			<>
				<Link href={`/warranty-claim/${warrantyClaim.id}`} className="text-link">
					{ProductStatusMessage[product.status]}
				</Link>
				<br />
				{WarrantyClaimStatusMessage[warrantyClaim.status]}
			</>
		);
	}

	return ProductStatusMessage[product.status];
};
