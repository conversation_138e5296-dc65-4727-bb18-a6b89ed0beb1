import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useState } from 'react';
import { LabelPrint } from '../printer/LabelPrint';

type Props = {
	product: ApiBody<'getProduct'>;
};

export const ProductLabelPrintSn: FC<Props> = ({ product }) => {
	const [shouldPrint, setShouldPrint] = useState(false);

	const { data: printLabelsData, isFetching: isLoadingLabels } = apiHooks.useGetPrintLabelsProductSn(
		{
			params: { productId: product.id },
		},
		{
			enabled: shouldPrint,
		},
	);

	return (
		<LabelPrint
			buttonLabel="Vytisknout SN produktu"
			printLabels={printLabelsData?._data}
			printerLocation="TESTING"
			isLoadingLabels={isLoadingLabels}
			handleClick={() => setShouldPrint(true)}
		/>
	);
};
