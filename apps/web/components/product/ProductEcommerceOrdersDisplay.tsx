import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { ParamItem } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { type FC } from 'react';
import { CreationDetails } from '../CreationDetails';

type Props = {
	product: ApiBody<'getProduct'>;
};

export const ProductEcommerceOrdersDisplay: FC<Props> = ({ product }) => {
	const { data: ecommerceOrdersData } = apiHooks.useGetEcommerceOrders({
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER, filter: { 'ecommerceOrderItem.productId': { eq: product.id } } },
	});
	const ecommerceOrders = ecommerceOrdersData?._data ?? [];
	if (ecommerceOrders.length === 0) return null;

	return (
		<>
			{ecommerceOrders.map((order) => (
				<ParamItem label="Objednávka" align="center" key={order.id}>
					<CreationDetails date={order.placedAt}>
						<Link className="mr-2 text-link" href={`/order/${order.id}`}>
							{order.code}
						</Link>
					</CreationDetails>
				</ParamItem>
			))}
		</>
	);
};
