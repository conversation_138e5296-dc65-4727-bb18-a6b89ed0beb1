import { NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { formatEnvelopeCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import Link from 'next/link';
import { type FC } from 'react';
import { CreationDetails } from '../CreationDetails';

type Props = {
	product: ApiBody<'getProduct'>;
};

export const EnvelopeAssignmentRecord: FC<Props> = ({ product }) => {
	if (!product.productEnvelope || !product.productCategory) return NOT_AVAILABLE;

	return (
		<CreationDetails date={product.productEnvelopeAssignedAt} user={product.productEnvelopeAssignedBy}>
			<Link className="mr-2 text-link" href={`/product/envelope/${product.productEnvelope.id}`}>
				{formatEnvelopeCode(product.productCategory.codePrefix)(product.productEnvelope.code)}
			</Link>
		</CreationDetails>
	);
};
