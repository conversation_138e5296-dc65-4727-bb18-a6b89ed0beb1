import { WARRANTY_TYPES, type WarrantyType, WarrantyTypesLabels } from '@pocitarna-nx-2023/config';
import {
	Button,
	ComboboxControl,
	DatepickerControl,
	DeleteDialog,
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
	FormContext,
	Icon,
	Stack,
	toast,
	useDialog,
} from '@pocitarna-nx-2023/ui';
import { type Warranty } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { z } from 'zod';
import { WarrantyDeadlineDisplay } from './WarrantyDeadlineDisplay';

type Props = {
	productId: string;
	warrantyType: WarrantyType;
	warranty?: Warranty;
	invalidateProductWarranties: () => void;
};

export const ProductWarrantyManagement: FC<
	Props & {
		loading: boolean;
	}
> = ({ productId, warranty, warrantyType, loading, invalidateProductWarranties }) => {
	const isCreation = warranty == null;
	const { closeDialog } = useDialog();

	const { mutate: deleteProductWarranty, isLoading } = apiHooks.useDeleteProductWarranty({
		params: { productId, warrantyId: warranty?.id ?? '' },
	});

	const onWarrantyDelete = () => {
		deleteProductWarranty(undefined, {
			onSuccess: () => {
				toast.success('Záruka byla smazána.');
				invalidateProductWarranties();
				closeDialog();
			},
		});
	};

	return (
		<div className="flex w-full items-center justify-between -mt-1.5">
			<WarrantyDeadlineDisplay warranty={warranty} loading={loading} />
			<div className="flex gap-2 ml-auto">
				{isCreation ? (
					<CreateOrEditProductWarranty
						productId={productId}
						warrantyType={warrantyType}
						invalidateProductWarranties={invalidateProductWarranties}
					/>
				) : (
					<>
						<CreateOrEditProductWarranty
							warranty={warranty}
							warrantyType={warrantyType}
							productId={productId}
							invalidateProductWarranties={invalidateProductWarranties}
						/>
						{/* Currently, in our schema for /product/:productId/warranties, the buy warranty is required, hence I'm disallowing deletion */}
						{warrantyType !== 'BUY' && (
							<DeleteDialog
								title="Smazat záruku"
								description="Opravdu si přejete odstranit položku?"
								triggerVariant="icon"
								onDelete={onWarrantyDelete}
								isLoading={isLoading}
							/>
						)}
					</>
				)}
			</div>
		</div>
	);
};

export const CreateOrEditProductWarranty: FC<Props> = (props) => {
	const isCreation = props.warranty == null;
	const verb = isCreation ? 'Vytvořit' : 'Upravit';

	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button size="sm" width="icon" variant="outline" className="ml-auto">
					<Icon name={isCreation ? 'plus' : 'pen'} />
				</Button>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>{verb} záruku</DialogTitle>
				</DialogHeader>
				<ProductWarrantyForm {...props} />
			</DialogContent>
		</Dialog>
	);
};

const schema = z.object({
	type: z.enum(WARRANTY_TYPES),
	expiredAt: z.coerce.date(),
});

const ProductWarrantyForm: FC<Props> = ({ productId, warrantyType, warranty, invalidateProductWarranties }) => {
	const isCreation = warranty == null;
	const verb = isCreation ? 'Vytvořit' : 'Upravit';
	const { closeDialog } = useDialog();

	const { mutate: updateProductWarranty, isLoading: editLoading } = apiHooks.useUpdateProductWarranty({
		params: { productId, warrantyId: warranty?.id ?? '' },
	});

	const { mutate: createProductWarranty, isLoading: creationLoading } = apiHooks.useCreateProductWarranty({
		params: { productId },
	});

	const onSuccess = () => {
		const message = isCreation ? 'Záruka vytvořena' : 'Záruka aktualizována';
		invalidateProductWarranties();
		toast.success(message);
		closeDialog();
	};

	return (
		<FormContext
			schema={schema}
			defaultValues={{
				type: warrantyType,
				expiredAt: warranty?.expiredAt ?? new Date(),
			}}
			onSubmit={(data) => {
				if (warranty) {
					updateProductWarranty(data, { onSuccess });
				} else {
					createProductWarranty(data, { onSuccess });
				}
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<ComboboxControl
						control={control}
						name="type"
						items={[{ value: warrantyType, label: WarrantyTypesLabels[warrantyType] }]}
						label="Typ"
						hideSearch={true}
						disabled
					/>
					<DatepickerControl control={control} name="expiredAt" label="Datum expirace" />

					<Button className="w-full" variant="default" type="submit" isLoading={editLoading || creationLoading}>
						{verb}
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
