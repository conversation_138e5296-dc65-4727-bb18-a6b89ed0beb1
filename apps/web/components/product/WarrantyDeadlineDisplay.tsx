import { LOADING_MESSAGE, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { formatDate } from '@pocitarna-nx-2023/utils';
import { type Warranty } from '@pocitarna-nx-2023/zodios';
import { isBefore } from 'date-fns';
import { type FC } from 'react';

type Props = {
	warranty: Warranty | undefined;
	loading?: boolean;
};

export const WarrantyDeadlineDisplay: FC<Props> = ({ warranty, loading }) => {
	if (loading) return LOADING_MESSAGE;

	if (!warranty) return NOT_AVAILABLE;

	const className = isBefore(warranty.expiredAt, new Date()) ? 'text-destructive' : undefined;

	return <span className={className}>{formatDate(warranty.expiredAt)}</span>;
};
