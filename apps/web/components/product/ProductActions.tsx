import { <PERSON><PERSON>, <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, Stack } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { useUserHasScope } from '../../hooks/useUserHasScope';
import { ProductEnvelopeLabelPrint } from '../productEnvelope/ProductEnvelopeLabelPrint';
import { CustomerClaimDialog } from './CustomerClaimDialog';
import { EditProductDialog } from './EditProductDialog';
import { ProductLabelPrint } from './ProductLabelPrint';
import { ProductLabelPrintSn } from './ProductLabelPrintSn';
import { ProductServiceCaseDialog } from './ProductServiceCaseDialog';
import { ProductWarrantyClaimDialog } from './ProductWarrantyClaimDialog';

type Props = {
	product: ApiBody<'getProduct'>;
};

export const ProductActions: FC<Props> = ({ product }) => {
	const isAdmin = useUserHasScope('admin');
	const isServiceWrite = useUserHasScope('serviceWrite');
	const isWarrantyClaimWrite = useUserHasScope('warrantyClaimWrite');
	const isCustomerClaimWrite = useUserHasScope('customerClaimWrite');
	const isTestLead = useUserHasScope('productTestLead');

	return (
		<Card sticky>
			<CardHeader>
				<CardTitle>Akce</CardTitle>
			</CardHeader>
			<CardContent>
				<Stack gap={4}>
					<ProductLabelPrint product={product} />
					<ProductLabelPrintSn product={product} />
					{product.productEnvelope ? (
						<ProductEnvelopeLabelPrint product={product} productEnvelope={product.productEnvelope} />
					) : null}
					{isAdmin && <EditProductDialog product={product} />}
					{(isServiceWrite || isWarrantyClaimWrite || isCustomerClaimWrite) && <Button>Vrátit do testu</Button>}
					{(isServiceWrite || isTestLead) && <ProductServiceCaseDialog product={product} />}
					{(isWarrantyClaimWrite || isTestLead) && <ProductWarrantyClaimDialog product={product} />}
					{isCustomerClaimWrite && ['SOLD'].includes(product.status) && <CustomerClaimDialog product={product} />}
				</Stack>
			</CardContent>
		</Card>
	);
};
