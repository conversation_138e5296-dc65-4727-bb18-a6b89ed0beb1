import { MAX_POSITIVE_INTEGER, TEN_SECONDS } from '@pocitarna-nx-2023/config';
import { Media } from '@pocitarna-nx-2023/ui';
import { formatProductCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, type ReactNode, useMemo } from 'react';
import { useUserHasScope } from '../../hooks/useUserHasScope';
import { FileUploader } from '../FileUploader';

type Props = {
	productCode: ApiBody<'getProductCode'>;
	description?: ReactNode;
	disabled?: boolean;
};

export const ProductCodeFilesCard: FC<Props> = ({ productCode, description, disabled = false }) => {
	const isAdmin = useUserHasScope('admin');
	const isTestLead = useUserHasScope('productTestLead');

	const { data: productCodeFilesData, invalidate: invalidateProductCodeFiles } = apiHooks.useGetProductCodeFiles(
		{ params: { productCodeId: productCode.id }, queries: { page: 1, limit: MAX_POSITIVE_INTEGER } },
		{ refetchInterval: TEN_SECONDS },
	);
	const productCodeFiles = useMemo(
		() => (productCodeFilesData?._data ?? []).map(({ file }: ApiBody<'getProductCodeFiles'>[number]) => file),
		[productCodeFilesData?._data],
	);

	const { mutate: updateSequence } = apiHooks.useUpdateProductCodeFileSequence(
		{ params: { productCodeId: productCode.id } },
		{ onSuccess: () => invalidateProductCodeFiles() },
	);
	const { mutate: deleteFiles, isLoading: isDeletingFiles } = apiHooks.useDeleteFiles(undefined, {
		onSuccess: () => invalidateProductCodeFiles(),
	});
	const { mutate: rotateImageFile, isLoading: isRotatingFile } = apiHooks.useRotateImageFile(undefined, {
		onSuccess: () => invalidateProductCodeFiles(),
	});
	const { mutateAsync: addFilesToProductCode, isLoading: isAddingFiles } = apiHooks.useAddFilesToProductCode(
		{ params: { productCodeId: productCode.id } },
		{ onSuccess: () => invalidateProductCodeFiles() },
	);

	return (
		<FileUploader
			pageType="detail"
			inputName="files"
			entityCode={formatProductCode(productCode)}
			entityId={productCode.id}
			entityType="productCode"
			isAddingFiles={isAddingFiles}
			addFilesToEntity={addFilesToProductCode}
			description={description}
			disabled={disabled}
		>
			<Media
				files={productCodeFiles}
				moveTo={disabled ? undefined : (file, sequence) => updateSequence({ fileId: file.id, sequence })}
				onDelete={disabled && (!isAdmin || !isTestLead) ? undefined : (file) => deleteFiles({ ids: [file.id] })}
				onRotation={disabled ? undefined : (file, rotation) => rotateImageFile({ fileId: file.id, rotation })}
				isLoading={isAddingFiles || isDeletingFiles || isRotatingFile}
			/>
		</FileUploader>
	);
};
