import { CustomerClaimStatusMessage, MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { ParamItem } from '@pocitarna-nx-2023/ui';
import { filterUndefined, formatCustomerClaimCode, uniques } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { type FC } from 'react';
import { CreationDetails } from '../CreationDetails';

type Props = {
	defects: ApiBody<'getProductDefects'>;
};

export const ProductCustomerClaimsDisplay: FC<Props> = ({ defects }) => {
	const customerClaimsIds = filterUndefined(uniques(defects.map((defect) => defect.customerClaimId)));

	const { data: customerClaimsData } = apiHooks.useGetCustomerClaims(
		{ queries: { page: 1, limit: MAX_POSITIVE_INTEGER, filter: { id: { eq: customerClaimsIds } } } },
		{ enabled: customerClaimsIds.length > 0 },
	);

	const customerClaims = customerClaimsData?._data ?? [];

	if (customerClaims.length === 0) return null;

	return (
		<>
			{customerClaims.map((customerClaim) => {
				const { status } = customerClaim;
				return (
					<ParamItem label={`Zák. reklamace (${CustomerClaimStatusMessage[status]})`} align="center" key={customerClaim.id}>
						<CreationDetails date={customerClaim.createdAt} user={customerClaim.createdBy}>
							<Link className="mr-2 text-link" href={`/customer-claim/${customerClaim.id}`}>
								{formatCustomerClaimCode(customerClaim.code)}
							</Link>
						</CreationDetails>
					</ParamItem>
				);
			})}
		</>
	);
};
