import { Button, Icon } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';

type Props = {
	productId: string;
	onDone?: () => void;
	disabled?: boolean;
};

export const ReturnProductToTestButton: FC<Props> = ({ productId, onDone, disabled = false }) => {
	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId } }, { enabled: false });
	const { mutate: updateProduct, isLoading: isUpdatingProduct } = apiHooks.useUpdateProduct(
		{ params: { productId } },
		{
			onSuccess: () => {
				invalidateProduct();
				onDone?.();
			},
		},
	);
	const handleReturnToTest = () => {
		if (disabled) return;
		updateProduct({ status: 'TO_TEST' });
	};

	return (
		<Button variant="destructive" type="button" onClick={handleReturnToTest} isLoading={isUpdatingProduct} disabled={disabled}>
			<Icon name="rotate-left" />
			Vrátit na test
		</Button>
	);
};
