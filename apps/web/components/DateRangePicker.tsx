import { Button, Calendar, cn, Icon, Popover, <PERSON>overContent, PopoverTrigger } from '@pocitarna-nx-2023/ui';
import { formatDate } from '@pocitarna-nx-2023/utils';
import {
	endOfDay,
	endOfMonth,
	endOfWeek,
	endOfYear,
	isSameDay,
	startOfDay,
	startOfMonth,
	startOfWeek,
	startOfYear,
	subMonths,
	subWeeks,
	subYears,
} from 'date-fns';
import { type FC, type HTMLAttributes, useState } from 'react';
import type { DateRange } from 'react-day-picker';

type Props = {
	range: DateRange | undefined;
	setRange: (value: DateRange | undefined) => void;
} & HTMLAttributes<HTMLDivElement>;

export const DateRangePicker: FC<Props> = ({ className, range, setRange }) => {
	const [open, setOpen] = useState(false);

	return (
		<div className={cn('grid gap-2', className)}>
			<Popover open={open} onOpenChange={setOpen}>
				<PopoverTrigger asChild>
					<Button
						id="date"
						variant={'outline'}
						className={cn('justify-start text-left font-normal', !range && 'text-muted-foreground')}
					>
						<Icon name="calendar" className="ml-auto h-3 w-3 opacity-50" />
						{range?.from ? (
							range.to ? (
								<>
									{formatDate(range.from)} - {formatDate(range.to)}
								</>
							) : (
								formatDate(range.from)
							)
						) : (
							<span>Pick a date</span>
						)}
					</Button>
				</PopoverTrigger>
				<PopoverContent className="w-auto p-0 flex" align="end">
					<div className="border-r border-border p-2 space-y-2 w-[150px]">
						<h3 className="font-medium text-sm px-2 py-1">Předvolby</h3>
						<div className="space-y-1">
							<Button
								variant="ghost"
								size="sm"
								className="w-full justify-start font-normal text-xs"
								onClick={() => {
									setRange({
										from: startOfDay(new Date()),
										to: endOfDay(new Date()),
									});
									setOpen(false);
								}}
							>
								Dnes
							</Button>
							<Button
								variant="ghost"
								size="sm"
								className="w-full justify-start font-normal text-xs"
								onClick={() => {
									setRange({
										from: startOfWeek(new Date(), { weekStartsOn: 1 }),
										to: endOfWeek(new Date(), { weekStartsOn: 1 }),
									});
									setOpen(false);
								}}
							>
								Tento týden
							</Button>
							<Button
								variant="ghost"
								size="sm"
								className="w-full justify-start font-normal text-xs"
								onClick={() => {
									setRange({
										from: startOfWeek(subWeeks(new Date(), 1), { weekStartsOn: 1 }),
										to: endOfWeek(subWeeks(new Date(), 1), { weekStartsOn: 1 }),
									});
									setOpen(false);
								}}
							>
								Minulý týden
							</Button>
							<Button
								variant="ghost"
								size="sm"
								className="w-full justify-start font-normal text-xs"
								onClick={() => {
									{
										setRange({
											from: startOfMonth(new Date()),
											to: endOfMonth(new Date()),
										});
										setOpen(false);
									}
								}}
							>
								Tento měsíc
							</Button>
							<Button
								variant="ghost"
								size="sm"
								className="w-full justify-start font-normal text-xs"
								onClick={() => {
									setRange({
										from: startOfMonth(subMonths(new Date(), 1)),
										to: endOfMonth(subMonths(new Date(), 1)),
									});
									setOpen(false);
								}}
							>
								Minulý měsíc
							</Button>
							<Button
								variant="ghost"
								size="sm"
								className="w-full justify-start font-normal text-xs"
								onClick={() => {
									setRange({
										from: startOfMonth(subMonths(new Date(), 3)),
										to: endOfMonth(new Date()),
									});
									setOpen(false);
								}}
							>
								Poslední 3 měsíce
							</Button>
							<Button
								variant="ghost"
								size="sm"
								className="w-full justify-start font-normal text-xs"
								onClick={() => {
									setRange({
										from: startOfYear(new Date()),
										to: endOfYear(new Date()),
									});
									setOpen(false);
								}}
							>
								Tento rok
							</Button>
							<Button
								variant="ghost"
								size="sm"
								className="w-full justify-start font-normal text-xs"
								onClick={() => {
									setRange({
										from: startOfYear(subYears(new Date(), 1)),
										to: endOfYear(subYears(new Date(), 1)),
									});
									setOpen(false);
								}}
							>
								Minulý rok
							</Button>
						</div>
					</div>
					<div>
						<Calendar
							initialFocus
							mode="range"
							defaultMonth={range?.from}
							selected={range}
							onSelect={(date) => {
								if (date?.from && date.to && isSameDay(date.from, date.to)) {
									setRange({
										from: startOfDay(date.from),
										to: endOfDay(date.from),
									});
								} else {
									setRange(date);
								}
							}}
							numberOfMonths={2}
						/>
					</div>
				</PopoverContent>
			</Popover>
		</div>
	);
};
