import { LOADING_MESSAGE, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { formatEnvelopeCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { Default<PERSON>enderer, type History<PERSON>enderer } from './DefaultRenderer';

export const ProductEnvelopeRenderer: HistoryRenderer = ({ value, ...props }) => {
	const { data, isLoading } = apiHooks.useGetProductEnvelope(
		{ params: { productEnvelopeId: value?.toString() ?? '' } },
		{ enabled: !!value },
	);
	const code = formatEnvelopeCode(data?._data?.productCategory?.codePrefix)(data?._data?.code);

	return (
		<DefaultRenderer
			value={
				isLoading ? (
					LOADING_MESSAGE
				) : data?._data.id ? (
					<Link className="text-link" href={`/product/envelope/${data?._data.id}`}>
						{code}
					</Link>
				) : (
					NOT_AVAILABLE
				)
			}
			{...props}
		/>
	);
};
