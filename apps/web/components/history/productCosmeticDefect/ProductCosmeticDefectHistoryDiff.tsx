import { Badge, cn } from '@pocitarna-nx-2023/ui';
import { parsePeriod } from '@pocitarna-nx-2023/utils';
import { type relatedProductCosmeticDefect } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { type z } from 'zod';

type Props = {
	current?: z.infer<typeof relatedProductCosmeticDefect> & { period: string };
	previous?: z.infer<typeof relatedProductCosmeticDefect> & { period: string };
};

export const ProductCosmeticDefectHistoryDiff: FC<Props> = ({ current, previous }) => {
	if (!current) return null;
	const isDelete = previous && !!parsePeriod(current.period)[1];
	const isChange = previous && !parsePeriod(current.period)[1];
	const isNew = !previous;

	return (
		<div>
			{isDelete && 'Smazaná'}
			{isChange && 'Změněná'}
			{isNew && 'Nová'} kosmetická vada:{' '}
			<span className={cn(isDelete && 'text-destructive line-through')}>{current.cosmeticDefect.name}</span>
			<span className="ml-2">
				<Badge variant="info">{current.cosmeticArea.name}</Badge>
			</span>
		</div>
	);
};
