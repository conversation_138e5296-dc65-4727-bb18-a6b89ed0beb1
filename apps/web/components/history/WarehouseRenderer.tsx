import { LOADING_MESSAGE, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { DefaultRenderer, type <PERSON><PERSON>enderer } from './DefaultRenderer';

export const WarehouseRenderer: HistoryRenderer = ({ value, ...props }) => {
	const { data, isLoading } = apiHooks.useGetWarehouse({ params: { warehouseId: value?.toString() ?? '' } }, { enabled: !!value });
	const name = data?._data.name;

	return (
		<DefaultRenderer
			value={
				isLoading ? (
					LOADING_MESSAGE
				) : name ? (
					<Link className="text-link" href={`/admin/warehouse/${data?._data.id}`}>
						{name}
					</Link>
				) : (
					NOT_AVAILABLE
				)
			}
			{...props}
		/>
	);
};
