import { LOADING_MESSAGE, NOT_AVAILABLE, PUBLIC_CUSTOMER_USER_NAME } from '@pocitarna-nx-2023/config';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useMemo } from 'react';
import { DefaultRenderer, type <PERSON><PERSON>enderer } from './DefaultRenderer';

export const UserRenderer: HistoryRenderer = ({ value, ...props }) => {
	const { data, isLoading } = apiHooks.useGetUser({ params: { userId: value?.toString() ?? '' } }, { enabled: !!value });
	return <DefaultRenderer value={isLoading ? LOADING_MESSAGE : data?._data.name ?? NOT_AVAILABLE} {...props} />;
};

export const PublicUserRenderer: HistoryRenderer = ({ value, ...props }) => {
	const { data, isLoading } = apiHooks.useGetUser({ params: { userId: value?.toString() ?? '' } }, { enabled: !!value });
	const user = useMemo(() => data?._data, [data?._data]);
	const isPublicUser = user?.name === PUBLIC_CUSTOMER_USER_NAME;
	const userName = isPublicUser ? user.name : 'Počítařná';
	return <DefaultRenderer value={isLoading ? LOADING_MESSAGE : userName ?? NOT_AVAILABLE} {...props} />;
};
