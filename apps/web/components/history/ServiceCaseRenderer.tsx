import { LOADING_MESSAGE, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { formatServiceCaseCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { Default<PERSON>enderer, type <PERSON><PERSON>enderer } from './DefaultRenderer';

export const ServiceCaseRenderer: HistoryRenderer = ({ value, ...props }) => {
	const { data, isLoading } = apiHooks.useGetServiceCase({ params: { serviceCaseId: value?.toString() ?? '' } }, { enabled: !!value });
	return (
		<DefaultRenderer
			value={
				isLoading ? (
					LOADING_MESSAGE
				) : data?._data.code ? (
					<Link className="text-link" href={`/service/${data?._data.id}`}>
						{formatServiceCaseCode(data._data.code)}
					</Link>
				) : (
					NOT_AVAILABLE
				)
			}
			{...props}
		/>
	);
};
