import { cn } from '@pocitarna-nx-2023/ui';
import { buildFileUrl, parsePeriod } from '@pocitarna-nx-2023/utils';
import { type relatedFile } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { type z } from 'zod';

type Props = {
	current?: z.infer<typeof relatedFile> & { period: string };
	previous?: z.infer<typeof relatedFile> & { period: string };
};

export const FileHistoryDiff: FC<Props> = ({ current, previous }) => {
	if (!current) return null;
	const isDelete = previous && !!parsePeriod(current.period)[1];
	const isChange = previous && !parsePeriod(current.period)[1];
	const isNew = !previous;

	return (
		<p>
			{isDelete && 'Smazaný'}
			{isChange && 'Změněný'}
			{isNew && 'Nový'} soubor:{' '}
			<a
				className={cn(
					'text-link underline hover:no-underline',
					isDelete && 'text-destructive line-through',
					isNew && 'text-success',
				)}
				href={buildFileUrl(current.file.id)}
				target="_blank"
				rel="noreferrer noopener"
			>
				{current.file.name}
			</a>
		</p>
	);
};
