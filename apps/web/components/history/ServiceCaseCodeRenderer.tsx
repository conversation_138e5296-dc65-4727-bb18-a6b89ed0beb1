import { LOADING_MESSAGE, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { formatServiceCaseCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { DefaultRenderer, type <PERSON><PERSON>enderer } from './DefaultRenderer';

export const ServiceCaseCodeRenderer: HistoryRenderer = ({ value, ...props }) => {
	const { data, isLoading } = apiHooks.useGetServiceCaseCode(
		{ params: { serviceCaseCodeId: value?.toString() ?? '' } },
		{ enabled: !!value },
	);
	const code = formatServiceCaseCode(data?._data);
	return (
		<DefaultRenderer
			value={
				isLoading ? (
					LOADING_MESSAGE
				) : data?._data.id ? (
					<Link className="text-link" href={`/service-case/${data?._data.id}`}>
						{code}
					</Link>
				) : (
					NOT_AVAILABLE
				)
			}
			{...props}
		/>
	);
};
