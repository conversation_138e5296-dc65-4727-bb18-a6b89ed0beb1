import type { productEnvelope } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import type { z } from 'zod';
import { type KeyDiffMap, KeyDiffViewer } from '../KeyDiffViewer';
import { PriceRenderer } from '../PriceRenderer';
import { ProductCategoryRenderer } from '../ProductCategoryRenderer';
import { ProductEnvelopeCodeRenderer } from '../ProductEnvelopeCodeRenderer';
import { UserRenderer } from '../UserRenderer';

type Props = {
	current?: z.infer<typeof productEnvelope>;
	previous?: z.infer<typeof productEnvelope>;
};

const keyDiffMap: KeyDiffMap<z.infer<typeof productEnvelope>> = {
	createdAt: {
		label: 'Vytvořeno',
	},
	codeId: {
		label: 'Kód',
		renderer: ProductEnvelopeCodeRenderer,
	},
	name: {
		label: 'Název',
	},
	productCategoryId: {
		label: 'Kate<PERSON>ie',
		renderer: ProductCategoryRenderer,
	},
	salePrice: {
		label: 'Prodejní cena',
		renderer: PriceRenderer,
	},
	standardPrice: {
		label: 'Standardní cena',
		renderer: PriceRenderer,
	},
	pricedAt: {
		label: 'Cena nastavena',
	},
	pricedById: {
		label: 'Cenu nastavil',
		renderer: UserRenderer,
	},
};

export const ProductEnvelopeHistoryDiff: FC<Props> = (props) => {
	return <KeyDiffViewer keyDiffMap={keyDiffMap} {...props} />;
};
