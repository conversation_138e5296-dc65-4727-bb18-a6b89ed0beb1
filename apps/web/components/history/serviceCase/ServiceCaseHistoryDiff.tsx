import { ServiceCaseStatusMessage } from '@pocitarna-nx-2023/config';
import type { serviceCase } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import type { z } from 'zod';
import { PriorityDisplay } from '../../test/PriorityDisplay';
import { type KeyDiffMap, KeyDiffViewer } from '../KeyDiffViewer';
import { ServiceCaseCodeRenderer } from '../ServiceCaseCodeRenderer';
import { ServiceCenterRenderer } from '../ServiceCenterRenderer';
import { UserRenderer } from '../UserRenderer';
import { WarrantyClaimRenderer } from '../WarrantyClaimRenderer';

type Props = {
	current?: z.infer<typeof serviceCase>;
	previous?: z.infer<typeof serviceCase>;
};

const keyDiffMap: KeyDiffMap<z.infer<typeof serviceCase>> = {
	createdAt: {
		label: 'Vytvořeno',
	},
	createdById: {
		label: 'Vytvořil',
		renderer: UserRenderer,
	},
	codeId: {
		label: 'Kód',
		renderer: ServiceCaseCodeRenderer,
	},
	status: {
		label: 'Stav',
		formatter: (value) => ServiceCaseStatusMessage[value as keyof typeof ServiceCaseStatusMessage],
	},
	serviceCenterId: {
		label: 'Externí servis',
		renderer: ServiceCenterRenderer,
	},
	priority: {
		label: 'Priorita',
		formatter: (value) => <PriorityDisplay entity={{ priority: value as number }} />,
	},
	trackingCode: {
		label: 'Tracking kód',
	},
	sourceWarrantyClaimId: {
		label: 'Dodavatelské reklamace',
		renderer: WarrantyClaimRenderer,
	},
};

export const ServiceCaseHistoryDiff: FC<Props> = (props) => {
	return <KeyDiffViewer keyDiffMap={keyDiffMap} {...props} />;
};
