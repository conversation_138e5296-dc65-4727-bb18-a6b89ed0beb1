import { WarrantyBaselineMessage } from '@pocitarna-nx-2023/config';
import type { vendor } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import type { z } from 'zod';
import { type KeyDiffMap, KeyDiffViewer } from '../KeyDiffViewer';
import { NoteRenderer } from '../NoteRenderer';

type Props = {
	current?: z.infer<typeof vendor>;
	previous?: z.infer<typeof vendor>;
};

const keyDiffMap: KeyDiffMap<z.infer<typeof vendor>> = {
	createdAt: {
		label: 'Vytvořeno',
	},
	email: {
		label: 'Email',
		formatter: (value) => (
			<a className="text-link" href={`mailto:${value}`}>
				{value as string}
			</a>
		),
	},
	eta: {
		label: 'ETA',
	},
	name: {
		label: 'Název',
	},
	note: {
		label: 'Poz<PERSON>m<PERSON>',
		renderer: <PERSON><PERSON><PERSON><PERSON>,
	},
	phone: {
		label: 'Telefon',
		formatter: (value) => (
			<a className="text-link" href={`tel:${value}`}>
				{value as string}
			</a>
		),
	},
	warranty: {
		label: '<PERSON><PERSON><PERSON><PERSON>',
	},
	warrantyBaseline: {
		label: 'Záruka od',
		formatter: (value) => WarrantyBaselineMessage[value as keyof typeof WarrantyBaselineMessage],
	},
};

export const VendorHistoryDiff: FC<Props> = (props) => {
	return <KeyDiffViewer keyDiffMap={keyDiffMap} {...props} />;
};
