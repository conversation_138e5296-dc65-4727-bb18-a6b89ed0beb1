import { type FC, type <PERSON>psWithChildren } from 'react';
import { defaultF<PERSON>atter, Default<PERSON>enderer, type HistoryFormatter, type History<PERSON>enderer } from './DefaultRenderer';

type Props = {
	label: string;
	renderer?: HistoryRenderer;
	formatter?: HistoryFormatter;
	previous?: unknown;
	previousEntity?: unknown;
	current?: unknown;
	currentEntity?: unknown;
};

export const DiffViewer: FC<PropsWithChildren<Props>> = ({
	label,
	renderer: Renderer = DefaultRenderer,
	formatter = defaultFormatter,
	previous,
	previousEntity,
	current,
	currentEntity,
}) => {
	const Label = () => (
		<>
			<span className="font-medium">{label}</span>:{' '}
		</>
	);
	const New = () => <Renderer value={current} entity={currentEntity} formatter={formatter} isNew />;
	const Old = () => <Renderer value={previous} entity={previousEntity} formatter={formatter} isOld />;

	if (!previous) {
		return (
			<div>
				<Label /> <New />
			</div>
		);
	}

	if (!current) {
		return (
			<div>
				<Label />
				<Old />
			</div>
		);
	}

	return (
		<div>
			<Label />
			<Old /> {' -> '}
			<New />
		</div>
	);
};
