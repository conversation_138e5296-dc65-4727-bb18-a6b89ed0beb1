import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, type PropsWithChildren } from 'react';
import { HistorySection } from '../HistorySection';

type Props = {
	customerClaim: ApiBody<'getCustomerClaim'>;
};

export const PublicCustomerClaimHistory: FC<PropsWithChildren<Props>> = ({ customerClaim, children }) => {
	const { data, isLoading } = apiHooks.useGetPublicCustomerClaimHistory({
		params: { customerClaimId: customerClaim.id },
		queries: {
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
		},
	});

	return (
		<HistorySection history={data?._data ?? []} isLoading={isLoading}>
			{children}
		</HistorySection>
	);
};
