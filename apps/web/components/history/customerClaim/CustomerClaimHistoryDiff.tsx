import { CustomerClaimStatusMessage } from '@pocitarna-nx-2023/config';
import type { customerClaim } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import type { z } from 'zod';
import { CustomerClaimCodeRenderer } from '../CustomerClaimCodeRenderer';
import { type KeyDiffMap, KeyDiffViewer } from '../KeyDiffViewer';

type Props = {
	current?: z.infer<typeof customerClaim>;
	previous?: z.infer<typeof customerClaim>;
};

const baseKeyDiffMap: KeyDiffMap<z.infer<typeof customerClaim>> = {
	createdAt: {
		label: 'Vytvořeno',
	},
	status: {
		label: 'Stav',
		formatter: (value) => CustomerClaimStatusMessage[value as keyof typeof CustomerClaimStatusMessage],
	},
};

const cmpKeyDiffMap: KeyDiffMap<z.infer<typeof customerClaim>> = {
	...baseKeyDiffMap,
	codeId: {
		label: 'Kód',
		renderer: CustomerClaimCodeRenderer,
	},
};

export const CustomerClaimHistoryDiff: FC<Props> = (props) => {
	return <KeyDiffViewer keyDiffMap={cmpKeyDiffMap} {...props} />;
};

export const PublicCustomerClaimHistoryDiff: FC<Props> = (props) => {
	return <KeyDiffViewer keyDiffMap={baseKeyDiffMap} {...props} />;
};
