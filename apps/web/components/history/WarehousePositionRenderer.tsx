import { LOADING_MESSAGE, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { formatWarehousePositionName } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { DefaultRenderer, type <PERSON><PERSON>enderer } from './DefaultRenderer';

export const WarehousePositionRenderer: HistoryRenderer = ({ value, ...props }) => {
	const { data, isLoading } = apiHooks.useGetWarehousePosition(
		{ params: { warehousePositionId: value?.toString() ?? '' } },
		{ enabled: !!value },
	);

	return (
		<DefaultRenderer
			value={
				isLoading ? (
					LOADING_MESSAGE
				) : data?._data.id ? (
					<Link className="text-link" href={`/warehouse/position/${data?._data.id}`}>
						{data._data.warehouse.name} - {formatWarehousePositionName(data._data)}
					</Link>
				) : (
					NOT_AVAILABLE
				)
			}
			{...props}
		/>
	);
};
