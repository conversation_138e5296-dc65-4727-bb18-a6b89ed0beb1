import { type warehousePosition } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import type { z } from 'zod';
import { type KeyDiffMap, KeyDiffViewer } from '../KeyDiffViewer';
import { WarehouseRenderer } from '../WarehouseRenderer';

type Props = {
	current?: z.infer<typeof warehousePosition>;
	previous?: z.infer<typeof warehousePosition>;
};

const keyDiffMap: KeyDiffMap<z.infer<typeof warehousePosition>> = {
	warehouseId: {
		label: 'Sklad',
		renderer: WarehouseRenderer,
	},

	sector: {
		label: 'Sektor',
	},
	rack: {
		label: 'Regál',
	},
	shelf: {
		label: 'Police',
	},
	box: {
		label: 'Box',
	},
	description: {
		label: 'Popis',
	},
};

export const WarehousePositionHistoryDiff: FC<Props> = (props) => {
	return <KeyDiffViewer keyDiffMap={keyDiffMap} {...props} />;
};
