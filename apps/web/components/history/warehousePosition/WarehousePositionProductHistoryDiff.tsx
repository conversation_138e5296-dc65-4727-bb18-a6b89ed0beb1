import { formatProductCode, formatWarehousePositionName } from '@pocitarna-nx-2023/utils';
import type { product } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { type FC } from 'react';
import type { z } from 'zod';
import { DefaultRenderer } from '../DefaultRenderer';

type Props = {
	current?: z.infer<typeof product>;
	previous?: z.infer<typeof product>;
};

export const WarehousePositionProductHistoryDiff: FC<Props> = ({ current }) => {
	const { query } = useRouter();
	const warehousePositionId = Array.isArray(query.warehousePositionId) ? query.warehousePositionId.at(0) : query.warehousePositionId;
	const productWarehousePositionId = current?.warehousePositionId;

	const { data: currentPosition } = apiHooks.useGetWarehousePosition(
		{ params: { warehousePositionId: productWarehousePositionId ?? '' } },
		{ enabled: !!productWarehousePositionId && warehousePositionId !== productWarehousePositionId },
	);

	if (!warehousePositionId) return null;

	return (
		<DefaultRenderer
			value={
				<>
					<Link className="text-link" href={`/product/${current?.id}`}>
						{formatProductCode(current?.code)}
					</Link>
					{productWarehousePositionId && warehousePositionId !== productWarehousePositionId && currentPosition?._data && (
						<>
							{' -> '}
							<Link className="text-link" href={`/admin/warehouse/${currentPosition._data.warehouse.id}`}>
								{currentPosition._data.warehouse.name}
							</Link>{' '}
							-{' '}
							<Link className="text-link" href={`/warehouse/position/${currentPosition._data.id}`}>
								{formatWarehousePositionName(currentPosition._data)}
							</Link>
						</>
					)}
				</>
			}
			isOld={warehousePositionId !== productWarehousePositionId}
			isNew={warehousePositionId === productWarehousePositionId}
		/>
	);
};
