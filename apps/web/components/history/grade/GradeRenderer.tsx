import { LOADING_MESSAGE, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { DefaultRenderer, type <PERSON><PERSON>enderer } from '../DefaultRenderer';

export const GradeRenderer: HistoryRenderer = ({ value, ...props }) => {
	const { data, isLoading } = apiHooks.useGetGrade({ params: { gradeId: value?.toString() ?? '' } }, { enabled: !!value });

	return (
		<DefaultRenderer
			value={
				isLoading ? (
					LOADING_MESSAGE
				) : data?._data.id ? (
					<Link className="text-link" href={`/admin/grade/${data?._data.id}`}>
						{data._data.name}
					</Link>
				) : (
					NOT_AVAILABLE
				)
			}
			{...props}
		/>
	);
};
