import { BatchDeliveryTypeMessage, BatchStatusMessage, BatchTypeNames, PaymentStatusMessage } from '@pocitarna-nx-2023/config';
import type { batch } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import type { z } from 'zod';
import { PriorityDisplay } from '../../test/PriorityDisplay';
import { CurrencyRateRenderer } from '../CurrencyRateRenderer';
import { type KeyDiffMap, KeyDiffViewer } from '../KeyDiffViewer';
import { NoteRenderer } from '../NoteRenderer';
import { UserRenderer } from '../UserRenderer';
import { VendorRenderer } from '../VendorRenderer';

type Props = {
	current?: z.infer<typeof batch>;
	previous?: z.infer<typeof batch>;
};

const keyDiffMap: KeyDiffMap<z.infer<typeof batch>> = {
	checkedAt: {
		label: 'Zkon<PERSON>lováno',
	},
	checkedById: {
		label: 'Zkon<PERSON>loval',
		renderer: UserRenderer,
	},
	checkedNote: {
		label: 'Poznámka z kontroly',
		renderer: NoteRenderer,
	},
	checkSn: {
		label: 'Kontrola SN',
	},
	checkedSnAt: {
		label: 'Zkontrolováno SN',
	},
	checkedSnById: {
		label: 'Zkontroloval SN',
		renderer: UserRenderer,
	},
	checkedSnNote: {
		label: 'Poznámka z kontroly SN',
		renderer: NoteRenderer,
	},
	createdAt: {
		label: 'Vytvořeno',
	},
	createdById: {
		label: 'Vytvořil',
		renderer: UserRenderer,
	},
	currencyRateId: {
		label: 'Kurz',
		renderer: CurrencyRateRenderer,
	},
	deadlineAt: {
		label: 'Termín',
	},
	deliveredAt: {
		label: 'Přijato',
	},
	deliveredById: {
		label: 'Přijal',
		renderer: UserRenderer,
	},
	deliveredNote: {
		label: 'Poznámka z příjmu',
		renderer: NoteRenderer,
	},
	deliveryPrice: {
		label: 'Cena dopravy',
	},
	deliveryType: {
		label: 'Typ dopravy',
		formatter: (value) => BatchDeliveryTypeMessage[value as keyof typeof BatchDeliveryTypeMessage],
	},
	eta: {
		label: 'ETA',
	},
	name: {
		label: 'Název',
	},
	note: {
		label: 'Poznámka',
		renderer: NoteRenderer,
	},
	pallets: {
		label: 'Počet palet',
	},
	paymentStatus: {
		label: 'Platba',
		formatter: (value) => PaymentStatusMessage[value as keyof typeof PaymentStatusMessage],
	},
	priority: {
		label: 'Priorita',
		formatter: (value) => <PriorityDisplay entity={{ priority: value as number }} />,
	},
	status: {
		label: 'Stav',
		formatter: (value) => BatchStatusMessage[value as keyof typeof BatchStatusMessage],
	},
	testedAt: {
		label: 'Otestováno',
	},
	testedById: {
		label: 'Otestoval',
		renderer: UserRenderer,
	},
	trackingCode: {
		label: 'Tracking kód',
	},
	type: {
		label: 'Typ várky',
		formatter: (value) => BatchTypeNames[value as keyof typeof BatchTypeNames],
	},
	variableSymbol: {
		label: 'Variabilní symbol',
	},
	vendorId: {
		label: 'Dodavatel',
		renderer: VendorRenderer,
	},
	warranty: {
		label: 'Záruka',
	},
};

export const BatchHistoryDiff: FC<Props> = (props) => {
	return <KeyDiffViewer keyDiffMap={keyDiffMap} {...props} />;
};
