import { cn, Icon } from '@pocitarna-nx-2023/ui';
import { formatDateTime, formatNumber } from '@pocitarna-nx-2023/utils';
import type { FC, ReactNode } from 'react';

export type HistoryFormatter = (input: unknown, entity?: unknown) => ReactNode | null;
export type HistoryRenderer = FC<{ value: unknown; entity?: unknown; isOld?: boolean; isNew?: boolean; formatter?: HistoryFormatter }>;

export const defaultFormatter: HistoryFormatter = (input: unknown) => {
	if (input instanceof Date) {
		return formatDateTime(input);
	} else if (typeof input === 'number') {
		return formatNumber(input);
	} else if (typeof input === 'boolean') {
		return <Icon name={input ? 'check' : 'close'} />;
	} else if (input !== null && typeof input === 'object' && 'type' in (input as any)) {
		return input as ReactNode;
	}

	return input?.toString() ?? null;
};

export const DefaultRenderer: HistoryRenderer = ({ value, entity, isOld, isNew, formatter = defaultFormatter }) => {
	const formattedValue = formatter(value, entity);
	if (!formattedValue) return null;

	return (
		<span className={cn(isOld && 'text-destructive [&_a]:text-destructive line-through', isNew && 'text-success [&_a]:text-success')}>
			{formattedValue}
		</span>
	);
};
