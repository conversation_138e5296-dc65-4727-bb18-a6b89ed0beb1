import { LOADING_MESSAGE, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { DefaultRenderer, type HistoryRenderer } from './DefaultRenderer';

export const ProductCategoryRenderer: HistoryRenderer = ({ value, ...props }) => {
	const { data, isLoading } = apiHooks.useGetProductCategory(
		{ params: { productCategoryId: value?.toString() ?? '' } },
		{ enabled: !!value },
	);
	return <DefaultRenderer value={isLoading ? LOADING_MESSAGE : data?._data.name ?? NOT_AVAILABLE} {...props} />;
};
