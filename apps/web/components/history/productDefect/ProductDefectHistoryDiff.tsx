import { cn } from '@pocitarna-nx-2023/ui';
import { parsePeriod } from '@pocitarna-nx-2023/utils';
import { type relatedDefect } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { type z } from 'zod';
import { Note<PERSON>enderer } from '../NoteRenderer';

type Props = {
	current?: z.infer<typeof relatedDefect> & { period: string };
	previous?: z.infer<typeof relatedDefect> & { period: string };
};

export const ProductDefectHistoryDiff: FC<Props> = ({ current, previous }) => {
	if (!current) return null;
	const isDelete = previous && !!parsePeriod(current.period)[1];
	const isChange = previous && !parsePeriod(current.period)[1];
	const isNew = !previous;
	const isClosed = current.serviceTaskId != null;

	return (
		<div>
			{isDelete && 'Smazaná'}
			{isChange && 'Změněná'}
			{isNew && 'Nová'} závada:{' '}
			<span className={cn(isDelete && 'text-destructive line-through', isClosed && 'text-success')}>
				{current.defectType.name} - <NoteRenderer value={current.note} /> {isClosed && ' (vyřešeno)'}
			</span>
		</div>
	);
};
