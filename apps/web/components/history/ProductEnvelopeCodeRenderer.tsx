import { LOADING_MESSAGE, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { formatEnvelopeCode } from '@pocitarna-nx-2023/utils';
import { type productEnvelope } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { type z } from 'zod';
import { DefaultRenderer, type HistoryRenderer } from './DefaultRenderer';

export const ProductEnvelopeCodeRenderer: HistoryRenderer = ({ value, entity, ...props }) => {
	const { data: productEnvelopeCodeData, isLoading: isLoadingProductEnvelope } = apiHooks.useGetProductEnvelopeCode(
		{ params: { productEnvelopeCodeId: value?.toString() ?? '' } },
		{ enabled: !!value },
	);
	const { data: productCategoryData, isLoading: isLoadingProductCategory } = apiHooks.useGetProductCategory(
		{ params: { productCategoryId: (entity as z.infer<typeof productEnvelope>)?.productCategoryId ?? '' } },
		{ enabled: !!(entity as z.infer<typeof productEnvelope>)?.productCategoryId },
	);
	const code = formatEnvelopeCode(productCategoryData?._data.codePrefix)(productEnvelopeCodeData?._data);
	return (
		<DefaultRenderer
			value={
				isLoadingProductEnvelope || isLoadingProductCategory ? (
					LOADING_MESSAGE
				) : productEnvelopeCodeData?._data.id ? (
					<Link className="text-link" href={`/product/envelope/${productEnvelopeCodeData?._data.id}`}>
						{code}
					</Link>
				) : (
					NOT_AVAILABLE
				)
			}
			entity={entity}
			{...props}
		/>
	);
};
