import { WarrantyClaimStatusMessage } from '@pocitarna-nx-2023/config';
import type { warrantyClaim } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import type { z } from 'zod';
import { type KeyDiffMap, KeyDiffViewer } from '../KeyDiffViewer';
import { ServiceCaseRenderer } from '../ServiceCaseRenderer';
import { UserRenderer } from '../UserRenderer';
import { WarrantyClaimCodeRenderer } from '../WarrantyClaimCodeRenderer';

type Props = {
	current?: z.infer<typeof warrantyClaim>;
	previous?: z.infer<typeof warrantyClaim>;
};

const keyDiffMap: KeyDiffMap<z.infer<typeof warrantyClaim>> = {
	createdAt: {
		label: 'Vytvořeno',
	},
	createdById: {
		label: 'Vytvořil',
		renderer: UserRenderer,
	},
	codeId: {
		label: 'Kód',
		renderer: WarrantyClaimCodeRenderer,
	},
	status: {
		label: 'Stav',
		formatter: (value) => WarrantyClaimStatusMessage[value as keyof typeof WarrantyClaimStatusMessage],
	},
	trackingCode: {
		label: 'Tracking kód',
	},
	vendorRMAIdentifier: {
		label: 'ID RMA dodavatele',
	},
	sourceServiceCaseId: {
		label: 'Servis',
		renderer: ServiceCaseRenderer,
	},
};

export const WarrantyClaimHistoryDiff: FC<Props> = (props) => {
	return <KeyDiffViewer keyDiffMap={keyDiffMap} {...props} />;
};
