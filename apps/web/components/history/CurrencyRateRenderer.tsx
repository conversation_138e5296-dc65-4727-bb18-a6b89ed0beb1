import { LOADING_MESSAGE, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { MutedText } from '@pocitarna-nx-2023/ui';
import { formatDate, formatPrice } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { DefaultRenderer, type HistoryRenderer } from './DefaultRenderer';

export const CurrencyRateRenderer: HistoryRenderer = ({ value, ...props }) => {
	const { data, isLoading } = apiHooks.useGetCurrencyRate({ params: { currencyRateId: value?.toString() ?? '' } }, { enabled: !!value });

	return (
		<DefaultRenderer
			value={
				isLoading ? (
					LOADING_MESSAGE
				) : data?._data ? (
					<>
						1 {data._data.currency.name} = {formatPrice(data._data.rate)}{' '}
						<MutedText>z {formatDate(data._data.createdAt)}</MutedText>
					</>
				) : (
					NOT_AVAILABLE
				)
			}
			{...props}
		/>
	);
};
