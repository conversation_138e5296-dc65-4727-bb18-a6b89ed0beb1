import deepDiff from 'deep-diff';
import { path } from 'rambdax';
import { Fragment } from 'react';
import type { HistoryF<PERSON>atter, HistoryRenderer } from './DefaultRenderer';
import { DiffViewer } from './DiffViewer';

export type KeyDiffMapProps = { label: string; renderer?: HistoryRenderer; formatter?: HistoryFormatter };

export type KeyDiffMap<T> = Partial<Record<keyof T, KeyDiffMapProps>>;

type Props<T> = {
	keyDiffMap: KeyDiffMap<T>;
	current?: T;
	previous?: T;
};

export const KeyDiffViewer = <T extends object>({ keyDiffMap, current, previous }: Props<T>) => {
	const diffs = deepDiff(previous, current);
	if (!diffs) return null;

	return diffs.map((diff) => {
		if (diff.kind === 'N' && diff.rhs) {
			return (
				<Fragment key="N">
					{Object.keys(diff.rhs).map((key) => {
						const value = (diff.rhs as T)[key as keyof T];
						if (!value) return null;

						const props: KeyDiffMapProps | undefined = path(key, keyDiffMap);
						if (!props) return null;

						return <DiffViewer key={key} current={value} currentEntity={current} {...props} />;
					})}
				</Fragment>
			);
		}

		if (diff.kind === 'D' && diff.lhs) {
			return (
				<Fragment key="D">
					{Object.keys(diff.lhs).map((key) => {
						const value = (diff.lhs as T)[key as keyof T];
						if (!value) return null;

						const props: KeyDiffMapProps | undefined = path(key, keyDiffMap);
						if (!props) return null;

						return <DiffViewer key={key} current={value} currentEntity={current} {...props} />;
					})}
				</Fragment>
			);
		}

		if (diff.kind === 'E') {
			if (!diff.path) return null;
			const props: KeyDiffMapProps | undefined = path(diff.path, keyDiffMap);
			if (!props) return null;

			return (
				<DiffViewer
					key={diff.path.join('.')}
					previous={diff.lhs}
					previousEntity={previous}
					current={diff.rhs}
					currentEntity={current}
					{...props}
				/>
			);
		}

		return null;
	});
};
