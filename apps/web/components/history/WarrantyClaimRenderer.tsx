import { LOADING_MESSAGE, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { formatWarrantyClaimCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { DefaultRenderer, type <PERSON><PERSON>enderer } from './DefaultRenderer';

export const WarrantyClaimRenderer: HistoryRenderer = ({ value, ...props }) => {
	const { data, isLoading } = apiHooks.useGetWarrantyClaim(
		{ params: { warrantyClaimId: value?.toString() ?? '' } },
		{ enabled: !!value },
	);
	return (
		<DefaultRenderer
			value={
				isLoading ? (
					LOADING_MESSAGE
				) : data?._data.code ? (
					<Link className="text-link" href={`/warranty-claim/${data?._data.id}`}>
						{formatWarrantyClaimCode(data._data.code)}
					</Link>
				) : (
					NOT_AVAILABLE
				)
			}
			{...props}
		/>
	);
};
