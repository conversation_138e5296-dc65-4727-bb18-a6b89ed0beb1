import { LOADING_MESSAGE, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { formatBatchCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { DefaultRenderer, type History<PERSON>enderer } from './DefaultRenderer';

export const BatchRenderer: HistoryRenderer = ({ value, ...props }) => {
	const { data, isLoading } = apiHooks.useGetBatch({ params: { batchId: value?.toString() ?? '' } }, { enabled: !!value });
	const code = formatBatchCode(data?._data?.code);
	return (
		<DefaultRenderer
			value={
				isLoading ? (
					LOADING_MESSAGE
				) : data?._data.id ? (
					<Link className="text-link" href={`/batch/${data?._data.id}`}>
						{code}
					</Link>
				) : (
					NOT_AVAILABLE
				)
			}
			{...props}
		/>
	);
};
