import { LOADING_MESSAGE, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { formatCustomerClaimCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { DefaultRenderer, type <PERSON><PERSON>enderer } from './DefaultRenderer';

export const CustomerClaimCodeRenderer: HistoryRenderer = ({ value, ...props }) => {
	const { data, isLoading } = apiHooks.useGetCustomerClaimCode(
		{ params: { customerClaimCodeId: value?.toString() ?? '' } },
		{ enabled: !!value },
	);
	const code = formatCustomerClaimCode(data?._data);
	return (
		<DefaultRenderer
			value={
				isLoading ? (
					LOADING_MESSAGE
				) : data?._data.id ? (
					<Link className="text-link" href={`/customer-claim/${data?._data.id}`}>
						{code}
					</Link>
				) : (
					NOT_AVAILABLE
				)
			}
			{...props}
		/>
	);
};
