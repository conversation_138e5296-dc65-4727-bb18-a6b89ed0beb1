import { ProductStatusMessage, ProductTypeMessage } from '@pocitarna-nx-2023/config';
import type { product } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import type { z } from 'zod';
import { SplitCodeDisplay } from '../../SplitCodeDisplay';
import { BatchRenderer } from '../BatchRenderer';
import { GradeRenderer } from '../grade/GradeRenderer';
import { type KeyDiffMap, KeyDiffViewer } from '../KeyDiffViewer';
import { ProductCategoryRenderer } from '../ProductCategoryRenderer';
import { ProductCodeRenderer } from '../ProductCodeRenderer';
import { ProductEnvelopeRenderer } from '../ProductEnvelopeRenderer';
import { UserRenderer } from '../UserRenderer';
import { WarehousePositionRenderer } from '../WarehousePositionRenderer';

type Props = {
	current?: z.infer<typeof product>;
	previous?: z.infer<typeof product>;
};

const keyDiffMap: KeyDiffMap<z.infer<typeof product>> = {
	createdAt: {
		label: 'Vytvořeno',
	},
	sn: {
		label: 'Sériové číslo',
		formatter: (value) => <SplitCodeDisplay code={value as string} />,
	},
	status: {
		label: 'Stav',
		formatter: (value) => ProductStatusMessage[value as keyof typeof ProductStatusMessage],
	},
	productCategoryId: {
		label: 'Kategorie',
		renderer: ProductCategoryRenderer,
	},
	codeId: {
		label: 'Kód',
		renderer: ProductCodeRenderer,
	},
	productEnvelopeId: {
		label: 'Karta produktu',
		renderer: ProductEnvelopeRenderer,
	},
	productEnvelopeAssignedAt: {
		label: 'Přiřazeno ke kartě',
	},
	productEnvelopeAssignedById: {
		label: 'Přiřadil',
		renderer: UserRenderer,
	},
	batchId: {
		label: 'Várka',
		renderer: BatchRenderer,
	},
	warehousePositionId: {
		label: 'Skladová pozice',
		renderer: WarehousePositionRenderer,
	},
	type: {
		label: 'Typ',
		formatter: (value) => ProductTypeMessage[value as keyof typeof ProductTypeMessage],
	},
	gradeId: {
		label: 'Stav',
		renderer: GradeRenderer,
	},
};

export const ProductHistoryDiff: FC<Props> = (props) => {
	return <KeyDiffViewer keyDiffMap={keyDiffMap} {...props} />;
};
