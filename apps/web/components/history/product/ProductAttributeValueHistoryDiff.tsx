import { parsePeriod } from '@pocitarna-nx-2023/utils';
import { type productAttributeValue } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { type z } from 'zod';
import { AttributeValue } from '../../AttributeValue';

type Props = {
	current?: z.infer<typeof productAttributeValue> & { period: string };
	previous?: z.infer<typeof productAttributeValue> & { period: string };
};

export const ProductAttributeValueHistoryDiff: FC<Props> = ({ current, previous }) => {
	const isTheSameAttribute = current && previous && current.attributeValue.attribute.id === previous.attributeValue.attribute.id;

	return (
		<p>
			{isTheSameAttribute && <Attribute attribute={current.attributeValue.attribute} />}
			{current && (
				<>
					{!isTheSameAttribute && <Attribute attribute={current.attributeValue.attribute} />}
					<span className={parsePeriod(current.period)[1] ? 'text-destructive line-through' : 'text-success'}>
						<AttributeValue attributeValue={current.attributeValue} />
					</span>
				</>
			)}
			{isTheSameAttribute && ` -> `}
			{previous && (
				<>
					{!isTheSameAttribute && <Attribute attribute={previous.attributeValue.attribute} />}
					<span className={parsePeriod(previous.period)[1] ? 'text-destructive line-through' : 'text-success'}>
						<AttributeValue attributeValue={previous.attributeValue} />
					</span>
				</>
			)}
		</p>
	);
};

const Attribute: FC<{ attribute: z.infer<typeof productAttributeValue>['attributeValue']['attribute'] }> = ({ attribute }) => (
	<>
		<span>{attribute.displayName}</span>:{' '}
	</>
);
