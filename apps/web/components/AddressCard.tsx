import { <PERSON>, CardContent, <PERSON>H<PERSON>er, <PERSON><PERSON><PERSON>le, Stack } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';

type Props = {
	invoiceAddress?: ApiBody<'getAddress'> | null;
	shippingAddress?: ApiBody<'getAddress'> | null;
	contact?: ApiBody<'getContact'> | null;
};

export const AddressCard: FC<Props> = ({ invoiceAddress, shippingAddress, contact }) => {
	return (
		<Card>
			<CardHeader>
				<CardTitle>Zákazník</CardTitle>
			</CardHeader>
			<CardContent>
				<Stack gap={4}>
					{contact && (
						<div>
							<h5>{contact.name}</h5>
							<p>
								<a href={`mailto:${contact.email}`} className="text-link">
									{contact.email}
								</a>
							</p>
							<p>
								<a href={`tel:${contact.phone}`} className="text-link">
									{contact.phone}
								</a>
							</p>
						</div>
					)}

					{invoiceAddress && (
						<div>
							<h5>Fakturační adresa:</h5>
							<p>{invoiceAddress.street}</p>
							<p>
								{invoiceAddress.postalCode} {invoiceAddress.city}
							</p>
						</div>
					)}

					{shippingAddress && shippingAddress.id !== invoiceAddress?.id && (
						<div>
							<h5>Dodací adresa:</h5>
							<p>{shippingAddress.street}</p>
							<p>
								{shippingAddress.postalCode} {shippingAddress.city}
							</p>
						</div>
					)}
				</Stack>
			</CardContent>
		</Card>
	);
};
