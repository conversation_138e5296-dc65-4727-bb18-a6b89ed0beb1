import { API_ORIGIN, MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { Button, cn, type FilterRules, ParamItem, ParamList, Spinner, Stack } from '@pocitarna-nx-2023/ui';
import { organizeInventoryItems } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useMemo } from 'react';
import { useInventoryItemFilter } from '../../../hooks/useInventoryItemFilter';
import { PriceDisplayer } from '../../PriceDisplayer';
import { EnvelopeOverview } from '../EnvelopeOverview';

type Props = {
	inventory: ApiBody<'getInventory'>;
	filters: FilterRules;
};

export const ClosedInventory: FC<Props> = ({ inventory, filters }) => {
	const filter = useInventoryItemFilter(filters);

	const { data: inventoryItemsData, isLoading } = apiHooks.useGetInventoryItems({
		params: { inventoryId: inventory.id },
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER, filter },
		sort: ['productEnvelopeCode'],
	});

	const inventoryItems = useMemo(() => inventoryItemsData?._data ?? [], [inventoryItemsData?._data]);
	const { untestedItems, forSaleItems, serviceItems, deadItems } = useMemo(
		() => organizeInventoryItems(inventoryItems),
		[inventoryItems],
	);

	const untestedPrice = useMemo(() => untestedItems.reduce((acc, item) => acc + item.buyPrice, 0), [untestedItems]);
	const forSalePrice = useMemo(() => forSaleItems.reduce((acc, item) => acc + item.buyPrice, 0), [forSaleItems]);
	const servicePrice = useMemo(() => serviceItems.reduce((acc, item) => acc + item.buyPrice, 0), [serviceItems]);
	const deadPrice = useMemo(() => deadItems.reduce((acc, item) => acc + item.buyPrice, 0), [deadItems]);

	if (isLoading) return <Spinner />;

	return (
		<Stack>
			<Stack gap={2}>
				<Stack direction="row" className="justify-between">
					<div>
						<h2 className="font-bold">Neotestované zboží</h2>
						<PriceInfo before={inventory.untestedPrice} after={untestedPrice} />
					</div>
					<Button asChild variant="outline">
						<a
							href={`${API_ORIGIN}/inventory/${inventory.id}/excel?q=${JSON.stringify({ variant: 'untestedItems' })}`}
							target="_blank"
							download
						>
							Exportovat do Excelu
						</a>
					</Button>
				</Stack>
				<EnvelopeOverview inventory={inventory} inventoryItems={untestedItems} />
			</Stack>

			<hr />

			<Stack gap={2}>
				<Stack direction="row" className="justify-between">
					<div>
						<h2 className="font-bold">Zboží skladem</h2>
						<PriceInfo before={inventory.forSalePrice} after={forSalePrice} />
					</div>
					<Button asChild variant="outline">
						<a
							href={`${API_ORIGIN}/inventory/${inventory.id}/excel?q=${JSON.stringify({ variant: 'forSaleItems' })}`}
							target="_blank"
							download
						>
							Exportovat do Excelu
						</a>
					</Button>
				</Stack>
				<EnvelopeOverview inventory={inventory} inventoryItems={forSaleItems} />
			</Stack>

			<hr />

			<Stack gap={2}>
				<Stack direction="row" className="justify-between">
					<div>
						<h2 className="font-bold">Servisně řešené zboží</h2>
						<PriceInfo before={inventory.servicePrice} after={servicePrice} />
					</div>
					<Button asChild variant="outline">
						<a
							href={`${API_ORIGIN}/inventory/${inventory.id}/excel?q=${JSON.stringify({ variant: 'serviceItems' })}`}
							target="_blank"
							download
						>
							Exportovat do Excelu
						</a>
					</Button>
				</Stack>
				<EnvelopeOverview inventory={inventory} inventoryItems={serviceItems} />
			</Stack>

			<hr />

			<Stack gap={2}>
				<Stack direction="row" className="justify-between">
					<div>
						<h2 className="font-bold">Mrtvoly</h2>
						<PriceInfo before={inventory.deadPrice} after={deadPrice} />
					</div>
					<Button asChild variant="outline">
						<a
							href={`${API_ORIGIN}/inventory/${inventory.id}/excel?q=${JSON.stringify({ variant: 'deadItems' })}`}
							target="_blank"
							download
						>
							Exportovat do Excelu
						</a>
					</Button>
				</Stack>
				<EnvelopeOverview inventory={inventory} inventoryItems={deadItems} />
			</Stack>
		</Stack>
	);
};

export const PriceInfo: FC<{ before: number; after: number }> = ({ before, after }) => {
	const diff = Math.abs(before - after);

	return (
		<ParamList className="gap-y-0">
			<ParamItem label="Odhad hodnoty">
				<PriceDisplayer price={before} variant="inline" />
			</ParamItem>
			<ParamItem label="Skutečná hodnota">
				<PriceDisplayer price={after} variant="inline" />
			</ParamItem>
			<ParamItem label="Rozdíl">
				<span className={cn(diff > 1 && 'text-destructive')}>
					<PriceDisplayer price={diff} variant="inline" />
				</span>
			</ParamItem>
		</ParamList>
	);
};
