import { NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { Table, TableBody, TableHead, TableHeader, TableRow } from '@pocitarna-nx-2023/ui';
import { formatInventoryCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { groupBy } from 'rambdax';
import { type FC, useMemo } from 'react';
import { type InventorySelection } from '../../pages/inventory/[inventoryId]';
import { EnvelopeAccordion } from './EnvelopeAccordion';

type Props = {
	inventory: ApiBody<'getInventory'>;
	inventoryItems: ApiBody<'getInventoryItems'>;
	selection?: InventorySelection;
};

export const EnvelopeOverview: FC<Props> = ({ inventory, inventoryItems, selection }) => {
	const byEnvelope = useMemo(
		() =>
			groupBy(
				(inventoryItem) =>
					inventoryItem.productEnvelopeCode && inventoryItem.productEnvelopeName
						? `${inventoryItem.productEnvelopeCode} - ${inventoryItem.productEnvelopeName}`
						: NOT_AVAILABLE,
				inventoryItems,
			),
		[inventoryItems],
	);

	// FIXME: remove the N/A ones?
	return (
		<Table expandable title={`Inventura č. ${formatInventoryCode(inventory.code)}`}>
			<TableHeader>
				<TableRow>
					<TableHead className="w-1">Kód karty</TableHead>
					<TableHead>Název karty</TableHead>
					<TableHead className="text-right">Kusů</TableHead>
					<TableHead className="w-1" />
				</TableRow>
			</TableHeader>
			<TableBody>
				{Object.entries(byEnvelope)
					.sort(([a], [b]) => {
						if (a === NOT_AVAILABLE) return 1;
						if (b === NOT_AVAILABLE) return -1;
						return a.localeCompare(b);
					})
					.map(([envelopeCode, inventoryItems]) => (
						<EnvelopeAccordion
							key={envelopeCode}
							inventory={inventory}
							inventoryItems={inventoryItems}
							selection={selection}
							shouldAutoExpand={selection?.selectedEnvelope === inventoryItems[0]?.productEnvelopeId}
						/>
					))}
			</TableBody>
		</Table>
	);
};
