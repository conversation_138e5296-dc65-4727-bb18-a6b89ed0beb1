import { Card, CardContent, CardHeader, CardTitle, ParamItem, ParamList } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { PriceDisplayer } from '../PriceDisplayer';

type Props = {
	inventory: ApiBody<'getInventories'>[number];
};

export const PriceOverview: FC<Props> = ({ inventory }) => {
	return (
		<Card>
			<CardHeader>
				<CardTitle>Odhad hodnoty skladu</CardTitle>
			</CardHeader>
			<CardContent>
				<ParamList className="gap-y-0">
					<ParamItem label="Neotestované zboží">
						<PriceDisplayer price={inventory.untestedPrice} />
					</ParamItem>
					<ParamItem label="Zboží skladem">
						<PriceDisplayer price={inventory.forSalePrice} />
					</ParamItem>
					<ParamItem label="Servisně řešen<PERSON> z<PERSON>">
						<PriceDisplayer price={inventory.servicePrice} />
					</ParamItem>
					<ParamItem label="Mrtvoly">
						<PriceDisplayer price={inventory.deadPrice} />
					</ParamItem>
				</ParamList>
			</CardContent>
		</Card>
	);
};
