import { Button, ComboboxControl, DialogContent, DialogHeader, DialogTitle, FormContext, Stack } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { Dialog } from '@radix-ui/react-dialog';
import { useQueryClient } from '@tanstack/react-query';
import { type FC } from 'react';
import { z } from 'zod';
import { onInventoryItemSuccess } from '../../../utils/inventory';

type Props = {
	inventoryId: string;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
	product: ApiBody<'getProduct'>;
	open: boolean;
	onOpenChange: (open: boolean) => void;
};

export const AddToOrderDialog: FC<Props> = ({ inventoryId, inventoryItem, product, open, onOpenChange }) => {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent size="lg">
				<DialogHeader>
					<DialogTitle>Přidat produkt do objednávky</DialogTitle>
				</DialogHeader>
				<AddToOrderForm product={product} inventoryId={inventoryId} inventoryItem={inventoryItem} onOpenChange={onOpenChange} />
			</DialogContent>
		</Dialog>
	);
};

const AddToOrderForm: FC<Omit<Props, 'open'>> = ({ inventoryId, inventoryItem, product, onOpenChange }) => {
	const queryClient = useQueryClient();
	const { data: ordersMissingProductsData } = apiHooks.useGetEcommerceOrders({
		queries: {
			filter: {
				'items.productEnvelopeId': { eq: product.productEnvelopeId },
				'items.productId': { eq: null },
			},
		},
	});

	const ordersMissingProducts = ordersMissingProductsData?._data ?? [];

	const { mutate: handleInventoryItemAction, isLoading } = apiHooks.useHandleInventoryItemAction({
		params: { inventoryId, inventoryItemId: inventoryItem.id },
	});

	const { invalidate: invalidateInventory } = apiHooks.useGetInventoryItems({ params: { inventoryId } }, { enabled: false });

	const orderQueryKey = apiHooks.getKeyByAlias('getEcommerceOrder');
	const invalidateOrder = () => queryClient.invalidateQueries(orderQueryKey);

	if (ordersMissingProducts.length === 0) return <>Nebyla nalezena žádná objednávka s chybějícími produkty odpovídající této kartě</>;

	return (
		<FormContext
			schema={z.object({
				orderToAddTo: z.string(),
				productToAdd: z.string(),
			})}
			defaultValues={{
				orderToAddTo: '',
				productToAdd: product.id,
			}}
			onSubmit={(data) => {
				handleInventoryItemAction(
					{
						actionType: 'ADD_PRODUCT_TO_ORDER',
						productEnvelopeId: product.productEnvelopeId,
						...data,
					},
					{
						onSuccess: () => {
							onOpenChange(false);
							invalidateOrder();
							invalidateInventory();
							onInventoryItemSuccess(inventoryItem);
						},
					},
				);
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<ComboboxControl
						control={control}
						name="orderToAddTo"
						label="Objednávka"
						items={ordersMissingProducts.map((item: ApiBody<'getEcommerceOrders'>[number]) => {
							return {
								value: item.id,
								label: item.code,
							};
						})}
						hideSearch={true}
					/>

					<Button type="submit" isLoading={isLoading}>
						Vytvořit
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
