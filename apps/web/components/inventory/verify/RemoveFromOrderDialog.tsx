import { DeleteDialogContent } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { Dialog } from '@radix-ui/react-dialog';
import { type FC } from 'react';
import { onInventoryItemSuccess } from '../../../utils/inventory';

type Props = {
	inventoryId: string;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
	orderId: string;
	productId: string;
	open: boolean;
	onOpenChange: (open: boolean) => void;
};

export const RemoveFromOrderDialog: FC<Props> = ({ inventoryId, inventoryItem, orderId, productId, open, onOpenChange }) => {
	const { mutate: handleInventoryItemAction, isLoading } = apiHooks.useHandleInventoryItemAction({
		params: { inventoryId, inventoryItemId: inventoryItem.id },
	});

	const { invalidate: invalidateOrder } = apiHooks.useGetEcommerceOrder({ params: { ecommerceOrderId: orderId } }, { enabled: false });
	const { invalidate: invalidateInventory } = apiHooks.useGetInventoryItems({ params: { inventoryId } }, { enabled: false });

	const onDelete = () => {
		handleInventoryItemAction(
			{
				actionType: 'REMOVE_PRODUCT_FROM_ORDER',
				productToDelete: productId,
			},
			{
				onSuccess: () => {
					invalidateOrder();
					invalidateInventory();
					onOpenChange(false);
					onInventoryItemSuccess(inventoryItem);
				},
			},
		);
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DeleteDialogContent
				title="Odstranit z objednávky"
				description="Opravdu si přejete odstranit produkt z objednávky?"
				onDelete={onDelete}
				isLoading={isLoading}
			/>
		</Dialog>
	);
};
