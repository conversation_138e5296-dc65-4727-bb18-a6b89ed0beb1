import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>alog<PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	<PERSON>alogTitle,
	DialogTrigger,
	Stack,
	toast,
} from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { useUserHasScope } from '../../../hooks/useUserHasScope';
import { InventoryNameUpdateDialog } from './InventoryNameUpdateDialog';

type Props = {
	inventory: ApiBody<'getInventories'>[number];
};

export const ActionButtons: FC<Props> = ({ inventory }) => {
	const isWarehouseManage = useUserHasScope('warehouseManage');
	const { mutate: updateInventory, isLoading } = apiHooks.useUpdateInventory({ params: { inventoryId: inventory.id } });
	const { invalidate: invalidateInventory } = apiHooks.useGetInventory({ params: { inventoryId: inventory.id } }, { enabled: false });

	return (
		<Card sticky>
			<CardHeader>
				<CardTitle>Akce</CardTitle>
			</CardHeader>
			<CardContent>
				<Stack gap={4}>
					<Button
						isLoading={isLoading}
						disabled={!isWarehouseManage}
						onClick={() => {
							updateInventory(
								{ status: 'CLOSED' },
								{
									onSuccess: () => {
										invalidateInventory();
										toast.success('Inventura byla uzavřena');
									},
								},
							);
						}}
					>
						Dokončit inventuru
					</Button>
					<Dialog>
						<DialogTrigger asChild>
							<Button type="button" variant="secondary" disabled={!isWarehouseManage}>
								Změnit název inventury
							</Button>
						</DialogTrigger>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>Změnit název inventury</DialogTitle>
							</DialogHeader>
							<InventoryNameUpdateDialog inventory={inventory} />
						</DialogContent>
					</Dialog>
				</Stack>
			</CardContent>
		</Card>
	);
};
