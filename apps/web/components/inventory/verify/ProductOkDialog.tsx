import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alogContent, <PERSON>alogHeader, DialogTitle, FormContext, Stack, TextControl } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useCallback } from 'react';
import { z } from 'zod';
import { onInventoryItemSuccess } from '../../../utils/inventory';

type Props = {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	inventoryId: string;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
};

const schema = z.object({
	actionDescription: z.string(),
});

export const ProductOkDialog: FC<Props> = ({ open, onOpenChange, inventoryId, inventoryItem }) => {
	const { mutate: handleInventoryItemAction, isLoading } = apiHooks.useHandleInventoryItemAction({
		params: { inventoryId, inventoryItemId: inventoryItem.id },
	});

	const { invalidate: invalidateInventory } = apiHooks.useGetInventoryItems({ params: { inventoryId } }, { enabled: false });

	const onSubmit = useCallback(
		({ actionDescription }: z.infer<typeof schema>) => {
			handleInventoryItemAction(
				{
					actionType: 'MARK_PRODUCT_AS_OK',
					actionDescription,
				},
				{
					onSuccess: () => {
						invalidateInventory();
						onOpenChange(false);
						onInventoryItemSuccess(inventoryItem);
					},
				},
			);
		},
		[handleInventoryItemAction, invalidateInventory, onOpenChange, inventoryItem],
	);

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Označit produkt jako OK</DialogTitle>
				</DialogHeader>
				<FormContext
					schema={schema}
					defaultValues={{
						actionDescription: '',
					}}
					onSubmit={onSubmit}
				>
					{(control) => (
						<Stack gap={4}>
							<TextControl control={control} name="actionDescription" label="Poznámka" />

							<Button className="w-full" variant="default" type="submit" isLoading={isLoading}>
								Uložit
							</Button>
						</Stack>
					)}
				</FormContext>
			</DialogContent>
		</Dialog>
	);
};
