import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle } from '@pocitarna-nx-2023/ui';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { onInventoryItemSuccess } from '../../../utils/inventory';
import { AddProductDefect } from '../../productDefect/AddProductDefect';

type Props = {
	product: ApiBody<'getProduct'>;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	inventoryId: string;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
};

export const ServiceCaseCreateDialog: FC<Props> = ({ product, open, onOpenChange, inventoryId, inventoryItem }) => {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent size="lg">
				<DialogHeader>
					<DialogTitle>Poslat do servisu</DialogTitle>
				</DialogHeader>
				<ServiceCaseForm product={product} inventoryId={inventoryId} inventoryItem={inventoryItem} onOpenChange={onOpenChange} />
			</DialogContent>
		</Dialog>
	);
};

const ServiceCaseForm: FC<Omit<Props, 'open'>> = ({ product, onOpenChange, inventoryId, inventoryItem }) => {
	const { invalidate: invalidateInventory } = apiHooks.useGetInventoryItems({ params: { inventoryId } }, { enabled: false });
	const { invalidate: invalidateProducts } = apiHooks.useGetAllProducts({}, { enabled: false });
	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId: product.id } }, { enabled: false });

	const { mutate: handleInventoryItemAction, isLoading } = apiHooks.useHandleInventoryItemAction({
		params: { inventoryId, inventoryItemId: inventoryItem.id },
	});

	const onDefectCreation = async (productDefect: ApiBody<'createProductDefect'>) => {
		if (!productDefect.serviceCaseId) return;

		handleInventoryItemAction(
			{
				actionType: 'CREATE_SERVICE_CASE',
				serviceCaseId: productDefect.serviceCaseId,
			},
			{
				onSuccess: () => {
					onOpenChange(false);
					invalidateInventory();
					invalidateProducts();
					invalidateProduct();
					onInventoryItemSuccess(inventoryItem);
				},
			},
		);
	};

	return (
		<AddProductDefect
			product={product}
			onSuccess={onDefectCreation}
			source="SERVICE_CASE"
			isStillLoading={isLoading}
			serviceCaseData={{ note: 'Založeno z inventury' }}
		/>
	);
};
