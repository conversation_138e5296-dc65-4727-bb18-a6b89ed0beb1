import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@pocitarna-nx-2023/ui';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { onInventoryItemSuccess } from '../../../utils/inventory';
import { CustomerClaimSubmissionForm } from '../../customerClaim/CustomerClaimSubmissionForm';

type Props = {
	product: ApiBody<'getProduct'>;
	inventoryId: string;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
	open: boolean;
	onOpenChange: (open: boolean) => void;
};

export const CustomerClaimDialog: FC<Props> = ({ inventoryId, inventoryItem, product, open, onOpenChange }) => {
	const { mutate: handleInventoryItemAction, isLoading } = apiHooks.useHandleInventoryItemAction({
		params: { inventoryId, inventoryItemId: inventoryItem.id },
	});

	const { data: orderData } = apiHooks.useGetEcommerceOrder(
		{ params: { ecommerceOrderId: inventoryItem.ecommerceOrderId ?? '' } },
		{ enabled: !!inventoryItem.ecommerceOrderId },
	);
	const order = orderData?._data;

	const { invalidate: invalidateCustomerClaims } = apiHooks.useGetCustomerClaims({}, { enabled: false });
	const { invalidate: invalidateProducts } = apiHooks.useGetAllProducts({}, { enabled: false });
	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId: product.id } }, { enabled: false });
	const { invalidate: invalidateInventory } = apiHooks.useGetInventoryItems({ params: { inventoryId } }, { enabled: false });

	if (!order || !inventoryItem.ecommerceOrderItemId) return null;

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent size="lg">
				<DialogHeader>
					<DialogTitle>Nová zákaznická reklamace</DialogTitle>
				</DialogHeader>
				<CustomerClaimSubmissionForm
					email={order.contact?.email ?? ''}
					ecommerceOrderItemId={inventoryItem.ecommerceOrderItemId}
					order={order}
					isLoading={isLoading}
					onSubmit={(data) => {
						handleInventoryItemAction(
							{
								actionType: 'CREATE_CUSTOMER_CLAIM',
								...data,
								ecommerceOrderItemId: inventoryItem.ecommerceOrderItemId,
							},
							{
								onSuccess: () => {
									onOpenChange(false);
									invalidateInventory();
									invalidateProducts();
									invalidateProduct();
									invalidateCustomerClaims();
									onInventoryItemSuccess(inventoryItem);
								},
							},
						);
					}}
				/>
			</DialogContent>
		</Dialog>
	);
};
