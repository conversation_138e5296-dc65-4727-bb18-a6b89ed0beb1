import { <PERSON>ton, Form<PERSON>ontext, InputControl, Stack, useDialog } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { z } from 'zod';

type Props = {
	inventory: ApiBody<'getInventory'>;
};

export const InventoryNameUpdateDialog: FC<Props> = ({ inventory }) => {
	const { mutate, isLoading } = apiHooks.useUpdateInventory({ params: { inventoryId: inventory.id } });
	const { invalidate } = apiHooks.useGetInventory({ params: { inventoryId: inventory.id } }, { enabled: false });
	const { invalidate: invalidateInventoryLists } = apiHooks.useGetInventories({}, { enabled: false });
	const { closeDialog } = useDialog();

	return (
		<FormContext
			schema={z.object({
				name: z.string(),
			})}
			defaultValues={{
				name: inventory.name,
			}}
			onSubmit={(data) =>
				mutate(data, {
					onSuccess: () => {
						invalidate();
						invalidateInventoryLists();
						closeDialog();
					},
				})
			}
		>
			{(control) => (
				<Stack gap={4}>
					<InputControl control={control} name="name" label="Název" />

					<Button className="w-full" variant="default" type="submit" isLoading={isLoading}>
						Uložit
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
