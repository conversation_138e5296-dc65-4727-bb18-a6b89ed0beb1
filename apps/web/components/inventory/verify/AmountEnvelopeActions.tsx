import {
	Button,
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
	FormContext,
	Icon,
	NumericInputControl,
	Stack,
	useDialog,
} from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { useToggle } from 'rooks';
import { z } from 'zod';
import { onInventoryItemSuccess } from '../../../utils/inventory';
import { PositiveConfirmDialog } from '../../PositiveConfirmDialog';

type Props = {
	inventoryId: string;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
	disabled: boolean;
};

export const AmountEnvelopeActions: FC<Props> = ({ inventoryId, inventoryItem, disabled }) => {
	const [open, toggleOpen] = useToggle();
	const [amountSettingModalIsOpen, toggleAmountSettingModal] = useToggle();
	const [amountConfirmationModalIsOpen, toggleAmountConfirmationModal] = useToggle();

	const { productEnvelopeId, envelopeProductsExpectedCount, envelopeProductsRealCount } = inventoryItem;

	const amountsDontMatch = envelopeProductsExpectedCount !== envelopeProductsRealCount;

	if (!productEnvelopeId) return null;

	return (
		<>
			<DropdownMenu open={open} onOpenChange={toggleOpen}>
				<DropdownMenuTrigger asChild>
					<Button variant="outline" size="sm" disabled={disabled}>
						Akce
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="start">
					<DropdownMenuItem onSelect={toggleAmountSettingModal}>
						<Icon name="pencil" />
						<span className="ml-2">Změnit množství produktů v kartě</span>
					</DropdownMenuItem>

					{amountsDontMatch && (
						<DropdownMenuItem onSelect={toggleAmountConfirmationModal}>
							<Icon name="check" />
							<span className="ml-2">Potvrdit množství produktů v kartě</span>
						</DropdownMenuItem>
					)}
				</DropdownMenuContent>
			</DropdownMenu>

			<EditAmountModal
				inventoryId={inventoryId}
				inventoryItem={inventoryItem}
				isOpen={amountSettingModalIsOpen}
				onOpenChange={toggleAmountSettingModal}
			/>

			<ConfirmAmountModal
				inventoryId={inventoryId}
				inventoryItem={inventoryItem}
				isOpen={amountConfirmationModalIsOpen}
				onOpenChange={toggleAmountConfirmationModal}
			/>
		</>
	);
};

const EditAmountModal: FC<{
	inventoryId: string;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
	isOpen: boolean;
	onOpenChange: () => void;
}> = ({ inventoryId, inventoryItem, isOpen, onOpenChange }) => {
	const { productEnvelopeId, envelopeProductsExpectedCount, envelopeProductsRealCount } = inventoryItem;

	const { mutate: handleInventoryItemAction, isLoading } = apiHooks.useHandleInventoryItemAction({
		params: { inventoryId, inventoryItemId: productEnvelopeId ?? '' },
	});

	if (!productEnvelopeId) return null;

	return (
		<Dialog open={isOpen} onOpenChange={onOpenChange}>
			<DialogContent size="lg">
				<DialogHeader>
					<DialogTitle>Potvrdit množství produktů v kartě</DialogTitle>
				</DialogHeader>
				<FormContext
					schema={z.object({
						productsAmount: z.number(),
						productEnvelopeId: z.string().uuid(),
					})}
					defaultValues={{
						productsAmount: envelopeProductsRealCount ?? envelopeProductsExpectedCount ?? 0,
						productEnvelopeId,
					}}
					onSubmit={(data) => {
						handleInventoryItemAction(
							{
								...data,
								actionType: 'EDIT_AMOUNT_ENVELOPE_PRODUCT_COUNT',
							},
							{
								onSuccess: () => {
									onOpenChange();
									onInventoryItemSuccess(inventoryItem, 'amount-envelope');
								},
							},
						);
					}}
				>
					{(control) => (
						<Stack gap={4}>
							<NumericInputControl name="productsAmount" control={control} numberFormat="integer" />

							<Button type="submit" isLoading={isLoading}>
								Potvrdit
							</Button>
						</Stack>
					)}
				</FormContext>
			</DialogContent>
		</Dialog>
	);
};

const ConfirmAmountModal: FC<{
	inventoryId: string;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
	isOpen: boolean;
	onOpenChange: () => void;
}> = ({ inventoryId, inventoryItem, isOpen, onOpenChange }) => {
	const { productEnvelopeId } = inventoryItem;
	const { closeDialog } = useDialog();

	const { mutate: handleInventoryItemAction, isLoading } = apiHooks.useHandleInventoryItemAction({
		params: { inventoryId, inventoryItemId: productEnvelopeId ?? '' },
	});

	if (!productEnvelopeId) return null;

	return (
		<PositiveConfirmDialog
			open={isOpen}
			toggleOpen={onOpenChange}
			title="Potvrdit množství produktů v kartě"
			message="Opravdu chcete pokračovat?"
			handleConfirm={() => {
				handleInventoryItemAction(
					{
						actionType: 'CONFIRM_AMOUNT_ENVELOPE_PRODUCT_COUNT',
						productEnvelopeId,
					},
					{
						onSuccess: () => {
							closeDialog();
							onInventoryItemSuccess(inventoryItem, 'amount-envelope');
						},
					},
				);
			}}
			isLoading={isLoading}
		/>
	);
};
