import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@pocitarna-nx-2023/ui';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useCallback } from 'react';
import { onInventoryItemSuccess } from '../../../utils/inventory';
import { AddProductDefect } from '../../productDefect/AddProductDefect';

type Props = {
	product: ApiBody<'getProduct'>;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	inventoryId: string;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
};

export const WarrantyClaimCreateDialog: FC<Props> = ({ product, open, onOpenChange, inventoryId, inventoryItem }) => {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent size="lg">
				<DialogHeader>
					<DialogTitle>Reklamovat dodavateli</DialogTitle>
				</DialogHeader>
				<WarrantyClaimForm product={product} inventoryId={inventoryId} inventoryItem={inventoryItem} onOpenChange={onOpenChange} />
			</DialogContent>
		</Dialog>
	);
};

export const WarrantyClaimForm: FC<Omit<Props, 'open'>> = ({ product, onOpenChange, inventoryId, inventoryItem }) => {
	const { invalidate: invalidateProducts } = apiHooks.useGetAllProducts({}, { enabled: false });
	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId: product.id } }, { enabled: false });
	const { invalidate: invalidateInventory } = apiHooks.useGetInventoryItems({ params: { inventoryId } }, { enabled: false });

	const { mutate: handleInventoryItemAction, isLoading } = apiHooks.useHandleInventoryItemAction({
		params: { inventoryId, inventoryItemId: inventoryItem.id },
	});

	const onDefectCreation = useCallback(
		async (productDefect: ApiBody<'createProductDefect'>) => {
			handleInventoryItemAction(
				{
					actionType: 'CREATE_WARRANTY_CLAIM',
					productId: product.id,
					productDefectId: productDefect.id,
				},
				{
					onSuccess: () => {
						onOpenChange(false);
						invalidateInventory();
						invalidateProducts();
						invalidateProduct();
						onInventoryItemSuccess(inventoryItem);
					},
				},
			);
		},
		[handleInventoryItemAction, invalidateInventory, invalidateProduct, invalidateProducts, inventoryItem, onOpenChange, product.id],
	);

	return <AddProductDefect product={product} onSuccess={onDefectCreation} source="WARRANTY_CLAIM" isStillLoading={isLoading} />;
};
