import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { type FilterRules, Spinner } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useMemo } from 'react';
import { useInventoryItemFilter } from '../../../hooks/useInventoryItemFilter';
import { type InventorySelection } from '../../../pages/inventory/[inventoryId]';
import { EnvelopeOverview } from '../EnvelopeOverview';

type Props = {
	inventory: ApiBody<'getInventories'>[number];
	filters: FilterRules;
	selection?: InventorySelection;
};

export const VerifyInventory: FC<Props> = ({ inventory, filters, selection }) => {
	const filter = useInventoryItemFilter(filters);

	const { data: inventoryItemsData, isLoading } = apiHooks.useGetInventoryItems({
		params: { inventoryId: inventory.id },
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER, filter },
		sort: ['productEnvelopeCode'],
	});
	const inventoryItems = useMemo(() => inventoryItemsData?._data ?? [], [inventoryItemsData?._data]);

	if (isLoading) return <Spinner />;

	return <EnvelopeOverview inventory={inventory} inventoryItems={inventoryItems} selection={selection} />;
};
