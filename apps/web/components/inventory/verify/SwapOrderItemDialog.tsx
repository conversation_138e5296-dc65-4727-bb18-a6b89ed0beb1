import { Button, ComboboxControl, DialogContent, DialogHeader, DialogTitle, FormContext, Stack } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { Dialog } from '@radix-ui/react-dialog';
import { type FC } from 'react';
import { z } from 'zod';
import { useLazyComboboxProps } from '../../../hooks/useLazyComboboxProps';
import { useSearchFilter } from '../../../hooks/useSearchFilter';
import { onInventoryItemSuccess } from '../../../utils/inventory';

type Props = {
	inventoryId: string;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
	orderId: string;
	product: ApiBody<'getProduct'>;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	variant: 'item-found' | 'item-not-found';
};

export const SwapOrderItemDialog: FC<Props> = ({ inventoryId, inventoryItem, orderId, product, open, onOpenChange, variant }) => {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent size="lg">
				<DialogHeader>
					<DialogTitle>Přesunout produkt do jiného produktu</DialogTitle>
				</DialogHeader>
				<SwapOrderItemForm
					product={product}
					orderId={orderId}
					inventoryId={inventoryId}
					inventoryItem={inventoryItem}
					onOpenChange={onOpenChange}
					variant={variant}
				/>
			</DialogContent>
		</Dialog>
	);
};

const SwapOrderItemForm: FC<Omit<Props, 'open'>> = ({ inventoryId, inventoryItem, orderId, product, onOpenChange, variant }) => {
	const { filter, updateSearchTerm } = useSearchFilter('code');

	const { mutate: handleInventoryItemAction, isLoading } = apiHooks.useHandleInventoryItemAction({
		params: { inventoryId, inventoryItemId: inventoryItem.id },
	});

	const { invalidate: invalidateOrder } = apiHooks.useGetEcommerceOrder({ params: { ecommerceOrderId: orderId } }, { enabled: false });
	const { invalidate: invalidateInventory } = apiHooks.useGetInventoryItems({ params: { inventoryId } }, { enabled: false });

	const filterToApply =
		variant === 'item-found'
			? {
					scannedAt: { eq: null },
					productEnvelopeId: { eq: product.productEnvelopeId },
					ecommerceOrderId: { eq: null },
					status: { eq: ['FOR_SALE', 'RESERVED', 'SOLD'] },
					...filter,
				}
			: {
					scannedAt: { ne: null },
					productEnvelopeId: { eq: product.productEnvelopeId },
					ecommerceOrderId: { ne: null },
					inventoryStatus: { eq: 'NEW_ADDITION' },
					status: { eq: ['FOR_SALE', 'RESERVED', 'SOLD'] },
					...filter,
				};

	const unmatchedProductsLazyComboboxProps = useLazyComboboxProps<ApiBody<'getInventoryItems'>[number]>('getInventoryItems', {
		params: { inventoryId },
		queries: {
			filter: filterToApply,
			sort: ['code'],
		},
		formatResult: (inventoryItem) => {
			return {
				value: inventoryItem.productId,
				label: inventoryItem.code,
			};
		},
	});

	return (
		<FormContext
			schema={z.object({
				productToSwapOut: z.string(),
				productToSwapIn: z.string(),
				orderToAddTo: z.string(),
			})}
			defaultValues={{
				productToSwapOut: product.id,
				productToSwapIn: '',
				orderToAddTo: orderId,
			}}
			onSubmit={(data) => {
				handleInventoryItemAction(
					{
						actionType: 'SWAP_PRODUCT_IN_ORDER',
						...data,
					},
					{
						onSuccess: () => {
							onOpenChange(false);
							invalidateOrder();
							invalidateInventory();
							onInventoryItemSuccess(inventoryItem);
						},
					},
				);
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<ComboboxControl
						control={control}
						name="productToSwapIn"
						label="Nový produkt"
						onSearchChange={updateSearchTerm}
						{...unmatchedProductsLazyComboboxProps}
					/>

					<Button type="submit" isLoading={isLoading}>
						Vytvořit
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
