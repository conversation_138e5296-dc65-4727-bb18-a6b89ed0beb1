import { type ProductStatus, ProductStatusMessage } from '@pocitarna-nx-2023/config';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useCallback } from 'react';
import { onInventoryItemSuccess } from '../../../utils/inventory';
import { PositiveConfirmDialog } from '../../PositiveConfirmDialog';

type Props = {
	productId: string;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	inventoryId: string;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
	status: ProductStatus;
};

export const StatusTransitionStatusDialog: FC<Props> = ({ open, onOpenChange, inventoryId, inventoryItem, productId, status }) => {
	const { mutate: handleInventoryItemAction, isLoading } = apiHooks.useHandleInventoryItemAction({
		params: { inventoryId, inventoryItemId: inventoryItem.id },
	});

	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId } }, { enabled: false });
	const { invalidate: invalidateInventory } = apiHooks.useGetInventoryItems({ params: { inventoryId } }, { enabled: false });

	const onConfirm = useCallback(() => {
		handleInventoryItemAction(
			{
				actionType: 'CHANGE_PRODUCT_STATUS',
				status,
				productId,
			},
			{
				onSuccess: () => {
					invalidateInventory();
					invalidateProduct();
					onInventoryItemSuccess(inventoryItem);
				},
			},
		);
	}, [handleInventoryItemAction, invalidateInventory, invalidateProduct, inventoryItem, productId, status]);

	return (
		<PositiveConfirmDialog
			open={open}
			toggleOpen={() => onOpenChange(!open)}
			title={`Změnit stav produktu na ${ProductStatusMessage[status]}`}
			message="Opravdu si přejete změnit stav produktu?"
			handleConfirm={onConfirm}
			isLoading={isLoading}
		/>
	);
};
