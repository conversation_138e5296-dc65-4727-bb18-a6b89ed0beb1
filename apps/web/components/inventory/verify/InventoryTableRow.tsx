import { TableRow } from '@pocitarna-nx-2023/ui';
import { type FC, type PropsWithChildren, useEffect, useRef } from 'react';

type Props = {
	isSelected: boolean;
};

export const InventoryTableRow: FC<PropsWithChildren<Props>> = ({ isSelected, children }) => {
	const rowRef = useRef<HTMLTableRowElement>(null);

	useEffect(() => {
		if (isSelected && rowRef.current) {
			setTimeout(() => {
				rowRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' });
			}, 300); // Delay to allow accordion to open
		}
	}, [isSelected]);

	return (
		<TableRow ref={rowRef} className={isSelected ? '!bg-amber-50' : ''}>
			{children}
		</TableRow>
	);
};
