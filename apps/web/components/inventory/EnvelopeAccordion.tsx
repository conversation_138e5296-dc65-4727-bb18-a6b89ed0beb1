import { EMPTY_VALUE, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { cn, Icon, TableCell, TableRow } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { cva, type VariantProps } from 'class-variance-authority';
import Link from 'next/link';
import { type FC, useEffect, useRef } from 'react';
import { useToggle } from 'rooks';
import { type InventorySelection } from '../../pages/inventory/[inventoryId]';
import { EnvelopeTypeBadge } from '../productEnvelope/EnvelopeTypeBadge';
import { AmountEnvelopeDisplay } from './AmountEnvelopeDisplay';
import { EnvelopeTable } from './EnvelopeTable';

type Props = {
	inventory: ApiBody<'getInventory'>;
	inventoryItems: ApiBody<'getInventoryItems'>;
	selection?: InventorySelection;
	shouldAutoExpand?: boolean;
};

const rowVariants = cva('cursor-pointer', {
	variants: {
		variant: {
			destructive: 'bg-red-100 hover:bg-red-200',
			warning: 'bg-yellow-100 hover:bg-yellow-200',
			success: 'bg-green-100 hover:bg-green-200',
			info: 'bg-blue-100 hover:bg-blue-200',
		},
	},
});

type Variants = VariantProps<typeof rowVariants>['variant'];

const cellVariants = cva('', {
	variants: {
		variant: {
			destructive: 'border-red-400 text-destructive',
			warning: 'border-yellow-400 text-warning',
			success: 'border-green-400 text-success',
			info: 'border-blue-400 text-info',
		},
	},
});

export const EnvelopeAccordion: FC<Props> = ({ inventory, inventoryItems, shouldAutoExpand = false, selection }) => {
	const referenceInventoryItem = inventoryItems[0];
	const { productEnvelopeId, productEnvelopeCode, productEnvelopeName, productEnvelopeType } = referenceInventoryItem;
	const [open, toggleOpen] = useToggle(shouldAutoExpand);
	const accordionRef = useRef<HTMLTableRowElement>(null);
	const isAmountEnvelope = productEnvelopeType === 'AMOUNT';
	const variant = getEnvelopeVariant(inventoryItems, isAmountEnvelope);

	useEffect(() => {
		if (shouldAutoExpand && accordionRef.current) {
			accordionRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
		}
	}, [shouldAutoExpand]);

	const accordionContent = isAmountEnvelope ? (
		<AmountEnvelopeDisplay inventory={inventory} inventoryItem={inventoryItems[0]} selection={selection} />
	) : (
		<EnvelopeTable inventory={inventory} inventoryItems={inventoryItems} selection={selection} />
	);

	return (
		<>
			<TableRow ref={accordionRef} className={rowVariants({ variant })} onClick={toggleOpen}>
				<TableCell className={cn(cellVariants({ variant }), 'w-1')}>
					{productEnvelopeCode ? (
						<Link href={`/product/envelope/${productEnvelopeId}`} className="underline hover:no-underline" target="_blank">
							{productEnvelopeCode}
						</Link>
					) : (
						NOT_AVAILABLE
					)}
				</TableCell>
				<TableCell className={cellVariants({ variant })}>
					<span className="mr-4">
						<EnvelopeTypeBadge productEnvelope={{ type: referenceInventoryItem.productEnvelopeType ?? 'USED' }} />
					</span>
					{productEnvelopeName ?? 'Produkty bez přidělené karty'}
				</TableCell>
				<TableCell className={cn(cellVariants({ variant }), 'text-right')}>
					<AccordionHeaderContent inventoryItems={inventoryItems} isAmountEnvelope={isAmountEnvelope} variant={variant} />
				</TableCell>
				<TableCell className={cn(cellVariants({ variant }), 'text-right w-1')}>
					<Icon name={open ? 'chevron-up' : 'chevron-down'} />
				</TableCell>
			</TableRow>
			<TableRow>
				<TableCell colSpan={4} className="!p-0 border-0">
					{open && accordionContent}
				</TableCell>
			</TableRow>
		</>
	);
};

const AccordionHeaderContent: FC<{ inventoryItems: ApiBody<'getInventoryItems'>; variant?: Variants; isAmountEnvelope: boolean }> = ({
	inventoryItems,
	isAmountEnvelope,
	variant,
}) => {
	if (isAmountEnvelope) {
		const referenceItem = inventoryItems[0];
		return (
			<>
				<span>{referenceItem.envelopeProductsExpectedCount ?? EMPTY_VALUE}</span> |{' '}
				<span className={variant ?? undefined}>{referenceItem.envelopeProductsRealCount ?? EMPTY_VALUE}</span>
			</>
		);
	}
	return (
		<>
			<span className={cellVariants({ variant: 'success' })}>
				{inventoryItems.filter((item) => item.inventoryStatus === 'OK').length}
			</span>{' '}
			|{' '}
			<span className={cellVariants({ variant: 'warning' })}>
				{inventoryItems.filter((item) => item.scannedAt == null && item.inventoryStatus !== 'OK').length}
			</span>{' '}
			|{' '}
			<span className={cellVariants({ variant: 'destructive' })}>
				{inventoryItems.filter((item) => item.inventoryStatus === 'ERROR').length}
			</span>{' '}
			|{' '}
			<span className={cellVariants({ variant: 'info' })}>
				{inventoryItems.filter((item) => item.inventoryStatus === 'NEW_ADDITION').length}
			</span>
		</>
	);
};

const getEnvelopeVariant = (inventoryItems: ApiBody<'getInventoryItems'>, isAmountEnvelope: boolean): Variants => {
	if (isAmountEnvelope) return getAmountEnvelopeVariant(inventoryItems);

	if (inventoryItems.every((item) => item.inventoryStatus === 'OK')) return 'success';
	if (inventoryItems.every((item) => item.scannedAt == null || item.inventoryStatus === 'ERROR')) return 'destructive';
	if (inventoryItems.some((item) => item.inventoryStatus === 'ERROR' || item.scannedAt == null)) return 'warning';
	if (inventoryItems.some((item) => item.inventoryStatus === 'NEW_ADDITION')) return 'info';
	return undefined;
};

const getAmountEnvelopeVariant = (inventoryItems: ApiBody<'getInventoryItems'>): Variants => {
	const referenceItem = inventoryItems[0];
	const { envelopeProductsExpectedCount, envelopeProductsRealCount } = referenceItem;

	if (envelopeProductsRealCount == null || envelopeProductsExpectedCount == null) return 'warning';
	if (envelopeProductsRealCount < envelopeProductsExpectedCount) return 'destructive';
	if (envelopeProductsRealCount > envelopeProductsExpectedCount) return 'info';
	return 'success';
};
