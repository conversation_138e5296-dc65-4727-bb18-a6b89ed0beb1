import { But<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>le, <PERSON><PERSON><PERSON><PERSON>ger, I<PERSON>, Spinner } from '@pocitarna-nx-2023/ui';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { ScanInventory } from './ScanInventory';

type Props = {
	inventory: ApiBody<'getInventories'>[number];
};

export const ScanInventoryDialog: FC<Props> = ({ inventory }) => {
	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button>
					<Icon name="barcode" />
					Skenovat produkty
				</Button>
			</DialogTrigger>
			<DialogContent size="lg">
				<DialogHeader>
					<DialogTitle>
						<>
							<span className="mr-2">Čekání na skenování</span>
							<Spinner />
						</>
					</DialogTitle>
				</DialogHeader>
				<ScanInventory inventory={inventory} />
			</DialogContent>
		</Dialog>
	);
};
