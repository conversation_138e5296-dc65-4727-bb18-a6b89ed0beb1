import {
	Alert,
	AlertDescription,
	AlertTitle,
	type FilterRules,
	Icon,
	Table,
	TableBody,
	TableHead,
	TableHeader,
	TableRow,
} from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, memo } from 'react';
import { useSorting } from '../../../contexts/SortingContext';
import { usePaging } from '../../../hooks/usePaging';
import { Pagination } from '../../Pagination/Pagination';
import { SortableColumn } from '../../sorting/SortableColumn';
import { InventoryRow } from './InventoryRow';

type Props = {
	filters: FilterRules;
};

export const ListTable: FC<Props> = ({ filters }) => {
	const [page, limit, setLimit] = usePaging();

	const filter = {
		...(filters['status'] ? { status: { eq: `%${filters['status']}%` } } : {}),
	};

	const { sorting } = useSorting();

	const { data, isSuccess } = apiHooks.useGetInventories({
		queries: {
			page,
			filter,
			sort: sorting,
			limit,
		},
	});

	if (isSuccess && !data?._data?.length) {
		return (
			<Alert variant="info">
				<Icon name="circle-info" />
				<AlertTitle>Žádné inventury.</AlertTitle>
				<AlertDescription>Pro zobrazení inventur zkuste upravit filtr.</AlertDescription>
			</Alert>
		);
	}

	return (
		<>
			<Table>
				<TableHeader>
					<TableRow>
						<SortableColumn property="code.code">No.</SortableColumn>
						<SortableColumn property="name">Název</SortableColumn>
						<SortableColumn property="createdAt">Vytvořil</SortableColumn>
						<TableHead>Stav</TableHead>
						<TableHead />
					</TableRow>
				</TableHeader>
				<TableBody>{data?._data.map((inventory) => <InventoryRow key={inventory.id} inventory={inventory} />)}</TableBody>
			</Table>

			<Pagination paging={data?._paging} rows={limit} setRows={setLimit} />
		</>
	);
};

export const InventoryListTable = memo(ListTable);
InventoryListTable.displayName = 'InventoryListTable';
