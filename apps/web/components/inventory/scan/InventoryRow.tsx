import { InventoryStatusMessage, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { Button, TableCell, TableRow } from '@pocitarna-nx-2023/ui';
import { formatDateTime, formatInventoryCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import Link from 'next/link';
import { type FC } from 'react';

type Props = {
	inventory: ApiBody<'getInventories'>[number];
};

export const InventoryRow: FC<Props> = ({ inventory }) => {
	return (
		<TableRow key={inventory.id}>
			<TableCell>
				<Link className="text-link" href={`/inventory/${inventory.id}`}>
					{formatInventoryCode(inventory.code)}
				</Link>
			</TableCell>

			<TableCell>{inventory.name}</TableCell>

			<TableCell>
				{formatDateTime(inventory.createdAt)} <br /> {inventory.createdBy?.name ?? NOT_AVAILABLE}
			</TableCell>
			<TableCell>{InventoryStatusMessage[inventory.status]}</TableCell>
			<TableCell align="right">
				<Button size="sm" asChild>
					<Link href={`/inventory/${inventory.id}`}>Detail</Link>
				</Button>
			</TableCell>
		</TableRow>
	);
};
