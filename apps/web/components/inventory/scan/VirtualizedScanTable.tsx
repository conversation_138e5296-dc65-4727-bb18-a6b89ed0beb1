import { EMPTY_VALUE } from '@pocitarna-nx-2023/config';
import { Icon, Spinner, Table, TableBody, Tooltip } from '@pocitarna-nx-2023/ui';
import { type InventoryItemScan } from '@pocitarna-nx-2023/zodios';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useRef } from 'react';

type Props = {
	items: InventoryItemScan[];
};

export const VirtualizedScanTable = ({ items }: Props) => {
	const parentRef = useRef<HTMLDivElement>(null);

	const virtualizer = useVirtualizer({
		count: items.length,
		getScrollElement: () => parentRef.current,
		estimateSize: () => 40,
		overscan: 10,
	});

	const virtualItems = virtualizer.getVirtualItems();

	return (
		<div className="h-full flex flex-col">
			<div className="flex-1 min-h-0">
				<Table>
					<TableBody>
						<tr>
							<td
								colSpan={2}
								style={{
									padding: 0,
									height: '100%',
									verticalAlign: 'top',
								}}
							>
								<div ref={parentRef} className="h-full overflow-y-auto">
									<div
										style={{
											height: virtualizer.getTotalSize(),
											position: 'relative',
										}}
									>
										{virtualItems.map((virtualRow) => {
											const item = items[virtualRow.index];

											return (
												<div
													key={item.pcn}
													data-index={virtualRow.index}
													className="flex border-b border-gray-100 hover:bg-gray-50"
													style={{
														position: 'absolute',
														top: 0,
														transform: `translateY(${virtualRow.start}px)`,
														width: '100%',
														height: `${virtualRow.size}px`,
													}}
												>
													<div className="flex items-center px-4 py-2" style={{ width: '33.3333%' }}>
														{item.pcn}
													</div>
													<div className="flex items-center px-4 py-2" style={{ width: '33.3333%' }}>
														{item.status === 'PENDING' && <Spinner />}
														{item.status === 'OK' && <Icon name="check" className="text-green-500 h-4 w-4" />}
														{item.status === 'ERROR' && (
															<Tooltip tooltip={item.error ?? 'Něco se pokazilo...'}>
																<div>
																	<Icon name="xmark" className="text-red-500 h-4 w-4" />
																</div>
															</Tooltip>
														)}
													</div>
													<div className="flex items-center px-4 py-2" style={{ width: '33.3333%' }}>
														{item.error ?? EMPTY_VALUE}
													</div>
												</div>
											);
										})}
									</div>
								</div>
							</td>
						</tr>
					</TableBody>
				</Table>
			</div>
		</div>
	);
};
