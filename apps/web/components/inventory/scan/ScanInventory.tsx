import { CODE_PREFIX, MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { type ApiBody, type InventoryItemScan } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useEffect } from 'react';
import { useScan } from '../../../hooks/useScan';
import { useScanQueue } from '../../../hooks/useScanQueue';
import { VirtualizedScanTable } from './VirtualizedScanTable';

type Props = {
	inventory: ApiBody<'getInventories'>[number];
};

export const ScanInventory: FC<Props> = ({ inventory }) => {
	const inventoryId = inventory.id;

	const [scanCode, reset] = useScan();

	const { data: scannedItemsData } = apiHooks.useGetInventoryItems({
		params: { inventoryId },
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER, filter: { scannedAt: { ne: null } } },
	});
	const scannedItems: InventoryItemScan[] = (scannedItemsData?._data ?? []).map((item) => ({
		pcn: item.code,
		status: 'OK',
	}));

	const { queue, addToQueue } = useScanQueue(inventoryId, scannedItems);

	useEffect(() => {
		if (scanCode) {
			if (scanCode.startsWith(CODE_PREFIX.PRODUCT)) {
				addToQueue(scanCode.toUpperCase());
			}
			reset();
		}
	}, [scanCode, addToQueue, reset]);

	return <VirtualizedScanTable items={[...queue, ...scannedItems]} />;
};
