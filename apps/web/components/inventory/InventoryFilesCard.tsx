import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { Media } from '@pocitarna-nx-2023/ui';
import { formatInventoryCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { FileUploader } from '../FileUploader';

type Props = {
	inventory: ApiBody<'getInventory'>;
	disabled?: boolean;
};

export const InventoryFilesCard: FC<Props> = ({ inventory, disabled }) => {
	const { data: inventoryFilesData, invalidate: invalidateInventoryFiles } = apiHooks.useGetInventoryFiles({
		params: { inventoryId: inventory.id },
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER },
	});
	const inventoryFiles = inventoryFilesData?._data ?? [];
	const { mutateAsync: addFilesToInventory, isLoading: isAddingFiles } = apiHooks.useAddFilesToInventory(
		{ params: { inventoryId: inventory.id } },
		{ onSuccess: () => invalidateInventoryFiles() },
	);
	const { mutate: deleteFiles, isLoading: isDeletingFiles } = apiHooks.useDeleteFiles(undefined, {
		onSuccess: () => invalidateInventoryFiles(),
	});
	const { mutate: rotateImageFile, isLoading: isRotatingFile } = apiHooks.useRotateImageFile(undefined, {
		onSuccess: () => invalidateInventoryFiles(),
	});
	const { mutate: updateSequence } = apiHooks.useUpdateInventoryFileSequence(
		{ params: { inventoryId: inventory.id } },
		{ onSuccess: () => invalidateInventoryFiles() },
	);

	const CardInner = (
		<Media
			files={inventoryFiles.map(({ file }) => file)}
			moveTo={disabled ? undefined : (file, sequence) => updateSequence({ fileId: file.id, sequence })}
			onDelete={disabled ? undefined : (file) => deleteFiles({ ids: [file.id] })}
			onRotation={disabled ? undefined : (file, rotation) => rotateImageFile({ fileId: file.id, rotation })}
			isLoading={isAddingFiles || isDeletingFiles || isRotatingFile}
		/>
	);

	return (
		<FileUploader
			pageType="detail"
			inputName="files"
			entityCode={formatInventoryCode(inventory.code)}
			entityId={inventory.id}
			entityType="inventory"
			isAddingFiles={isAddingFiles}
			addFilesToEntity={addFilesToInventory}
			disabled={disabled}
		>
			{CardInner}
		</FileUploader>
	);
};
