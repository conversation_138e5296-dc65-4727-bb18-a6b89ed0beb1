import { EMPTY_VALUE } from '@pocitarna-nx-2023/config';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { type InventorySelection } from '../../pages/inventory/[inventoryId]';
import { AmountEnvelopeActions } from './verify/AmountEnvelopeActions';
import { InventoryTableRow } from './verify/InventoryTableRow';

type Props = {
	inventory: ApiBody<'getInventory'>;
	inventoryItem: ApiBody<'getInventoryItems'>[number];
	selection?: InventorySelection;
};

export const AmountEnvelopeDisplay: FC<Props> = ({ inventory, inventoryItem, selection }) => {
	const { productEnvelopeId, envelopeProductsExpectedCount, envelopeProductsRealCount } = inventoryItem;
	const isSelected = selection?.selectedEnvelope === productEnvelopeId;

	if (!productEnvelopeId) return null;

	return (
		<Table inCard>
			<TableHeader>
				<TableRow>
					<TableHead>Očekávané množství produktů v kartě</TableHead>
					<TableHead>Skutečné množství produktů v kartě</TableHead>
					<TableHead>Potvrzeno</TableHead>
					{inventory.status !== 'CLOSED' && <TableHead className="w-1" />}
				</TableRow>
			</TableHeader>
			<TableBody>
				<InventoryTableRow isSelected={isSelected}>
					<TableCell>{envelopeProductsExpectedCount}</TableCell>
					<TableCell className={getProductsRealCountClassName(inventoryItem)}>
						{envelopeProductsRealCount ?? EMPTY_VALUE}
					</TableCell>
					<TableCell>{inventoryItem.envelopeProductsCountWasConfirmed ? 'Ano' : 'Ne'}</TableCell>
					{inventory.status !== 'CLOSED' && (
						<TableCell className="w-1 text-right">
							<AmountEnvelopeActions
								inventoryId={inventory.id}
								inventoryItem={inventoryItem}
								disabled={inventoryItem.envelopeProductsCountWasConfirmed === true}
							/>
						</TableCell>
					)}
				</InventoryTableRow>
			</TableBody>
		</Table>
	);
};

const getProductsRealCountClassName = (inventoryItem: ApiBody<'getInventoryItems'>[number]) => {
	const { envelopeProductsExpectedCount, envelopeProductsRealCount } = inventoryItem;

	if (envelopeProductsExpectedCount == null || envelopeProductsRealCount == null) return undefined;
	if (envelopeProductsRealCount < envelopeProductsExpectedCount) return 'text-destructive';
	if (envelopeProductsRealCount > envelopeProductsExpectedCount) return 'text-info';
	return 'text-success';
};
