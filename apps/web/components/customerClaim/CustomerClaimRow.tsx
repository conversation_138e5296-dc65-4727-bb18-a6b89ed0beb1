import { CustomerClaimStatusMessage, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { Button, Stack, TableCell, TableRow } from '@pocitarna-nx-2023/ui';
import { formatDateTime, formatEnvelopeCode, formatProductCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import Link from 'next/link';
import { type FC, Fragment } from 'react';
import { useCustomerClaimData } from '../../hooks/useCustomerClaimData';
import { ProductDefectTypeDisplay } from '../ProductDefectTypeDisplay';
import { CustomerClaimCountdown } from './CustomerClaimCountdown';

type Props = {
	customerClaim: ApiBody<'getCustomerClaims'>[number];
};

export const CustomerClaimRow: FC<Props> = ({ customerClaim }) => {
	const { product, productDefects, allAffectedProducts } = useCustomerClaimData(customerClaim, 'all-affected-products');

	if (!product) return null;

	return (
		<TableRow>
			<TableCell>
				{allAffectedProducts.map((item) => {
					return (
						<Fragment key={item.id}>
							<Link className="text-link" href={`/product/${item.id}`}>
								{formatProductCode(item.code)}
							</Link>
							<br />
							{item.sn.toUpperCase()}
						</Fragment>
					);
				})}
			</TableCell>

			<TableCell>
				{formatDateTime(customerClaim.createdAt)} <br /> {customerClaim.createdBy?.name ?? NOT_AVAILABLE}
			</TableCell>
			<TableCell>
				{product.productEnvelope ? (
					<Stack gap={1}>
						{product.productCategory && (
							<Link className="text-link" href={`/product/envelope/${product.productEnvelope.id}`}>
								{formatEnvelopeCode(product.productCategory.codePrefix)(product.productEnvelope.code)}
							</Link>
						)}
						<Link className="text-link" href={`/product/envelope/${product.productEnvelope.id}`}>
							{product.productEnvelope.name}
						</Link>
					</Stack>
				) : (
					NOT_AVAILABLE
				)}
			</TableCell>
			<TableCell>
				{product.productCategory ? (
					<Link className="text-link" href={`/product/envelope/category/${product.productCategory.id}`}>
						{product.productCategory.name}
					</Link>
				) : (
					NOT_AVAILABLE
				)}
			</TableCell>
			<TableCell>
				{productDefects.length > 0
					? productDefects.map((productDefect, index, array) => (
							<Fragment key={productDefect.id}>
								<ProductDefectTypeDisplay productDefect={productDefect} />
								{index < array.length - 1 && (
									<>
										, <br />
									</>
								)}
							</Fragment>
						))
					: NOT_AVAILABLE}
			</TableCell>
			<TableCell>{CustomerClaimStatusMessage[customerClaim.status]}</TableCell>
			<TableCell>
				<CustomerClaimCountdown customerClaim={customerClaim} />
			</TableCell>

			<TableCell sticky="right">
				<Stack direction="row" gap={2} className="justify-end">
					<Button size="sm" asChild>
						<Link href={`/customer-claim/${customerClaim.id}`}>Detail</Link>
					</Button>
				</Stack>
			</TableCell>
		</TableRow>
	);
};
