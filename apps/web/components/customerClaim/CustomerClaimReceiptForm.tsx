import { CUSTOMER_CLAIM_STATUS_NAMES } from '@pocitarna-nx-2023/config';
import { Button, FormContext, Stack, toast } from '@pocitarna-nx-2023/ui';
import { type ApiBody, zDate } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { z } from 'zod';

type Props = {
	customerClaim: ApiBody<'getCustomerClaim'>;
	product: ApiBody<'getProduct'>;
};

export const CustomerClaimReceiptForm: FC<Props> = ({ customerClaim, product }) => {
	const { mutate, isLoading } = apiHooks.useUpdateCustomerClaim({ params: { customerClaimId: customerClaim.id } });
	const { invalidate: invalidateCustomerClaim } = apiHooks.useGetCustomerClaim(
		{ params: { customerClaimId: customerClaim.id } },
		{ enabled: false },
	);
	const { invalidate: invalidateFiles } = apiHooks.useGetPublicCustomerClaimFiles(
		{
			params: { customerClaimId: customerClaim.id },
		},
		{ enabled: false },
	);

	return (
		<Stack gap={4}>
			<FormContext
				schema={z.object({
					status: z.enum(CUSTOMER_CLAIM_STATUS_NAMES),
					productId: z.string().uuid(),
					receivedAt: zDate,
				})}
				defaultValues={{ status: 'RECEIVED' as const, productId: product.id, receivedAt: new Date() }}
				onSubmit={(data) => {
					mutate(
						{
							...data,
							receivedAt: new Date(),
						},
						{
							onSuccess: () => {
								toast.success('Zaktualizováno');
								invalidateCustomerClaim();
								invalidateFiles();
							},
						},
					);
				}}
			>
				{() => {
					return (
						<Stack gap={4}>
							{/* TODO: Add cosmetic defects after open PRs are merged */}
							{/* <CosmeticDefectsForm product={product} showGrade={false} /> */}

							<Button className="w-full" variant="default" type="submit" isLoading={isLoading}>
								Vytvořit přijmový reklamační protokol
							</Button>
						</Stack>
					);
				}}
			</FormContext>
		</Stack>
	);
};
