import { CUSTOMER_CLAIM_MAX_PROCESSING_DAYS } from '@pocitarna-nx-2023/config';
import { Card, CardContent, Stack } from '@pocitarna-nx-2023/ui';
import { getDaysLeft } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';

type Props = {
	customerClaim: ApiBody<'getCustomerClaim'>;
};

export const CustomerClaimCountdown: FC<Props> = ({ customerClaim }) => {
	if (!customerClaim.receivedAt) return null;

	const countdown = getDaysLeft(customerClaim.receivedAt, CUSTOMER_CLAIM_MAX_PROCESSING_DAYS);
	const countdownClassName = countdown < 3 ? 'text-destructive' : countdown < 7 ? 'text-warning' : undefined;

	return <span className={countdownClassName}>{countdown}</span>;
};

export const CustomerClaimCountdownCard: FC<Props> = ({ customerClaim }) => {
	if (!customerClaim.receivedAt) return null;

	return (
		<Card>
			<CardContent>
				<Stack gap={2} className="flex items-center justify-center">
					Do konce reklamace zbývá
					<p className="font-bold text-4xl">
						<CustomerClaimCountdown customerClaim={customerClaim} />
					</p>
				</Stack>
			</CardContent>
		</Card>
	);
};
