import { Button } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';

type Props = {
	customerClaim: ApiBody<'getCustomerClaim'>;
};

export const PdfProtocolLink: FC<Props> = ({ customerClaim }) => {
	const { data: pdfProtocolLinkData } = apiHooks.useGetCustomerClaimPdfProtocolLink(
		{
			params: { customerClaimId: customerClaim.id },
		},
		{ enabled: Boolean(customerClaim.receivedAt != null && customerClaim.status !== 'NEW') },
	);

	const pdfProtocolLink = pdfProtocolLinkData?._data;

	if (!pdfProtocolLink) return null;

	return (
		<Button variant="secondary" asChild>
			<a href={pdfProtocolLink} target="_blank">
				Vytisknout reklamační protokol (PDF)
			</a>
		</Button>
	);
};
