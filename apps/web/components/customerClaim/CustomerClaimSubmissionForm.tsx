import {
	CmpDeliveryMethodMessage,
	CUSTOMER_CLAIM_DELIVERY_METHODS,
	CUSTOMER_CLAIM_HANDLING_METHODS,
	CustomerClaimHandlingMethodMessage,
	CustomerDeliveryMethodMessage,
} from '@pocitarna-nx-2023/config';
import { Button, ComboboxControl, FormContext, InputControl, Stack, TextControl, useDialog } from '@pocitarna-nx-2023/ui';
import { type ApiBody, customerClaimCreate } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { useToggle } from 'rooks';
import { type z } from 'zod';
import { AddressInputs } from '../AddressInputs';
import { ContactInputs } from '../ContactInputs';
import { TemporaryFileUploader } from '../TemporaryFileUploader';

type Props = {
	email?: string;
	order: ApiBody<'getPublicEcommerceOrders'>[number];
	ecommerceOrderItemId: string;
	onSubmit: (data: z.infer<typeof customerClaimCreate>) => void;
	isLoading: boolean;
};

export const CustomerClaimSubmissionForm: FC<Props> = ({ email, order, ecommerceOrderItemId, onSubmit, isLoading }) => {
	const [isUploadingFile, toggleUploadingFile] = useToggle();
	const { closeDialog } = useDialog();

	return (
		<FormContext
			schema={customerClaimCreate}
			defaultValues={{
				message: '',
				handlingMethod: 'REPAIR',
				customerDeliveryMethod: 'SHIPMENT',
				cmpDeliveryMethod: 'SHIPMENT',
				address: {
					city: order.shippingAddress?.city ?? '',
					country: order.shippingAddress?.country ?? '',
					postalCode: order.shippingAddress?.postalCode ?? '',
					street: order.shippingAddress?.street ?? '',
					addressSpecification: order.shippingAddress?.addressSpecification ?? '',
				},
				contact: {
					email: email ?? '',
					name: order.contact?.name ?? '',
					phone: order.contact?.phone ?? '',
				},
				ecommerceOrderItem: { id: ecommerceOrderItemId },
				files: [] as string[],
			}}
			onSubmit={(data) => {
				onSubmit(data);
				closeDialog();
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<InputControl control={control} name="contact.email" label="E-mail" required />
					<TextControl control={control} name="message" label="Co nejpodrobněji popište závadu" required />
					<TemporaryFileUploader
						inputName="files"
						pageType="customerClaim"
						entityCode="customerClaim"
						hideSubmitButton={true}
						isUploadingForm={isLoading}
						isUploadingFile={isUploadingFile}
						toggleUploadingFile={toggleUploadingFile}
						withQrPhotoUpload={false}
					/>
					<ComboboxControl
						control={control}
						name="handlingMethod"
						items={CUSTOMER_CLAIM_HANDLING_METHODS.map((method) => ({
							value: method,
							label: CustomerClaimHandlingMethodMessage[method],
						}))}
						label="Preferovaný způsob vyřízení reklamace"
						hideSearch={true}
					/>
					<ComboboxControl
						control={control}
						name="customerDeliveryMethod"
						items={CUSTOMER_CLAIM_DELIVERY_METHODS.map((method) => ({
							value: method,
							label: CustomerDeliveryMethodMessage[method],
						}))}
						label="Způsob doručení"
						hideSearch={true}
					/>
					<ComboboxControl
						control={control}
						name="cmpDeliveryMethod"
						items={CUSTOMER_CLAIM_DELIVERY_METHODS.map((method) => ({
							value: method,
							label: CmpDeliveryMethodMessage[method],
						}))}
						label="Způsob vrácení"
						hideSearch={true}
					/>
					<AddressInputs />
					<ContactInputs variant="customer" />
					<Button className="w-full" variant="default" type="submit" isLoading={isLoading}>
						Vytvořit
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
