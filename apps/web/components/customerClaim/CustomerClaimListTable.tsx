import { type FilterRules, Table, TableBody, TableHead, TableHeader, TableRow, useDataTable } from '@pocitarna-nx-2023/ui';
import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, memo, useMemo } from 'react';
import { useSorting } from '../../contexts/SortingContext';
import { usePaging } from '../../hooks/usePaging';
import { Pagination } from '../Pagination/Pagination';
import { SortableColumn } from '../sorting/SortableColumn';
import { CustomerClaimRow } from './CustomerClaimRow';

type Props = {
	tabFilter: ListProps['filter'];
	filters: FilterRules;
};

export const ListTable: FC<Props> = ({ tabFilter, filters }) => {
	const [page, limit, setLimit] = usePaging();

	const filter = {
		...tabFilter,
		...(filters['status'] ? { status: { eq: filters['status'] } } : {}),
	};
	const { sorting } = useSorting();
	const table = useDataTable({});

	const { data } = apiHooks.useGetCustomerClaims({
		queries: { page, filter, sort: sorting, limit },
	});

	const customerClaims = useMemo(() => data?._data ?? [], [data?._data]);

	return (
		<>
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>PCN | SN</TableHead>
						<TableHead>Vytvořil</TableHead>
						<TableHead>Karta produktu</TableHead>
						<TableHead>Kategorie</TableHead>
						<TableHead>Typ závady</TableHead>
						<SortableColumn property="status">Stav</SortableColumn>
						<TableHead>Termín</TableHead>
						<TableHead sticky="right" />
					</TableRow>
				</TableHeader>
				<TableBody>
					{customerClaims.map((customerClaim) => (
						<CustomerClaimRow key={customerClaim.id} customerClaim={customerClaim} />
					))}
				</TableBody>
			</Table>

			<Pagination paging={data?._paging} rows={limit} setRows={setLimit} selectedItemsCount={table.selectedRows.length} />
		</>
	);
};

export const CustomerClaimListTable = memo(ListTable);
CustomerClaimListTable.displayName = 'CustomerClaimListTable';
