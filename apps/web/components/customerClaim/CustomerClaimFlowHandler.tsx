import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';

type Props = {
	product: ApiBody<'getProduct'>;
	productDefects: ApiBody<'getAllProductDefects'>;
	customerClaim: ApiBody<'getCustomerClaim'>;
	disabled: boolean;
};

export const CustomerClaimFlowHandler: FC<Props> = ({ customerClaim }) => {
	// const unsolvedDefects = productDefects.filter((defect) => defect.serviceTask?.status !== 'CLOSED');

	// const { mutate: updateCustomerClaim, isLoading: isUpdatingCustomerClaim } = apiHooks.useUpdateCustomerClaim({
	// 	params: { customerClaimId: customerClaim.id },
	// });

	// const { invalidate: invalidateProductDefects } = apiHooks.useGetAllProductDefects(undefined, { enabled: false });
	// const { invalidate: invalidateCustomerClaimHistory } = apiHooks.useGetCustomerClaimHistory(
	// 	{ params: { customerClaimId: customerClaim.id } },
	// 	{ enabled: false },
	// );
	// const { invalidate: invalidateCustomerClaim } = apiHooks.useGetCustomerClaim(
	// 	{ params: { customerClaimId: customerClaim.id } },
	// 	{ enabled: false },
	// );

	const { status } = customerClaim;

	if (status === 'NEW') return null;

	return null;

	// if (unsolvedDefects.length === 0) {
	// 	return (
	// 		<Button
	// 			disabled={disabled}
	// 			onClick={() => {
	// 				if (disabled) return;
	// 				updateCustomerClaim(
	// 					{
	// 						status: 'RESOLVED',
	// 						productId,
	// 					},
	// 					{
	// 						onSuccess: () => {
	// 							toast.success(`Upraveno do stavu ${CustomerClaimStatusMessage['RESOLVED']}`);
	// 							invalidateCustomerClaimHistory();
	// 							invalidateProductDefects();
	// 							invalidateCustomerClaim();
	// 						},
	// 					},
	// 				);
	// 			}}
	// 			isLoading={isUpdatingCustomerClaim}
	// 		>
	// 			Uzavřít reklamaci
	// 		</Button>
	// 	);
	// }

	// return <ProductDefectSolverDialog unsolvedDefects={unsolvedDefects} disabled={disabled} source="CUSTOMER_CLAIM" />;
};
