import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, Param<PERSON><PERSON>, ParamList } from '@pocitarna-nx-2023/ui';
import { formatDateTime } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';

type Props = {
	customerClaim: ApiBody<'getCustomerClaim'>;
};

export const OrderCard: FC<Props> = ({ customerClaim }) => {
	const order = customerClaim.ecommerceOrderItem.ecommerceOrder;

	return (
		<Card>
			<CardHeader>
				<h3 className="font-semibold leading-none tracking-tight">Objednávka</h3>
			</CardHeader>
			<CardContent>
				<ParamList>
					<ParamItem label="Číslo">{order.code}</ParamItem>
					<ParamItem label="Datum obj.">{formatDateTime(order.placedAt)}</ParamItem>
				</ParamList>
			</CardContent>
		</Card>
	);
};
