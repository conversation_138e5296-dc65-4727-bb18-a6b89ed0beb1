import { cn, Markdown } from '@pocitarna-nx-2023/ui';
import { cva, type VariantProps } from 'class-variance-authority';
import { type FC, useEffect, useRef, useState } from 'react';
import { useToggle } from 'rooks';

const expandableTextVariants = cva('flex', {
	variants: {
		maxLines: {
			1: 'line-clamp-1',
			2: 'line-clamp-2',
			3: 'line-clamp-3',
			6: 'line-clamp-6',
			12: 'line-clamp-12',
		},
	},
	defaultVariants: {
		maxLines: 3,
	},
});

type ExpandableTextVariants = VariantProps<typeof expandableTextVariants>;

type Props = {
	text: string;
} & ExpandableTextVariants;

export const ExpandableText: FC<Props> = ({ text, maxLines = 3 }) => {
	const [expanded, toggleExpanded] = useToggle(false);
	const [isOverflowing, setIsOverflowing] = useState(false);
	const textRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		const checkOverflow = () => {
			const el = textRef.current;
			if (el) {
				const hasOverflow = el.scrollHeight > el.clientHeight;
				setIsOverflowing(hasOverflow);
			}
		};

		checkOverflow();

		const observer = new ResizeObserver(checkOverflow);
		if (textRef.current) observer.observe(textRef.current);

		return () => observer.disconnect();
	}, [text]);

	return (
		<div
			ref={textRef}
			className={cn(
				'transition-all duration-300',
				expanded && 'cursor-zoom-out',
				isOverflowing && !expanded && 'cursor-zoom-in',
				!expanded && expandableTextVariants({ maxLines }),
			)}
			onClick={toggleExpanded}
		>
			<Markdown value={text} />
			{!expanded && isOverflowing && <span>...</span>}
		</div>
	);
};
