import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { useEnvelopeCode } from '../../hooks/useEnvelopeCode';

type Props = {
	productEnvelope: ApiBody<'getProductEnvelope'>;
};

export const EnvelopeName: FC<Props> = ({ productEnvelope }) => {
	const envelopeCode = useEnvelopeCode(productEnvelope);

	return (
		<>
			{envelopeCode}
			{productEnvelope.name ? ` ${productEnvelope.name}` : ''}
		</>
	);
};
