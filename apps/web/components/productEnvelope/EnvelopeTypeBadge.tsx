import { type EnvelopeType, EnvelopeTypeNames } from '@pocitarna-nx-2023/config';
import { Badge } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type ComponentProps, type FC } from 'react';

type Props = {
	productEnvelope: Pick<ApiBody<'getProductEnvelope'>, 'type'>;
};

const badgeTypeMap: Record<EnvelopeType, ComponentProps<typeof Badge>['variant']> = {
	NEW: 'info',
	USED: 'default',
	AMOUNT: 'secondary',
};

export const EnvelopeTypeBadge: FC<Props> = ({ productEnvelope }) => {
	const envelopeType = productEnvelope.type;
	return <Badge variant={badgeTypeMap[envelopeType]}>{EnvelopeTypeNames[productEnvelope.type]}</Badge>;
};
