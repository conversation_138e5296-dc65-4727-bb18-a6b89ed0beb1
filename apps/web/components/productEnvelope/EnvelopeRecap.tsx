import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { AttributeValuesOverviewByEnvelope } from '../AttributeValuesOverviewByEnvelope';
import { EnvelopeHeader } from '../batchCheck/smallTest/EnvelopeHeader';

type Props = {
	productEnvelope: ApiBody<'getProductEnvelope'>;
	withAttributes?: boolean;
};

export const EnvelopeRecap: FC<Props> = ({ productEnvelope, withAttributes = true }) => {
	return (
		<>
			<EnvelopeHeader envelope={productEnvelope} />

			{withAttributes && <AttributeValuesOverviewByEnvelope envelope={productEnvelope} include={['other'] as const} />}
		</>
	);
};
