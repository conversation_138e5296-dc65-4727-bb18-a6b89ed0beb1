import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { BatchStatusOverview } from '../batch/BatchStatusOverview';

type Props = {
	batch: ApiBody<'getBatch'>;
};

export const CheckSnOverview: FC<Props> = ({ batch }) => (
	<BatchStatusOverview
		date={batch.checkedSnAt}
		statusName="kontrole SN"
		dateLabel="Zkontrolovány SN"
		batch={batch}
		note={batch.checkedSnNote}
		user={batch.checkedSnBy}
	/>
);
