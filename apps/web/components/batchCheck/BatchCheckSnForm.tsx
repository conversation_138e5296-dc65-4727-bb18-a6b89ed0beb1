import { type BatchStatus, BatchStatusMessage, MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { <PERSON><PERSON>, FormContext, Stack, TextControl, toast } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useMemo } from 'react';
import { z } from 'zod';

type Props = {
	batch: ApiBody<'getBatch'>;
};

export const BatchCheckSnForm: FC<Props> = ({ batch }) => {
	const { invalidate: invalidateBatch } = apiHooks.useGetBatch({ params: { batchId: batch.id } }, { enabled: false });
	const { invalidate: invalidateBatchHistory } = apiHooks.useGetBatchHistory({ params: { batchId: batch.id } }, { enabled: false });
	const { mutate: setBatchCheckSnDate, isLoading } = apiHooks.useSetBatchCheckSnDate({ params: { batchId: batch.id } });
	const { invalidate: invalidateBatchesList } = apiHooks.useGetBatches({}, { enabled: false });
	const { data: productsData, invalidate: invalidateBatchProducts } = apiHooks.useGetBatchProducts({
		params: { batchId: batch.id },
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER },
	});

	const allProductsHaveSn = useMemo(() => (productsData?._data ?? []).every((product) => !!product.sn), [productsData?._data]);

	return (
		<FormContext
			schema={z.object({
				checkedSnNote: z.string(),
			})}
			defaultValues={{
				checkedSnNote: '',
			}}
			onSubmit={(data) => {
				const batchStatus: BatchStatus = batch.type === 'USED' ? 'TO_TEST' : 'CLOSED';

				setBatchCheckSnDate(
					{ status: batchStatus, checkedSnNote: data.checkedSnNote },
					{
						onSuccess: () => {
							invalidateBatchesList();
							invalidateBatchProducts();
							invalidateBatch();
							invalidateBatchHistory();

							if (batch.type === 'USED') {
								toast.success(`Stav várky byl změněn na ${BatchStatusMessage[batchStatus]}`);
							} else {
								toast.success('Zaktualizováno');
							}
						},
					},
				);
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<TextControl control={control} name="checkedSnNote" label="Poznámka ke kontrole várky" />

					<Button className="w-full" size="lg" type="submit" isLoading={isLoading} disabled={!allProductsHaveSn}>
						Ukončit kontrolu
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
