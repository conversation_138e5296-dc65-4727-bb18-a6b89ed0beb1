import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useState } from 'react';
import { LabelPrint } from '../../printer/LabelPrint';

type Props = {
	batchId: string;
	productEnvelopeIds: string[];
};

export const SmallTestPrint: FC<Props> = ({ batchId, productEnvelopeIds }) => {
	const [shouldPrint, setShouldPrint] = useState(false);

	const { data: printLabelsData, isFetching: isLoadingLabels } = apiHooks.useGetBatchSmallTestPrintLabels(
		{ params: { batchId }, queries: { productEnvelopeIds } },
		{ enabled: shouldPrint },
	);

	return (
		<LabelPrint
			buttonLabel="Vytisknout štítek varky"
			printLabels={printLabelsData?._data}
			printerLocation="TESTING"
			isLoadingLabels={isLoadingLabels}
			handleClick={() => setShouldPrint(true)}
		/>
	);
};
