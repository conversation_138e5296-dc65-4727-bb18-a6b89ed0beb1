import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import {
	Alert,
	AlertDescription,
	AlertTitle,
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	Icon,
	Table,
	TableBody,
	TableHead,
	TableHeader,
	TableRow,
} from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, memo, useEffect, useMemo, useRef } from 'react';
import { EnvelopeHeader } from './EnvelopeHeader';
import { SmallTestProductRow } from './SmallTestProductRow';

type Props = {
	batchId: string;
	envelope: ApiBody<'getProductEnvelope'>;
	isSelected: boolean;
	invalidateProducts: () => void;
};

export const ListTable: FC<Props> = ({ batchId, envelope, isSelected, invalidateProducts }) => {
	const sectionRef = useRef<HTMLDivElement>(null);

	const { data: productsData, isSuccess } = apiHooks.useGetProductEnvelopeProducts({
		params: { productEnvelopeId: envelope.id },
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER, filter: { batchId: { eq: batchId } } },
	});

	const products = useMemo(() => productsData?._data ?? [], [productsData?._data]);

	useEffect(() => {
		if (isSelected && sectionRef.current) {
			sectionRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
		}
	}, [isSelected]);

	if (isSuccess && !productsData?._data?.length) {
		return (
			<Alert variant="info">
				<Icon name="circle-info" />
				<AlertTitle>Žádné produkty.</AlertTitle>
				<AlertDescription>Pro zobrazení produktů zkuste upravit filtr.</AlertDescription>
			</Alert>
		);
	}

	return (
		<Card ref={sectionRef} className={isSelected ? 'border border-red-500 rounded-md p-4' : ''}>
			<CardHeader>
				<CardTitle>
					<EnvelopeHeader envelope={envelope} />
				</CardTitle>
			</CardHeader>
			<CardContent>
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Kód</TableHead>
							<TableHead>SN</TableHead>
							<TableHead>Stav</TableHead>
							<TableHead />
							<TableHead />
						</TableRow>
					</TableHeader>
					<TableBody>
						{products.map((product) => (
							<SmallTestProductRow key={product.id} product={product} invalidateProducts={invalidateProducts} />
						))}
					</TableBody>
				</Table>
			</CardContent>
		</Card>
	);
};

export const SmallTestProductsListTable = memo(ListTable);
SmallTestProductsListTable.displayName = 'SmallTestProductsListTable';
