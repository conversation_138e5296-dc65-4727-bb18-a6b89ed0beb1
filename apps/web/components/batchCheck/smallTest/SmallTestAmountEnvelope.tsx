import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { Card, CardContent, CardHeader, CardTitle, NumericInputControl } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useEffect, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { type z } from 'zod';
import { EnvelopeHeader } from './EnvelopeHeader';
import { type amountEnvelopesSchema } from './SmallTest';

type Props = {
	batchId: string;
	envelope: ApiBody<'getProductEnvelope'>;
};

export const SmallTestAmountEnvelope: FC<Props> = ({ batchId, envelope }) => {
	const [shouldShowWarning, setShouldShowWarning] = useState(false);

	const { control, watch, setError, clearErrors } = useFormContext<z.input<typeof amountEnvelopesSchema>>();
	const inputName = `data.${envelope.id}` as const;
	const selectedAmount = watch(inputName);

	const { data: productsInEnvelopeData } = apiHooks.useGetProductEnvelopeProducts({
		params: { productEnvelopeId: envelope.id },
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER, filter: { batchId: { eq: batchId } } },
	});

	const productsInEnvelope = useMemo(() => productsInEnvelopeData?._data ?? [], [productsInEnvelopeData?._data]);

	useEffect(() => {
		if (selectedAmount > productsInEnvelope.length) {
			setError(inputName, { message: `Nelze mít více produktů než je v kartě (${productsInEnvelope.length})` });
		} else {
			clearErrors(inputName);
		}
	}, [envelope.id, inputName, productsInEnvelope.length, selectedAmount, setError, clearErrors]);

	useEffect(() => {
		setShouldShowWarning(selectedAmount < productsInEnvelope.length);
	}, [productsInEnvelope.length, selectedAmount]);

	return (
		<Card>
			<CardHeader>
				<CardTitle>
					<EnvelopeHeader envelope={envelope} />
				</CardTitle>
			</CardHeader>
			<CardContent>
				<NumericInputControl
					numberFormat="integer"
					control={control}
					name={inputName}
					label="Počet produktů"
					onBlurCapture={() => {
						setShouldShowWarning(selectedAmount < productsInEnvelope.length);
					}}
				/>
				{shouldShowWarning && (
					<small className="text-warning">
						Zadal jste číslo menší, než je počet produktů importovaných v této kartě. Zbývající produkty budou odeslány do
						reklamace.
					</small>
				)}
			</CardContent>
		</Card>
	);
};
