import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import Link from 'next/link';
import { type FC } from 'react';
import { EnvelopeName } from '../../productEnvelope/EnvelopeName';
import { EnvelopeTypeBadge } from '../../productEnvelope/EnvelopeTypeBadge';

type Props = {
	envelope: ApiBody<'getProductEnvelope'>;
};

export const EnvelopeHeader: FC<Props> = ({ envelope }) => {
	return (
		<Link href={`/product/envelope/${envelope.id}`} className="text-link">
			<EnvelopeName productEnvelope={envelope} />
			<span className="ml-2">
				<EnvelopeTypeBadge productEnvelope={envelope} />
			</span>
		</Link>
	);
};
