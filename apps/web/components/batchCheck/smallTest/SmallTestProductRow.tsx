import { REQUIRED_FIELD } from '@pocitarna-nx-2023/config';
import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>alog<PERSON>ontent,
	<PERSON>alogHeader,
	DialogT<PERSON>le,
	DialogTrigger,
	FormContext,
	Icon,
	InputControl,
	Stack,
	TableCell,
	TableRow,
	toast,
	Tooltip,
	useDialog,
} from '@pocitarna-nx-2023/ui';
import { formatProductCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { type FC } from 'react';
import { z } from 'zod';
import { InformativeProductStatus } from '../../product/InformativeProductStatus';
import { SplitCodeDisplay } from '../../SplitCodeDisplay';

type Props = {
	product: ApiBody<'getAllProducts'>[number];
	invalidateProducts: () => void;
};

export const SmallTestProductRow: FC<Props> = ({ product, invalidateProducts }) => {
	return (
		<TableRow>
			<TableCell>
				<Link className="text-link" href={`/product/${product.id}`}>
					{formatProductCode(product.code)}
				</Link>
			</TableCell>
			<TableCell>
				<SplitCodeDisplay code={product.sn} />
			</TableCell>
			<TableCell>
				<InformativeProductStatus product={product} />
			</TableCell>
			<TableCell>{product.code?.code != null && <Icon name="check" />}</TableCell>
			<TableCell sticky="right">
				<Dialog>
					<DialogTrigger asChild>
						<Tooltip tooltip="Upravit SN">
							<Button size="sm" width="icon" variant="outline">
								<Icon name="pencil" className="w-4 h-4" />
							</Button>
						</Tooltip>
					</DialogTrigger>
					<DialogContent size="lg">
						<DialogHeader>
							<DialogTitle>Přiřaďte SN</DialogTitle>
						</DialogHeader>
						<AssignSnModal product={product} invalidateProducts={invalidateProducts} />
					</DialogContent>
				</Dialog>
			</TableCell>
		</TableRow>
	);
};

const AssignSnModal: FC<Props> = ({ product, invalidateProducts }) => {
	const { closeDialog } = useDialog();

	const { mutate: updateProductSn, isLoading } = apiHooks.usePairSmallTestProduct(
		{
			params: { batchId: product.batchId as string },
		},
		{
			onSuccess: () => {
				toast.success('Zaktualizováno');
				invalidateProducts();
				closeDialog();
			},
		},
	);

	return (
		<FormContext
			schema={z.object({
				sn: z.string().min(1, { message: REQUIRED_FIELD }),
			})}
			defaultValues={{ sn: product.sn ?? '' }}
			onSubmit={(data) => {
				updateProductSn({ productId: product.id, envelopeId: product.productEnvelopeId as string, sn: data.sn, productCode: null });
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<InputControl control={control} name="sn" />

					<Button type="submit" isLoading={isLoading}>
						Uložit
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
