import { <PERSON>, CardContent, <PERSON>Header, <PERSON><PERSON><PERSON><PERSON>, Stack } from '@pocitarna-nx-2023/ui';
import { type FC } from 'react';
import { SmallTestPrint } from './SmallTestPrint';

type Props = {
	batchId: string;
	productEnvelopeIds: string[];
};

export const SmallTestActions: FC<Props> = ({ batchId, productEnvelopeIds }) => {
	return (
		<Card sticky>
			<CardHeader>
				<CardTitle>Akce</CardTitle>
			</CardHeader>
			<CardContent>
				<Stack gap={4}>
					<SmallTestPrint batchId={batchId} productEnvelopeIds={productEnvelopeIds} />
				</Stack>
			</CardContent>
		</Card>
	);
};
