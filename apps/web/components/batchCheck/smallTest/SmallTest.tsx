import { CODE_PREFIX, MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import {
	Button,
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	FormContext,
	type FormContextRef,
	Stack,
	toast,
} from '@pocitarna-nx-2023/ui';
import { formatEnvelopeCode, splitEnvelopesByType } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { type FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { z } from 'zod';
import { useScan } from '../../../hooks/useScan';
import { SmallTestAmountEnvelope } from './SmallTestAmountEnvelope';
import { SmallTestProductsListTable } from './SmallTestProductsListTable';

type Props = {
	batch: ApiBody<'getBatch'>;
	envelopesInBatch: ApiBody<'getAllProductEnvelopes'>;
};

export const amountEnvelopesSchema = z.object({
	data: z.record(z.string().uuid(), z.number()),
});

export const SmallTest: FC<Props> = ({ batch, envelopesInBatch }) => {
	const [scanCode, reset] = useScan(true);
	const [scannedEnvelopeId, setScannedEnvelopeId] = useState<null | string>(null);
	const [selectedProductId, setSelectedProductId] = useState<null | string>(null);
	const [error, setError] = useState<null | string>(null);

	const queryClient = useQueryClient();
	const formRef = useRef<FormContextRef<typeof amountEnvelopesSchema>>(null);

	const { NEW: newEnvelopes, AMOUNT: amountEnvelopes } = splitEnvelopesByType(envelopesInBatch);

	const { data: categoriesData } = apiHooks.useGetProductCategoriesList({
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER },
	});

	const categoriesPrefixes = useMemo(() => {
		return (categoriesData?._data ?? []).map((category) => category.codePrefix);
	}, [categoriesData?._data]);
	const { invalidate: invalidateBatchProductCodes } = apiHooks.useGetBatchProductCodes(
		{
			params: { batchId: batch.id },
		},
		{ enabled: false },
	);

	const { invalidate: invalidateBatchProducts } = apiHooks.useGetBatchProducts(
		{
			params: { batchId: batch.id },
		},
		{ enabled: false },
	);

	const { invalidate: invalidateBatch } = apiHooks.useGetBatch(
		{
			params: { batchId: batch.id },
		},
		{ enabled: false },
	);

	// TODO: check performance and improve if necessary
	const invalidateProducts = () => {
		invalidateBatchProducts();
		invalidateEnvelopeProducts();
	};

	const { mutate: pairProduct } = apiHooks.usePairSmallTestProduct(
		{
			params: { batchId: batch.id },
		},
		{
			onSuccess: (data) => {
				setSelectedProductId(data._data);
				invalidateProducts();
				invalidateBatchProductCodes();
				toast.success('Zaktualizováno');
			},
			onError: (error) => {
				if (error instanceof AxiosError && error.response) {
					setError(error.response.data._error.message);
				}
			},
		},
	);

	const { mutate: finishSmallTest, isLoading: isFinishingSmallTest } = apiHooks.useFinishSmallTest(
		{
			params: { batchId: batch.id },
		},
		{
			onSuccess: () => {
				invalidateBatch();
				toast.success('Hotovo');
			},
		},
	);

	const getEnvelopeProductsQueryKey = apiHooks.getKeyByAlias('getProductEnvelopeProducts');
	const invalidateEnvelopeProducts = () => queryClient.invalidateQueries(getEnvelopeProductsQueryKey);

	const processScan = useCallback(
		(scanCode: string) => {
			if (!scanCode) return;

			// Envelope code
			if (categoriesPrefixes.some((prefix) => scanCode.startsWith(prefix))) {
				const match = envelopesInBatch.find(
					(envelope) => formatEnvelopeCode(envelope.productCategory?.codePrefix)(envelope.code) === scanCode,
				);
				if (match) {
					setScannedEnvelopeId(match.id);
				} else {
					setError('Nepodařilo se nalézt kartu produktu');
				}

				reset();
				return;
			}

			if (!scannedEnvelopeId) {
				setError('Nejdříve načti kód karty');
				reset();
				return;
			}

			// PCN
			if (scanCode.startsWith(`${CODE_PREFIX.PRODUCT}0`)) {
				pairProduct({ envelopeId: scannedEnvelopeId, productCode: scanCode, sn: null, productId: null });
				reset();
				return;
			}

			// SN
			if (!selectedProductId) {
				setError('Nejdříve načti kód produktu');
				reset();
				return;
			}

			if (scanCode.length < 1) {
				setError('Prosím, uveďte platné sériové číslo.');
				reset();
				return;
			} else {
				pairProduct({ envelopeId: scannedEnvelopeId, productId: selectedProductId, sn: scanCode, productCode: null });
				reset();
				return;
			}
		},
		[categoriesPrefixes, envelopesInBatch, reset, pairProduct, scannedEnvelopeId, selectedProductId],
	);

	useEffect(() => {
		if (!scanCode) return;
		processScan(scanCode);
	}, [scanCode, processScan]);

	const defaultValues = useMemo(() => {
		return amountEnvelopes.reduce(
			(acc, envelope) => {
				acc[envelope.id] = 0;
				return acc;
			},
			{} as Record<string, number>,
		);
	}, [amountEnvelopes]);

	return (
		<FormContext
			ref={formRef}
			schema={amountEnvelopesSchema}
			defaultValues={{
				data: defaultValues,
			}}
			onSubmit={(data) => {
				const dataToSend = Object.entries(data.data).map(([envelopeId, amount]) => ({ envelopeId, productsAmount: amount }));
				finishSmallTest(dataToSend);
			}}
		>
			{() => (
				<Stack gap={8}>
					{amountEnvelopes.map((envelope) => {
						return <SmallTestAmountEnvelope key={envelope.id} batchId={batch.id} envelope={envelope} />;
					})}
					{newEnvelopes.map((envelope) => {
						return (
							<SmallTestProductsListTable
								key={envelope.id}
								batchId={batch.id}
								envelope={envelope}
								isSelected={scannedEnvelopeId === envelope.id}
								invalidateProducts={invalidateProducts}
							/>
						);
					})}
					<Button
						isLoading={isFinishingSmallTest}
						type="submit"
						disabled={Object.keys(formRef?.current?.formState?.errors ?? {}).length > 0}
					>
						Dokončit testování
					</Button>
					<Dialog open={error != null} onOpenChange={() => setError(null)}>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>Něco se pokazilo...</DialogTitle>
							</DialogHeader>
							<DialogDescription>{error}</DialogDescription>
							<DialogFooter>
								<Button
									onClick={() => {
										setError(null);
									}}
									variant="secondary"
								>
									Zkusit znovu
								</Button>
							</DialogFooter>
						</DialogContent>
					</Dialog>
				</Stack>
			)}
		</FormContext>
	);
};
