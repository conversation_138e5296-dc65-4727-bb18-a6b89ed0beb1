import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { useBatchProductsByCategory } from '../../hooks/useBatchProductsByCategory';
import { ListCategoriesWithAttributesCoupled } from '../categoriesWithAttributes/ListCategoriesWithAttributesCoupled';

type Props = { batch: ApiBody<'getBatch'> };

export const BatchCheckSnProductsTable: FC<Props> = ({ batch }) => {
	const { productsByCategory } = useBatchProductsByCategory(batch.id);

	return (
		<>
			<h2 className="h1 mb-4">Produkty</h2>
			<ListCategoriesWithAttributesCoupled batch={batch} productsByCategory={productsByCategory} stage="check-sn" />
		</>
	);
};
