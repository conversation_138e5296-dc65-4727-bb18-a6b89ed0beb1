import { getInflection } from '@pocitarna-nx-2023/utils';
import { type ApiPaging } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';

type Props = {
	paging: ApiPaging | undefined;
	selectedItemsCount?: number;
};

export const TableItemsCount: FC<Props> = ({ paging, selectedItemsCount }) => {
	if (!paging) return null;

	const totalCount = paging.total;

	return (
		<p>
			{formatTableCount(totalCount, 'total')}
			{selectedItemsCount ? formatTableCount(selectedItemsCount, 'selected') : ''}
		</p>
	);
};

const formatTableCount = (count: number, scope: 'total' | 'selected') => {
	const isTotal = scope === 'total';
	const inflectedCount = getInflection(isTotal ? 'totalTableItems' : 'selectedTableItems', count);
	const toPrepend = isTotal ? '' : ', ';
	return (
		<span className="text-sm font-medium">
			{toPrepend}
			<strong>{count}</strong> {inflectedCount} {isTotal ? <span className="max-lg:hidden">celkem</span> : ''}
		</span>
	);
};
