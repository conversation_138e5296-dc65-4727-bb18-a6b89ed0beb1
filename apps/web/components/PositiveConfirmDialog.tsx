import {
	Button,
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@pocitarna-nx-2023/ui';
import { type FC } from 'react';

type Props = {
	open: boolean;
	toggleOpen: () => void;
	title: string;
	message: string;
	handleConfirm: () => void;
	isLoading: boolean;
};

export const PositiveConfirmDialog: FC<Props> = ({ open, toggleOpen, title, message, handleConfirm, isLoading }) => {
	const onConfirm = () => {
		handleConfirm();
		toggleOpen();
	};

	return (
		<Dialog open={open} onOpenChange={toggleOpen}>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
				</DialogHeader>
				<DialogDescription>{message}</DialogDescription>
				<DialogFooter>
					<DialogClose asChild>
						<Button variant="ghost">Z<PERSON>ět</Button>
					</DialogClose>
					<Button autoFocus onClick={onConfirm} isLoading={isLoading}>
						Pokračovat
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};
