import { cn } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';

type Props = {
	productDefect: ApiBody<'getProductDefects'>[number];
};

export const ProductDefectTypeDisplay: FC<Props> = ({ productDefect }) => {
	const { data: defectTypeData } = apiHooks.useGetProductDefectType({ params: { defectId: productDefect.id } });

	if (defectTypeData?._data) {
		return <span className={cn(productDefect.serviceTaskId != null && 'text-success')}>{defectTypeData?._data?.name}</span>;
	}

	return null;
};
