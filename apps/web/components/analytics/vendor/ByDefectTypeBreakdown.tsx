import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { ErrorRateView } from '../ErrorRateView';

type Props = {
	byDefectType: ApiBody<'getVendorAnalytics'>['totals']['byDefectType'];
};

type ByDefectTypeItem = ApiBody<'getVendorAnalytics'>['totals']['byDefectType'][number];

export const ByDefectTypeBreakdown: FC<Props> = ({ byDefectType }) => {
	const total = byDefectType.reduce<number>((acc: number, curr: ByDefectTypeItem) => acc + curr.serviceCases + curr.warrantyClaims, 0);
	const sorted = byDefectType.toSorted((a: ByDefectTypeItem, b: ByDefectTypeItem) => {
		const ratioA = (a.serviceCases + a.warrantyClaims) / total;
		const ratioB = (b.serviceCases + b.warrantyClaims) / total;
		return ratioB - ratioA;
	});

	return (
		<Table>
			<TableHeader>
				<TableRow>
					<TableHead>Typ závady</TableHead>
					<TableHead>Servis</TableHead>
					<TableHead>Dod. reklamace</TableHead>
					<TableHead>Chybovost</TableHead>
				</TableRow>
			</TableHeader>
			<TableBody>
				{sorted.map(({ name, serviceCases, warrantyClaims }: ByDefectTypeItem) => (
					<TableRow key={name}>
						<>
							<TableCell>{name}</TableCell>
							<TableCell>{serviceCases}</TableCell>
							<TableCell>{warrantyClaims}</TableCell>
							<TableCell>
								<ErrorRateView total={total} errors={serviceCases + warrantyClaims} />
							</TableCell>
						</>
					</TableRow>
				))}
			</TableBody>
		</Table>
	);
};
