import {
	<PERSON>,
	<PERSON>H<PERSON>er,
	CardT<PERSON>le,
	Table,
	TableBody,
	TableCell,
	TableFooter,
	TableHead,
	TableHeader,
	TableRow,
} from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC, memo } from 'react';
import { ErrorRateView } from '../ErrorRateView';
import { DefectsBreakdownView } from './DefectsBreakdownView';
import { VendorRow } from './VendorRow';

type Props = {
	title: string;
	categories: ApiBody<'getVendorAnalytics'>['categories'];
	vendors: ApiBody<'getVendorAnalytics'>['vendors'];
	totals: ApiBody<'getVendorAnalytics'>['totals'];
};

type TotalsByCategory = ApiBody<'getVendorAnalytics'>['totals']['byCategory'][number];

const ListTable: FC<Props> = ({ title, categories, vendors, totals }) => {
	return (
		<Card>
			<CardHeader>
				<CardTitle>{title}</CardTitle>
			</CardHeader>
			<Table expandable title={title}>
				<TableHeader>
					<TableRow>
						<TableHead>Dodavatel</TableHead>
						{categories.map((category: ApiBody<'getVendorAnalytics'>['categories'][number]) => (
							<TableHead key={category.id}>{category.name}</TableHead>
						))}
						<TableHead>Celkový počet testů</TableHead>
						<TableHead>Chybovost</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{vendors.map((vendor: ApiBody<'getVendorAnalytics'>['vendors'][number]) => (
						<VendorRow key={vendor.id} vendor={vendor} categories={categories} />
					))}
				</TableBody>
				<TableFooter>
					<TableRow>
						<TableCell>
							<strong>Celkový počet reklamací</strong>
						</TableCell>
						{categories.map((category: ApiBody<'getVendorAnalytics'>['categories'][number]) => {
							const categoryAmount =
								totals.byCategory.find((entry: TotalsByCategory) => entry.categoryId === category.id)?.total ?? 0;
							const withoutDefects =
								categoryAmount -
								(totals.byCategory.find((entry: TotalsByCategory) => entry.categoryId === category.id)?.defects ?? 0);

							return (
								<TableCell key={category.id}>
									<strong>
										<DefectsBreakdownView
											total={categoryAmount}
											withoutDefects={withoutDefects}
											withDefectsToStock={
												totals.byCategory.find((entry: TotalsByCategory) => entry.categoryId === category.id)
													?.defectStats.straightToStock ?? 0
											}
											withDefectsToService={
												totals.byCategory.find((entry: TotalsByCategory) => entry.categoryId === category.id)
													?.defectStats.afterTestingWithServiceCase ?? 0
											}
											withDefectsToWarrantyClaim={
												totals.byCategory.find((entry: TotalsByCategory) => entry.categoryId === category.id)
													?.defectStats.afterTestingWithWarrantyClaim ?? 0
											}
											byDefectType={
												totals.byCategory.find((entry: TotalsByCategory) => entry.categoryId === category.id)
													?.byDefectType
											}
										/>
									</strong>
								</TableCell>
							);
						})}
						<TableCell>
							<strong>
								<DefectsBreakdownView
									total={totals.total}
									withoutDefects={totals.total - totals.defects}
									withDefectsToStock={totals.defectStats.straightToStock}
									withDefectsToService={totals.defectStats.afterTestingWithServiceCase}
									withDefectsToWarrantyClaim={totals.defectStats.afterTestingWithWarrantyClaim}
									byDefectType={totals.byDefectType}
								/>
							</strong>
						</TableCell>
						<TableCell>
							<strong>
								<ErrorRateView total={totals.total} errors={totals.defects} />
							</strong>
						</TableCell>
					</TableRow>
				</TableFooter>
			</Table>
		</Card>
	);
};

export const VendorTable = memo(ListTable);
VendorTable.displayName = 'VendorTable';
