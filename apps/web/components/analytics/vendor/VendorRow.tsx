import { TableCell, TableRow } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { ErrorRateView } from '../ErrorRateView';
import { DefectsBreakdownView } from './DefectsBreakdownView';

type Props = {
	vendor: ApiBody<'getVendorAnalytics'>['vendors'][number];
	categories: ApiBody<'getVendorAnalytics'>['categories'];
};

type CategoryStats = ApiBody<'getVendorAnalytics'>['vendors'][number]['categoryStats'][number];

export const VendorRow: FC<Props> = ({ vendor, categories }) => {
	return (
		<TableRow>
			<TableCell>{vendor.name}</TableCell>
			{categories.map((category: ApiBody<'getVendorAnalytics'>['categories'][number]) => {
				const categoryAmount = vendor.categoryStats.find((entry: CategoryStats) => entry.categoryId === category.id)?.total ?? 0;
				const withoutDefects =
					categoryAmount - (vendor.categoryStats.find((entry: CategoryStats) => entry.categoryId === category.id)?.defects ?? 0);

				return (
					<TableCell key={category.id}>
						<DefectsBreakdownView
							total={categoryAmount}
							withoutDefects={withoutDefects}
							withDefectsToStock={
								vendor.categoryStats.find((entry: CategoryStats) => entry.categoryId === category.id)?.defectStats
									.straightToStock ?? 0
							}
							withDefectsToService={
								vendor.categoryStats.find((entry: CategoryStats) => entry.categoryId === category.id)?.defectStats
									.afterTestingWithServiceCase ?? 0
							}
							withDefectsToWarrantyClaim={
								vendor.categoryStats.find((entry: CategoryStats) => entry.categoryId === category.id)?.defectStats
									.afterTestingWithWarrantyClaim ?? 0
							}
						/>
					</TableCell>
				);
			})}
			<TableCell>
				<DefectsBreakdownView
					total={vendor.totalTests}
					withoutDefects={vendor.totalTests - vendor.totalDefects}
					withDefectsToStock={vendor.defectStats.straightToStock}
					withDefectsToService={vendor.defectStats.afterTestingWithServiceCase}
					withDefectsToWarrantyClaim={vendor.defectStats.afterTestingWithWarrantyClaim}
					byDefectType={vendor.byDefectType}
				/>
			</TableCell>

			<TableCell>
				<ErrorRateView total={vendor.totalTests} errors={vendor.totalDefects} />
			</TableCell>
		</TableRow>
	);
};
