import { EMPTY_VALUE } from '@pocitarna-nx-2023/config';
import { cn, Dialog, DialogContent, DialogHeader, DialogTitle, Tooltip } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC, useCallback } from 'react';
import { useToggle } from 'rooks';
import { ByDefectTypeBreakdown } from './ByDefectTypeBreakdown';

type Props = {
	total: number;
	withoutDefects: number;
	withDefectsToStock: number;
	withDefectsToService: number;
	withDefectsToWarrantyClaim: number;
	byDefectType?: ApiBody<'getVendorAnalytics'>['totals']['byDefectType'];
};

export const DefectsBreakdownView: FC<Props> = ({
	total,
	withoutDefects,
	withDefectsToStock,
	withDefectsToService,
	withDefectsToWarrantyClaim,
	byDefectType,
}) => {
	const shouldShowByDefectTypeModals = byDefectType != null && byDefectType.length > 0;
	const [categoryModalIsOpen, toggleCategoryModal] = useToggle();

	const onClick = useCallback(() => {
		if (!shouldShowByDefectTypeModals) return;

		toggleCategoryModal();
	}, [shouldShowByDefectTypeModals, toggleCategoryModal]);

	return (
		<>
			<Tooltip
				tooltip={
					<span>
						Otestované produkty <br />
						Do naskladnění <br />
						Se servisem <br />
						S dod. reklamací <br />
					</span>
				}
			>
				<span className={cn(shouldShowByDefectTypeModals && 'cursor-pointer')} onClick={onClick}>
					<Amount quantity={total} />
					{' | '}
					<Amount quantity={withDefectsToStock + withoutDefects} />
					{' | '}
					<Amount className={cn(withDefectsToService > 0 && 'text-destructive')} quantity={withDefectsToService} />
					{' | '}
					<Amount className={cn(withDefectsToWarrantyClaim > 0 && 'text-destructive')} quantity={withDefectsToWarrantyClaim} />
				</span>
			</Tooltip>
			{shouldShowByDefectTypeModals && (
				<Dialog open={categoryModalIsOpen} onOpenChange={toggleCategoryModal}>
					<DialogContent size="lg">
						<DialogHeader>
							<DialogTitle>Přehled servisů a reklamací</DialogTitle>
						</DialogHeader>
						<ByDefectTypeBreakdown byDefectType={byDefectType} />
					</DialogContent>
				</Dialog>
			)}
		</>
	);
};

const Amount: FC<{ quantity: number; className?: string }> = ({ quantity, className }) => {
	if (quantity === 0) return EMPTY_VALUE;

	return <span className={className}>{quantity}</span>;
};
