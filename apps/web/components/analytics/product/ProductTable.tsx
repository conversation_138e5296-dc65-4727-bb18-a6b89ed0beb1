import { Card, type FilterRules, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC, memo, useMemo } from 'react';

type Props = {
	title: string;
	categoryData: ApiBody<'getProductAnalytics'>[number]['data'];
	filters: FilterRules;
};
const ListTable: FC<Props> = ({ title, categoryData, filters }) => {
	const rowsToShow: { identifier: string; count: number; manufacturer: string; model: string; caseSize: string | undefined }[] = useMemo(
		() =>
			(
				categoryData as { identifier: string; count: number; manufacturer: string; model: string; caseSize: string | undefined }[]
			).filter((entry) => {
				if (
					filters['manufacturer'] &&
					!entry.manufacturer.toLowerCase().includes((filters['manufacturer'] as string).toLowerCase())
				)
					return false;
				if (filters['model'] && !entry.identifier.toLowerCase().includes((filters['model'] as string).toLowerCase())) return false;
				if (filters['caseSize'] && !entry.identifier.toLowerCase().includes((filters['caseSize'] as string).toLowerCase()))
					return false;
				return true;
			}),
		[categoryData, filters],
	);

	return (
		<Card>
			<Table expandable title={title}>
				<TableHeader>
					<TableRow>
						<TableHead>Produkt</TableHead>
						<TableHead>Počet</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{rowsToShow.map((entry) => (
						<ProductRow key={entry.identifier} item={entry} />
					))}
				</TableBody>
			</Table>
		</Card>
	);
};

export const ProductTable = memo(ListTable);
ProductTable.displayName = 'ProductTable';

const ProductRow: FC<{ item: ApiBody<'getProductAnalytics'>[number]['data'][number] }> = ({ item }) => {
	const { identifier, count } = item;

	return (
		<TableRow>
			<TableCell>{identifier}</TableCell>
			<TableCell>{count}</TableCell>
		</TableRow>
	);
};
