import { TableCell, TableRow } from '@pocitarna-nx-2023/ui';
import { formatNumber } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { type DateRange } from 'react-day-picker';
import { ErrorRateView } from '../ErrorRateView';
import { CountView } from './CountView';

type Props = {
	range: DateRange | undefined;
	tester: ApiBody<'getTestingAnalytics'>['testers'][number];
	categories: ApiBody<'getTestingAnalytics'>['categories'];
	withFixes: boolean;
};

type Category = ApiBody<'getTestingAnalytics'>['categories'][number];

export const TestingRow: FC<Props> = ({ tester, categories, range, withFixes }) => {
	return (
		<TableRow>
			<TableCell>{tester.name}</TableCell>
			{categories.map((category: Category) => {
				const categoryAmount =
					tester.categoryStats.find((entry: { categoryId: string }) => entry.categoryId === category.id)?.total ?? 0;
				const fixesAmount =
					tester.categoryStats.find((entry: { categoryId: string }) => entry.categoryId === category.id)?.fixes ?? 0;

				return (
					<TableCell key={category.id}>
						<CountView total={categoryAmount} fixes={fixesAmount} withFixes={withFixes} range={range} />
					</TableCell>
				);
			})}
			<TableCell>{formatNumber(tester.dailyAverage, 0, 0)}</TableCell>
			<TableCell>
				<CountView
					total={tester.totalTests}
					fixes={tester.totalFixes}
					withFixes={withFixes}
					testerForBreakdown={tester.id}
					range={range}
				/>
			</TableCell>
			{withFixes && (
				<TableCell>
					<ErrorRateView total={tester.totalTests} errors={tester.totalFixes} />
				</TableCell>
			)}
		</TableRow>
	);
};
