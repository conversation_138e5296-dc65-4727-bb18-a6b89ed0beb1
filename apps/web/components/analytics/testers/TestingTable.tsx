import {
	<PERSON>,
	CardHeader,
	CardTitle,
	Table,
	TableBody,
	TableCell,
	TableFooter,
	TableHead,
	TableHeader,
	TableRow,
} from '@pocitarna-nx-2023/ui';
import { formatNumber } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC, memo } from 'react';
import { type DateRange } from 'react-day-picker';
import { ErrorRateView } from '../ErrorRateView';
import { CountView } from './CountView';
import { TestingRow } from './TestingRow';

type Props = {
	range: DateRange | undefined;
	title: string;
	categories: ApiBody<'getTestingAnalytics'>['categories'];
	testers: ApiBody<'getTestingAnalytics'>['testers'];
	totals: ApiBody<'getTestingAnalytics'>['totals'];
	withFixes?: boolean;
};

type Category = ApiBody<'getTestingAnalytics'>['categories'][number];
type TotalByCategory = ApiBody<'getTestingAnalytics'>['totals']['byCategory'][number];

const ListTable: FC<Props> = ({ range, title, categories, testers, totals, withFixes = false }) => {
	return (
		<Card>
			<CardHeader>
				<CardTitle>{title}</CardTitle>
			</CardHeader>
			<Table expandable title={title} inCard>
				<TableHeader>
					<TableRow>
						<TableHead>Tester</TableHead>
						{(categories as { id: string; name: string }[]).map((category) => (
							<TableHead key={category.id}>{category.name}</TableHead>
						))}
						<TableHead>Denní průměr</TableHead>
						<TableHead>Celkový počet testů</TableHead>
						{withFixes && <TableHead>Chybovost</TableHead>}
					</TableRow>
				</TableHeader>
				<TableBody>
					{testers.map(
						(tester: {
							id: string;
							name: string;
							totalTests: number;
							totalFixes: number;
							categoryStats: { categoryId: string; total: number; fixes: number }[];
							dailyAverage: number;
						}) => (
							<TestingRow key={tester.id} tester={tester} categories={categories} range={range} withFixes={withFixes} />
						),
					)}
				</TableBody>
				<TableFooter>
					<TableRow>
						<TableCell>
							<strong>Celkem</strong>
						</TableCell>
						{categories.map((category: Category) => {
							const categoryAmount =
								totals.byCategory.find((entry: TotalByCategory) => entry.categoryId === category.id)?.total ?? 0;
							const fixesByCategory =
								totals.byCategory.find((entry: TotalByCategory) => entry.categoryId === category.id)?.fixes ?? 0;
							return (
								<TableCell key={category.id}>
									<strong>
										<CountView total={categoryAmount} fixes={fixesByCategory} withFixes={withFixes} range={range} />
									</strong>
								</TableCell>
							);
						})}
						<TableCell>
							<strong>{formatNumber(totals.dailyAverage, 0, 0)}</strong>
						</TableCell>
						<TableCell>
							<strong>
								<CountView
									total={totals.total}
									fixes={totals.fixes}
									withFixes={withFixes}
									testerForBreakdown="ALL"
									range={range}
								/>
							</strong>
						</TableCell>
						{withFixes && (
							<TableCell>
								<strong>
									<ErrorRateView total={totals.total} errors={totals.fixes} />
								</strong>
							</TableCell>
						)}
					</TableRow>
				</TableFooter>
			</Table>
		</Card>
	);
};

export const TestingTable = memo(ListTable);
TestingTable.displayName = 'TestingTable';
