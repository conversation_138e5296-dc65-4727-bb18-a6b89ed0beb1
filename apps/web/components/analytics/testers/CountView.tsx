import { cn, Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@pocitarna-nx-2023/ui';
import { type FC } from 'react';
import { type DateRange } from 'react-day-picker';
import { useToggle } from 'rooks';
import { FixedAttributesBreakdownView } from './FixedAttributesBreakdownView';

type Props = {
	range: DateRange | undefined;
	total: number;
	fixes: number;
	withFixes: boolean;
	testerForBreakdown?: string;
};

export const CountView: FC<Props> = ({ range, total, fixes, withFixes, testerForBreakdown }) => {
	const [attributesModalIsOpen, toggleAttributesModal] = useToggle();

	const hasFixes = fixes > 0;

	if (!withFixes) return total;

	const fixesCount = (
		<span>
			/<span className={cn(hasFixes && 'text-destructive', testerForBreakdown != null && hasFixes && 'cursor-pointer')}>{fixes}</span>
		</span>
	);

	if (!hasFixes || !testerForBreakdown) {
		return (
			<>
				{total}
				{fixesCount}
			</>
		);
	}

	return (
		<>
			{total}
			<Dialog open={attributesModalIsOpen} onOpenChange={toggleAttributesModal}>
				<DialogTrigger asChild>{fixesCount}</DialogTrigger>
				<DialogContent size="lg">
					<DialogHeader>
						<DialogTitle>Přehled parametrů</DialogTitle>
					</DialogHeader>
					<FixedAttributesBreakdownView range={range} tester={testerForBreakdown} />
				</DialogContent>
			</Dialog>
		</>
	);
};
