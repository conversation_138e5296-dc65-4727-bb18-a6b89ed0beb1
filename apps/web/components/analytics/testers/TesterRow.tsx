import { TableCell, TableRow } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';

type Props = {
	testedProducts: ApiBody<'getAllProducts'>;
	tester: ApiBody<'getUsers'>[number];
	categories: ApiBody<'getProductCategories'>;
};
export const TesterRow: FC<Props> = ({ testedProducts, tester, categories }) => {
	const testerTests = testedProducts.filter((product) => product.productTest?.testedBy?.id === tester.id);

	return (
		<TableRow key={tester.id}>
			<TableCell>{tester.name}</TableCell>
			{categories.map((category) => {
				const categoryAmount = testerTests.filter((product) => product.productCategory?.id === category.id).length;
				return <TableCell key={category.id}>{categoryAmount}</TableCell>;
			})}
			<TableCell>
				<strong>{testerTests.length}</strong>
			</TableCell>
		</TableRow>
	);
};
