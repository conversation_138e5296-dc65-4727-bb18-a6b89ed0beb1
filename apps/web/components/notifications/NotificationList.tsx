import type { <PERSON>piB<PERSON> } from '@pocitarna-nx-2023/zodios';
import type { FC } from 'react';
import { NotificationListItem } from './NotificationListItem';

type Props = {
	notifications: ApiBody<'getNotifications'>;
};

export const NotificationList: FC<Props> = ({ notifications }) => {
	return (
		<ul>
			{notifications.map((notification) => (
				<NotificationListItem key={notification.id} notification={notification} />
			))}
		</ul>
	);
};
