import { Button, cn, <PERSON><PERSON>, <PERSON>ack, Tooltip } from '@pocitarna-nx-2023/ui';
import { formatDateTime } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import type { <PERSON>, MouseEventHandler } from 'react';

type Props = {
	notification: ApiBody<'getNotifications'>[number];
};

export const NotificationListItem: FC<Props> = ({ notification }) => {
	const { invalidate: invalidateNotifications } = apiHooks.useGetNotifications(undefined, { enabled: false });
	const { mutate: updateNotification, isLoading } = apiHooks.useUpdateNotification(
		{ params: { notificationId: notification.id } },
		{
			onSuccess: () => {
				invalidateNotifications();
			},
		},
	);

	const setViewed = () => {
		if (!notification.viewedAt) {
			updateNotification({ viewedAt: new Date() });
		}
	};

	const toggleViewed: MouseEventHandler<HTMLButtonElement> = (event) => {
		event.preventDefault();
		event.stopPropagation();
		updateNotification({ viewedAt: notification.viewedAt ? null : new Date() });
	};

	return (
		<li>
			<Link href={notification.href} onClick={setViewed} className="group">
				<Stack gap={0} className={cn('border-b p-2', !notification.viewedAt && 'bg-muted')}>
					<Stack direction="row" gap={1} className="justify-between items-center">
						<div>
							<p className="font-bold">{notification.title}</p>
							<p className="text-muted-foreground text-xs">{formatDateTime(notification.createdAt)}</p>
						</div>

						<Tooltip tooltip={`Označit jako ${notification.viewedAt ? 'ne' : ''}přečtené`}>
							<Button
								variant="ghost"
								width="icon"
								onClick={toggleViewed}
								className="transition opacity-0 group-hover:opacity-100"
								isLoading={isLoading}
							>
								<Icon name={notification.viewedAt ? 'circle' : 'circle-dot'} className="text-primary" />
							</Button>
						</Tooltip>
					</Stack>
					<p className="text-sm leading-1">{notification.body}</p>
				</Stack>
			</Link>
		</li>
	);
};
