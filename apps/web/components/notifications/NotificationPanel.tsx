import { SSE_MESSAGE } from '@pocitarna-nx-2023/config';
import { useReceiveInvalidation, useReceiveNotification, useSSE } from '@pocitarna-nx-2023/sse-client';
import { Checkbox, cn, Icon, Label, Popover, PopoverContent, PopoverTrigger, Tooltip } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { type FC, useMemo } from 'react';
import { useToggle } from 'rooks';
import { NotificationList } from './NotificationList';

export const NotificationPanel: FC = () => {
	const router = useRouter();
	const session = useSession();
	const userId = useMemo(() => session.data?.user.userId, [session]);
	const [onlyNotViewed, toggleOnlyNotViewed] = useToggle(true);
	const isConnected = useSSE([
		[SSE_MESSAGE.INVALIDATION, useReceiveInvalidation()],
		[SSE_MESSAGE.NOTIFICATION, useReceiveNotification({ userId, onNavigate: router.push })],
	]);
	const { data: notificationsData } = apiHooks.useGetNotifications(
		{ queries: { filter: { ...(onlyNotViewed ? { viewedAt: { eq: null } } : {}) } } },
		{ enabled: isConnected },
	);
	const notifications = notificationsData?._data ?? [];
	const unreadNotifications = notifications.filter((notification) => !notification.viewedAt);

	return isConnected ? (
		<Popover>
			<PopoverTrigger>
				<div className="bg-white rounded-full p-2 text-2xl leading-4 relative">
					<Icon name="bell" className="text-success" />
					{unreadNotifications.length > 0 && (
						<span className="absolute -top-1 -right-1 bg-destructive rounded-full w-4 h-4 flex items-center justify-center text-white text-xs">
							{unreadNotifications.length}
						</span>
					)}
				</div>
			</PopoverTrigger>
			<PopoverContent className="p-0">
				<div>
					<Label className={cn('flex gap-2 items-center font-normal px-2 py-1')}>
						<Checkbox checked={onlyNotViewed} onCheckedChange={toggleOnlyNotViewed} />
						<span>Pouze nepřečtené</span>
					</Label>
					{notifications.length > 0 ? (
						<NotificationList notifications={notifications} />
					) : (
						<p className="text-muted-foreground text-sm text-center p-2">Žádné notifikace k zobrazení</p>
					)}
				</div>
			</PopoverContent>
		</Popover>
	) : (
		<Tooltip tooltip="Notifikace nedostupné, jste připojeni k internetu? Zkuste obnovit aplikaci...">
			<div className="bg-white rounded-full p-2 text-2xl leading-4">
				<Icon name="bell-slash" className="text-destructive" />
			</div>
		</Tooltip>
	);
};
