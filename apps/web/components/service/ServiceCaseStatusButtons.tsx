import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, toast } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { useUserHasScope } from '../../hooks/useUserHasScope';

type Props = {
	serviceCase: ApiBody<'getServiceCase'>;
	product: ApiBody<'getProduct'>;
};

export const ServiceCaseStatusButtons: FC<Props> = ({ serviceCase, product }) => {
	const isServiceManager = useUserHasScope('serviceManage');
	const { id: serviceCaseId } = serviceCase;
	const { mutate: updateServiceCase, isLoading } = apiHooks.useUpdateServiceCase({ params: { serviceCaseId } });
	const { invalidate: invalidateServiceCaseHistory } = apiHooks.useGetServiceCaseHistory(
		{ params: { serviceCaseId } },
		{ enabled: false },
	);
	const { invalidate: invalidateServiceCases } = apiHooks.useGetServiceCases(undefined, { enabled: false });
	const { invalidate: invalidateServiceCase } = apiHooks.useGetServiceCase({ params: { serviceCaseId } }, { enabled: false });
	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId: product.id } }, { enabled: false });
	const { invalidate: invalidateProductHistory } = apiHooks.useGetProductHistory(
		{ params: { productId: product.id } },
		{ enabled: false },
	);

	const { invalidate: invalidateWarrantyClaims } = apiHooks.useGetWarrantyClaims({}, { enabled: false });

	const invalidate = () => {
		invalidateProduct();
		invalidateProductHistory();
		invalidateServiceCaseHistory();
		invalidateServiceCase();
		invalidateServiceCases();
		invalidateWarrantyClaims();
	};

	if (!isServiceManager) return null;

	return (
		<Card sticky>
			<CardHeader className="flex flex-col justify-between items-start md:flex-row">
				<CardTitle>Abnormality</CardTitle>
			</CardHeader>
			<CardContent>
				<Stack gap={4}>
					<Button
						variant="secondary"
						onClick={() => {
							updateServiceCase(
								{
									status: 'NEW',
									productStatus: 'SERVICE',
									actionDescription: 'Abnormalita (Reset)',
								},
								{
									onSuccess: () => {
										invalidate();
										toast.success(`Servisní případ byl resetován`);
									},
								},
							);
						}}
						isLoading={isLoading}
					>
						Reset případu
					</Button>
					<Button
						variant="secondary"
						onClick={() => {
							updateServiceCase(
								{
									status: 'CLOSED',
									productStatus: 'WARRANTY_CLAIM',
									shouldCreateWarrantyClaim: true,
									actionDescription: 'Abnormalita (Produkt přesunut do dod. reklamací)',
								},
								{
									onSuccess: () => {
										invalidate();
										toast.success(`Produkt přesunut do dod. reklamací`);
									},
								},
							);
						}}
						isLoading={isLoading}
					>
						Přesunout produkt do dod. reklamací
					</Button>
					<Button
						variant="secondary"
						onClick={() => {
							updateServiceCase(
								{
									status: 'CLOSED',
									productStatus: 'STOCK',
									actionDescription: 'Abnormalita (Produkt přesunut do naskladnění)',
								},
								{
									onSuccess: () => {
										invalidate();
										toast.success(`Produkt přesunut do naskladnění`);
									},
								},
							);
						}}
						isLoading={isLoading}
					>
						Přesunout produkt do naskladnění
					</Button>
					<Button
						variant="secondary"
						onClick={() => {
							updateServiceCase(
								{
									status: 'CLOSED',
									productStatus: 'TO_TEST',
									actionDescription: 'Abnormalita (Produkt přesunut do testování)',
								},
								{
									onSuccess: () => {
										invalidate();
										toast.success(`Produkt přesunut do testování`);
									},
								},
							);
						}}
						isLoading={isLoading}
					>
						Přesunout produkt do testování
					</Button>
					<Button
						variant="secondary"
						onClick={() => {
							updateServiceCase(
								{
									status: 'CLOSED',
									productStatus: 'TESTED',
									actionDescription: 'Abnormalita (Produkt přesunut do otestováno)',
								},
								{
									onSuccess: () => {
										invalidate();
										toast.success(`Produkt přesunut do otestováno`);
									},
								},
							);
						}}
						isLoading={isLoading}
					>
						Přesunout produkt do otestováno
					</Button>
				</Stack>
			</CardContent>
		</Card>
	);
};
