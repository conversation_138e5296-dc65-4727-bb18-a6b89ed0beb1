import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, ParamItem, ParamList } from '@pocitarna-nx-2023/ui';
import { formatWarrantyClaimCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { type FC } from 'react';

type Props = {
	serviceCaseId: string;
};

export const RelatedWarrantyClaimCard: FC<Props> = ({ serviceCaseId }) => {
	const { data: relatedClaimData } = apiHooks.useGetWarrantyClaims({
		queries: {
			page: 1,
			limit: 1,
			filter: {
				'action.endpoint': {
					eq: `/service-case/${serviceCaseId}`,
				},
				'action.method': {
					eq: 'PATCH',
				},
			},
		},
	});

	const relatedClaim = relatedClaimData?._data?.[0];

	if (!relatedClaim) return null;

	return (
		<Card>
			<CardHeader className="flex flex-col justify-between items-start md:flex-row">
				<CardTitle>Dod. reklamace</CardTitle>
			</CardHeader>
			<CardContent>
				<ParamList>
					<ParamItem label="Reklamace" align="center">
						<Link className="text-link" href={`/warranty-claim/${relatedClaim.id}`}>
							{formatWarrantyClaimCode(relatedClaim.code)}
						</Link>
					</ParamItem>
				</ParamList>
			</CardContent>
		</Card>
	);
};
