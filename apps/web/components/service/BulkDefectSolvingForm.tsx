import { REQUIRED_FIELD } from '@pocitarna-nx-2023/config';
import { Button, ComboboxControl, FormContext, Stack, TextControl, useDialog } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { z } from 'zod';
import { useLazyComboboxProps } from '../../hooks/useLazyComboboxProps';
import { useSearchFilter } from '../../hooks/useSearchFilter';
import { ServiceAttributeValues } from '../productDefect/ServiceAttributeValues';
import { ServiceTaskPriceInput } from '../productDefect/ServiceTaskPriceInput';

const schema = z.object({
	note: z.string(),
	serviceTaskTypeId: z.string().uuid({ message: REQUIRED_FIELD }),
	attributeValues: z.array(z.object({ attributeId: z.string().uuid(), attributeValueId: z.string().uuid() })),
	price: z.coerce.number(),
});

type Props = {
	defects: ApiBody<'getAllProductDefects'>;
};

export const BulkDefectSolvingForm: FC<Props> = ({ defects }) => {
	const { closeDialog } = useDialog();
	const { updateSearchTerm, filter: searchTermFilter } = useSearchFilter('name');

	const { mutate: bulkCreateServiceTask, isLoading } = apiHooks.useBulkCreateServiceTask();
	const { invalidate: invalidateProduct } = apiHooks.useGetProduct(
		{ params: { productId: defects[0].productId ?? '' } },
		{ enabled: false },
	);
	const { invalidate: invalidateProductAttributeValues } = apiHooks.useGetProductAttributeValues(
		{ params: { productId: defects[0].productId ?? '' } },
		{ enabled: false },
	);

	const { invalidate: invalidateProductDefects } = apiHooks.useGetAllProductDefects(undefined, { enabled: false });

	const serviceTaskTypesLazyComboboxProps = useLazyComboboxProps<ApiBody<'getServiceTaskTypes'>[number]>('getServiceTaskTypes', {
		queries: { filter: { ...searchTermFilter, type: { eq: '{SERVICE}' } } },
		formatResult: (type) => ({ value: type.id, label: type.name }),
	});

	return (
		<FormContext
			schema={schema}
			defaultValues={{ note: '', serviceTaskTypeId: '', attributeValues: [], price: 0 }}
			onSubmit={(data) => {
				bulkCreateServiceTask(
					{
						price: data.price,
						attributeValues: data.attributeValues,
						note: data.note,
						serviceTaskTypeId: data.serviceTaskTypeId,
						productDefectsIds: defects.map((defect) => defect.id),
						status: 'CLOSED',
					},
					{
						onSuccess: () => {
							invalidateProduct();
							invalidateProductAttributeValues();
							invalidateProductDefects();
							closeDialog();
						},
					},
				);
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<ComboboxControl
						control={control}
						name="serviceTaskTypeId"
						label="Vyřešit pomocí"
						enableFilter
						onSearchChange={updateSearchTerm}
						{...serviceTaskTypesLazyComboboxProps}
					/>

					<ServiceTaskPriceInput />
					{defects[0].productId && <ServiceAttributeValues productId={defects[0].productId} />}
					<TextControl control={control} name="note" label="Poznámka" />
					<Button className="w-full" variant="default" isLoading={isLoading} type="submit">
						Uložit
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
