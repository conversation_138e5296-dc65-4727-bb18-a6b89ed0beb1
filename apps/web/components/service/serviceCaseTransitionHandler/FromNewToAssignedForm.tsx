import { MAX_POSITIVE_INTEGER, ServiceCaseStatusMessage } from '@pocitarna-nx-2023/config';
import { Button, ComboboxControl, FormContext, type FormContextRef, Stack, TextControl, toast, useDialog } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useEffect, useRef, useState } from 'react';
import { z } from 'zod';
import { useSearchFilter } from '../../../hooks/useSearchFilter';
import { useGetServiceCaseTransitionInvalidations } from '../../../hooks/useServiceCaseStatusTransitionMap';
import type { TransitionFormProps } from './DefaultStatusTransitionForm';

const schema = z.object({
	actionDescription: z.string(),
	status: z.enum(['ASSIGNED_TO_INTERNAL_SERVICE', 'ASSIGNED_TO_EXTERNAL_SERVICE']),
	serviceCenterId: z.union([z.string().uuid(), z.literal('')]),
});

export const FromNewToAssignedForm: FC<TransitionFormProps> = ({ serviceCase }) => {
	const isBulkUpdate = Array.isArray(serviceCase);
	const idsToProcess = isBulkUpdate ? serviceCase.map((item) => item.id) : [serviceCase.id];
	const formRef = useRef<FormContextRef<typeof schema>>(null);
	const [selectedStatus, setSelectedStatus] = useState<'ASSIGNED_TO_INTERNAL_SERVICE' | 'ASSIGNED_TO_EXTERNAL_SERVICE'>(
		'ASSIGNED_TO_INTERNAL_SERVICE',
	);
	const { closeDialog } = useDialog();
	const { invalidate } = useGetServiceCaseTransitionInvalidations();
	const { invalidate: invalidateProducts } = apiHooks.useGetAllProducts({}, { enabled: false });
	const { mutate: updateServiceCase, isLoading } = apiHooks.useUpdateServiceCase({ params: { serviceCaseId: idsToProcess[0] } });
	const { mutate: bulkUpdateServiceCases, isLoading: isBulkLoading } = apiHooks.useBulkUpdateServiceCases();

	const { updateSearchTerm, filter } = useSearchFilter('name');
	const { data: serviceCenters } = apiHooks.useGetServiceCenters({
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER, filter, sort: ['name'] },
	});
	const serviceCentersList =
		serviceCenters?._data?.map((serviceCenter) => ({ value: serviceCenter.id, label: serviceCenter.name })) ?? [];

	const onSubmit = (data: z.infer<typeof schema>) => {
		const onSuccess = () => {
			invalidateProducts();
			invalidate();
			toast.success(`Upraveno do stavu ${ServiceCaseStatusMessage[data.status]}`);
			closeDialog();
		};
		const transformedData = {
			...data,
			serviceCenterId:
				data.status === 'ASSIGNED_TO_INTERNAL_SERVICE' || data.serviceCenterId === '' ? undefined : data.serviceCenterId,
		};

		if (isBulkUpdate) {
			bulkUpdateServiceCases({ ids: idsToProcess, data: transformedData }, { onSuccess });
		} else {
			updateServiceCase(transformedData, { onSuccess });
		}
	};

	useEffect(() => {
		const subscription = formRef.current?.watch(({ status }) => {
			if (status) {
				setSelectedStatus(status);
			}
		});

		return () => {
			subscription?.unsubscribe();
		};
	}, []);

	return (
		<FormContext
			ref={formRef}
			schema={schema}
			defaultValues={{
				actionDescription: '',
				status: selectedStatus,
				serviceCenterId: '',
			}}
			onSubmit={onSubmit}
		>
			{(control) => (
				<Stack gap={4}>
					<ComboboxControl
						control={control}
						name="status"
						label="Vyřešit pomocí"
						items={[
							{ value: 'ASSIGNED_TO_INTERNAL_SERVICE', label: 'Interního servisu' },
							{ value: 'ASSIGNED_TO_EXTERNAL_SERVICE', label: 'Externího servisu' },
						]}
						hideSearch={true}
					/>

					{selectedStatus === 'ASSIGNED_TO_EXTERNAL_SERVICE' && (
						<ComboboxControl
							control={control}
							name="serviceCenterId"
							label="Externí servis"
							placeholder="Vyberte externí servis"
							searchPlaceholder="Najít externí servis"
							onSearchChange={updateSearchTerm}
							items={serviceCentersList}
							emptyElement="Nenalezen žádný externí servis"
						/>
					)}

					<TextControl control={control} name="actionDescription" label="Poznámka" />

					<Button className="w-full" variant="default" type="submit" isLoading={isLoading || isBulkLoading}>
						Uložit
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
