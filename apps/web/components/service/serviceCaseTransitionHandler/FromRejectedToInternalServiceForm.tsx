import { SERVICE_CASE_STATUS_NAMES, ServiceCaseStatusMessage } from '@pocitarna-nx-2023/config';
import { <PERSON><PERSON>, <PERSON><PERSON>ontext, Stack, TextControl, toast, useDialog } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { z } from 'zod';
import { useGetServiceCaseTransitionInvalidations } from '../../../hooks/useServiceCaseStatusTransitionMap';
import type { TransitionFormProps } from './DefaultStatusTransitionForm';

const schema = z.object({
	actionDescription: z.string(),
	status: z.enum(SERVICE_CASE_STATUS_NAMES),
});

export const FromRejectedToInternalServiceForm: FC<TransitionFormProps> = ({ serviceCase, selectedTargetTransition }) => {
	const isBulkUpdate = Array.isArray(serviceCase);
	const idsToProcess = isBulkUpdate ? serviceCase.map((item) => item.id) : [serviceCase.id];
	const { closeDialog } = useDialog();
	const { invalidate } = useGetServiceCaseTransitionInvalidations();
	const { mutate: updateServiceCase, isLoading } = apiHooks.useUpdateServiceCase({ params: { serviceCaseId: idsToProcess[0] } });
	const { mutate: bulkUpdateServiceCases, isLoading: isBulkLoading } = apiHooks.useBulkUpdateServiceCases();

	const onSubmit = (data: z.infer<typeof schema>) => {
		const onSuccess = () => {
			invalidate();
			toast.success(`Upraveno do stavu ${ServiceCaseStatusMessage[data.status]}`);
			closeDialog();
		};

		if (isBulkUpdate) {
			bulkUpdateServiceCases({ ids: idsToProcess, data }, { onSuccess });
		} else {
			updateServiceCase(data, { onSuccess });
		}
	};

	return (
		<FormContext
			schema={schema}
			defaultValues={{
				actionDescription: '',
				status: selectedTargetTransition.status,
			}}
			onSubmit={onSubmit}
		>
			{(control) => (
				<Stack gap={4}>
					<TextControl control={control} name="actionDescription" label="Poznámka" />

					<Button className="w-full" variant="default" type="submit" isLoading={isLoading || isBulkLoading}>
						Uložit
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
