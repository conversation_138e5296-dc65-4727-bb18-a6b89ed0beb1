import { type FC } from 'react';
import { type TransitionFormProps } from './DefaultStatusTransitionForm';
import { WithProductTransitionForm } from './WithProductTransitionForm';

export const FromRepairToNewForm: FC<TransitionFormProps> = (props) => {
	const { productDefects } = props;
	const referenceDefect = productDefects[0];
	const serviceTaskId = referenceDefect?.serviceTaskId;

	return <WithProductTransitionForm {...props} productStatus="SERVICE" serviceTaskToDelete={serviceTaskId} />;
};
