import { ServiceCaseStatusMessage } from '@pocitarna-nx-2023/config';
import { Button, toast } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { ProductDefectSolverDialog } from '../../productDefect/ProductDefectSolverDialog';

type Props = {
	productDefects: ApiBody<'getAllProductDefects'>;
	serviceCase: ApiBody<'getServiceCase'>;
	disabled: boolean;
};

export const InternalServiceCaseHandler: FC<Props> = ({ productDefects, serviceCase, disabled }) => {
	const unsolvedDefects = productDefects.filter((defect) => defect.serviceTask?.status !== 'CLOSED');

	const { mutate: updateServiceCase, isLoading } = apiHooks.useUpdateServiceCase({ params: { serviceCaseId: serviceCase.id } });

	const { invalidate: invalidateProductDefects } = apiHooks.useGetAllProductDefects(undefined, { enabled: false });
	const { invalidate: invalidateServiceCaseHistory } = apiHooks.useGetServiceCaseHistory(
		{ params: { serviceCaseId: serviceCase.id } },
		{ enabled: false },
	);
	const { invalidate: invalidateServiceCase } = apiHooks.useGetServiceCase(
		{ params: { serviceCaseId: serviceCase.id } },
		{ enabled: false },
	);
	const { data: associatedOrderData } = apiHooks.useGetEcommerceOrders({
		queries: { filter: { serviceCaseId: { eq: serviceCase.id } } },
	});

	const associatedOrder = associatedOrderData?._data?.[0];
	const productsNextStatus = associatedOrder ? 'RESERVED' : 'TO_TEST';

	if (unsolvedDefects.length === 0) {
		return (
			<Button
				disabled={disabled}
				onClick={() => {
					if (disabled) return;
					updateServiceCase(
						{
							status: 'CLOSED',
							productStatus: productsNextStatus,
						},
						{
							onSuccess: () => {
								toast.success(`Upraveno do stavu ${ServiceCaseStatusMessage['CLOSED']}`);
								invalidateServiceCaseHistory();
								invalidateProductDefects();
								invalidateServiceCase();
							},
						},
					);
				}}
				isLoading={isLoading}
			>
				Uzavřít servis
			</Button>
		);
	}

	return <ProductDefectSolverDialog unsolvedDefects={unsolvedDefects} disabled={disabled} />;
};
