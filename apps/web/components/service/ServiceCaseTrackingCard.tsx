import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { TrackingCard, type WithTrackingCode } from '../TrackingCard';

type Props = {
	serviceCase: ApiBody<'getServiceCase'>;
	disabled: boolean;
	invalidate: () => void;
};

export const ServiceCaseTrackingCard: FC<Props> = ({ serviceCase, disabled, invalidate }) => {
	const { mutate: updateServiceCase, isLoading } = apiHooks.useUpdateServiceCase({ params: { serviceCaseId: serviceCase.id } });

	const onSubmit = (data: WithTrackingCode) => {
		if (disabled) return;

		updateServiceCase(data, {
			onSuccess: () => {
				invalidate();
			},
		});
	};

	return <TrackingCard entity={serviceCase} onSubmit={onSubmit} isLoading={isLoading} disabled={disabled} />;
};
