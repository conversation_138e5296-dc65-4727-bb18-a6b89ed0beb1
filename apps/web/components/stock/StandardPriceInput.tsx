import { CheckboxControl, NumericInputControl, Spinner, toast } from '@pocitarna-nx-2023/ui';
import { withoutVat } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, type FocusEventHandler, useCallback } from 'react';
import { useController, useFormContext } from 'react-hook-form';
import { type StockFormSchema } from './StockForm';

type Props = {
	envelopeId: string;
	invalidatePrices: () => void;
	isFetching?: boolean;
};

export const StandardPriceInput: FC<Props> = ({ envelopeId, invalidatePrices, isFetching = false }) => {
	const { control } = useFormContext<StockFormSchema>();
	const {
		field: { value: confirmedState },
	} = useController({ control, name: `data.${envelopeId}.confirmed` });

	const { mutate: updateEnvelope, isLoading } = apiHooks.useUpdateProductEnvelope({ params: { productEnvelopeId: envelopeId } });
	const { invalidate: invalidateEnvelope } = apiHooks.useGetProductEnvelope(
		{ params: { productEnvelopeId: envelopeId } },
		{ enabled: false },
	);

	const saveStandardPrice: FocusEventHandler<HTMLInputElement> = useCallback(
		(event) => {
			const standardPrice = Number(event.target.value);
			if (!standardPrice || isNaN(standardPrice)) return;

			updateEnvelope(
				{ standardPrice: withoutVat(standardPrice) },
				{
					onSuccess: () => {
						toast.success('Aktualizovaná standardní cena karty');
						invalidateEnvelope();
						invalidatePrices();
					},
				},
			);
		},
		[invalidateEnvelope, invalidatePrices, updateEnvelope],
	);

	if (isFetching || isLoading) {
		return (
			<div className="flex items-center justify-left px-3 h-9 border border-stone-200 rounded-md bg-white shadow-sm cursor-not-allowed opacity-50 grow">
				<Spinner />
			</div>
		);
	}

	return (
		<>
			<NumericInputControl
				numberFormat="decimal"
				control={control}
				name={`data.${envelopeId}.standardPrice`}
				disabled={confirmedState}
				onBlurCapture={saveStandardPrice}
			/>
			<div className="sr-only">
				<CheckboxControl control={control} name={`data.${envelopeId}.confirmed`} />
			</div>
		</>
	);
};
