import { Button, cn, toast } from '@pocitarna-nx-2023/ui';
import { withVat } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { useFormContext } from 'react-hook-form';
import { PriceDisplayer } from '../PriceDisplayer';
import type { StockFormSchema } from './StockForm';

type Props = {
	price: number;
	envelopeId: string;
	className?: string;
};

export const SelectablePrice: FC<Props> = ({ price, envelopeId, className }) => {
	const { setValue } = useFormContext<StockFormSchema>();
	const { mutate: updateEnvelope } = apiHooks.useUpdateProductEnvelope({ params: { productEnvelopeId: envelopeId } });
	const { invalidate: invalidateEnvelope } = apiHooks.useGetProductEnvelope(
		{ params: { productEnvelopeId: envelopeId } },
		{ enabled: false },
	);
	const { invalidate: invalidatePrices } = apiHooks.useGetProductEnvelopePrices(
		{ params: { productEnvelopeId: envelopeId } },
		{ enabled: false },
	);

	const onClick = () => {
		setValue(`data.${envelopeId}.salePrice`, withVat(price));
		updateEnvelope(
			{ salePrice: price },
			{
				onSuccess: () => {
					toast.success('Aktualizovaná prodejní cena karty');
					invalidateEnvelope();
					invalidatePrices();
				},
			},
		);
	};

	return (
		<Button type="button" variant="link" onClick={onClick} className={cn(className, 'text-left p-0')}>
			<PriceDisplayer price={price} />
		</Button>
	);
};
