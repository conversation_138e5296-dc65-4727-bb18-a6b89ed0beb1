import { Button, CheckboxControl, Icon, NumericInputControl, Spinner, toast } from '@pocitarna-nx-2023/ui';
import { withoutVat } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, type FocusEventHandler, useCallback } from 'react';
import { useController, useFormContext } from 'react-hook-form';
import { type StockFormSchema } from './StockForm';

type Props = {
	envelopeId: string;
	invalidatePrices: () => void;
	isFetching?: boolean;
};

export const SalePriceInput: FC<Props> = ({ envelopeId, invalidatePrices, isFetching = false }) => {
	const { control, setValue } = useFormContext<StockFormSchema>();
	const {
		field: { value: confirmedState },
	} = useController({ control, name: `data.${envelopeId}.confirmed` });

	const { mutate: updateEnvelope, isLoading } = apiHooks.useUpdateProductEnvelope({ params: { productEnvelopeId: envelopeId } });
	const { invalidate: invalidateEnvelope } = apiHooks.useGetProductEnvelope(
		{ params: { productEnvelopeId: envelopeId } },
		{ enabled: false },
	);

	const saveSalePrice: FocusEventHandler<HTMLInputElement> = useCallback(
		(event) => {
			const salePrice = Number(event.target.value);
			if (!salePrice || isNaN(salePrice)) return;

			updateEnvelope(
				{ salePrice: withoutVat(salePrice) },
				{
					onSuccess: () => {
						toast.success('Aktualizovaná prodejní cena karty');
						invalidateEnvelope();
						invalidatePrices();
					},
				},
			);
		},
		[invalidateEnvelope, invalidatePrices, updateEnvelope],
	);

	if (isFetching || isLoading) {
		return (
			<div className="flex gap-2">
				<div className="flex items-center justify-left px-3 h-9 border border-stone-200 rounded-md bg-white shadow-sm cursor-not-allowed opacity-50 grow">
					<Spinner />
				</div>

				<Button type="button" variant={confirmedState ? undefined : 'outline'} width="icon" disabled>
					<Icon name="check" />
				</Button>
			</div>
		);
	}

	return (
		<>
			<NumericInputControl
				numberFormat="decimal"
				control={control}
				name={`data.${envelopeId}.salePrice`}
				disabled={confirmedState}
				compoundAfter={
					<Button
						variant={confirmedState ? undefined : 'outline'}
						type="button"
						width="icon"
						onClick={() => {
							setValue(`data.${envelopeId}.confirmed`, !confirmedState);
						}}
					>
						<Icon name="check" />
					</Button>
				}
				onBlurCapture={saveSalePrice}
			/>
			<div className="sr-only">
				<CheckboxControl control={control} name={`data.${envelopeId}.confirmed`} />
			</div>
		</>
	);
};
