import { cn, Tooltip } from '@pocitarna-nx-2023/ui';
import { type StockAttributeValue } from '@pocitarna-nx-2023/zodios';
import { type FC, Fragment } from 'react';
import { AttributeValue } from '../AttributeValue';

type Props = {
	brandStockAttributeValues: StockAttributeValue[];
	modelStockAttributeValues: StockAttributeValue[];
	otherStockAttributeValues: StockAttributeValue[];
};

export const AttributeValuesOverview: FC<Props> = ({ brandStockAttributeValues, modelStockAttributeValues, otherStockAttributeValues }) => {
	return (
		<>
			{[...brandStockAttributeValues, ...modelStockAttributeValues].map((item, index) => (
				<Fragment key={index}>
					{index > 0 && ' '}
					{item.matches.map((attributeValue, index) => (
						<Fragment key={attributeValue.id}>
							{index > 0 && ' '}
							<AttributeValue attributeValue={attributeValue} />
						</Fragment>
					))}
				</Fragment>
			))}
			{[...brandStockAttributeValues, ...modelStockAttributeValues].length > 0 && <br />}

			{otherStockAttributeValues.length > 0 && (
				<span className="flex flex-wrap gap-x-1 mt-2 min-w-52">
					{otherStockAttributeValues.map((item, index) => (
						<span key={item.attributeId} className="whitespace-nowrap">
							{item.matches.map(
								(attributeValue, index) =>
									attributeValue.value !== '-' && (
										<Fragment key={attributeValue.id}>
											{index > 0 && ', '}
											<Tooltip tooltip={attributeValue.attribute.displayName}>
												<span
													className={cn(
														attributeValue.attribute.displayName === 'Displej - rozlišení' &&
															Number(attributeValue.value.toString().slice(0, 1)) > 1 &&
															'font-black',
													)}
												>
													<AttributeValue attributeValue={attributeValue} />
												</span>
											</Tooltip>
										</Fragment>
									),
							)}
							{index < otherStockAttributeValues.length - 1 && ', '}
						</span>
					))}
				</span>
			)}
		</>
	);
};
