import { History, HistoryItem, Media, Spinner } from '@pocitarna-nx-2023/ui';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';

export const StockHistory = () => {
	const { data: filesData, isLoading } = apiHooks.useGetFiles({
		queries: {
			filter: { mime: { eq: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }, name: { eq: 'naskladneni-%' } },
			limit: 100,
		},
	});
	const files = filesData?._data ?? [];

	return (
		<History>
			{isLoading ? (
				<Spinner />
			) : (
				files.map((file: ApiBody<'getFiles'>[number]) => (
					<HistoryItem
						key={file.id}
						timestamp={file.action.createdAt}
						user={file.action.authentication.user}
						verb={'naskladnil produkty'}
					>
						<Media files={[file]} />
					</HistoryItem>
				))
			)}
		</History>
	);
};
