import { Button, Icon, Label, Stack } from '@pocitarna-nx-2023/ui';
import type { FC, ReactNode } from 'react';
import { type Control, useFieldArray } from 'react-hook-form';
import { FilterAttributeValuesField } from './FilterAttributeValuesField';

type Props = {
	control: Control<FormSection>;
	label: ReactNode;
};

type FormSection = {
	attributeValues: { attributeValueId: string; attributeId: string }[];
};

export const FilterAttributeValues: FC<Props> = ({ control, label }) => {
	const { fields, insert, remove } = useFieldArray<FormSection>({
		control,
		name: 'attributeValues',
	});

	return (
		<Stack gap={1} className="col-span-2">
			<Label>{label}</Label>
			<Stack gap={4} direction="row">
				<Button
					type="button"
					variant="outline"
					width="icon"
					onClick={() => insert(fields.length, { attributeId: '', attributeValueId: '' })}
				>
					<Icon name="plus" />
				</Button>
				<Stack gap={1} className="-mt-5 ml-4 flex-grow">
					{fields.map((field, index) => (
						<FilterAttributeValuesField key={field.id} index={index} onRemove={() => remove(index)} />
					))}
				</Stack>
			</Stack>
		</Stack>
	);
};
