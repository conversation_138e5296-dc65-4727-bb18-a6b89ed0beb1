import { Button, ComboboxControl, Icon, Stack } from '@pocitarna-nx-2023/ui';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { useLazyComboboxProps } from '../../hooks/useLazyComboboxProps';
import { useSearchFilter } from '../../hooks/useSearchFilter';
import { AttributeValue } from '../AttributeValue';

type Props = {
	onRemove: () => void;
	index: number;
};

type FormSection = {
	attributeValues: { attributeValueId: string; attributeId: string }[];
};

export const FilterAttributeValuesField: FC<Props> = ({ onRemove, index }) => {
	const { updateSearchTerm: updateAttributeSearchTerm, filter: attributeFilter } = useSearchFilter('displayName');
	const { updateSearchTerm: updateAttributeValueSearchTerm, filter: attributeValueFilter } = useSearchFilter('value->>0');
	const { control, watch, setValue } = useFormContext<FormSection>();
	const attributeId = watch(`attributeValues.${index}.attributeId`);

	const attributesLazyComboboxProps = useLazyComboboxProps<ApiBody<'getAttributes'>[number]>('getAttributes', {
		queries: {
			filter: { ...attributeFilter },
			sort: ['name'],
		},
		formatResult: (item) => ({
			value: item.id,
			label: (
				<span>
					{item.displayName} {item.name !== item.displayName && <small>({item.name})</small>}
				</span>
			),
		}),
	});

	const attributeValuesLazyComboboxProps = useLazyComboboxProps<ApiBody<'getAttributeValues'>[number]>('getAttributeValues', {
		params: { attributeId },
		canRun: !!attributeId,
		queries: {
			filter: { ...attributeValueFilter, temporary: { eq: false } },
			sort: ['value'],
		},
		formatResult: (item) => ({
			value: item.id,
			label: <AttributeValue attributeValue={item} />,
		}),
	});

	useEffect(() => {
		setValue(`attributeValues.${index}.attributeValueId`, '');
	}, [attributeId, index, setValue]);

	return (
		<Stack direction="row" gap={1}>
			<div className="grow w-full">
				<ComboboxControl
					control={control}
					name={`attributeValues.${index}.attributeId`}
					label="Parametr"
					onSearchChange={updateAttributeSearchTerm}
					onValueDelete={() => setValue(`attributeValues.${index}.attributeId`, '')}
					{...attributesLazyComboboxProps}
				/>
			</div>
			<div className="grow w-full">
				<ComboboxControl
					disabled={!attributeId}
					control={control}
					name={`attributeValues.${index}.attributeValueId`}
					label="Hodnota"
					onSearchChange={updateAttributeValueSearchTerm}
					onValueDelete={() => setValue(`attributeValues.${index}.attributeValueId`, '')}
					{...attributeValuesLazyComboboxProps}
				/>
			</div>
			<div>
				<p className="text-sm mb-1">&nbsp;</p>
				<Button type="button" width="icon" variant="outline" onClick={onRemove}>
					<Icon name="trash" />
				</Button>
			</div>
		</Stack>
	);
};
