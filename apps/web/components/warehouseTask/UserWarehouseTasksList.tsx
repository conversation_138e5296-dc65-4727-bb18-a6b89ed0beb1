import { Alert, AlertDescription, AlertTitle, Icon, Stack } from '@pocitarna-nx-2023/ui';
import { useUserWarehouseTaskList } from '../../useUserWarehouseTaskList';
import { UserWarehouseTask } from './UserWarehouseTask';

export const UserWarehouseTasksList = () => {
	const { warehouseTasks } = useUserWarehouseTaskList();

	if (warehouseTasks.length === 0) {
		return (
			<Alert variant="info">
				<Icon name="circle-info" />
				<AlertTitle>Žádné produkty</AlertTitle>
				<AlertDescription>Přidej si některé na svůj seznam a obnov pak aplikaci.</AlertDescription>
			</Alert>
		);
	}

	return (
		<Stack gap={2}>
			{warehouseTasks.map((task) => (
				<UserWarehouseTask key={task.id} warehouseTask={task} />
			))}
		</Stack>
	);
};
