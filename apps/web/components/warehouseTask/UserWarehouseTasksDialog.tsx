import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@pocitarna-nx-2023/ui';
import type { ComponentProps, FC } from 'react';
import { useUserWarehouseTaskList } from '../../useUserWarehouseTaskList';
import { UserWarehouseTasksList } from './UserWarehouseTasksList';

export const UserWarehouseTasksDialog: FC<ComponentProps<typeof Dialog>> = (props) => {
	const { warehouseTasks } = useUserWarehouseTaskList();

	return (
		<>
			<Dialog {...props}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>M<PERSON>j skladový seznam ({warehouseTasks.length})</DialogTitle>
					</DialogHeader>

					<UserWarehouseTasksList />
				</DialogContent>
			</Dialog>
		</>
	);
};
