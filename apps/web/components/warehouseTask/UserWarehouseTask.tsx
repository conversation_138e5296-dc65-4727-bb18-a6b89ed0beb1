import { NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { Button, Icon, Stack } from '@pocitarna-nx-2023/ui';
import { formatProductCode, formatWarehousePositionName } from '@pocitarna-nx-2023/utils';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import type { FC } from 'react';
import { toast } from 'sonner';
import { useUserWarehouseTaskList } from '../../useUserWarehouseTaskList';

type Props = {
	warehouseTask: ApiBody<'getWarehouseTasks'>[number];
};

export const UserWarehouseTask: FC<Props> = ({ warehouseTask }) => {
	const { invalidateUserWarehouseTasks } = useUserWarehouseTaskList();
	const { mutate: deleteShiftWarehouseTask } = apiHooks.useDeleteShiftWarehouseTask(
		{ params: { productId: warehouseTask.product.id } },
		{
			onSuccess: () => {
				invalidateUserWarehouseTasks();
				toast.success('Produkt odebrán ze seznamu...');
			},
		},
	);

	return (
		<div className="w-full border rounded px-2 py-1 shadow-sm">
			<Stack gap={2} direction="row" className="justify-between items-center">
				<div>
					<p>
						{warehouseTask.product.warehousePosition ? (
							<Link href={`/warehouse/position/${warehouseTask.product.warehousePosition.id}`} className="text-link">
								<strong>
									{warehouseTask.product.warehousePosition.warehouse.name} -{' '}
									{formatWarehousePositionName(warehouseTask.product.warehousePosition)}
								</strong>
							</Link>
						) : warehouseTask.product.pickedAt ? (
							<strong>V přesunu - {warehouseTask.product.pickedBy?.name ?? NOT_AVAILABLE}</strong>
						) : (
							<strong>{NOT_AVAILABLE}</strong>
						)}
					</p>
					<p>
						<Link href={`/product/${warehouseTask.product.id}`} className="text-link">
							<strong>{formatProductCode(warehouseTask.product.code)}</strong>
						</Link>
					</p>
					<p>
						<Link href={`/product/envelope/${warehouseTask.product.productEnvelope?.id}`} className="text-link">
							{warehouseTask.product.productEnvelope?.name}
						</Link>
					</p>
				</div>
				<Button
					type="button"
					variant="outline"
					size="sm"
					width="icon"
					tabIndex={-1}
					onClick={() => deleteShiftWarehouseTask(undefined)}
				>
					<Icon name="trash" className="h-4 w-4" />
				</Button>
			</Stack>
		</div>
	);
};
