import {
	Button,
	Dialog,
	DialogContent,
	DialogTitle,
	DialogTrigger,
	FormContext,
	NumericInputControl,
	Stack,
	useDialog,
} from '@pocitarna-nx-2023/ui';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import type { FC } from 'react';
import { toast } from 'sonner';
import { z } from 'zod';
import { useUserWarehouseTaskList } from '../../useUserWarehouseTaskList';

type Props = {
	productEnvelope: ApiBody<'getProductEnvelope'>;
};

export const UserWarehouseTasksEnvelopeDialog: FC<Props> = ({ productEnvelope }) => (
	<Dialog>
		<DialogTrigger asChild>
			<Button type="button" variant="secondary">
				Přidat na skladový seznam
			</Button>
		</DialogTrigger>
		<DialogContent>
			<DialogTitle>Přidat produkty na skladový seznam</DialogTitle>
			<UserWarehouseTasksEnvelopeForm productEnvelope={productEnvelope} />
		</DialogContent>
	</Dialog>
);

const UserWarehouseTasksEnvelopeForm: FC<Props> = ({ productEnvelope }) => {
	const { closeDialog } = useDialog();
	const { invalidateUserWarehouseTasks } = useUserWarehouseTaskList();
	const { mutate: createShiftWarehouseTask } = apiHooks.useCreateShiftWarehouseTask(undefined, {
		onSuccess: () => {
			invalidateUserWarehouseTasks();
			toast.success('Produkty přidány na seznam...');
			closeDialog();
		},
	});
	const schema = z.object({
		amount: z.number().gte(1),
		productEnvelopeId: z.string().uuid(),
	});
	const defaultValues = {
		amount: 1,
		productEnvelopeId: productEnvelope.id,
	};

	return (
		<FormContext schema={schema} defaultValues={defaultValues} onSubmit={(data) => createShiftWarehouseTask(data)}>
			{(control) => (
				<Stack gap={4}>
					<NumericInputControl numberFormat="integer" control={control} name="amount" label="Množství" />
					<Button>Přidat na skladový seznam</Button>
				</Stack>
			)}
		</FormContext>
	);
};
