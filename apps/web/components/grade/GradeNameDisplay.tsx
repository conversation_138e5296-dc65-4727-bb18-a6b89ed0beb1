import { NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { type FC } from 'react';

type Props = {
	gradeId: string | null | undefined;
	variant?: 'inline';
};

export const GradeNameDisplay: FC<Props> = ({ gradeId, variant }) => {
	const { data: gradeData } = apiHooks.useGetGrade({ params: { gradeId: gradeId ?? '' } }, { enabled: Boolean(gradeId) });

	const grade = gradeData?._data;

	if (!grade) return NOT_AVAILABLE;

	if (variant === 'inline') return grade.name;

	return (
		<Link className="text-link" href={`/admin/grade/${grade.id}`}>
			{grade.name}
		</Link>
	);
};
