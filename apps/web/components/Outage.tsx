import { SSE_MESSAGE } from '@pocitarna-nx-2023/config';
import { useReceiveInvalidation, useSSE } from '@pocitarna-nx-2023/sse-client';
import { <PERSON><PERSON>, Card, CardContent, Logo, Page, PageTitle, Separator, Stack } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useEffect } from 'react';

export const Outage = () => {
	const router = useRouter();
	const { data } = useSession();
	const { data: outageStatusData } = apiHooks.useGetOutageStatus();
	const { mutate: toggleOutageStatus, isLoading: isTogglingOutageStatus } = apiHooks.useToggleOutageStatus();
	useSSE([[SSE_MESSAGE.INVALIDATION, useReceiveInvalidation()]]);
	useEffect(() => () => router.reload(), [router]);

	return (
		<>
			<PageTitle title="Režim údržby" />
			<Page className="loginpage min-h-full items-center bg-primary p-4 justify-center flex flex-col md:p-10">
				<Logo className="h-10 text-white mb-6" />
				<Card className="shadow-2xl border-0 max-w-lg">
					<CardContent className="text-center p-10 px-4 md:px-10">
						<Stack gap={2}>
							<h1>Režim údržby</h1>
							<p>
								Aplikace momentálně není dostupná z důvodu probíhajících prací na její infrastruktuře nebo z důvodu
								zachování integrity dat. Děkujeme za pochopení, zkuste to prosím později.
							</p>
							{outageStatusData?._data?.createdById === data?.user?.userId && (
								<>
									<Separator />
									<Button
										onClick={() => toggleOutageStatus(undefined)}
										variant="outline"
										width="full"
										className="h-12"
										isLoading={isTogglingOutageStatus}
									>
										Ukončit režim údržby
									</Button>
								</>
							)}
						</Stack>
					</CardContent>
				</Card>
			</Page>
		</>
	);
};
