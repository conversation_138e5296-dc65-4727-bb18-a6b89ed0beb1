import { type ProductDefectSource, type ServiceTaskResolutionScope } from '@pocitarna-nx-2023/config';
import { Button } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { type z } from 'zod';
import { type defectResolutionSchema } from './ProductDefectSolver';

type Props = {
	unsolvedDefects: ApiBody<'getAllProductDefects'>;
	source?: ProductDefectSource;
	isLoading: boolean;
	handleResolutionScopeChange: (scope: ServiceTaskResolutionScope) => void;
};

// This will need to be adjusted when multiple defects are solved at once
export const DefectResolutionButtons: FC<Props> = ({ unsolvedDefects, source, isLoading, handleResolutionScopeChange }) => {
	const { watch } = useFormContext<z.infer<typeof defectResolutionSchema>>();
	const serviceTaskTypeId = watch('serviceTaskTypeId');
	const defectsSelected = watch('productDefectIds');
	const shouldShowDoubleResolution = source === 'WARRANTY_CLAIM';

	const { data: discountServiceTypeData } = apiHooks.useGetServiceTaskTypes({
		queries: { filter: { name: { eq: 'Sleva' } }, page: 1, limit: 1 },
	});

	const { data: mismatchDefectData } = apiHooks.useGetDefectTypes({
		queries: { filter: { name: { eq: 'Neshodující se parametry' } }, page: 1, limit: 1 },
	});

	const discountServiceTypeId = useMemo(() => discountServiceTypeData?._data?.[0]?.id, [discountServiceTypeData?._data]);
	const mismatchDefectTypeId = useMemo(() => mismatchDefectData?._data?.[0]?.id, [mismatchDefectData?._data]);

	const discountWasSelected = useMemo(() => serviceTaskTypeId === discountServiceTypeId, [serviceTaskTypeId, discountServiceTypeId]);

	const mismatchDefectWasSelected = useMemo(() => {
		if (defectsSelected.length !== 1) return false;
		const selectedDefect = unsolvedDefects.find((defect) => defect.id === defectsSelected[0]);
		return selectedDefect?.defectTypeId === mismatchDefectTypeId;
	}, [defectsSelected, mismatchDefectTypeId, unsolvedDefects]);

	if (discountWasSelected && !mismatchDefectWasSelected) {
		return <VendorTaskButton isLoading={isLoading} handleResolutionScopeChange={handleResolutionScopeChange} />;
	}

	if (shouldShowDoubleResolution) {
		return (
			<>
				<VendorTaskButton isLoading={isLoading} handleResolutionScopeChange={handleResolutionScopeChange} />
				<ResolutionButton isLoading={isLoading} onClick={() => handleResolutionScopeChange('full')} label="Opravit vadu" />
			</>
		);
	}

	return <ResolutionButton isLoading={isLoading} onClick={() => handleResolutionScopeChange('serviceTask')} />;
};

const ResolutionButton: FC<{
	isLoading: boolean;
	onClick: () => void;
	label?: string;
}> = ({ isLoading, onClick, label = 'Uložit' }) => {
	return (
		<Button className="w-full" variant="default" isLoading={isLoading} type="submit" onClick={onClick}>
			{label}
		</Button>
	);
};

export const VendorTaskButton: FC<{ isLoading: boolean; handleResolutionScopeChange: (scope: ServiceTaskResolutionScope) => void }> = ({
	isLoading,
	handleResolutionScopeChange,
}) => {
	return <ResolutionButton isLoading={isLoading} onClick={() => handleResolutionScopeChange('vendorTask')} />;
};
