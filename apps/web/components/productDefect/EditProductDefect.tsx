import { MAX_POSITIVE_INTEGER, type ProductDefectSource, REQUIRED_FIELD } from '@pocitarna-nx-2023/config';
import {
	Button,
	ComboboxControl,
	DeleteDialog,
	FormContext,
	Icon,
	Media,
	Stack,
	TextControl,
	toast,
	useDialog,
} from '@pocitarna-nx-2023/ui';
import { filterUndefined, formatProductCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { useToggle } from 'rooks';
import { z } from 'zod';
import { useLazyComboboxProps } from '../../hooks/useLazyComboboxProps';
import { useSearchFilter } from '../../hooks/useSearchFilter';
import { TemporaryFileUploader } from '../TemporaryFileUploader';
import { ProductDefectFormCard } from './ProductDefectFormCard';

type Props = {
	defect: ApiBody<'getProductDefects'>[number];
	product: ApiBody<'getProduct'>;
	source: ProductDefectSource;
	order?: number;
	isUncovered?: boolean;
	disabled?: boolean;
	invalidate?: () => void;
};

export const EditProductDefect: FC<Props> = ({ defect, order, product, source, isUncovered = false, disabled = false, invalidate }) => {
	const [isUploadingFile, toggleUploadingFile] = useToggle();
	const { closeDialog } = useDialog();
	const { filter, updateSearchTerm } = useSearchFilter('name');

	const { mutate, isLoading: isUploadingForm } = apiHooks.useUpdateProductDefect({
		params: { defectId: defect.id },
	});
	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId: product.id } }, { enabled: false });
	const { invalidate: invalidateDefects } = apiHooks.useGetProductDefects({ params: { productId: product.id } }, { enabled: false });

	const { invalidate: invalidateAllProductDefects } = apiHooks.useGetAllProductDefects({}, { enabled: false });

	const { data: productDefectFilesData, invalidate: invalidateProductDefectFiles } = apiHooks.useGetProductDefectFiles({
		params: { defectId: defect.id },
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER },
	});
	const { mutate: deleteFiles, isLoading: isDeletingFiles } = apiHooks.useDeleteFiles(undefined, {
		onSuccess: () => invalidateProductDefectFiles(),
	});
	const { mutate: rotateImageFile, isLoading: isRotatingFile } = apiHooks.useRotateImageFile(undefined, {
		onSuccess: () => invalidateProductDefectFiles(),
	});
	const { mutate: updateSequence } = apiHooks.useUpdateProductDefectFileSequence(
		{ params: { productDefectId: defect.id } },
		{ onSuccess: () => invalidateProductDefectFiles() },
	);

	const { mutate: deleteDefect, isLoading: deleteDefectLoading } = apiHooks.useDeleteProductDefect({
		params: { defectId: defect.id, productId: product.id },
	});

	const productDefectFiles = productDefectFilesData?._data ?? [];

	const onDefectDelete = () => {
		if (!isOpen || disabled || (product.productDefects.length <= 1 && source !== 'TESTING')) return;

		deleteDefect(undefined, {
			onSuccess: () => {
				invalidate?.();
				invalidateProduct();
				invalidateDefects();
				invalidateAllProductDefects();
				toast.success('Vada byla smazána.');
				closeDialog();
			},
		});
	};

	const isOpen = !defect.serviceCaseId && !defect.serviceTask;

	const lazyComboboxProps = useLazyComboboxProps<ApiBody<'getDefectTypes'>[number]>('getDefectTypes', {
		queries: {
			filter: {
				...filter,
				name: {
					...(filter?.['name' as keyof typeof filter] ?? {}),
					ne: 'Neshodující se parametry',
				},
				'productCategory.id': { eq: product.productCategoryId },
			},
			sort: ['name'],
		},
		formatResult: (type) => ({ value: type.id, label: type.name }),
		selectedItem: { value: defect.defectType.id, label: defect.defectType.name },
	});

	return (
		<ProductDefectFormCard order={order} isUncovered={isUncovered}>
			<FormContext
				schema={z.object({
					note: z.string(),
					files: z.array(z.string()),
					defectType: z.string().uuid({ message: REQUIRED_FIELD }),
				})}
				defaultValues={{
					note: defect.note,
					files: filterUndefined(productDefectFiles.map((file) => file?.id) ?? []),
					defectType: defect.defectType.id,
				}}
				onSubmit={(data) => {
					if (disabled) return;

					mutate(
						{
							note: data.note,
							files: data.files.map((file) => ({ id: file })),
							defectType: { id: data.defectType },
							serviceTask: null,
						},
						{
							onSuccess: () => {
								invalidate?.();
								invalidateProduct();
								invalidateDefects();
								invalidateProductDefectFiles();
								toast.success('Zaktualizováno');
							},
						},
					);
				}}
			>
				{(control) => (
					<Stack gap={4}>
						<ComboboxControl
							control={control}
							name="defectType"
							label="Typ závady"
							onSearchChange={updateSearchTerm}
							{...lazyComboboxProps}
							disabled={disabled}
						/>

						<TextControl control={control} name="note" label="Popis závady" rows={4} disabled={disabled} />

						<Media
							files={productDefectFiles.map(({ file }) => file)}
							moveTo={disabled ? undefined : (file, sequence) => updateSequence({ fileId: file.id, sequence })}
							onDelete={disabled ? undefined : (file) => deleteFiles({ ids: [file.id] })}
							onRotation={disabled ? undefined : (file, rotation) => rotateImageFile({ fileId: file.id, rotation })}
							isLoading={isUploadingFile || isDeletingFiles || isRotatingFile}
						/>

						<TemporaryFileUploader
							inputName="files"
							pageType="defect"
							entityCode={formatProductCode(product.code)}
							hideSubmitButton={true}
							isUploadingForm={isUploadingForm}
							isUploadingFile={isUploadingFile}
							toggleUploadingFile={toggleUploadingFile}
							disabled={disabled}
						/>

						<Stack gap={4} direction="row">
							<Button
								type="submit"
								isLoading={isUploadingFile || isUploadingForm}
								className="grow"
								variant="secondary"
								disabled={deleteDefectLoading || disabled}
							>
								Upravit
							</Button>
							<DeleteDialog
								title="Smazat vadu"
								description="Opravdu si přejete smazat vadu?"
								onDelete={onDefectDelete}
								trigger={
									<Button
										type="button"
										variant="outline"
										disabled={
											isUploadingFile ||
											isUploadingForm ||
											!isOpen ||
											disabled ||
											(product.productDefects.length <= 1 && source !== 'TESTING')
										}
										isLoading={deleteDefectLoading}
										width="icon"
									>
										<Icon name="trash" />
									</Button>
								}
								isLoading={deleteDefectLoading}
							/>
						</Stack>
					</Stack>
				)}
			</FormContext>
		</ProductDefectFormCard>
	);
};
