import { type ProductDefectSource, SERVICE_TASK_STATUS_TYPES } from '@pocitarna-nx-2023/config';
import {
	Button,
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
	FormContext,
	Icon,
	NumericInputControl,
	Stack,
	TextControl,
	useDialog,
} from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { z } from 'zod';
import { useServiceTaskResolutionScope } from '../../hooks/useServiceTaskResolutionScope';
import { ServiceAttributeValues } from './ServiceAttributeValues';

const schema = z.object({
	note: z.string(),
	attributeValues: z.array(z.object({ attributeId: z.string().uuid(), attributeValueId: z.string().uuid() })),
	price: z.coerce.number(),
	status: z.enum(SERVICE_TASK_STATUS_TYPES),
});

type Props = {
	serviceTask: NonNullable<ApiBody<'getProductDefects'>[number]['serviceTask']>;
	productId: string;
	source?: ProductDefectSource;
};

export const ServiceTaskEditDialog: FC<Props> = ({ serviceTask, productId, source }) => {
	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button data-dialog="editServiceTask" size="sm" variant="outline" width="icon">
					<Icon name="pen" />
				</Button>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Upravit servisní zásah</DialogTitle>
				</DialogHeader>
				<ServiceTaskEditForm serviceTask={serviceTask} productId={productId} source={source} />
			</DialogContent>
		</Dialog>
	);
};

export const ServiceTaskEditForm: FC<Props> = ({ serviceTask, productId, source }) => {
	const { closeDialog } = useDialog();
	const { resolutionScope, handleResolutionScopeChange } = useServiceTaskResolutionScope();
	const shouldShowDoubleResolution = source === 'WARRANTY_CLAIM';

	const { mutate: updateServiceTask, isLoading } = apiHooks.useUpdateServiceTask({ params: { serviceTaskId: serviceTask.id } });
	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId } }, { enabled: false });
	const { invalidate: invalidateProductAttributeValues } = apiHooks.useGetProductAttributeValues(
		{ params: { productId } },
		{ enabled: false },
	);
	const { invalidate: invalidateProductDefects } = apiHooks.useGetAllProductDefects(undefined, { enabled: false });

	const handleSubmit = (data: z.infer<typeof schema>) => {
		updateServiceTask(
			{ ...data, resolutionScope },
			{
				onSuccess: () => {
					invalidateProduct();
					invalidateProductAttributeValues();
					invalidateProductDefects();
					closeDialog();
				},
			},
		);
	};

	return (
		<FormContext
			schema={schema}
			defaultValues={{ note: serviceTask.note, attributeValues: [], price: serviceTask.price, status: serviceTask.status }}
			onSubmit={handleSubmit}
		>
			{(control) => (
				<Stack gap={4}>
					<TextControl control={control} name="note" label="Poznámka" />
					<NumericInputControl numberFormat="decimal" control={control} name="price" label="Cena opravy bez DPH" />
					<ServiceAttributeValues productId={productId} />
					{shouldShowDoubleResolution ? (
						<>
							<Button
								className="w-full"
								variant="default"
								isLoading={isLoading}
								type="submit"
								onClick={() => {
									handleResolutionScopeChange('vendorTask');
								}}
							>
								Uložit
							</Button>
							<Button
								className="w-full"
								variant="default"
								isLoading={isLoading}
								type="submit"
								onClick={() => {
									handleResolutionScopeChange('full');
								}}
							>
								Opravit vadu
							</Button>
						</>
					) : (
						<Button
							className="w-full"
							variant="default"
							isLoading={isLoading}
							type="submit"
							onClick={() => {
								handleResolutionScopeChange('serviceTask');
							}}
						>
							Uložit
						</Button>
					)}
				</Stack>
			)}
		</FormContext>
	);
};
