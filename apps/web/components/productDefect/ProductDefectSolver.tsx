import { type ProductDefectSource, REQUIRED_FIELD } from '@pocitarna-nx-2023/config';
import { ComboboxControl, FormContext, MultiSelectControl, Stack, TextControl, useDialog } from '@pocitarna-nx-2023/ui';
import { safeDivide } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { z } from 'zod';
import { useLazyComboboxProps } from '../../hooks/useLazyComboboxProps';
import { useSearchFilter } from '../../hooks/useSearchFilter';
import { useServiceTaskResolutionScope } from '../../hooks/useServiceTaskResolutionScope';
import { DefectResolutionButtons } from './DefectResolutionButtons';
import { ServiceAttributeValues } from './ServiceAttributeValues';
import { ServiceTaskPriceInput } from './ServiceTaskPriceInput';

export const defectResolutionSchema = z.object({
	note: z.string(),
	serviceTaskTypeId: z.string().uuid({ message: REQUIRED_FIELD }),
	productDefectIds: z.array(z.string().uuid({ message: REQUIRED_FIELD })).min(1, { message: REQUIRED_FIELD }),
	attributeValues: z.array(z.object({ attributeId: z.string().uuid(), attributeValueId: z.string().uuid() })),
	price: z.coerce.number(),
});

type Props = {
	unsolvedDefects: ApiBody<'getAllProductDefects'>;
	source?: ProductDefectSource;
};

export const ProductDefectSolver: FC<Props> = ({ unsolvedDefects, source }) => {
	const { closeDialog } = useDialog();
	const { resolutionScope, handleResolutionScopeChange } = useServiceTaskResolutionScope();
	const { updateSearchTerm, filter: searchTermFilter } = useSearchFilter('name');

	const { mutate: bulkCreateServiceTask, isLoading } = apiHooks.useBulkCreateServiceTask();
	const { invalidate: invalidateProduct } = apiHooks.useGetProduct(
		{ params: { productId: unsolvedDefects[0]?.productId ?? '' } },
		{ enabled: false },
	);
	const { invalidate: invalidateProductAttributeValues } = apiHooks.useGetProductAttributeValues(
		{ params: { productId: unsolvedDefects[0]?.productId ?? '' } },
		{ enabled: false },
	);

	const { invalidate: invalidateProductDefects } = apiHooks.useGetAllProductDefects(undefined, { enabled: false });

	const serviceTaskTypesLazyComboboxProps = useLazyComboboxProps<ApiBody<'getServiceTaskTypes'>[number]>('getServiceTaskTypes', {
		queries: { filter: { ...searchTermFilter, type: { eq: '{SERVICE}' } } },
		formatResult: (type) => ({ value: type.id, label: type.name }),
	});

	const handleSubmit = (data: z.infer<typeof defectResolutionSchema>) => {
		const individualPrice = safeDivide(data.price, data.productDefectIds.length);

		bulkCreateServiceTask(
			{
				price: individualPrice,
				attributeValues: data.attributeValues,
				note: data.note,
				serviceTaskTypeId: data.serviceTaskTypeId,
				productDefectsIds: data.productDefectIds,
				status: 'CLOSED',
				resolutionScope,
				priceType: source === 'CUSTOMER_CLAIM' ? 'customerClaim' : 'service',
			},
			{
				onSuccess: () => {
					invalidateProduct();
					invalidateProductAttributeValues();
					invalidateProductDefects();
					closeDialog();
				},
			},
		);
	};

	if (unsolvedDefects.length === 0) return null;

	return (
		<FormContext
			schema={defectResolutionSchema}
			defaultValues={{ note: '', serviceTaskTypeId: '', productDefectIds: [], attributeValues: [], price: 0 }}
			onSubmit={handleSubmit}
		>
			{(control) => (
				<Stack gap={4}>
					<MultiSelectControl
						control={control}
						name="productDefectIds"
						label="Vybrat vady produktu"
						options={unsolvedDefects.map((item) => ({ value: item.id, label: item.defectType.name }))}
					/>
					<ComboboxControl
						control={control}
						name="serviceTaskTypeId"
						label="Vyřešit pomocí"
						enableFilter
						onSearchChange={updateSearchTerm}
						{...serviceTaskTypesLazyComboboxProps}
					/>

					<ServiceTaskPriceInput />
					{unsolvedDefects[0].productId && <ServiceAttributeValues productId={unsolvedDefects[0].productId} />}
					<TextControl control={control} name="note" label="Poznámka" />
					<DefectResolutionButtons
						unsolvedDefects={unsolvedDefects}
						isLoading={isLoading}
						source={source}
						handleResolutionScopeChange={handleResolutionScopeChange}
					/>
				</Stack>
			)}
		</FormContext>
	);
};
