import { type ProductDefectSource } from '@pocitarna-nx-2023/config';
import { Button, Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { ProductDefectSolver } from './ProductDefectSolver';

type Props = {
	unsolvedDefects: ApiBody<'getAllProductDefects'>;
	source?: ProductDefectSource;
	disabled: boolean;
};

export const ProductDefectSolverDialog: FC<Props> = ({ unsolvedDefects, source, disabled }) => (
	<Dialog>
		<DialogTrigger asChild>
			<Button disabled={disabled}>Řešit vady</Button>
		</DialogTrigger>
		<DialogContent>
			<DialogHeader>
				<DialogTitle>Řešit vady</DialogTitle>
			</DialogHeader>
			<ProductDefectSolver unsolvedDefects={unsolvedDefects} source={source} />
		</DialogContent>
	</Dialog>
);
