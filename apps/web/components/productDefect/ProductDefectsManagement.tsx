import { type ProductDefectSource } from '@pocitarna-nx-2023/config';
import { Button, Icon, Stack } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC, useEffect, useRef, useState } from 'react';
import { AddProductDefect } from './AddProductDefect';
import { EditProductDefect } from './EditProductDefect';
import { ProductDefectCard } from './ProductDefectCard';

type Props = {
	product: ApiBody<'getProduct'>;
	source: ProductDefectSource;
	serviceCaseId?: string;
	warrantyClaimId?: string;
	customerClaimId?: string;
	vendorUncoveredDefectTypes?: string[];
	disabled?: boolean;
	invalidate?: () => void;
};

export const ProductDefectsManagement: FC<Props> = ({
	product,
	source,
	serviceCaseId,
	warrantyClaimId,
	customerClaimId,
	vendorUncoveredDefectTypes = [],
	disabled = false,
	invalidate,
}) => {
	const [showAddDefect, setShowAddDefect] = useState<boolean>();
	const addRef = useRef<HTMLButtonElement>(null);

	const defects = product.productDefects ?? [];

	let pertinentDefects = defects;

	if (serviceCaseId) {
		pertinentDefects = defects.filter((item) => item.serviceCaseId === serviceCaseId);
	} else if (warrantyClaimId) {
		pertinentDefects = defects.filter((item) => item.warrantyClaimId === warrantyClaimId);
	} else if (customerClaimId) {
		pertinentDefects = defects.filter((item) => item.customerClaimId === customerClaimId);
	}

	useEffect(() => {
		if (showAddDefect === false) addRef.current?.focus();
	}, [addRef, showAddDefect]);

	return (
		<Stack gap={4}>
			{pertinentDefects.map((defect, i) => {
				const isUncovered = vendorUncoveredDefectTypes.includes(defect.defectType.id);

				return defect.serviceTaskId || defect.vendorTaskId ? (
					<ProductDefectCard key={defect.id} product={product} defect={defect} isUncovered={isUncovered} source={source} />
				) : (
					<EditProductDefect
						key={defect.id}
						defect={defect}
						order={i + 1}
						source={source}
						product={product}
						isUncovered={isUncovered}
						disabled={disabled}
						invalidate={invalidate}
					/>
				);
			})}
			{showAddDefect && (
				<AddProductDefect
					product={product}
					source={source}
					onSuccess={() => {
						setShowAddDefect(false);
						invalidate?.();
					}}
					order={pertinentDefects.length + 1}
					serviceCaseData={serviceCaseId ? { id: serviceCaseId } : undefined}
					warrantyClaimId={warrantyClaimId}
					customerClaimId={customerClaimId}
					disabled={disabled}
				/>
			)}

			<Button ref={addRef} type="button" variant="outline" onClick={() => setShowAddDefect(!showAddDefect)} disabled={disabled}>
				{showAddDefect ? (
					<>
						<Icon name="minus" /> Zavřít přidávání závady
					</>
				) : (
					<>
						<Icon name="plus" /> Přidat závadu
					</>
				)}
			</Button>
		</Stack>
	);
};
