import { Alert, AlertDescription, AlertTitle } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { PriceDisplayer } from '../PriceDisplayer';

type Props = {
	productDefect: ApiBody<'getProductDefects'>[number];
};

export const ProductDefectResolution: FC<Props> = ({ productDefect }) => {
	const serviceTask = productDefect.serviceTask;
	const vendorTask = productDefect.vendorTask;

	const taskToShow = serviceTask ?? vendorTask;

	if (!taskToShow) return null;

	const taskHasOnlyVendorResolution = productDefect.serviceTaskId == null && productDefect.vendorTaskId != null;

	return (
		<Alert variant={taskHasOnlyVendorResolution ? 'info' : taskToShow.status === 'CLOSED' ? 'success' : 'warning'}>
			<AlertTitle>
				{taskToShow.status === 'CLOSED' ? 'Vyřešeno' : 'V řešení'} pomocí <em>{taskToShow.serviceTaskType.name}</em>
				{taskToShow.price !== 0 && (
					<>
						{' '}
						za <PriceDisplayer price={taskToShow.price} variant="inline" />
					</>
				)}
				.
			</AlertTitle>
			<AlertDescription>{taskToShow.note}</AlertDescription>
		</Alert>
	);
};
