import { Button, Icon } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import type { FC } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { ServiceAttributeValuesField } from './ServiceAttributeValuesField';

type Props = {
	productId: string;
};

type FormSection = {
	attributeValues: { attributeId: string; attributeValueId: string }[];
};

export const ServiceAttributeValues: FC<Props> = ({ productId }) => {
	const { data: product } = apiHooks.useGetProduct({ params: { productId } });
	const { control } = useFormContext<FormSection>();
	const { fields, insert, remove } = useFieldArray<FormSection>({
		control,
		name: 'attributeValues',
	});

	return (
		<>
			<p><PERSON><PERSON><PERSON> ke z<PERSON>ěně parametrů u produktu?</p>
			{product?._data &&
				fields.map((field, index) => (
					<ServiceAttributeValuesField key={field.id} product={product._data} index={index} onRemove={() => remove(index)} />
				))}
			<Button type="button" variant="outline" onClick={() => insert(fields.length, { attributeId: '', attributeValueId: '' })}>
				<Icon name="plus" /> přidat další parametr
			</Button>
		</>
	);
};
