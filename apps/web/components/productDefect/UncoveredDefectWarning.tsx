import { CardDescription, cn, Icon } from '@pocitarna-nx-2023/ui';
import { type FC } from 'react';

type Props = {
	className?: string;
};

export const UncoveredDefectWarning: FC<Props> = ({ className }) => {
	return (
		<CardDescription className={cn('text-warning items-center flex gap-1', className && className)}>
			<Icon name="warning" className="w-4 h-4" />
			Tento typ závady nelze dodavateli reklamovat.
		</CardDescription>
	);
};
