import { REQUIRED_FIELD } from '@pocitarna-nx-2023/config';
import { NumericInputControl } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { z } from 'zod';

const schema = z.object({
	serviceTaskTypeId: z.string().uuid({ message: REQUIRED_FIELD }),
	price: z.coerce.number(),
});

export const ServiceTaskPriceInput: FC = () => {
	const { watch, setValue, control } = useFormContext<z.infer<typeof schema>>();
	const serviceTaskTypeId = watch('serviceTaskTypeId');
	const { data } = apiHooks.useGetServiceTaskType({ params: { serviceTaskTypeId } }, { enabled: Boolean(serviceTaskTypeId) });

	useEffect(() => {
		if (data?._data) {
			setValue('price', data._data.price);
		}
	}, [data?._data, setValue]);

	return <NumericInputControl numberFormat="decimal" control={control} name="price" label="Cena opravy bez DPH" />;
};
