import { CardContent, cn } from '@pocitarna-nx-2023/ui';
import { type FC, type PropsWithChildren } from 'react';
import { DefectCardWrapper } from './DefectCardWrapper';
import { UncoveredDefectWarning } from './UncoveredDefectWarning';

type Props = PropsWithChildren<{
	order?: number;
	isUncovered?: boolean;
}>;

export const ProductDefectFormCard: FC<Props> = ({ order, isUncovered = false, children }) => {
	return (
		<DefectCardWrapper isUncovered={isUncovered}>
			<CardContent>
				{isUncovered && <UncoveredDefectWarning className="mb-4" />}
				<div className={cn('relative', order && 'pt-11 md:pl-11 md:pt-0')}>
					{order && (
						<span className="absolute top-0 left-0 rounded-full w-8 h-8 bg-black text-white flex items-center justify-center font-semibold">
							{order}
						</span>
					)}
					{children}
				</div>
			</CardContent>
		</DefectCardWrapper>
	);
};
