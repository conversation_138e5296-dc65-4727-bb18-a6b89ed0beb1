import { PRIORITY_NAMES, PRIORITY_VALUES, PriorityMessage } from '@pocitarna-nx-2023/config';
import { Button, ComboboxControl, FormContext, Stack, toast, useDialog } from '@pocitarna-nx-2023/ui';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useQueryClient } from '@tanstack/react-query';
import { type FC } from 'react';
import { z } from 'zod';

type Props = {
	order: ApiBody<'getEcommerceOrder'>;
};

export const OrderPriorityForm: FC<Props> = ({ order }) => {
	const queryClient = useQueryClient();
	const { closeDialog } = useDialog();
	// eslint-disable-next-line @typescript-eslint/ban-ts-comment
	// @ts-ignore - Too deep type, skip checking
	const getEcommerceOrderQueryKey = apiHooks.getKeyByAlias('getEcommerceOrder', { params: { ecommerceOrderId: order.id } });
	const invalidateEcommerceOrder = () => queryClient.invalidateQueries(getEcommerceOrderQueryKey);
	const getEcommerceOrdersQueryKey = apiHooks.getKeyByAlias('getEcommerceOrders');
	const invalidateEcommerceOrders = () => queryClient.invalidateQueries(getEcommerceOrdersQueryKey);
	const { mutate: updateEcommerceOrder, isLoading } = apiHooks.useUpdateEcommerceOrder(
		{ params: { ecommerceOrderId: order.id } },
		{
			onSuccess: () => {
				toast.success('Změněna priorita');
				invalidateEcommerceOrder();
				invalidateEcommerceOrders();
				closeDialog();
			},
		},
	);

	return (
		<FormContext
			schema={z.object({ priority: z.number() })}
			defaultValues={{ priority: PRIORITY_VALUES.MEDIUM }}
			onSubmit={(data) => updateEcommerceOrder(data)}
		>
			{(control) => (
				<Stack gap={4}>
					<ComboboxControl
						control={control}
						name="priority"
						label="Priorita"
						placeholder="Vyberte prioritu"
						items={PRIORITY_NAMES.map((name) => ({
							label: PriorityMessage[name],
							value: PRIORITY_VALUES[name] as unknown as string,
						}))}
						emptyElement="Nenalezena žádná priorita"
						enableFilter
					/>

					<Button variant="default" type="submit" isLoading={isLoading}>
						Uložit
					</Button>
				</Stack>
			)}
		</FormContext>
	);
};
