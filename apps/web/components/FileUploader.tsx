import { type PHOTO_USAGE, type PHOTO_USAGE_ENTITY } from '@pocitarna-nx-2023/config';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, cn, FileInputForUpload, Spinner, Stack } from '@pocitarna-nx-2023/ui';
import { type FC, type ReactNode, useState } from 'react';
import ReactDropzone from 'react-dropzone';
import { DialogQRAppPhotoSession } from './DialogQRAppPhotoSession';
import { DialogQRUploadPhoto } from './DialogQRUploadPhoto';

type Props = {
	inputName: string;
	pageType: keyof typeof PHOTO_USAGE;
	entityCode: string;
	isAddingFiles: boolean;
	addFilesToEntity: (fileIds: string[]) => void;
	toggleUploadingFile?: () => void;
	entityId?: string;
	entityType?: keyof typeof PHOTO_USAGE_ENTITY;
	photoSessionId?: string | null;
	label?: string;
	description?: ReactNode;
	setPhotoSessionId?: (photoSessionId: string | null) => void;
	disabled?: boolean;
	children?: ReactNode;
	withQrPhotoUpload?: boolean;
};

export const FileUploader: FC<Props> = ({
	inputName,
	label,
	pageType,
	entityCode,
	entityId,
	entityType,
	isAddingFiles,
	addFilesToEntity,
	toggleUploadingFile,
	description,
	disabled = false,
	photoSessionId,
	setPhotoSessionId,
	children,
	withQrPhotoUpload = true,
}) => {
	const [droppedFileList, setDroppedFileList] = useState<null | FileList>(null);
	const isEntityFileUploader = entityId != null && entityType != null;
	const isTemporaryFileUploader = setPhotoSessionId != null;

	const compoundAfter =
		isEntityFileUploader && withQrPhotoUpload ? (
			<DialogQRUploadPhoto usage={pageType} entityCode={entityCode} entityType={entityType} entityId={entityId} />
		) : isTemporaryFileUploader && withQrPhotoUpload ? (
			<DialogQRAppPhotoSession
				usage={pageType}
				entityCode={entityCode}
				photoSessionId={photoSessionId ?? null}
				setPhotoSessionId={setPhotoSessionId}
				disabled={disabled}
			/>
		) : undefined;

	const content = (
		<Stack>
			{description && <CardDescription>{description}</CardDescription>}
			{children}

			{!disabled && (
				<Stack gap={4} className="grow">
					<FileInputForUpload
						name={inputName}
						label={label ?? 'Přidat soubory'}
						toggleUploading={toggleUploadingFile}
						disabled={isAddingFiles}
						droppedFileList={droppedFileList}
						addFilesToEntity={addFilesToEntity}
						compoundAfter={compoundAfter}
					/>

					{isAddingFiles && (
						<p>
							Soubory se nahrávají <Spinner />
						</p>
					)}
				</Stack>
			)}
		</Stack>
	);

	const contentToShow = isEntityFileUploader ? (
		<Card>
			<CardHeader>
				<CardTitle>Soubory</CardTitle>
			</CardHeader>
			<CardContent>{content}</CardContent>
		</Card>
	) : (
		content
	);

	return (
		<ReactDropzone
			multiple
			onDropAccepted={(files) => setDroppedFileList(files as unknown as FileList)}
			noClick
			disabled={isAddingFiles || disabled}
		>
			{({ isDragActive, getRootProps }) => (
				<Stack
					className={cn('focus:outline-none', isDragActive && 'outline-dashed outline-amber-500 bg-amber-100')}
					{...getRootProps()}
				>
					{contentToShow}
				</Stack>
			)}
		</ReactDropzone>
	);
};
