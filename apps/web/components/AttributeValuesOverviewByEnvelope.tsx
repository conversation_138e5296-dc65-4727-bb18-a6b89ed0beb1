import type { AttributeCategoryType } from '@pocitarna-nx-2023/config';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import type { FC } from 'react';
import { useCategoryAttributes } from '../hooks/useCategoryAttributes';
import { useEnvelopeMappedAttributeValues } from '../hooks/useEnvelopeMappedAttributeValues';
import { AttributeValuesOverview } from './stock/AttributeValuesOverview';

type Include = 'brand' | 'model' | 'other';

type Props = {
	envelope: ApiBody<'getProductEnvelope'>;
	categoryAttributeType?: AttributeCategoryType;
	include: Include[];
};

export const AttributeValuesOverviewByEnvelope: FC<Props> = ({
	envelope,
	categoryAttributeType = 'stock',
	include = ['brand', 'model', 'other'],
}) => {
	const { categoryAttributes } = useCategoryAttributes(envelope.productCategoryId, categoryAttributeType);

	const mappedAttributesValue = useEnvelopeMappedAttributeValues(categoryAttributes, envelope.id);

	const mappedStockAttributeValues = mappedAttributesValue
		.map((attributeValue) => {
			const filteredMatches = attributeValue.matches.filter((match) => match.value !== '-');
			return { ...attributeValue, matches: filteredMatches };
		})
		.filter((attributeValue) => attributeValue.matches.length > 0);

	const brandStockAttributeValues = include.includes('brand')
		? mappedStockAttributeValues.filter((attributeValue) =>
				['Značka', 'Značka (výrobce)'].includes(attributeValue.matches[0]?.attribute.displayName),
			)
		: [];

	const modelStockAttributeValues = include.includes('model')
		? mappedStockAttributeValues.filter((attributeValue) => attributeValue.matches[0]?.attribute.displayName === 'Model')
		: [];

	const otherStockAttributeValues = include.includes('other')
		? mappedStockAttributeValues.filter(
				(attributeValue) => !['Značka', 'Značka (výrobce)', 'Model'].includes(attributeValue.matches[0]?.attribute.displayName),
			)
		: [];

	return (
		<AttributeValuesOverview
			brandStockAttributeValues={brandStockAttributeValues}
			modelStockAttributeValues={modelStockAttributeValues}
			otherStockAttributeValues={otherStockAttributeValues}
		/>
	);
};
