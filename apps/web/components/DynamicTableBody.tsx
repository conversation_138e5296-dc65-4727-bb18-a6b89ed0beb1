import { type DataTable, TableBody } from '@pocitarna-nx-2023/ui';
import { Fragment, type ReactNode, useEffect, useMemo } from 'react';

type Props<T> = {
	items: T[];
	renderRow: (item: T) => ReactNode;
	table: DataTable;
	getKey: (item: T) => string | number;
};
export const DynamicTableBody = <T,>({ items, renderRow, table, getKey }: Props<T>) => {
	const itemKeys = useMemo(() => items.map(getKey), [items, getKey]);

	useEffect(() => {
		table.reset();
		// Table is unstable, hence not included here
	}, [itemKeys]);

	return (
		<TableBody>
			{items.map((item) => {
				// Not ideal using the fragment, but better than having to remember to pass it
				return <Fragment key={getKey(item)}>{renderRow(item)}</Fragment>;
			})}
		</TableBody>
	);
};
