import { useDialog } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useCallback, useState } from 'react';
import { LabelPrint } from '../printer/LabelPrint';

type Props = {
	warehousePositionIds: string[];
};

export const WarehousePositionBulkLabelPrint: FC<Props> = ({ warehousePositionIds }) => {
	const [shouldPrint, setShouldPrint] = useState(false);
	const { closeDialog } = useDialog();

	const { data: printLabelsData, isFetching: isLoadingLabels } = apiHooks.useGetBulkPrintLabelsWarehousePosition(
		{ queries: { warehousePositionIds } },
		{ enabled: shouldPrint && warehousePositionIds.length > 0 },
	);

	const onPrintPress = useCallback(async () => {
		closeDialog();
	}, [closeDialog]);

	return (
		<LabelPrint
			buttonLabel="Vytisknout štítky"
			printLabels={printLabelsData?._data}
			printerLocation="TESTING"
			isLoadingLabels={isLoadingLabels}
			handleClick={() => setShouldPrint(true)}
			onPrintPress={onPrintPress}
			variant="default"
		/>
	);
};
