import { MAX_POSITIVE_INTEGER, TEN_SECONDS } from '@pocitarna-nx-2023/config';
import { DeleteDialog, Popover, PopoverContent, PopoverTrigger, Stack, Table, TableBody, TableCell, TableRow } from '@pocitarna-nx-2023/ui';
import { formatProductCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useCallback } from 'react';
import { DialogQRUploadPhoto } from '../DialogQRUploadPhoto';
import { CosmeticDefectFiles } from './CosmeticDefectFiles';

type Props = {
	product: ApiBody<'getProduct'>;
	productCosmeticDefectsByArea: ApiBody<'getProductCosmeticDefects'>;
	onSuccess?: () => void;
	interactive?: boolean;
};

export const CosmeticDefectByAreaTable: FC<Props> = ({ product, productCosmeticDefectsByArea, onSuccess, interactive = true }) => {
	return (
		<Table>
			<TableBody>
				{productCosmeticDefectsByArea.map((item) => (
					<CosmeticDefectByAreaTableRow
						key={item.id}
						productCosmeticDefect={item}
						product={product}
						onSuccess={onSuccess}
						interactive={interactive}
					/>
				))}
			</TableBody>
		</Table>
	);
};

const CosmeticDefectByAreaTableRow: FC<
	Omit<Props, 'productCosmeticDefectsByArea'> & {
		productCosmeticDefect: ApiBody<'getProductCosmeticDefects'>[number];
	}
> = ({ productCosmeticDefect, product, onSuccess, interactive = true }) => {
	const { mutate: deleteCosmeticDefect } = apiHooks.useDeleteProductCosmeticDefect({
		params: { productId: product.id, cosmeticDefectId: productCosmeticDefect.cosmeticDefectId },
	});

	const { data: defectFilesData } = apiHooks.useGetProductCosmeticDefectFiles(
		{
			params: { productCosmeticDefectId: productCosmeticDefect.id },
			queries: { page: 1, limit: MAX_POSITIVE_INTEGER },
		},
		{ refetchInterval: TEN_SECONDS },
	);
	const defectFiles = defectFilesData?._data ?? [];

	const onDelete = useCallback(() => {
		deleteCosmeticDefect(undefined, {
			onSuccess,
		});
	}, [deleteCosmeticDefect, onSuccess]);

	return (
		<TableRow>
			<TableCell>
				<strong>{productCosmeticDefect.cosmeticDefect.name}</strong>
			</TableCell>
			<TableCell className="w-0">
				{defectFiles.length > 0 && (
					<Popover>
						<PopoverTrigger asChild>
							<span className="text-link cursor-pointer">Média</span>
						</PopoverTrigger>
						<PopoverContent>
							<CosmeticDefectFiles
								defectFiles={defectFiles}
								productCosmeticDefect={productCosmeticDefect}
								interactive={interactive}
							/>
						</PopoverContent>
					</Popover>
				)}
			</TableCell>
			{interactive && (
				<TableCell className="w-0">
					<Stack direction="row" className="justify-end" gap={2}>
						{productCosmeticDefect.cosmeticDefect.pictureRequired && (
							<DialogQRUploadPhoto
								usage="detail"
								entityCode={formatProductCode(product.code)}
								entityId={productCosmeticDefect.id}
								entityType="productCosmeticDefect"
								withButtonText={false}
							/>
						)}
						<DeleteDialog
							title="Smazat vadu"
							description="Opravdu chcete smazat položku?"
							onDelete={onDelete}
							triggerVariant="icon"
						/>
					</Stack>
				</TableCell>
			)}
		</TableRow>
	);
};
