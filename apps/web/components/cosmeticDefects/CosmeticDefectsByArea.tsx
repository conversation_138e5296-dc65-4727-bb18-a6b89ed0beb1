import { CheckboxControl, ComboboxControl, Stack, toast } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useCallback, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { CosmeticDefectByAreaTable } from './CosmeticDefectsByAreaTable';

type Props = {
	cosmeticAreaId: string;
	product: ApiBody<'getProduct'>;
	areaCosmeticDefects: ApiBody<'getCosmeticDefects'>;
	productCosmeticDefectsByArea: ApiBody<'getProductCosmeticDefects'>;
	interactive?: boolean;
	isFix?: boolean;
};

export const CosmeticDefectsByArea: FC<Props> = ({
	cosmeticAreaId,
	product,
	areaCosmeticDefects,
	productCosmeticDefectsByArea,
	interactive = true,
	isFix = false,
}) => {
	const { invalidate: invalidateProductCosmeticDefects } = apiHooks.useGetProductCosmeticDefects(
		{ queries: { filter: { productId: { eq: product.id } } } },
		{ enabled: false },
	);

	const onSuccess = useCallback(() => {
		invalidateProductCosmeticDefects();
		toast.success('Zaktualizováno');
	}, [invalidateProductCosmeticDefects]);

	return (
		<Stack gap={4}>
			{interactive && (
				<InteractiveElements
					areaCosmeticDefects={areaCosmeticDefects}
					cosmeticAreaId={cosmeticAreaId}
					product={product}
					isFix={isFix}
					onSuccess={onSuccess}
					productCosmeticDefectsByArea={productCosmeticDefectsByArea}
				/>
			)}

			{productCosmeticDefectsByArea.length > 0 && (
				<CosmeticDefectByAreaTable
					product={product}
					productCosmeticDefectsByArea={productCosmeticDefectsByArea}
					onSuccess={onSuccess}
					interactive={interactive}
				/>
			)}

			{productCosmeticDefectsByArea.length === 0 && !interactive && (
				<p className="text-muted-foreground">Žádné kosmetické vady v této oblasti</p>
			)}
		</Stack>
	);
};

const InteractiveElements: FC<Props & { onSuccess: () => void }> = ({
	areaCosmeticDefects,
	cosmeticAreaId,
	product,
	onSuccess,
	isFix,
	productCosmeticDefectsByArea,
}) => {
	const { control, watch } = useFormContext<{ clearCategoryCosmeticAreas: Record<string, boolean> }>();
	const areaIsMarkedAsAllClear = watch(`clearCategoryCosmeticAreas.${cosmeticAreaId}`);

	return (
		<>
			<DefectPicker
				areaCosmeticDefects={areaCosmeticDefects}
				cosmeticAreaId={cosmeticAreaId}
				product={product}
				isFix={isFix}
				onSuccess={onSuccess}
				disabled={areaIsMarkedAsAllClear}
			/>

			<CheckboxControl
				control={control}
				name={`clearCategoryCosmeticAreas.${cosmeticAreaId}`}
				label="Žádné kosmetické vady v této oblasti"
				disabled={productCosmeticDefectsByArea.length > 0}
			/>
		</>
	);
};

const DefectPicker: FC<Omit<Props, 'interactive' | 'productCosmeticDefectsByArea'> & { onSuccess: () => void; disabled?: boolean }> = ({
	areaCosmeticDefects,
	cosmeticAreaId,
	product,
	onSuccess,
	isFix,
	disabled = false,
}) => {
	const { control } = useFormContext<{ cosmeticDefects: Record<string, string[]> }>();

	const { mutate: addCosmeticDefect } = apiHooks.useAddCosmeticDefectsToProductArea({
		params: { productId: product.id, cosmeticAreaId },
	});

	const valueName = useMemo(() => `cosmeticDefects.${cosmeticAreaId}` as const, [cosmeticAreaId]);

	const onSelect = useCallback(
		(cosmeticDefectId: string) => {
			addCosmeticDefect(
				{ cosmeticDefectId, isFix },
				{
					onSuccess,
				},
			);
		},
		[addCosmeticDefect, isFix, onSuccess],
	);

	return (
		<ComboboxControl
			control={control}
			name={valueName}
			onSelect={onSelect}
			items={areaCosmeticDefects.map((item) => ({ value: item.id, label: item.name }))}
			placeholder="Vyberte vady"
			searchPlaceholder="Vyhledat vady"
			enableFilter
			disabled={disabled}
		/>
	);
};
