import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Title } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { CosmeticDefectsBreakdown } from './CosmeticDefectsBreakdown';

type Props = {
	product: ApiBody<'getProduct'>;
};

export const CosmeticDefectsCard: FC<Props> = ({ product }) => {
	return (
		<Card>
			<CardHeader>
				<CardTitle>Kosmetické vady</CardTitle>
			</CardHeader>
			<CardContent>
				<CosmeticDefectsBreakdown product={product} isFix={false} interactive={false} />
			</CardContent>
		</Card>
	);
};
