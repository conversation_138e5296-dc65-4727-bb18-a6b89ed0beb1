import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { Media } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';

type Props = {
	productCosmeticDefect: ApiBody<'getProductCosmeticDefects'>[number];
	disabled?: boolean;
	defectFiles: ApiBody<'getProductCosmeticDefectFiles'>;
	interactive: boolean;
};

export const CosmeticDefectFiles: FC<Props & { defectFiles: ApiBody<'getProductCosmeticDefectFiles'>; interactive: boolean }> = ({
	productCosmeticDefect,
	disabled = false,
	defectFiles,
	interactive,
}) => {
	const { invalidate: invalidateProductCosmeticDefects } = apiHooks.useGetProductCosmeticDefects(
		{
			queries: {
				filter: {
					productId: {
						eq: productCosmeticDefect.productId,
					},
				},
			},
		},
		{ enabled: false },
	);

	const { invalidate: invalidateDefectFiles } = apiHooks.useGetProductCosmeticDefectFiles(
		{
			params: { productCosmeticDefectId: productCosmeticDefect.id },
			queries: { page: 1, limit: MAX_POSITIVE_INTEGER },
		},
		{ enabled: false },
	);

	const onSuccess = () => {
		invalidateProductCosmeticDefects();
		invalidateDefectFiles();
	};

	const { mutate: deleteFiles, isLoading: isDeletingFiles } = apiHooks.useDeleteFiles(undefined, {
		onSuccess,
	});
	const { mutate: rotateImageFile, isLoading: isRotatingFile } = apiHooks.useRotateImageFile(undefined, {
		onSuccess,
	});
	const { mutate: updateSequence } = apiHooks.useUpdateProductCosmeticDefectFileSequence(
		{ params: { productCosmeticDefectId: productCosmeticDefect.id } },
		{ onSuccess },
	);

	return (
		<Media
			files={defectFiles.map(({ file }) => file)}
			moveTo={!disabled && interactive ? (file, sequence) => updateSequence({ fileId: file.id, sequence }) : undefined}
			onDelete={disabled || !interactive ? undefined : (file) => deleteFiles({ ids: [file.id] })}
			onRotation={disabled || !interactive ? undefined : (file, rotation) => rotateImageFile({ fileId: file.id, rotation })}
			isLoading={isDeletingFiles || isRotatingFile}
		/>
	);
};
