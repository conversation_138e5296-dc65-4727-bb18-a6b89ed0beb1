import { NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { Table, TableBody, TableCell, TableRow } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC, memo, type PropsWithChildren } from 'react';
import { useCosmeticDefectsFormData } from '../../hooks/useCosmeticDefectsFormData';
import { CosmeticDefectsByArea } from './CosmeticDefectsByArea';

type Props = {
	product: ApiBody<'getProduct'>;
	isFix?: boolean;
};

export const CosmeticDefectsBreakdown: FC<PropsWithChildren<Props & { interactive?: boolean }>> = memo(
	({ product, isFix, interactive = true, children }) => {
		const { cosmeticAreas, productCosmeticDefects, categoryCosmeticDefects } = useCosmeticDefectsFormData(product);

		if (!interactive && productCosmeticDefects.length < 1) {
			return NOT_AVAILABLE;
		}

		return (
			<Table>
				<TableBody>
					{cosmeticAreas.map((area) => {
						return (
							<TableRow key={area.id}>
								<TableCell>{area.name}</TableCell>
								<TableCell>
									<CosmeticDefectsByArea
										cosmeticAreaId={area.id}
										product={product}
										areaCosmeticDefects={categoryCosmeticDefects.filter((defect) =>
											defect.cosmeticAreaCosmeticDefects?.some((item) => item.cosmeticAreaId === area.id),
										)}
										productCosmeticDefectsByArea={productCosmeticDefects.filter(
											(defect) => defect.cosmeticAreaId === area.id,
										)}
										isFix={isFix}
										interactive={interactive}
									/>
								</TableCell>
							</TableRow>
						);
					})}
				</TableBody>
				{children}
			</Table>
		);
	},
);

CosmeticDefectsBreakdown.displayName = 'CosmeticDefectsBreakdown';
