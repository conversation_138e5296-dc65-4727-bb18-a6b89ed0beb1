import { PRODUCT_TYPES, ProductTypeMessage } from '@pocitarna-nx-2023/config';
import { ComboboxControl, Stack, TableCell, TableFooter, TableRow } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC, memo, type PropsWithChildren, useEffect } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { type z } from 'zod';
import { useTestDiffContext } from '../../contexts/TestDiffContext';
import { useCosmeticDefectsFormData } from '../../hooks/useCosmeticDefectsFormData';
import { useUserHasScope } from '../../hooks/useUserHasScope';
import { CosmeticDefectsBreakdown } from './CosmeticDefectsBreakdown';

type Props = {
	product: ApiBody<'getProduct'>;
	isFix?: boolean;
};

export const CosmeticDefectsHandling: FC<PropsWithChildren<Props>> = memo(({ product, isFix, children }) => {
	const isTestLead = useUserHasScope('productTestLead');
	const { rankedGrades, schema: cosmetiDefectsSchema } = useCosmeticDefectsFormData(product);
	const testDiffContext = useTestDiffContext();

	const { control, watch } = useFormContext<z.input<typeof cosmetiDefectsSchema>>();
	const gradeId = watch('gradeId');
	const productType = watch('type');
	const clearCategoryCosmeticAreas = useWatch({
		control,
		name: 'clearCategoryCosmeticAreas',
	});

	useEffect(() => {
		if (testDiffContext) {
			testDiffContext.setGradeId(gradeId);
			testDiffContext.setProductType(productType);
			testDiffContext.setclearCategoryCosmeticAreas(clearCategoryCosmeticAreas);
		}
	}, [gradeId, productType, testDiffContext, clearCategoryCosmeticAreas]);

	return (
		<Stack gap={4}>
			<h2 className="h3">Kosmetické vady</h2>
			<CosmeticDefectsBreakdown product={product} isFix={isFix}>
				<TableFooter>
					<TableRow>
						<TableCell>
							<strong>Stav produkutu</strong>
						</TableCell>
						<TableCell>
							<ComboboxControl
								control={control}
								name="gradeId"
								items={rankedGrades.map((grade) => ({ value: grade.id, label: grade.name }))}
								disabled={!isTestLead}
							/>
						</TableCell>
					</TableRow>
					<TableRow>
						<TableCell>
							<strong>Typ produkutu</strong>
						</TableCell>
						<TableCell>
							<ComboboxControl
								control={control}
								name="type"
								items={PRODUCT_TYPES.map((type) => ({ value: type, label: ProductTypeMessage[type] }))}
							/>
						</TableCell>
					</TableRow>
				</TableFooter>
			</CosmeticDefectsBreakdown>
			{children}
		</Stack>
	);
});

CosmeticDefectsHandling.displayName = 'CosmeticDefectsForm';
