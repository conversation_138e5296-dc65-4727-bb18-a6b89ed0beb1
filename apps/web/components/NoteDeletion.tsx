import { Button, DeleteDialog, Icon, toast, useDialog } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { useUserHasScope } from '../hooks/useUserHasScope';

export const NoteDeletion: FC<{ noteId: string; invalidate: () => void }> = ({ noteId, invalidate }) => {
	const isAdmin = useUserHasScope('admin');
	const { closeDialog } = useDialog();

	const { mutate: deleteNote, isLoading } = apiHooks.useDeleteNote(
		{
			params: { noteId },
		},
		{
			onSuccess: () => {
				closeDialog();
				invalidate();
				toast.success('Poznámka byla smazána.');
			},
		},
	);

	return (
		<DeleteDialog
			title="Odstranit poznámku"
			description="Opravdu si přejete odstranit položku?"
			triggerVariant="icon"
			trigger={
				<Button variant="outline" width="icon" disabled={!isAdmin}>
					<Icon name="trash" />
				</Button>
			}
			onDelete={() => deleteNote(undefined)}
			isLoading={isLoading}
		/>
	);
};
