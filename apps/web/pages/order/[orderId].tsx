import { NOT_AVAILABLE, ORDER_CANCEL_STATUSES, ORDER_SUCCESS_STATUSES } from '@pocitarna-nx-2023/config';
import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	Card,
	CardContent,
	Card<PERSON>eader,
	CardTitle,
	Container,
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
	ParamItem,
	ParamList,
	SidebarGrid,
	Stack,
	Title,
} from '@pocitarna-nx-2023/ui';
import { filterUndefined, formatDateTime } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps } from 'next';
import { type FC } from 'react';
import { toast } from 'sonner';
import { OrderAddressCard } from '../../components/order/OrderAddressCard';
import { OrderPriorityForm } from '../../components/order/OrderPriorityForm';
import { OrderProductsList } from '../../components/order/OrderProductsList';
import { PriorityDisplay } from '../../components/test/PriorityDisplay';
import { SortingProvider } from '../../contexts/SortingContext';
import { useUserHasScope } from '../../hooks/useUserHasScope';
import { useUserWarehouseTaskList } from '../../useUserWarehouseTaskList';
import { checkScope } from '../../utils/checkScope';

type Props = { orderId: string };

const Order: FC<Props> = ({ orderId }) => {
	const isOrderWrite = useUserHasScope('orderWrite');
	const { data: orderData } = apiHooks.useGetEcommerceOrder({ params: { ecommerceOrderId: orderId } });
	const order = orderData?._data;
	const { mutate: triggerOrderUpdate } = apiHooks.useTriggerOrderUpdate(
		{ params: { ecommerceOrderId: orderId } },
		{
			onSuccess: () => {
				toast.success('Objednávka se aktualizuje...');
			},
		},
	);
	const productIds = filterUndefined(order?.items.map((orderItem) => orderItem.productId) ?? []);
	const { invalidateUserWarehouseTasks } = useUserWarehouseTaskList();
	const { mutate: createShiftWarehouseTask } = apiHooks.useCreateShiftWarehouseTask(undefined, {
		onSuccess: () => {
			invalidateUserWarehouseTasks();
			toast.success('Produkty přidány na seznam...');
		},
	});

	if (!order) return null;

	const shippingItems = order.items?.filter((item) => item.type === 'SHIPPING');
	const paymentItems = order.items?.filter((item) => item.type === 'PAYMENT');
	const orderIsFrozen = ORDER_CANCEL_STATUSES.includes(order.status) || ORDER_SUCCESS_STATUSES.includes(order.status) || !isOrderWrite;
	const isCMP = order.items.some((item) => item.type === 'PRODUCT' && item.productEnvelopeId);

	return (
		<SortingProvider>
			<Container>
				<Stack>
					<Title
						title={`Objednávka č. ${order.code}`}
						backlink={{ url: '/order', label: 'Objednávky' }}
						titleChildren={
							<>
								<span className="cursor-default text-2xl leading-3">{order.country === 'CZ' ? '🇨🇿' : '🇸🇰'}</span>
								{formatDateTime(order.placedAt)}
								<Badge variant="info">{order.status}</Badge>
								{isCMP && <Badge variant="success">CMP</Badge>}
							</>
						}
					>
						<Button asChild>
							<a href={order.shoptetUrl} target="_blank" rel="noreferrer noopener">
								Shoptet detail
							</a>
						</Button>
					</Title>

					<SidebarGrid>
						<Stack>
							{order.items.length > 0 ? (
								<OrderProductsList orderId={order.id} orderItems={order.items} orderIsFrozen={orderIsFrozen} />
							) : (
								<p>V této objednávce se nenacházejí žádné produkty.</p>
							)}
						</Stack>
						<Stack>
							<Card>
								<CardHeader>
									<CardTitle>Poznámky</CardTitle>
								</CardHeader>
								<CardContent>
									<ParamList>
										<ParamItem label="Priorita">
											<PriorityDisplay entity={order} />
										</ParamItem>
										<ParamItem label="Interní poznámka">{order.internalNote || NOT_AVAILABLE}</ParamItem>
										<ParamItem label="Zákaznická poznámka">{order.customerNote || NOT_AVAILABLE}</ParamItem>
									</ParamList>
								</CardContent>
							</Card>

							<Card>
								<CardHeader>
									<CardTitle>Doprava a platba</CardTitle>
								</CardHeader>
								<CardContent>
									<ParamList>
										<ParamItem label="Doprava">
											{shippingItems.length > 0 ? shippingItems.map((item) => item.name).join(', ') : NOT_AVAILABLE}
										</ParamItem>
										<ParamItem label="Platba">
											{paymentItems.length > 0 ? paymentItems.map((item) => item.name).join(', ') : NOT_AVAILABLE}
										</ParamItem>
									</ParamList>
								</CardContent>
							</Card>

							<OrderAddressCard order={order} />

							<Card>
								<CardHeader>
									<CardTitle>Akce</CardTitle>
								</CardHeader>
								<CardContent>
									<Stack gap={4}>
										<Dialog>
											<DialogTrigger asChild>
												<Button type="button" variant="secondary" className="w-full" disabled={!isOrderWrite}>
													Změnit prioritu
												</Button>
											</DialogTrigger>
											<DialogContent>
												<DialogHeader>
													<DialogTitle>Změnit prioritu</DialogTitle>
												</DialogHeader>
												<OrderPriorityForm order={order} />
											</DialogContent>
										</Dialog>
										{isOrderWrite && (
											<Button type="button" variant="secondary" onClick={() => triggerOrderUpdate(undefined)}>
												Ručně synchronizovat...
											</Button>
										)}
										<Button type="button" variant="secondary" onClick={() => createShiftWarehouseTask({ productIds })}>
											Přidat na skladový seznam
										</Button>
									</Stack>
								</CardContent>
							</Card>
						</Stack>
					</SidebarGrid>
				</Stack>
			</Container>
		</SortingProvider>
	);
};

export default Order;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	const orderId = ctx.params?.orderId;

	return {
		props: {
			orderId: typeof orderId === 'string' ? orderId : Array.isArray(orderId) ? orderId[0] : '',
		},
		redirect: await checkScope(ctx, 'orderRead'),
	};
};
