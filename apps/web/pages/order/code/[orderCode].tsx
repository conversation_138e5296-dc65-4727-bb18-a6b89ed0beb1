import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import type { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import { type FC, useEffect, useMemo } from 'react';
import { checkScope } from '../../../utils/checkScope';

type Props = { orderCode: string };

const OrderCode: FC<Props> = ({ orderCode }) => {
	const router = useRouter();
	const {
		data: orderData,
		isSuccess,
		isFetched,
	} = apiHooks.useGetEcommerceOrderByCode({ params: { ecommerceOrderCode: orderCode } }, { enabled: !!orderCode });
	const order = useMemo(() => orderData?._data, [orderData?._data]);

	useEffect(() => {
		if (isFetched) {
			if (isSuccess && order) {
				router.push(`/order/${order.id}`);
			} else {
				router.push('/404');
			}
		}
	}, [order, isSuccess, isFetched, router]);

	return null;
};

export default OrderCode;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	const orderCode = ctx.params?.orderCode;

	return {
		props: {
			orderCode: typeof orderCode === 'string' ? orderCode : Array.isArray(orderCode) ? orderCode[0] : '',
		},
		redirect: await checkScope(ctx, 'orderRead'),
	};
};
