import { <PERSON><PERSON>, <PERSON>ertD<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>le, Container, <PERSON><PERSON>, Spinner, Stack, Title } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps, type NextPage } from 'next';
import { StockTable } from '../../../components/analytics/aging-stock/StockTable';
import { FilteringTabs } from '../../../components/filtering/FilteringTabs';
import { type FilterItem } from '../../../components/filtering/Filters';
import { SortingProvider } from '../../../contexts/SortingContext';
import { checkScope } from '../../../utils/checkScope';

const filterDefinition: FilterItem[] = [
	{
		key: 'manufacturer',
		label: '<PERSON>na<PERSON><PERSON>',
		type: 'input',
	},
];

const AgingStockAnalytics: NextPage = () => {
	const { data: agingStockData, isLoading } = apiHooks.useGetAgingStockAnalytics();

	const data = agingStockData?._data ?? [];

	return (
		<Container>
			<Stack>
				<div className="w-full flex justify-between items-center">
					<Title title="Statistiky - Stárnoucí sklad" />
				</div>

				{isLoading ? (
					<Spinner />
				) : data.length === 0 ? (
					<Alert variant="info">
						<Icon name="circle-info" />
						<AlertTitle>Žádné produkty.</AlertTitle>
						<AlertDescription>Pro zobrazení produktů zkuste upravit datumy.</AlertDescription>
					</Alert>
				) : (
					data.map((entry: ApiBody<'getAgingStockAnalytics'>[number]) => {
						return (
							<SortingProvider key={entry.category}>
								<FilteringTabs
									filterEndpoint="getAttributes"
									filterDefinition={filterDefinition}
									alternativeTabs={<h2 className="h1">{entry.category}</h2>}
								>
									{(_, filters) => <StockTable title={entry.category} categoryData={entry.data} filters={filters} />}
								</FilteringTabs>
							</SortingProvider>
						);
					})
				)}
			</Stack>
		</Container>
	);
};

export default AgingStockAnalytics;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	return {
		props: {},
		redirect: await checkScope(ctx, 'admin'),
	};
};
