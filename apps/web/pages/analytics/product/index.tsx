import { DEFAULT_RANGE } from '@pocitarna-nx-2023/config';
import { Alert, AlertDescription, AlertTitle, Container, <PERSON><PERSON>, Spinner, Stack, Title } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps, type NextPage } from 'next';
import { useState } from 'react';
import type { DateRange } from 'react-day-picker';
import { ProductTable } from '../../../components/analytics/product/ProductTable';
import { DateRangePicker } from '../../../components/DateRangePicker';
import { FilteringTabs } from '../../../components/filtering/FilteringTabs';
import { type FilterItem } from '../../../components/filtering/Filters';
import { SortingProvider } from '../../../contexts/SortingContext';
import { checkScope } from '../../../utils/checkScope';

const filterDefinition: FilterItem[] = [
	{
		key: 'manufacturer',
		label: 'Značka',
		type: 'input',
	},
	{
		key: 'model',
		label: 'Model',
		type: 'input',
	},
];

const ProductAnalytics: NextPage = () => {
	const [range, setRange] = useState<DateRange | undefined>(DEFAULT_RANGE);

	const { data: productData, isLoading } = apiHooks.useGetProductAnalytics(
		{
			queries: {
				startDate: range?.from ?? new Date(),
				endDate: range?.to ?? new Date(),
			},
		},
		{ enabled: range?.from != null && range?.to != null },
	);

	const data = productData?._data ?? [];

	return (
		<Container>
			<SortingProvider>
				<Stack>
					<div className="w-full flex justify-between items-center">
						<Title title="Statistiky - Produkty" />
						<DateRangePicker range={range} setRange={setRange} />
					</div>

					{isLoading ? (
						<Spinner />
					) : data.length > 0 ? (
						data.map((entry: ApiBody<'getProductAnalytics'>[number]) => {
							const filters: FilterItem[] =
								entry.category === 'Počítače'
									? [
											...filterDefinition,
											{
												key: 'caseSize',
												label: 'Provedení',
												type: 'input',
											},
										]
									: filterDefinition;

							return (
								<FilteringTabs
									key={entry.category}
									filterEndpoint="getAttributes"
									filterDefinition={filters}
									alternativeTabs={<h2 className="h1">{entry.category}</h2>}
								>
									{(_, filters) => <ProductTable title={entry.category} categoryData={entry.data} filters={filters} />}
								</FilteringTabs>
							);
						})
					) : (
						<Alert variant="info">
							<Icon name="circle-info" />
							<AlertTitle>Žádné produkty.</AlertTitle>
							<AlertDescription>Pro zobrazení produktů zkuste upravit datumy.</AlertDescription>
						</Alert>
					)}
				</Stack>
			</SortingProvider>
		</Container>
	);
};

export default ProductAnalytics;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	return {
		props: {},
		redirect: await checkScope(ctx, 'admin'),
	};
};
