import { DEFAULT_RANGE } from '@pocitarna-nx-2023/config';
import { Checkbox, Container, Spinner, Stack, Title } from '@pocitarna-nx-2023/ui';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps, type NextPage } from 'next';
import { useMemo, useState } from 'react';
import type { DateRange } from 'react-day-picker';
import { useToggle } from 'rooks';
import { VendorTable } from '../../../components/analytics/vendor/VendorTable';
import { DateRangePicker } from '../../../components/DateRangePicker';
import { checkScope } from '../../../utils/checkScope';

const VendorAnalytics: NextPage = () => {
	const [range, setRange] = useState<DateRange | undefined>(DEFAULT_RANGE);
	const [shouldRemoveInactive, setShouldRemoveInactive] = useToggle(true);

	const { data: vendorData, isLoading: isLoadingVendorData } = apiHooks.useGetVendorAnalytics(
		{
			queries: {
				startDate: range?.from ?? new Date(),
				endDate: range?.to ?? new Date(),
			},
		},
		{ enabled: range?.from != null && range?.to != null },
	);

	const categories = useMemo(() => vendorData?._data?.categories ?? [], [vendorData?._data?.categories]);

	const vendors = useMemo(() => {
		const allVendors = vendorData?._data?.vendors ?? [];

		if (!shouldRemoveInactive) return allVendors;

		return allVendors.filter((vendor: ApiBody<'getVendorAnalytics'>['vendors'][number]) => vendor.totalTests > 0);
	}, [shouldRemoveInactive, vendorData?._data?.vendors]);

	return (
		<Container>
			<Stack>
				<div className="w-full flex justify-between items-center">
					<Title title="Statistiky - Dodavatelé" />
					<DateRangePicker range={range} setRange={setRange} />
				</div>
				<Stack gap={2} direction="row">
					<p>Zobrazit pouze dodavatele s testovanými produkty</p>
					<Checkbox checked={shouldRemoveInactive} onCheckedChange={setShouldRemoveInactive} />
				</Stack>
				{isLoadingVendorData ? (
					<Spinner />
				) : vendorData?._data ? (
					<VendorTable
						title="Dodavatelské reklamace"
						categories={categories}
						vendors={vendors}
						totals={vendorData._data.totals}
					/>
				) : null}
			</Stack>
		</Container>
	);
};

export default VendorAnalytics;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	return {
		props: {},
		redirect: await checkScope(ctx, 'admin'),
	};
};
