import { DEFAULT_RANGE } from '@pocitarna-nx-2023/config';
import { Checkbox, Container, Spinner, Stack, Title } from '@pocitarna-nx-2023/ui';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps, type NextPage } from 'next';
import { useMemo, useState } from 'react';
import type { DateRange } from 'react-day-picker';
import { useToggle } from 'rooks';
import { TestingTable } from '../../../components/analytics/testers/TestingTable';
import { DateRangePicker } from '../../../components/DateRangePicker';
import { checkScope } from '../../../utils/checkScope';

const TestingAnalytics: NextPage = () => {
	const [range, setRange] = useState<DateRange | undefined>(DEFAULT_RANGE);
	const [shouldRemoveInactive, setShouldRemoveInactive] = useToggle(true);

	const { data: testingData, isLoading: isLoadingTestingData } = apiHooks.useGetTestingAnalytics(
		{
			queries: {
				startDate: range?.from ?? new Date(),
				endDate: range?.to ?? new Date(),
			},
		},
		{ enabled: range?.from != null && range?.to != null },
	);

	const categories = useMemo(() => testingData?._data?.categories ?? [], [testingData?._data?.categories]);

	const testers = useMemo(() => {
		const allTesters = testingData?._data?.testers ?? [];

		if (!shouldRemoveInactive) return allTesters;

		return allTesters.filter((tester: ApiBody<'getTestingAnalytics'>['testers'][number]) => tester.totalTests > 0);
	}, [shouldRemoveInactive, testingData?._data?.testers]);

	return (
		<Container>
			<Stack>
				<div className="w-full flex justify-between items-center">
					<Title title="Statistiky - Testování" />
					<DateRangePicker range={range} setRange={setRange} />
				</div>
				<Stack gap={2} direction="row">
					<p>Zobrazit pouze aktivní testery</p>
					<Checkbox checked={shouldRemoveInactive} onCheckedChange={setShouldRemoveInactive} />
				</Stack>
				{isLoadingTestingData ? (
					<Spinner />
				) : testingData?._data ? (
					<Stack gap={12}>
						<TestingTable
							title="Testeři"
							categories={categories}
							testers={testers}
							totals={testingData?._data?.totals}
							range={range}
						/>
						<TestingTable
							title="Chybovost testerů"
							categories={categories}
							testers={testers}
							totals={testingData._data.totals}
							withFixes
							range={range}
						/>
					</Stack>
				) : null}
			</Stack>
		</Container>
	);
};

export default TestingAnalytics;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	return {
		props: {},
		redirect: await checkScope(ctx, 'productTestLead'),
	};
};
