import { Container, Stack, Title } from '@pocitarna-nx-2023/ui';
import { type GetServerSideProps } from 'next';
import { type FC } from 'react';
import { UserNotificationsListTable } from '../../../components/adminPanel/UserNotificationsListTable';
import { FilteringTabs } from '../../../components/filtering/FilteringTabs';
import { type FilterItem } from '../../../components/filtering/Filters';
import { SortingProvider } from '../../../contexts/SortingContext';
import { checkScope } from '../../../utils/checkScope';

const filterDefinition: FilterItem[] = [
	{
		key: 'name',
		label: 'J<PERSON><PERSON>',
		type: 'input',
	},
	{
		key: 'email',
		label: 'E-mail',
		type: 'input',
	},
];

type Props = never;

const AdminNotificationsPage: FC<Props> = () => {
	return (
		<SortingProvider>
			<Container>
				<Stack>
					<Title title="Nastavení notifikací" />
					<Stack gap={4}>
						<FilteringTabs filterEndpoint="getUsers" filterDefinition={filterDefinition}>
							{(_, filters) => <UserNotificationsListTable filters={filters} />}
						</FilteringTabs>
					</Stack>
				</Stack>
			</Container>
		</SortingProvider>
	);
};

export default AdminNotificationsPage;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	return {
		props: {},
		redirect: await checkScope(ctx, 'admin'),
	};
};
