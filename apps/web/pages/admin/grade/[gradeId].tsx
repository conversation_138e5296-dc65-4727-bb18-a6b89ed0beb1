import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import {
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	Container,
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
	Icon,
	Stack,
	Title,
} from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps } from 'next';
import { type FC, useMemo } from 'react';
import { AddOrEditCosmeticDefect } from '../../../components/adminPanel/AddOrEditCosmeticDefect';
import { CosmeticDefectListTable } from '../../../components/adminPanel/CosmeticDefectListTable';
import { EditGrade } from '../../../components/adminPanel/EditGrade';
import { checkScope } from '../../../utils/checkScope';

type Props = { gradeId: string };

const Grade: FC<Props> = ({ gradeId }) => {
	const { data: gradeData } = apiHooks.useGetGrade({ params: { gradeId } });
	const grade = gradeData?._data;

	const { data: cosmeticDefectsData } = apiHooks.useGetCosmeticDefects({
		queries: {
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
			filter: { gradeId: { eq: gradeId } },
			sort: [['name', 'asc']],
		},
	});

	const cosmeticDefects = useMemo(() => cosmeticDefectsData?._data ?? [], [cosmeticDefectsData?._data]);

	if (!grade) return null;

	return (
		<Container>
			<Stack>
				<Title title={`Stav "${grade.name}"`} backlink={{ url: '/admin/grade', label: 'Stavy produktů' }} />

				<Card>
					<CardContent>
						<EditGrade grade={grade} />
					</CardContent>
				</Card>

				<Stack gap={4}>
					<div className="flex justify-between items-center max-md:flex-col max-md:items-stretch gap-4">
						<h2 className="h1">Kosmetické vady</h2>
						<Dialog>
							<DialogTrigger asChild>
								<Button variant="secondary">
									<Icon name="add" />
									Vytvořit kosmetickou vadu
								</Button>
							</DialogTrigger>
							<DialogContent>
								<DialogHeader>
									<DialogTitle>Vytvořit kosmetickou vadu</DialogTitle>
								</DialogHeader>
								<AddOrEditCosmeticDefect />
							</DialogContent>
						</Dialog>
					</div>
					<CosmeticDefectListTable cosmeticDefects={cosmeticDefects} />
				</Stack>
			</Stack>
		</Container>
	);
};

export default Grade;

export const getServerSideProps: GetServerSideProps<Props> = async (ctx) => {
	const gradeId = ctx.params?.gradeId;

	return {
		props: {
			gradeId: typeof gradeId === 'string' ? gradeId : Array.isArray(gradeId) ? gradeId[0] : '',
		},
		redirect: await checkScope(ctx, 'admin'),
	};
};
