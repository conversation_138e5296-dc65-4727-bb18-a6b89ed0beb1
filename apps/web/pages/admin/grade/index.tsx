import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	Dialog<PERSON>ontent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
	Icon,
	Stack,
	Title,
} from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps, type NextPage } from 'next';
import { useMemo } from 'react';
import { AddGrade } from '../../../components/adminPanel/AddGrade';
import { GradeListTable } from '../../../components/adminPanel/GradeListTable';
import { checkScope } from '../../../utils/checkScope';

const AdminGradePage: NextPage = () => {
	const { data, invalidate } = apiHooks.useGetRankedGrades({});
	const grades = useMemo(() => data?._data ?? [], [data?._data]);

	return (
		<Container>
			<Stack>
				<Title title="Nasta<PERSON><PERSON> stavů">
					<Dialog>
						<DialogTrigger asChild>
							<Button>
								<Icon name="add" />
								Vytvořit stav
							</Button>
						</DialogTrigger>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>Vytvořit stav</DialogTitle>
							</DialogHeader>
							<AddGrade gradesCount={grades.length} />
						</DialogContent>
					</Dialog>
				</Title>

				<Stack gap={4}>
					{grades.length === 0 ? (
						<Alert variant="info">
							<Icon name="circle-info" />
							<AlertTitle>Žádné stavy k zobrazení</AlertTitle>
						</Alert>
					) : (
						<GradeListTable grades={grades} invalidate={invalidate} />
					)}
				</Stack>
			</Stack>
		</Container>
	);
};

AdminGradePage.displayName = 'AdminGradePage';

export default AdminGradePage;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	return {
		props: {},
		redirect: await checkScope(ctx, 'admin'),
	};
};
