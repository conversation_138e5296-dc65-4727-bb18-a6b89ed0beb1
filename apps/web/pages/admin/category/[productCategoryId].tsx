import { API_ORIGIN, MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { Button, Container, Stack, Table, TableBody, TableHead, TableHeader, TableRow, Title } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps } from 'next';
import { type FC } from 'react';
import { CategoryCosmeticsManagement } from '../../../components/adminPanel/CategoryCosmeticsManagement';
import { EditProductCategory } from '../../../components/adminPanel/EditProductCategory';
import { RecursiveProductCategoryRowFull } from '../../../components/adminPanel/ProductCategoryRow';
import { ProductCategoryAttributes } from '../../../components/productCategory/ProductCategoryAttributes';
import { SortingProvider } from '../../../contexts/SortingContext';
import { checkScope } from '../../../utils/checkScope';

type Props = { productCategoryId: string };

const Category: FC<Props> = ({ productCategoryId }) => {
	const { data } = apiHooks.useGetProductCategory({ params: { productCategoryId } });
	const { data: dataDescendants } = apiHooks.useGetProductCategoryDescendants({ params: { productCategoryId } });
	const { data: dataAscendants } = apiHooks.useGetProductCategoryAncestors({ params: { productCategoryId } });
	const { data: categoryAttributesData, invalidate: invalidateCategoryAttributes } = apiHooks.useGetProductCategoryAttributes({
		params: { productCategoryId },
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER },
	});
	const categoryAttributes = categoryAttributesData?._data ?? [];

	return (
		<SortingProvider>
			<Container>
				<Stack>
					<Title title={data?._data?.name ?? 'Kategorie'} backlink={{ url: '/admin/category', label: 'Kategorie produktů' }}>
						{categoryAttributes.length > 0 && (
							<Button asChild>
								<a href={`${API_ORIGIN}/product/category/${productCategoryId}/import-file`} target="_blank" download>
									Vygenerovat importní soubor pro tuto kategorii
								</a>
							</Button>
						)}
					</Title>
					{data?._data && <EditProductCategory category={data._data} />}
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Název</TableHead>
								<TableHead>Marže</TableHead>
								<TableHead>Prefix kódů</TableHead>
								<TableHead>Shoptet kategorie</TableHead>
								<TableHead>Recyklační poplatek</TableHead>
								<TableHead />
							</TableRow>
						</TableHeader>
						<TableBody>
							{dataAscendants && dataDescendants && (
								<RecursiveProductCategoryRowFull
									categoryDescendants={dataDescendants._data}
									categoryAscendants={dataAscendants._data}
								/>
							)}
						</TableBody>
					</Table>

					{data?._data && (
						<ProductCategoryAttributes
							productCategory={data._data}
							categoryAttributes={categoryAttributes}
							invalidateCategoryAttributes={invalidateCategoryAttributes}
						/>
					)}
					<CategoryCosmeticsManagement productCategoryId={productCategoryId} />
				</Stack>
			</Container>
		</SortingProvider>
	);
};

export default Category;

export const getServerSideProps: GetServerSideProps<Props> = async (ctx) => {
	const productCategoryId = ctx.params?.productCategoryId;

	return {
		props: {
			productCategoryId:
				typeof productCategoryId === 'string' ? productCategoryId : Array.isArray(productCategoryId) ? productCategoryId[0] : '',
		},
		redirect: await checkScope(ctx, 'productAdmin'),
	};
};
