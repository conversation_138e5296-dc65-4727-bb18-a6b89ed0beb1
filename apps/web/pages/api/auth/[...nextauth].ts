import { AZURE_AD_CLIENT_ID, AZURE_AD_CLIENT_SECRET, AZURE_AD_TENANT_ID, DOMAIN_NAME, IS_DEV, ONE_WEEK } from '@pocitarna-nx-2023/config';
import { apiClient } from '@pocitarna-nx-2023/zodios-client';
import axios from 'axios';
import Cookies from 'cookies';
import { type NextApiRequest, type NextApiResponse } from 'next';
import NextAuth, { type NextAuthOptions } from 'next-auth';
import { decode, encode } from 'next-auth/jwt';
import AzureADProvider from 'next-auth/providers/azure-ad';
import Credentials from 'next-auth/providers/credentials';
import { AuthRestAdapter } from '../../../utils/authRestAdapter';

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
	const data = requestWrapper(req, res);
	return await NextAuth(...data);
};

export default handler;

const adapter = AuthRestAdapter();

const fetchImage = async (accessToken?: string) => {
	if (!accessToken) return null;

	try {
		const { data, headers } = await axios('https://graph.microsoft.com/v1.0/me/photos/48x48/$value', {
			responseType: 'arraybuffer',
			headers: { Authorization: `Bearer ${accessToken}` },
		});
		return data ? `data:${headers['content-type']};base64,${Buffer.from(data, 'binary').toString('base64')}` : null;
	} catch (error) {
		// intentionally not printing the error to not polute console, because it's expected
		console.warn('Error fetching user image');
		return null;
	}
};

export const authOptions: NextAuthOptions = {
	providers: [
		AzureADProvider({
			clientId: AZURE_AD_CLIENT_ID,
			clientSecret: AZURE_AD_CLIENT_SECRET,
			tenantId: AZURE_AD_TENANT_ID,
			allowDangerousEmailAccountLinking: true,
			profilePhotoSize: 48,
			authorization: {
				params: {
					scope: 'openid profile email User.Read',
				},
			},
			profile: async (profile, tokens) => ({
				id: profile.oid,
				name: profile.name,
				email: profile.preferred_username,
				image: await fetchImage(tokens.access_token),
			}),
		}),
		Credentials({
			name: 'QR Code',
			credentials: {
				code: { label: 'QR Code', type: 'text', placeholder: 'QR Code' },
			},
			type: 'credentials',
			id: 'qr-code',
			authorize: async (credentials) => {
				if (!credentials?.code) return null;
				const { _data } = await apiClient.getUserByQR({ params: { code: credentials.code } });
				return _data;
			},
		}),
	],
	adapter,

	cookies: {
		sessionToken: {
			name: 'next-auth.session-token',
			options: {
				httpOnly: true,
				sameSite: IS_DEV ? 'strict' : 'none',
				path: '/',
				secure: !IS_DEV,
				domain: IS_DEV ? 'localhost' : DOMAIN_NAME,
				priority: 'high',
			},
		},
	},
	useSecureCookies: !IS_DEV,
	debug: false,
	logger: {
		debug: () => {
			return;
		},
		// error: () => {
		// 	return;
		// },
		// warn: () => {
		// 	return;
		// },
	},

	pages: {
		signIn: '/login',
		error: '/login',
	},
};

export function requestWrapper(
	req: NextApiRequest,
	res: NextApiResponse,
): [req: NextApiRequest, res: NextApiResponse, opts: NextAuthOptions] {
	const fromDate = (time: number, date = Date.now()) => new Date(date + time * 1000);

	if (req.url === '/api/auth/providers') {
		const cookies = new Cookies(req, res, { secure: !IS_DEV });
		cookies.set('next-auth.session-token', '', {
			expires: new Date(0),
			httpOnly: true,
			sameSite: IS_DEV ? 'strict' : 'none',
			path: '/',
			secure: !IS_DEV,
			domain: IS_DEV ? 'localhost' : DOMAIN_NAME,
			priority: 'high',
		});
	}

	authOptions.jwt = {
		encode: async ({ token, secret, maxAge }) => {
			if (req.query.nextauth?.includes('callback') && req.query.nextauth.includes('qr-code') && req.method === 'POST') {
				const cookies = new Cookies(req, res, { secure: !IS_DEV });
				const cookie = cookies.get('next-auth.session-token');

				if (cookie) {
					return cookie;
				} else {
					return '';
				}
			}
			// Revert to default behaviour when not in the credentials provider callback flow
			return encode({ token, secret, maxAge });
		},
		decode: async ({ token, secret }) => {
			if (req.query.nextauth?.includes('callback') && req.query.nextauth.includes('qr-code') && req.method === 'POST') {
				return null;
			}

			// Revert to default behaviour when not in the credentials provider callback flow
			return decode({ token, secret });
		},
	};

	authOptions.callbacks = {
		session: async ({ session, user }) => {
			const { _data: userWithRoleAndScopes } = await apiClient.getAuthUser({ params: { userId: user.id } });

			return { expires: session.expires, user: { ...session.user, ...userWithRoleAndScopes } };
		},

		async signIn({ user, credentials, profile, account }) {
			if (user?.id) {
				try {
					const { _data: dbUser } = await apiClient.getAuthUser({ params: { userId: user.id } });

					if (dbUser?.deletedAt) {
						console.warn(`Blocked sign-in for deleted user ${dbUser.email}`);
						return false;
					}
				} catch (err) {
					console.error('Error checking user active state', err);
					return false;
				}
			}

			if (profile && account && account.provider === 'azure-ad') {
				const image = await fetchImage(account.access_token);
				try {
					await adapter.updateUser?.({ id: user.id, name: profile.name, image });
				} catch (err) {
					console.error('Error updating user', err);
				}
			}

			// Check if this sign in callback is being called in the credentials authentication flow. If so, use the next-auth adapter to create a session entry in the database (SignIn is called after authorize so we can safely assume the user is valid and already authenticated).
			if (req.query.nextauth?.includes('callback') && req.query.nextauth?.includes('qr-code') && req.method === 'POST' && user) {
				const sessionExpiry = fromDate(ONE_WEEK);

				if (!credentials?.code || typeof credentials.code !== 'string') return false;

				const session = await adapter.createSessionForVerificationToken?.(credentials.code);

				const cookies = new Cookies(req, res, { secure: !IS_DEV });
				cookies.set('next-auth.session-token', session?.sessionToken, {
					expires: session?.expires ?? sessionExpiry,
					httpOnly: true,
					sameSite: IS_DEV ? 'strict' : 'none',
					path: '/',
					secure: !IS_DEV,
					domain: IS_DEV ? 'localhost' : DOMAIN_NAME,
					priority: 'high',
				});
			}

			return true;
		},
	};

	return [req, res, authOptions];
}
