import { Button, Container, Stack, Title, toast } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps, type NextPage } from 'next';
import { useRouter } from 'next/router';
import { FilteringTabs } from '../../components/filtering/FilteringTabs';
import { type FilterItem } from '../../components/filtering/Filters';
import { InventoryListTable } from '../../components/inventory/scan/InventoryListTable';
import { SortingProvider } from '../../contexts/SortingContext';
import { useUserHasScope } from '../../hooks/useUserHasScope';
import { checkScope } from '../../utils/checkScope';

const filterDefinition: FilterItem[] = [
	{
		key: 'status',
		label: 'Stav',
		type: 'input',
	},
];

const Inventories: NextPage = () => {
	const isWarehouseManage = useUserHasScope('warehouseManage');
	const { mutate, isLoading } = apiHooks.useCreateInventory({});
	const router = useRouter();

	const createInventory = () => {
		mutate(undefined, {
			onSuccess: (data) => {
				toast.success('Inventura byla vytvořena');
				router.push(`/inventory/${data._data.id}`);
			},
		});
	};

	return (
		<SortingProvider>
			<Container>
				<Stack>
					<Title title="Inventura">
						<Button isLoading={isLoading} disabled={isLoading || !isWarehouseManage} onClick={createInventory}>
							Vytvořit inventuru
						</Button>
					</Title>
					<Stack gap={4}>
						<FilteringTabs filterEndpoint="getEcommerceOrders" filterDefinition={filterDefinition}>
							{(_tabFilter, filters) => <InventoryListTable filters={filters} />}
						</FilteringTabs>
					</Stack>
				</Stack>
			</Container>
		</SortingProvider>
	);
};

export default Inventories;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	return {
		props: {},
		redirect: await checkScope(ctx, 'warehouseRead'),
	};
};
