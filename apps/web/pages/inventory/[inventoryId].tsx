import {
	ENVELOPE_TYPES,
	EnvelopeTypeNames,
	INVENTORY_ITEM_STATUS_NAMES,
	type InventoryItemStatus,
	InventoryItemStatusMessage,
	InventoryStatusMessage,
	MAX_POSITIVE_INTEGER,
} from '@pocitarna-nx-2023/config';
import { Badge, Container, SidebarGrid, Stack, Title } from '@pocitarna-nx-2023/ui';
import { formatInventoryCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps } from 'next';
import { type FC, useMemo } from 'react';
import { FilteringTabs } from '../../components/filtering/FilteringTabs';
import { type FilterItem } from '../../components/filtering/Filters';
import { ClosedInventory } from '../../components/inventory/closed/ClosedInventory';
import { InventoryFilesCard } from '../../components/inventory/InventoryFilesCard';
import { PriceOverview } from '../../components/inventory/PriceOverview';
import { ScanInventoryDialog } from '../../components/inventory/scan/ScanInventoryDialog';
import { ActionButtons } from '../../components/inventory/verify/ActionButtons';
import { VerifyInventory } from '../../components/inventory/verify/VerifyInventory';
import { SortingProvider } from '../../contexts/SortingContext';
import { checkScope } from '../../utils/checkScope';

export type InventorySelection = {
	selectedEnvelope: string;
	selectedProduct?: string;
};

type Props = {
	inventoryId: string;
	selection?: InventorySelection;
};

const inventoryStatusClassnamesMap: Record<InventoryItemStatus, string> = {
	PENDING: 'text-warning',
	OK: 'text-success',
	ERROR: 'text-destructive',
	NEW_ADDITION: 'text-info',
};

const Inventory: FC<Props> = ({ inventoryId, selection }) => {
	const { data: inventoryData } = apiHooks.useGetInventory({ params: { inventoryId } });
	const inventory = inventoryData?._data;

	const { data: productCategoriesData } = apiHooks.useGetProductCategoriesList({
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER, sort: ['name'] },
	});
	const productCategories = useMemo(() => productCategoriesData?._data ?? [], [productCategoriesData?._data]);

	const filterDefinition: FilterItem[] = [
		{
			key: 'inventoryStatus',
			label: 'Stav produktu',
			type: 'multi',
			items: INVENTORY_ITEM_STATUS_NAMES.map((status) => ({
				value: status,
				label: <span className={inventoryStatusClassnamesMap[status]}>{InventoryItemStatusMessage[status]}</span>,
			})),
		},
		{
			key: 'productEnvelopeType',
			label: 'Typ karty',
			type: 'select',
			items: ENVELOPE_TYPES.map((type) => ({
				value: type,
				label: EnvelopeTypeNames[type],
			})),
		},
		{
			key: 'code',
			label: 'PCN nebo kód karty',
			type: 'input',
		},
		{
			key: 'productCategoryId',
			label: 'Kategorie',
			type: 'select',
			items: productCategories.map((category) => ({ value: category.id, label: category.name })),
		},
		{
			key: 'manufacturer',
			label: 'Značka (výrobce)',
			type: 'input',
		},
	];

	if (!inventory) return null;

	return (
		<Container>
			<Stack>
				<Title
					title={`Inventura${inventory.name ? ` ${inventory.name}` : ''} č. ${formatInventoryCode(inventory.code)}`}
					backlink={{ url: '/inventory', label: 'Inventury' }}
					titleChildren={<Badge variant="info">{InventoryStatusMessage[inventory.status]}</Badge>}
				>
					{inventory.status === 'OPEN' && <ScanInventoryDialog inventory={inventory} />}
				</Title>

				<SidebarGrid>
					<SortingProvider>
						<Stack gap={4}>
							<FilteringTabs filterEndpoint="getInventoryItems" filterDefinition={filterDefinition}>
								{(_, filters) =>
									inventory.status === 'OPEN' ? (
										<VerifyInventory inventory={inventory} filters={filters} selection={selection} />
									) : (
										<ClosedInventory inventory={inventory} filters={filters} />
									)
								}
							</FilteringTabs>
						</Stack>
					</SortingProvider>

					<Stack>
						<PriceOverview inventory={inventory} />
						<InventoryFilesCard inventory={inventory} />
						{inventory.status === 'OPEN' && <ActionButtons inventory={inventory} />}
					</Stack>
				</SidebarGrid>
			</Stack>
		</Container>
	);
};

export default Inventory;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	const inventoryId = ctx.params?.inventoryId;
	const selectedItemQuery = Array.isArray(ctx.query.selectedItem) ? ctx.query.selectedItem[0] : ctx.query.selectedItem;
	const [selectedEnvelope, selectedProduct] = selectedItemQuery?.split('|') ?? [undefined, undefined];

	return {
		props: {
			inventoryId: typeof inventoryId === 'string' ? inventoryId : Array.isArray(inventoryId) ? inventoryId[0] : '',
			selection: !selectedEnvelope ? null : { selectedEnvelope, selectedProduct: selectedProduct ?? null },
		},
		redirect: await checkScope(ctx, 'warehouseRead'),
	};
};
