import { <PERSON>, PageTitle, <PERSON>ack, Title } from '@pocitarna-nx-2023/ui';
import { formatProductCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps } from 'next';
import { type ReactElement } from 'react';
import { CosmeticDefectsCard } from '../../../components/cosmeticDefects/CosmeticDefectsCard';
import { PublicLayout } from '../../../components/public/Layout';
import { PublicProductFilesCard } from '../../../components/public/product/PublicProductFilesCard';
import { type NextPageWithLayout } from '../../../types/next';

type Props = { productId: string };

const PublicProductDetail: NextPageWithLayout<Props> = ({ productId }) => {
	const { data } = apiHooks.useGetPublicProduct({ params: { productId } });
	const { data: batchData } = apiHooks.useGetBatch(
		{ params: { batchId: data?._data.batchId ?? '' } },
		{ enabled: Boolean(data?._data.batchId) },
	);

	const product = data?._data;
	const batch = batchData?._data;

	if (!product || !batch) return null;

	return (
		<>
			<PageTitle title={`Produkt ${formatProductCode(product.code)}`} />

			<Page className="min-h-full p-4 justify-center flex flex-col md:p-10">
				<Stack>
					<Title title={`Produkt ${formatProductCode(product.code)}`} />
					<Stack>
						<PublicProductFilesCard product={product} />
					</Stack>
					<CosmeticDefectsCard product={product} />
				</Stack>
			</Page>
		</>
	);
};

export default PublicProductDetail;

PublicProductDetail.getLayout = (page: ReactElement) => {
	return <PublicLayout>{page}</PublicLayout>;
};

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	const productId = ctx.params?.productId;

	return {
		props: {
			productId: typeof productId === 'string' ? productId : Array.isArray(productId) ? productId[0] : '',
		},
	};
};
