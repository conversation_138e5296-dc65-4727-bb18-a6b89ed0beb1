import { CODE_PREFIX, MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { Alert, AlertDescription, AlertTitle, Icon, Page, PageTitle, Stack, Title } from '@pocitarna-nx-2023/ui';
import { stripCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useQueryState } from 'nuqs';
import { type ReactElement, useMemo } from 'react';
import { CustomerClaimList } from '../../../components/public/customerClaim/CustomerClaimList';
import { CustomerClaimOrderProductsList } from '../../../components/public/customerClaim/CustomerClaimOrderProductsList';
import { CustomerClaimSearchForm } from '../../../components/public/customerClaim/CustomerClaimSearchForm';
import { PublicLayout } from '../../../components/public/Layout';
import { type NextPageWithLayout } from '../../../types/next';

const PublicCustomerClaimManagement: NextPageWithLayout = () => {
	const [code, setCode] = useQueryState('code');
	const [email, setEmail] = useQueryState('email');

	const {
		data: customerClaimsData,
		isLoading: isLoadingCustomerClaims,
		isFetching: isFetchingCustomerClaims,
		isFetched: isFetchedCustomerClaims,
	} = apiHooks.useGetPublicCustomerClaims({
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER, filter: { 'contact.email': { eq: email } } },
	});

	const customerClaims = useMemo(() => customerClaimsData?._data ?? [], [customerClaimsData?._data]);

	const {
		data: orderFoundData,
		isLoading: isLoadingOrder,
		isFetching: isFetchingOrder,
		isFetched: isFetchedOrder,
	} = apiHooks.useGetPublicEcommerceOrders(
		{
			queries: { page: 1, limit: 1, filter: { code: { eq: code ?? '' }, 'contact.email': { eq: email ?? '' } } },
		},
		{ enabled: code != null && !code.startsWith(CODE_PREFIX.CUSTOMER_CLAIM) && email != null },
	);

	const orderFound = useMemo(() => {
		if (code?.startsWith(CODE_PREFIX.CUSTOMER_CLAIM)) return null;
		return orderFoundData?._data?.[0] ?? null;
	}, [orderFoundData?._data, code]);

	const customerClaimFound = useMemo(() => {
		if (!code?.startsWith(CODE_PREFIX.CUSTOMER_CLAIM)) return null;
		const cleanCode = stripCode(code);
		return customerClaims.find((claim) => claim.code.code === cleanCode);
	}, [code, customerClaims]);

	return (
		<>
			<PageTitle title="Zákaznické reklamace" />

			<Page className="min-h-full p-4 justify-center flex flex-col md:p-10">
				<Stack>
					<Title title="Zákaznické reklamace" />

					<CustomerClaimSearchForm
						isLoading={(isLoadingCustomerClaims && isFetchingCustomerClaims) || (isLoadingOrder && isFetchingOrder)}
						onSubmit={({ code, email }) => {
							setCode(code);
							setEmail(email);
						}}
					/>

					{isFetchedCustomerClaims && isFetchedOrder && !customerClaimFound && !orderFound && (
						<Alert variant="info">
							<Icon name="circle-info" />
							<AlertTitle>Zadané číslo objednávky / reklamace nebylo nalezeno</AlertTitle>
							<AlertDescription>Zadejte jiný kód nebo kontaktujte Počítarnu.</AlertDescription>
						</Alert>
					)}
					{customerClaimFound && !orderFound && <CustomerClaimList customerClaims={[customerClaimFound]} />}
					{email && orderFound && !customerClaimFound && (
						<>
							<CustomerClaimOrderProductsList order={orderFound} email={email} customerClaims={customerClaims} />
							<CustomerClaimList customerClaims={customerClaims} />
						</>
					)}
				</Stack>
			</Page>
		</>
	);
};

export default PublicCustomerClaimManagement;

PublicCustomerClaimManagement.getLayout = (page: ReactElement) => {
	return <PublicLayout>{page}</PublicLayout>;
};
