import {
	CustomerClaimHandlingMethodMessage,
	CustomerClaimStatusMessage,
	CustomerDeliveryMethodMessage,
	MAX_POSITIVE_INTEGER,
} from '@pocitarna-nx-2023/config';
import {
	Badge,
	Card,
	CardContent,
	CardHeader,
	MutedText,
	Page,
	PageTitle,
	ParamItem,
	ParamList,
	SidebarGrid,
	Stack,
	Title,
} from '@pocitarna-nx-2023/ui';
import { formatCustomerClaimCode, formatDateTime, formatProductCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps } from 'next';
import { type FC, useMemo } from 'react';
import { AddressCard } from '../../components/AddressCard';
import { CustomerClaimActions } from '../../components/customerClaim/CustomerClaimActions';
import { CustomerClaimCountdownCard } from '../../components/customerClaim/CustomerClaimCountdown';
import { CustomerClaimFiles } from '../../components/customerClaim/CustomerClaimFiles';
import { CustomerClaimFlowHandler } from '../../components/customerClaim/CustomerClaimFlowHandler';
import { CustomerClaimReceiptForm } from '../../components/customerClaim/CustomerClaimReceiptForm';
import { OrderCard } from '../../components/customerClaim/OrderCard';
import { CustomerClaimHistory } from '../../components/history/customerClaim/CustomerClaimHistory';
import { HistoryAccordion } from '../../components/history/HistoryAccordion';
import { NotesManagement } from '../../components/NotesManagement';
import { ProductPlacementInfo } from '../../components/product/ProductPlacementInfo';
import { ProductPriceOverview } from '../../components/product/ProductPriceOverview';
import { ProductDefectsManagement } from '../../components/productDefect/ProductDefectsManagement';
import { useUserHasScope } from '../../hooks/useUserHasScope';
import { checkScope } from '../../utils/checkScope';

type Props = { customerClaimId: string };

const CustomerClaim: FC<Props> = ({ customerClaimId }) => {
	const { data: customerClaimData, invalidate } = apiHooks.useGetCustomerClaim(
		{ params: { customerClaimId } },
		{ enabled: Boolean(customerClaimId) },
	);
	const customerClaim = useMemo(() => customerClaimData?._data, [customerClaimData?._data]);

	if (!customerClaim) return null;

	return <PageContent customerClaim={customerClaim} invalidate={invalidate} />;
};

const PageContent: FC<{ customerClaim: ApiBody<'getCustomerClaim'>; invalidate: () => Promise<void> }> = ({
	customerClaim,
	invalidate,
}) => {
	const orderItem = customerClaim.ecommerceOrderItem;
	const { data: productData } = apiHooks.useGetProduct(
		{ params: { productId: orderItem.productId ?? '' } },
		{ enabled: Boolean(orderItem.productId) },
	);
	const product = productData?._data;
	const { data: filesData } = apiHooks.useGetPublicCustomerClaimFiles({
		params: { customerClaimId: customerClaim.id },
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER },
	});

	const files = filesData?._data ?? [];
	const title = `Reklamace č. ${formatCustomerClaimCode(customerClaim.code)}`;
	const customerMessages = customerClaim.messages.filter((message) => message.contactId !== null);

	const hasCustomerClaimWriteRights = useUserHasScope('customerClaimWrite');

	if (!product) return null;

	return (
		<>
			<PageTitle title={title} />

			<Page className="min-h-full p-4 justify-center flex flex-col md:p-10">
				<Stack>
					<Title
						title={title}
						backlink={{ label: 'Reklamace', url: '/customer-claim' }}
						titleChildren={
							<>
								<MutedText>{formatDateTime(customerClaim.createdAt)}</MutedText>
								<Badge variant="info">{CustomerClaimStatusMessage[customerClaim.status]}</Badge>
							</>
						}
					/>

					<SidebarGrid>
						<Stack>
							<Card>
								<CardHeader>
									<h3 className="font-semibold leading-none tracking-tight">Hlášení reklamace</h3>
								</CardHeader>
								<CardContent>
									<Stack>
										<Stack gap={1}>
											<h5 className="font-semibold leading-none tracking-tight">{orderItem.name}</h5>
											<MutedText>
												{formatProductCode(product.code)}
												<br />
												{product?.sn?.toUpperCase()}
											</MutedText>
										</Stack>
										<ParamList>
											<ParamItem label="Popis závady">
												{customerMessages.map((message) => message.message).join('\n')}
											</ParamItem>
											<ParamItem label="Preferovaný způsob vyřízení reklamace">
												{CustomerClaimHandlingMethodMessage[customerClaim.handlingMethod]}
											</ParamItem>
											<ParamItem label="Způsob doručení">
												{CustomerDeliveryMethodMessage[customerClaim.customerDeliveryMethod]}
											</ParamItem>
										</ParamList>

										<CustomerClaimFiles files={files} />
									</Stack>
								</CardContent>
							</Card>
							<ProductDefectsManagement product={product} source="CUSTOMER_CLAIM" customerClaimId={customerClaim.id} />
							{customerClaim.status === 'NEW' && <CustomerClaimReceiptForm customerClaim={customerClaim} product={product} />}
						</Stack>
						<Stack>
							{customerClaim.receivedAt && <CustomerClaimCountdownCard customerClaim={customerClaim} />}
							<OrderCard customerClaim={customerClaim} />
							<AddressCard invoiceAddress={customerClaim.address} contact={customerClaim.contact} />
							<ProductPlacementInfo product={product} />
							<ProductPriceOverview product={product} />
							<NotesManagement
								entity={customerClaim}
								entityType="customerClaim"
								invalidate={invalidate}
								disabled={!hasCustomerClaimWriteRights}
							/>
							<CustomerClaimActions customerClaim={customerClaim} />
						</Stack>
					</SidebarGrid>
					<HistoryAccordion defaultOpen>
						<CustomerClaimHistory customerClaim={customerClaim}>
							{customerClaim?.status === 'NEW' ? (
								<CustomerClaimFlowHandler
									product={product}
									productDefects={product.productDefects.filter((defect) => defect.customerClaimId === customerClaim.id)}
									customerClaim={customerClaim}
									disabled={!hasCustomerClaimWriteRights}
								/>
							) : null}
						</CustomerClaimHistory>
					</HistoryAccordion>
				</Stack>
			</Page>
		</>
	);
};

export default CustomerClaim;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	const customerClaimId = ctx.params?.customerClaimId;

	return {
		props: {
			customerClaimId:
				typeof customerClaimId === 'string' ? customerClaimId : Array.isArray(customerClaimId) ? customerClaimId[0] : '',
		},
		redirect: await checkScope(ctx, 'customerClaimRead'),
	};
};
