import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import type { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import { type FC, useEffect, useMemo } from 'react';
import { checkScope } from '../../../utils/checkScope';

type Props = { batchCode: string };

const BatchCode: FC<Props> = ({ batchCode }) => {
	const router = useRouter();
	const {
		data: batchData,
		isSuccess,
		isFetched,
	} = apiHooks.useGetBatchByCode({ params: { batchCode: batchCode } }, { enabled: !!batchCode });
	const batch = useMemo(() => batchData?._data, [batchData?._data]);

	useEffect(() => {
		if (isFetched) {
			if (isSuccess && batch) {
				router.push(`/batch/${batch.id}`);
			} else {
				router.push('/404');
			}
		}
	}, [batch, isSuccess, isFetched, router]);

	return null;
};

export default BatchCode;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	const batchCode = ctx.params?.batchCode;

	return {
		props: {
			batchCode: typeof batchCode === 'string' ? batchCode : Array.isArray(batchCode) ? batchCode[0] : '',
		},
		redirect: await checkScope(ctx, 'batchRead'),
	};
};
