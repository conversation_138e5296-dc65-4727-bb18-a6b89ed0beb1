import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import type { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import { type FC, useEffect, useMemo } from 'react';
import { checkScope } from '../../../../utils/checkScope';

type Props = { productEnvelopeCode: string };

const ProductEnvelopeCode: FC<Props> = ({ productEnvelopeCode }) => {
	const router = useRouter();
	const {
		data: productEnvelopeData,
		isSuccess,
		isFetched,
	} = apiHooks.useGetProductEnvelopeByCode({ params: { productEnvelopeCode: productEnvelopeCode } }, { enabled: !!productEnvelopeCode });
	const productEnvelope = useMemo(() => productEnvelopeData?._data, [productEnvelopeData?._data]);

	useEffect(() => {
		if (isFetched) {
			if (isSuccess && productEnvelope) {
				router.push(`/product/envelope/${productEnvelope.id}`);
			} else {
				router.push('/404');
			}
		}
	}, [productEnvelope, isSuccess, isFetched, router]);

	return null;
};

export default ProductEnvelopeCode;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	const productEnvelopeCode = ctx.params?.productEnvelopeCode;

	return {
		props: {
			productEnvelopeCode:
				typeof productEnvelopeCode === 'string'
					? productEnvelopeCode
					: Array.isArray(productEnvelopeCode)
						? productEnvelopeCode[0]
						: '',
		},
		redirect: await checkScope(ctx, 'productRead'),
	};
};
