import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import type { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import { type FC, useEffect, useMemo } from 'react';
import { checkScope } from '../../../utils/checkScope';

type Props = { productCode: string };

const ProductCode: FC<Props> = ({ productCode }) => {
	const router = useRouter();
	const {
		data: productData,
		isSuccess,
		isFetched,
	} = apiHooks.useGetProductByCode({ params: { productCode: productCode } }, { enabled: !!productCode });
	const product = useMemo(() => productData?._data, [productData?._data]);

	useEffect(() => {
		if (isFetched) {
			if (isSuccess && product) {
				router.push(`/product/${product.id}`);
			} else {
				router.push('/404');
			}
		}
	}, [product, isSuccess, isFetched, router]);

	return null;
};

export default ProductCode;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	const productCode = ctx.params?.productCode;

	return {
		props: {
			productCode: typeof productCode === 'string' ? productCode : Array.isArray(productCode) ? productCode[0] : '',
		},
		redirect: await checkScope(ctx, 'productRead'),
	};
};
