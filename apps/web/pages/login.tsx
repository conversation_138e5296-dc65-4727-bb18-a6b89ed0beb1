import { DOMAIN_NAME, FIVE_SECONDS, IS_DEV } from '@pocitarna-nx-2023/config';
import { <PERSON>ton, Card, CardContent, cn, Icon, Logo, Page, PageTitle, Separator, Stack, toast } from '@pocitarna-nx-2023/ui';
import Cookies from 'cookies';
import { type GetServerSideProps } from 'next';
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/router';
import { getSession, signIn } from 'next-auth/react';
import { type ReactElement, useEffect } from 'react';
import QRCode from 'react-qr-code';
import { useIntervalWhen, useToggle } from 'rooks';
import { useScan } from '../hooks/useScan';
import { type NextPageWithLayout } from '../types/next';

type Props = {
	deviceCode?: string;
};

const Login: NextPageWithLayout<Props> = ({ deviceCode }) => {
	const router = useRouter();
	const [isWaitingForScanning, toggleScanningMode] = useToggle();
	const [code] = useScan(isWaitingForScanning);
	const searchParams = useSearchParams();

	const loginWithMicrosoft = () => {
		signIn('azure-ad');
	};

	useEffect(() => {
		const errorMessage = searchParams?.get('error');
		if (errorMessage != null) {
			const timeoutId = setTimeout(() => {
				toast.error('Přihlášení nebylo úspěšné', { description: errorMessage });
			}, 0);

			return () => {
				clearTimeout(timeoutId);
			};
		}
	}, [searchParams]);

	useEffect(() => {
		if (code) {
			signIn('qr-code', { code, redirect: true });
		}
	}, [code]);

	useIntervalWhen(
		async () => {
			const session = await getSession();
			if (session) {
				router.reload();
			}
		},
		FIVE_SECONDS,
		!!deviceCode,
	);

	return (
		<>
			<PageTitle title="Přihlášení" />

			<Page className="loginpage min-h-full items-center bg-primary p-4 justify-center flex flex-col md:p-10">
				<Logo className="h-10 text-white mb-6" />
				<Card className="shadow-2xl border-0">
					<CardContent className="text-center p-10 px-4 md:px-10">
						<h1 className="mb-8">Přihlásit se</h1>
						<Stack gap={2} direction="row">
							<Stack gap={2}>
								<Button onClick={loginWithMicrosoft} variant="outline" width="full" className="h-12">
									<Icon name="right-to-bracket" />
									Přihlášení pomocí Microsoft účtu
								</Button>
								<Separator />
								<Button
									onClick={toggleScanningMode}
									variant="outline"
									width="full"
									className="h-12"
									isLoading={isWaitingForScanning}
								>
									<Icon name="qrcode" />
									Načíst QR kód
								</Button>
							</Stack>
							{deviceCode && (
								<>
									<Separator type="vertical" />
									<QRCode
										value={deviceCode}
										className={cn('aspect-[1/1] overflow-hidden object-contain object-center')}
										size={161}
									/>
								</>
							)}
						</Stack>
					</CardContent>
				</Card>
			</Page>
		</>
	);
};

export const getServerSideProps: GetServerSideProps<Props, { returnUrl?: string }> = async (ctx) => {
	const session = await getSession({ ctx });
	const isLoggedIn = !!session?.user;

	if (!isLoggedIn) {
		const deviceCode = crypto.randomUUID();
		const cookies = new Cookies(ctx.req, ctx.res, { secure: !IS_DEV });
		if (!cookies.get('next-auth.session-token')) {
			cookies.set('next-auth.session-token', deviceCode, {
				httpOnly: true,
				sameSite: 'strict',
				path: '/',
				secure: !IS_DEV,
				domain: IS_DEV ? 'localhost' : DOMAIN_NAME,
				priority: 'high',
			});
		}

		return { props: { deviceCode } };
	}

	const redirectUrl = ctx.query.returnUrl ?? session.user.role?.[0]?.defaultUrl ?? '/';
	return {
		props: {},
		redirect: { statusCode: 302, destination: redirectUrl },
	};
};

Login.getLayout = (page: ReactElement) => {
	return page;
};

export default Login;
