import { <PERSON><PERSON>, Container, Icon, <PERSON>barGrid, Stack, Title, toast } from '@pocitarna-nx-2023/ui';
import { formatBatchCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import type { GetServerSideProps } from 'next';
import { type FC } from 'react';
import { useToggle } from 'rooks';
import { BatchOverview } from '../../../components/batch/BatchOverview';
import { BatchCheckSnForm } from '../../../components/batchCheck/BatchCheckSnForm';
import { BatchCheckSnProductsTable } from '../../../components/batchCheck/BatchCheckSnProductsTable';
import { CheckSnOverview } from '../../../components/batchCheck/CheckSnOverview';
import { BatchUploader } from '../../../components/BatchUploader';
import { BatchHistory } from '../../../components/history/batch/BatchHistory';
import { HistoryAccordion } from '../../../components/history/HistoryAccordion';
import { SortingProvider } from '../../../contexts/SortingContext';
import { useUserHasScope } from '../../../hooks/useUserHasScope';
import { checkScope } from '../../../utils/checkScope';

type Props = { batchId: string };

const BatchCheckSn: FC<Props> = ({ batchId }) => {
	const [showDropzone, toggle] = useToggle(false);
	const hasBatchWriteRights = useUserHasScope('batchWrite');
	const { data: batchData } = apiHooks.useGetBatch({ params: { batchId } });

	const batch = batchData?._data;

	if (!batch) return null;

	const batchWasAlreadyChecked = batch.checkedSnAt !== null;

	return (
		<SortingProvider>
			<Container>
				<Stack>
					<Title title={`Kontrola SN várky ${formatBatchCode(batch.code)}`} backlink={{ url: '/check', label: 'Kontrola' }}>
						<Button onClick={toggle} disabled={!hasBatchWriteRights}>
							<Icon name="file-arrow-up" />
							Nahrát soubor
						</Button>
					</Title>

					{showDropzone && (
						<BatchUploader
							batchId={batchId}
							onFinish={() => {
								toggle();
								toast.success('Várka byla rozšířena o nový soubor');
							}}
							disabled={!hasBatchWriteRights}
						/>
					)}

					<SidebarGrid>
						<Stack>{batchWasAlreadyChecked ? <CheckSnOverview batch={batch} /> : <BatchCheckSnForm batch={batch} />}</Stack>

						<BatchOverview batch={batch} />
					</SidebarGrid>

					<BatchCheckSnProductsTable batch={batch} />

					<HistoryAccordion>
						<BatchHistory batch={batch} />
					</HistoryAccordion>
				</Stack>
			</Container>
		</SortingProvider>
	);
};

export default BatchCheckSn;

export const getServerSideProps: GetServerSideProps<Props> = async (ctx) => {
	const batchId = ctx.params?.batchId;

	return {
		props: {
			batchId: typeof batchId === 'string' ? batchId : Array.isArray(batchId) ? batchId[0] : '',
		},
		redirect: await checkScope(ctx, 'batchCheck'),
	};
};
