const { sentryEsbuildPlugin } = require('@sentry/esbuild-plugin');

/**
 * @type {import('esbuild').BuildOptions}
 **/
module.exports = {
	sourcemap: true,
	outExtension: {
		'.js': '.js',
	},
	plugins: [
		sentryEsbuildPlugin({
			disable: process.env.NODE_ENV !== 'production',
			org: 'pocitarna',
			project: 'api',
			authToken:
				'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',
		}),
	],
};
