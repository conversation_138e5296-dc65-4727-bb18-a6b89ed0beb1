{"name": "api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"platform": "node", "outputPath": "dist/{projectRoot}", "format": ["cjs"], "bundle": false, "main": "{projectRoot}/src/main.ts", "tsConfig": "{projectRoot}/tsconfig.app.json", "assets": ["{projectRoot}/src/assets"], "skipTypeCheck": true, "esbuildConfig": "{projectRoot}/esbuild.config.js"}, "configurations": {"development": {}, "production": {"generatePackageJson": true, "bundle": true}}}, "deploy": {"executor": "nx:run-commands", "defaultConfiguration": "staging", "configurations": {"staging": {"command": "nx run cdk:deploy:staging Api"}, "prod": {"command": "nx run cdk:deploy:prod Api"}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "api:build", "runBuildTargetDependencies": true}, "configurations": {"development": {"buildTarget": "api:build:development"}, "production": {"buildTarget": "api:build:production"}}}, "lint": {}, "typecheck": {}}}