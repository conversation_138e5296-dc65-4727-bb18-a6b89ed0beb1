import './sentry';
import { ALLOWED_ORIGINS, HTTP_METHODS_NAMES, ONE_MINUTE, ONE_SECOND, SSE_ENDPOINT, TWO_SECONDS } from '@pocitarna-nx-2023/config';
import { Database } from '@pocitarna-nx-2023/database';
import { sseMiddleware, sseRouter } from '@pocitarna-nx-2023/sse-server';
import { api } from '@pocitarna-nx-2023/zodios';
import * as Sentry from '@sentry/node';
import { zodiosContext } from '@zodios/express';
import bodyParser from 'body-parser';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import type { NextFunction, Request, Response } from 'express';
import * as httpContext from 'express-http-context';
import helmet from 'helmet';
import morgan from 'morgan';
import { createActionMiddleware } from './middlewares/action';
import { createAuthenticationMiddleware } from './middlewares/authentication';
import { contextMiddleware } from './middlewares/contextMiddleware';
import { createTransactionMiddleware, rollbackTransaction } from './middlewares/transaction';
import { rootRouter } from './routes';

const ctx = zodiosContext();
const app = ctx.app(api, { enableJsonBodyParser: false });

app.set('query parser', (str: string) =>
	str && str.length > 0 && str.startsWith('q=') ? JSON.parse(decodeURIComponent(str.slice(2))) : {},
);
app.set('trust proxy', 2); // ALB + Cloudfront

app.use(
	cors({
		origin: (origin, callback) => {
			if (!origin) return callback(null, true);
			if (ALLOWED_ORIGINS.includes(origin)) return callback(null, true);
			return callback(new Error('CORS: origin not allowed'));
		},
		credentials: true,
		methods: [...HTTP_METHODS_NAMES, 'OPTIONS'],
		allowedHeaders: ['Content-Type', 'Accept', 'Cookie', 'Origin'],
		exposedHeaders: ['Content-Length', 'Content-Type'],
		maxAge: 24 * 60 * 60, // 24h preflight cache
	}),
);
app.use(helmet({ crossOriginResourcePolicy: { policy: 'cross-origin' } }));
app.use(compression());
app.use(cookieParser());
app.use(bodyParser.json({ limit: '100mb' }));
app.use(
	morgan(`:date[iso] - :remote-addr :method :url HTTP/:http-version :status :res[content-length]B - :response-time ms - :total-time ms`, {
		skip: (req) =>
			req.method === 'OPTIONS' ||
			req.originalUrl === '/' ||
			req.originalUrl === '/error' ||
			req.originalUrl === '/healthcheck' ||
			req.originalUrl.startsWith('/sse') ||
			(req.method === 'GET' && req.originalUrl.startsWith('/auth/') && !req.originalUrl.startsWith('/auth/admin')),
	}),
);
app.use(httpContext.middleware);
app.use(contextMiddleware);
app.use(createTransactionMiddleware);
app.use(createAuthenticationMiddleware);
app.use(createActionMiddleware);
app.use(sseMiddleware);
app.use('/', rootRouter(ctx));
app.use(SSE_ENDPOINT, sseRouter());

// @ts-expect-error - Sentry doesn't like augmented express
Sentry.setupExpressErrorHandler(app);

app.use(async (error: Error, req: Request, res: Response, _next: NextFunction) => {
	console.error(req.method, req.path, req.originalUrl, req.query, req.body, error);
	await rollbackTransaction();
	res.status(500).json({
		_error: {
			message: error.message,
			stack: error.stack,
		},
	});
});

Database.initialize().then(() => {
	const port = process.env['PORT'] || 3333;
	const server = app.listen(port);
	server.keepAliveTimeout = ONE_MINUTE + ONE_SECOND;
	server.headersTimeout = ONE_MINUTE + TWO_SECONDS;
	server.on('error', console.error);
});
