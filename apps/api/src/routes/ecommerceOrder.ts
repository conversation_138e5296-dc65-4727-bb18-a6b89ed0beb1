import { EcommerceOrderController, EcommerceOrderItemController, listAll, WarehouseTaskController } from '@pocitarna-nx-2023/database';
import { ecommerceOrderApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const ecommerceOrderRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(ecommerceOrderApi);

	router.get('/', scopeMiddleware('orderRead'), async () => {
		const [orders, getCount] = await new EcommerceOrderController().list(getListProps());
		respondWithPaging<'getEcommerceOrders'>(orders, await getCount());
	});

	router.get('/:ecommerceOrderId', scopeMiddleware('orderRead'), async (req, res) => {
		const order = await new EcommerceOrderController().findById(req.params.ecommerceOrderId);
		if (!order) return res.status(404).json();
		respond<'getEcommerceOrder'>(order);
	});

	router.get('/code/:ecommerceOrderCode', scopeMiddleware('orderRead'), async (req, res) => {
		const order = await new EcommerceOrderController().findByCode(req.params.ecommerceOrderCode);
		if (!order) return res.status(404).json();
		respond<'getEcommerceOrderByCode'>(order);
	});

	router.patch('/:ecommerceOrderId', scopeMiddleware('orderWrite'), async (req) => {
		await new EcommerceOrderController().update(req.params.ecommerceOrderId, req.body);

		const [warehouseTasks] = await new WarehouseTaskController().list(
			listAll({ filter: { ecommerceOrderId: { eq: req.params.ecommerceOrderId } } }),
		);
		await Promise.all(
			warehouseTasks.map((warehouseTask) => new WarehouseTaskController().update(warehouseTask.id, { priority: req.body.priority })),
		);

		respond<'updateEcommerceOrder'>(true);
	});

	router.patch('/:ecommerceOrderId/:ecommerceOrderItemId', scopeMiddleware('orderWrite'), async (req, res) => {
		if (req.body.product?.id) {
			// Shouldn't we keep track of the orderItem status in a new EcommerceOrderItem column? Its status might differ from the one of the order
			const order = await new EcommerceOrderController().findById(req.params.ecommerceOrderId);
			if (!order) return res.status(404).json();

			const orderItem = await new EcommerceOrderItemController().findById(req.params.ecommerceOrderItemId);

			// Unlink the previously associated product
			if (orderItem?.productId) {
				await new EcommerceOrderController().unlinkProduct(req.params.ecommerceOrderId, orderItem.productId);
			}

			// Unlink the newly associated product from its previous order
			const previousOrder = await new EcommerceOrderController().findByProduct(req.body.product.id);
			if (previousOrder) {
				await new EcommerceOrderController().unlinkProduct(previousOrder.id, req.body.product.id);
			}

			// Link the newly associated product
			await new EcommerceOrderController().handleProductInOrder({
				order,
				productId: req.body.product.id,
			});
		}

		await new EcommerceOrderItemController().update(req.params.ecommerceOrderItemId, req.body);

		respond<'updateEcommerceOrderItem'>(true);
	});

	return router;
};
