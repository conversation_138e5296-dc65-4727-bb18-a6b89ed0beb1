import { ShoptetCategoryController } from '@pocitarna-nx-2023/database';
import { shoptetCategoryApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const shoptetCategoryRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(shoptetCategoryApi);

	router.get('/', scopeMiddleware('admin'), async () => {
		const [shoptetCategories, getCount] = await new ShoptetCategoryController().list(getListProps());
		respondWithPaging<'getAllShoptetCategories'>(shoptetCategories, await getCount());
	});

	router.get('/:shoptetCategoryId', scopeMiddleware('admin'), async (req, res) => {
		const shoptetCategory = await new ShoptetCategoryController().findById(req.params.shoptetCategoryId);
		if (!shoptetCategory) return res.status(404).json();
		respond<'getShoptetCategory'>(shoptetCategory);
	});

	return router;
};
