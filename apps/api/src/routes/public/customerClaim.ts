import { CustomerClaimController, CustomerClaimHistoryController } from '@pocitarna-nx-2023/database';
import { publicCustomerClaimApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { getListProps } from '../../utils/getListProps';
import { maskPublicHistory, mergeHistoriesByAction } from '../../utils/history';
import { respond } from '../../utils/respond';

export const publicCustomerClaimRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(publicCustomerClaimApi);

	router.get('/', async () => {
		const [customerClaims] = await new CustomerClaimController().list(getListProps());
		respond<'getPublicCustomerClaims'>(customerClaims);
	});

	router.get('/:customerClaimId', async (req, res) => {
		const customerClaim = await new CustomerClaimController().findById(req.params.customerClaimId);
		if (!customerClaim) return res.status(404).json();

		respond<'getPublicCustomerClaim'>(customerClaim);
	});

	router.post('/', async (req) => {
		await new CustomerClaimController().create(req.body);

		respond<'submitCustomerClaim'>(true);
	});

	router.get('/:customerClaimId/files', async (req) => {
		const [files] = await new CustomerClaimController().listFiles(req.params.customerClaimId);
		respond<'getPublicCustomerClaimFiles'>(files);
	});

	router.patch('/:customerClaimId/files', async (req) => {
		const fileIds = req.body;
		const customerClaimId = req.params.customerClaimId;
		await new CustomerClaimController().addFiles(customerClaimId, fileIds);

		respond<'publicAddFilesToCustomerClaim'>(true);
	});

	router.delete('/:customerClaimId/files/:fileId', async (req, res) => {
		const entity = await new CustomerClaimController().deleteFile(req.params.customerClaimId, req.params.fileId);
		if (!entity) return res.status(500).send();

		respond<'publicDeleteCustomerClaimFile'>(true);
	});

	// History
	router.get('/:customerClaimId/history', async (req) => {
		const publicCustomerClaim = await new CustomerClaimHistoryController().listById(req.params.customerClaimId);

		const mergedHistories = mergeHistoriesByAction({ publicCustomerClaim: maskPublicHistory(publicCustomerClaim) });

		respond<'getPublicCustomerClaimHistory'>(mergedHistories);
	});

	return router;
};
