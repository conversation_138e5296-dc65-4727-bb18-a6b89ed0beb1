import { EcommerceOrderController } from '@pocitarna-nx-2023/database';
import { publicEcommerceOrderApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { getListProps } from '../../utils/getListProps';
import { respond } from '../../utils/respond';

export const publicEcommerceOrderRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(publicEcommerceOrderApi);

	router.get('/', async () => {
		const [orders] = await new EcommerceOrderController().list(getListProps());
		respond<'getPublicEcommerceOrders'>(orders);
	});

	router.get('/:ecommerceOrderId', async (req, res) => {
		const order = await new EcommerceOrderController().findById(req.params.ecommerceOrderId);
		if (!order) return res.status(404).json();
		respond<'getPublicEcommerceOrder'>(order);
	});

	router.get('/code/:ecommerceOrderCode', async (req, res) => {
		const order = await new EcommerceOrderController().findByCode(req.params.ecommerceOrderCode);
		if (!order) return res.status(404).json();
		respond<'getPublicEcommerceOrderByCode'>(order);
	});

	return router;
};
