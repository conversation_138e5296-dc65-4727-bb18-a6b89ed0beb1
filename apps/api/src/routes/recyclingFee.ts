import { RecyclingFeeController } from '@pocitarna-nx-2023/database';
import { recyclingFeeApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const recyclingFeeRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(recyclingFeeApi);

	router.get('/', scopeMiddleware('admin'), async () => {
		const [recyclingFees, getCount] = await new RecyclingFeeController().list(getListProps());
		respondWithPaging<'getAllRecyclingFees'>(recyclingFees, await getCount());
	});

	router.get('/:recyclingFeeId', scopeMiddleware('admin'), async (req, res) => {
		const recyclingFee = await new RecyclingFeeController().findById(req.params.recyclingFeeId);
		if (!recyclingFee) return res.status(404).json();
		respond<'getRecyclingFee'>(recyclingFee);
	});

	return router;
};
