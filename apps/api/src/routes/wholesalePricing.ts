import { WholesalePricingController } from '@pocitarna-nx-2023/database';
import { wholesalePricingApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const wholesalePricingRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(wholesalePricingApi);

	router.post('/', scopeMiddleware('admin'), async (req) => {
		const wholesalePricing = await new WholesalePricingController().create(req.body);
		respond<'createWholesalePricing'>(wholesalePricing);
	});

	router.get('/', scopeMiddleware('admin', 'stock', 'productRead', 'productWrite'), async () => {
		const [wholesalePricings, getCount] = await new WholesalePricingController().list({
			...getListProps(),
			sort: [['coefficient', 'asc']],
		});
		respondWithPaging<'getAllWholesalePricings'>(wholesalePricings, await getCount());
	});

	router.get('/:wholesalePricingId', scopeMiddleware('admin', 'stock'), async (req, res) => {
		const wholesalePricing = await new WholesalePricingController().findById(req.params.wholesalePricingId);
		if (!wholesalePricing) return res.status(404).json();

		respond<'getWholesalePricing'>(wholesalePricing);
	});

	router.patch('/:wholesalePricingId', scopeMiddleware('admin'), async (req, res) => {
		const wholesalePricing = await new WholesalePricingController().update(req.params.wholesalePricingId, req.body);
		if (!wholesalePricing) return res.status(404).json();

		respond<'updateWholesalePricing'>(wholesalePricing);
	});

	router.delete('/:wholesalePricingId', scopeMiddleware('admin'), async (req, res) => {
		const wholesalePricing = await new WholesalePricingController().findById(req.params.wholesalePricingId);
		if (!wholesalePricing) return res.status(404).send();

		const result = await new WholesalePricingController().delete(wholesalePricing.id);

		respond<'deleteWholesalePricing'>(result);
	});

	return router;
};
