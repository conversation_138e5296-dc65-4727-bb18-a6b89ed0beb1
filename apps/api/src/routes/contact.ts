import { ContactController } from '@pocitarna-nx-2023/database';
import { contactApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { respond } from '../utils/respond';

export const contactRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(contactApi);

	router.get('/:contactId', scopeMiddleware('admin'), async (req, res) => {
		const contact = await new ContactController().findById(req.params.contactId);

		if (!contact) return res.status(404).json({ _error: { message: 'Contact not found' } });

		respond<'getContact'>(contact);
	});

	return router;
};
