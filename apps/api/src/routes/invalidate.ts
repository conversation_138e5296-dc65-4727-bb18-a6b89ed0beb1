import { triggerEmail } from '@pocitarna-nx-2023/aws';
import { PRIORITY_VALUES } from '@pocitarna-nx-2023/config';
import { listAll, NotificationController } from '@pocitarna-nx-2023/database';
import { publishInvalidation, publishNotification } from '@pocitarna-nx-2023/sse-server';
import { type ApiAliases, invalidateApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { respond } from '../utils/respond';

export const invalidateRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(invalidateApi);

	router.post('/', scopeMiddleware('admin'), async (req) => {
		publishInvalidation(...(req.body as ApiAliases[]));
		respond<'postInvalidate'>(true);
	});

	router.post('/resend-unread-notfications', async () => {
		const [unreadUrgentNotifications] = await new NotificationController().list(
			listAll({ filter: { viewedAt: { eq: null }, viewUntil: { lt: new Date() }, priority: { eq: PRIORITY_VALUES.HIGH } } }),
		);

		if (unreadUrgentNotifications.length > 0) {
			await Promise.all(
				unreadUrgentNotifications
					.filter((notification) => notification.user.email != null)
					.map((notification) =>
						triggerEmail({
							recipients: notification.user.email as string,
							subject: notification.title,
							message: notification.body,
						}),
					),
			);

			publishNotification(...unreadUrgentNotifications);
		}

		respond<'resendUnreadNotifications'>(true);
	});

	return router;
};
