import { NoteController } from '@pocitarna-nx-2023/database';
import { noteApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { respond } from '../utils/respond';

export const noteRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(noteApi);

	router.post('/', scopeMiddleware('admin', 'serviceWrite'), async (req) => {
		await new NoteController().create(req.body);

		respond<'createNote'>(true);
	});

	router.delete('/:noteId', scopeMiddleware('admin'), async (req) => {
		await new NoteController().delete(req.params.noteId);

		respond<'deleteNote'>(true);
	});

	return router;
};
