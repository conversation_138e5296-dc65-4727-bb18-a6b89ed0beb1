import { listAll, WarehouseController, WarehousePositionController } from '@pocitarna-nx-2023/database';
import { warehouseApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const warehouseRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(warehouseApi);

	router.get('/', scopeMiddleware('warehouseRead'), async () => {
		const [warehouses, getCount] = await new WarehouseController().list(getListProps());
		respondWithPaging<'getWarehouses'>(warehouses, await getCount());
	});

	router.post('/', scopeMiddleware('warehouseManage'), async (req) => {
		await new WarehouseController().create(req.body);

		respond<'createWarehouse'>(true);
	});

	router.put('/:warehouseId', scopeMiddleware('warehouseManage'), async (req, res) => {
		const warehouse = await new WarehouseController().update(req.params.warehouseId, req.body);
		if (!warehouse) return res.status(404).json();

		respond<'updateWarehouse'>(warehouse);
	});

	router.get('/:warehouseId', scopeMiddleware('warehouseRead'), async (req, res) => {
		const warehouse = await new WarehouseController().findById(req.params.warehouseId);
		if (!warehouse) return res.status(404).json();

		respond<'getWarehouse'>(warehouse);
	});

	router.delete('/:warehouseId', scopeMiddleware('warehouseManage'), async (req, res) => {
		const warehouse = await new WarehouseController().findById(req.params.warehouseId);

		if (!warehouse) return res.status(404).send();

		const [positionsInWarehouse] = await new WarehousePositionController().list(
			listAll({ filter: { 'warehouse.id': { eq: req.params.warehouseId } } }),
		);

		if (positionsInWarehouse.length > 0) {
			return res.status(409).json({
				_error: {
					message: 'Tento sklad nelze smazat, protože má přiřazené pozice.',
				},
			});
		}

		const result = await new WarehouseController().delete(warehouse.id);

		respond<'deleteWarehouse'>(result);
	});

	return router;
};
