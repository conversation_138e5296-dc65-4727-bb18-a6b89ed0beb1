import { ServiceTaskController, WarrantyClaimCodeController, WarrantyClaimController } from '@pocitarna-nx-2023/database';
import { warrantyClaimApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { isFilteringBy } from '../utils/isFilteringBy';
import { respond, respondWithPaging } from '../utils/respond';

export const warrantyClaimRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(warrantyClaimApi);

	router.post('/', scopeMiddleware('warrantyClaimWrite'), async (req, res) => {
		const warrantyClaim = await new WarrantyClaimController().create(req.body);

		if (!warrantyClaim) return res.status(500).json({ _error: { message: 'Could not create warranty claim' } });

		respond<'createWarrantyClaim'>(warrantyClaim);
	});

	router.get('/', scopeMiddleware('warrantyClaimRead', 'warrantyClaimWrite', 'productRead', 'productWrite'), async () => {
		// Actions cannot be added to the base queryBuilder (they would clash with the version queryBuilder loadActions),
		// hence we decided to adjust the main list method to choose a more specific queryBuilder for that specific use-case

		const listProps = getListProps();
		const isFilteringByAction = isFilteringBy(listProps, 'action');
		const isFilteringByModel = isFilteringBy(listProps, 'attributeValue.value');

		const [data, getCount] = isFilteringByAction
			? await new WarrantyClaimController().listWithAction(listProps)
			: await new WarrantyClaimController().listWithProduct(listProps, isFilteringByModel);

		return respondWithPaging<'getWarrantyClaims'>(data, await getCount());
	});

	router.get(
		'/:warrantyClaimId',
		scopeMiddleware('warrantyClaimRead', 'warrantyClaimWrite', 'productRead', 'productWrite'),
		async (req, res) => {
			const warrantyClaim = await new WarrantyClaimController().findById(req.params.warrantyClaimId);
			if (!warrantyClaim) return res.status(404).json();
			respond<'getWarrantyClaim'>(warrantyClaim);
		},
	);

	router.get(
		'/code/:warrantyClaimCodeId',
		scopeMiddleware('warrantyClaimRead', 'warrantyClaimWrite', 'productRead', 'testRead', 'productTest', 'batchRead'),
		async (req, res) => {
			const warrantyClaimCode = await new WarrantyClaimCodeController().findById(req.params.warrantyClaimCodeId);
			if (!warrantyClaimCode) return res.status(404).json();
			respond<'getWarrantyClaimCode'>(warrantyClaimCode);
		},
	);

	router.patch('/bulk', scopeMiddleware('warrantyClaimWrite'), async (req) => {
		await Promise.all(
			req.body.ids.map(async (id) => {
				await new WarrantyClaimController().handleTransition(id, req.body.data);
			}),
		);

		return respond<'bulkUpdateWarrantyClaims'>(true);
	});

	router.patch('/:warrantyClaimId', scopeMiddleware('warrantyClaimWrite'), async (req) => {
		const { warrantyClaimId } = req.params;

		if (!('status' in req.body)) {
			await new WarrantyClaimController().update(warrantyClaimId, {
				trackingCode: req.body.trackingCode,
				vendorRMAIdentifier: req.body.vendorRMAIdentifier,
			});

			return respond<'updateWarrantyClaim'>(true);
		}

		await new WarrantyClaimController().handleTransition(warrantyClaimId, req.body);

		respond<'updateWarrantyClaim'>(true);
	});

	router.patch('/:warrantyClaimId/files', scopeMiddleware('warrantyClaimWrite', 'productWrite'), async (req) => {
		const fileIds = req.body;
		const warrantyClaimId = req.params.warrantyClaimId;
		await new WarrantyClaimController().addFiles(warrantyClaimId, fileIds);
		respond<'addFilesToWarrantyClaim'>(true);
	});

	router.delete('/:warrantyClaimId/files/:fileId', scopeMiddleware('warrantyClaimWrite', 'productWrite'), async (req, res) => {
		const entity = await new WarrantyClaimController().deleteFile(req.params.warrantyClaimId, req.params.fileId);
		if (!entity) return res.status(500).send();

		return respond<'deleteWarrantyClaimFile'>(true);
	});

	router.get('/:warrantyClaimId/service-task', scopeMiddleware('warrantyClaimRead'), async (req, res) => {
		const product = await new WarrantyClaimController().findProduct(req.params.warrantyClaimId);

		if (!product) return res.status(404).json();

		const serviceTasks = await new ServiceTaskController().findByProduct(product.id);
		respond<'getWarrantyClaimServiceTasks'>(serviceTasks);
	});

	return router;
};
