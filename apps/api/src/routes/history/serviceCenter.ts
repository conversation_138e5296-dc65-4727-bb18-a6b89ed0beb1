import { ServiceCenterHistoryController } from '@pocitarna-nx-2023/database';
import { serviceCenterHistoryApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../../middlewares/scope';
import { mergeHistoriesByAction } from '../../utils/history';
import { respond } from '../../utils/respond';

export const serviceCenterHistoryRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(serviceCenterHistoryApi);

	router.get('/:serviceCenterId', scopeMiddleware('admin'), async (req) => {
		const [serviceCenter] = await Promise.all([new ServiceCenterHistoryController().listById(req.params.serviceCenterId)]);

		const mergedHistories = mergeHistoriesByAction({ serviceCenter });

		respond<'getServiceCenterHistory'>(mergedHistories);
	});

	return router;
};
