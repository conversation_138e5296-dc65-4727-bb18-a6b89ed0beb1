import { ProductEnvelopeHistoryController } from '@pocitarna-nx-2023/database';
import { productEnvelopeHistoryApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../../middlewares/scope';
import { mergeHistoriesByAction } from '../../utils/history';
import { respond } from '../../utils/respond';

export const productEnvelopeHistoryRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(productEnvelopeHistoryApi);

	router.get('/:productEnvelopeId', scopeMiddleware('productRead'), async (req) => {
		const [productEnvelope] = await Promise.all([new ProductEnvelopeHistoryController().listById(req.params.productEnvelopeId)]);

		const mergedHistories = mergeHistoriesByAction({ productEnvelope });

		respond<'getProductEnvelopeHistory'>(mergedHistories);
	});

	return router;
};
