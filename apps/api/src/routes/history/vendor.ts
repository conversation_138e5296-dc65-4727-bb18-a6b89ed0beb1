import { FileVendorHistoryController, VendorHistoryController } from '@pocitarna-nx-2023/database';
import { vendorHistoryApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../../middlewares/scope';
import { mergeHistoriesByAction } from '../../utils/history';
import { respond } from '../../utils/respond';

export const vendorHistoryRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(vendorHistoryApi);

	router.get('/:vendorId', scopeMiddleware('admin'), async (req) => {
		const [vendor, fileVendor] = await Promise.all([
			new VendorHistoryController().listById(req.params.vendorId),
			new FileVendorHistoryController().listByVendorId(req.params.vendorId),
		]);

		const mergedHistories = mergeHistoriesByAction({ vendor, fileVendor });

		respond<'getVendorHistory'>(mergedHistories);
	});

	return router;
};
