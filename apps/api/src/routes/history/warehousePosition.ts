import { ProductHistoryController, WarehousePositionHistoryController } from '@pocitarna-nx-2023/database';
import { warehousePositionHistoryApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../../middlewares/scope';
import { mergeHistoriesByAction } from '../../utils/history';
import { respond } from '../../utils/respond';

export const warehousePositionHistoryRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(warehousePositionHistoryApi);

	router.get('/:warehousePositionId', scopeMiddleware('productRead'), async (req) => {
		const [warehousePosition, productInserts] = await Promise.all([
			new WarehousePositionHistoryController().listById(req.params.warehousePositionId),
			new ProductHistoryController().listByWarehousePositionId(req.params.warehousePositionId),
		]);

		const productExits = await new ProductHistoryController().getWarehousePositionProductHistory(
			req.params.warehousePositionId,
			productInserts.map((product) => product.id),
		);

		const mergedHistories = mergeHistoriesByAction({
			warehousePosition,
			warehousePositionProduct: [...productInserts, ...productExits],
		});

		respond<'getWarehousePositionHistory'>(mergedHistories);
	});

	return router;
};
