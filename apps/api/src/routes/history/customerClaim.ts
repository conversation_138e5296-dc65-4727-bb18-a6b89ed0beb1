import { CustomerClaimHistoryController, ProductDefectHistoryController } from '@pocitarna-nx-2023/database';
import { customerClaimHistoryApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../../middlewares/scope';
import { mergeHistoriesByAction } from '../../utils/history';
import { respond } from '../../utils/respond';

export const customerClaimHistoryRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(customerClaimHistoryApi);

	router.get('/:customerClaimId', scopeMiddleware('customerClaimRead'), async (req) => {
		const [customerClaim, productDefect] = await Promise.all([
			new CustomerClaimHistoryController().listById(req.params.customerClaimId),
			new ProductDefectHistoryController().listByCustomerClaimId(req.params.customerClaimId),
		]);

		const mergedHistories = mergeHistoriesByAction({ customerClaim, productDefect });

		respond<'getCustomerClaimHistory'>(mergedHistories);
	});

	return router;
};
