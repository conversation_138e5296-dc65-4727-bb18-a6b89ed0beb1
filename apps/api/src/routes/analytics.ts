import { NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import {
	listAll,
	type Product,
	ProductAttributeValueController,
	ProductController,
	type ProductDefect,
	ProductDefectController,
	ProductEnvelopeController,
	UserController,
	VendorController,
} from '@pocitarna-nx-2023/database';
import {
	filterUndefined,
	formatEnvelopeCode,
	groupBy,
	safeDivide,
	sortByCreationDate,
	toISODate,
	uniques,
	uniquesBy,
} from '@pocitarna-nx-2023/utils';
import { type agingStockAnalytics, analyticsApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { groupBy as groupByRambda } from 'rambdax';
import { type z } from 'zod';
import { scopeMiddleware } from '../middlewares/scope';
import { respond } from '../utils/respond';

export const analyticsRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(analyticsApi);

	router.get('/testing', scopeMiddleware('productTestLead'), async (req) => {
		const { startDate, endDate } = req.query;

		const [allTesters, testedProductsFixes, testedProducts] = await Promise.all([
			new UserController()
				.list(
					listAll({
						filter: {
							'scope.name': { eq: ['productTest', 'productTestLead'] },
						},
						sort: [['name', 'asc']],
					}),
				)
				.then(([res]) => res),

			new ProductController()
				.list(
					listAll({
						filter: {
							'productTest.testedById': { ne: null },
							'productTest.testedAt': { gte: startDate, lt: endDate },
							'attributeValues.isFix': { eq: true },
							'attributeValues.attributeValue.attribute.name': { ne: ['Baterie - stav', '[Mobil / Tablet] Počet cyklů'] },
						},
					}),
				)
				.then(([res]) => res),

			new ProductController()
				.list(
					listAll({
						filter: {
							'productTest.testedById': { ne: null },
							'productTest.testedAt': { gte: startDate, lt: endDate },
						},
					}),
				)
				.then(([res]) => res),
		]);

		const categories = filterUndefined(
			uniquesBy(
				testedProducts.filter((product) => product.productCategory != null).map((product) => product.productCategory),
				(category) => category?.id || '',
			),
		)
			.sort((a, b) => a.name.localeCompare(b.name))
			.map((item) => ({ id: item.id, name: item.name }));

		const allWorkedDays = uniques(testedProducts.map((item) => toISODate(item.productTest.testedAt as Date)));

		const testers = allTesters.map((tester) => {
			const testerProducts = testedProducts.filter((product) => product.productTest?.testedById === tester.id);
			const testerWorkedDays = uniques(testerProducts.map((item) => toISODate(item.productTest.testedAt as Date)));

			const testerFixes = testedProductsFixes.filter((product) => product.productTest?.testedById === tester.id);

			const categoryStats = categories.map((category) => {
				const productsInCategory = testerProducts.filter((product) => product.productCategory?.id === category.id);
				const fixesInCategory = testerFixes.filter((product) => product.productCategory?.id === category.id);

				return {
					categoryId: category.id,
					total: productsInCategory.length,
					fixes: fixesInCategory.length,
				};
			});

			return {
				id: tester.id,
				name: tester.name,
				totalTests: testerProducts.length,
				totalFixes: testerFixes.length,
				categoryStats: categoryStats,
				dailyAverage: safeDivide(testerProducts.length, testerWorkedDays.length),
			};
		});

		const data = {
			categories,
			testers,
			totals: {
				byCategory: categories.map((category) => {
					const productsInCategory = testedProducts.filter((product) => product.productCategory?.id === category.id);
					const fixesInCategory = testedProductsFixes.filter((product) => product.productCategory?.id === category.id);

					return {
						categoryId: category.id,
						total: productsInCategory.length,
						fixes: fixesInCategory.length,
					};
				}),
				dailyAverage: safeDivide(testedProducts.length, allWorkedDays.length),
				total: testedProducts.length,
				fixes: testedProductsFixes.length,
			},
		};

		respond<'getTestingAnalytics'>(data);
	});

	router.get('/testing/fixes', scopeMiddleware('productTestLead'), async (req) => {
		const { startDate, endDate, testerId } = req.query;

		const [fixedAttributeValues] = await new ProductAttributeValueController().list(
			listAll({
				filter: {
					isFix: { eq: true },
					createdAt: { gte: startDate, lt: endDate },
					'attributeValue.attribute.name': { ne: ['Baterie - stav', '[Mobil / Tablet] Počet cyklů'] },
					...(testerId ? { 'product.productTest.testedById': { eq: testerId } } : {}),
				},
			}),
		);

		const attributeCountMap = new Map<string, number>();

		for (const item of fixedAttributeValues) {
			const name = item.attributeValue.attribute.name;
			attributeCountMap.set(name, (attributeCountMap.get(name) ?? 0) + 1);
		}

		respond<'getTestingFixesAnalytics'>({
			fixedAttributes: Array.from(attributeCountMap.entries()),
			total: fixedAttributeValues.length,
		});
	});

	router.get('/vendor', scopeMiddleware('admin'), async (req) => {
		const { startDate, endDate } = req.query;

		const [allVendors, testedProducts] = await Promise.all([
			new VendorController()
				.list(
					listAll({
						sort: [['name', 'asc']],
					}),
				)
				.then(([res]) => res),

			new ProductController()
				.listWithVendorAnalytics(
					listAll({
						filter: {
							'productTest.testedById': { ne: null },
							'productTest.testedAt': { gte: startDate, lt: endDate },
						},
					}),
				)
				.then(([res]) => res),
		]);

		const categories = filterUndefined(
			uniquesBy(
				testedProducts.filter((product) => product.productCategory != null).map((product) => product.productCategory),
				(category) => category?.id || '',
			),
		)
			.sort((a, b) => a.name.localeCompare(b.name))
			.map((item) => ({ id: item.id, name: item.name }));

		const productsWithDefects = testedProducts.filter((product) => product.productDefects.length > 0);
		const allDefects = productsWithDefects.flatMap((product) => product.productDefects);
		const defectsToAnalyze = groupBy(allDefects, 'productId').flatMap((group) => sortByCreationDate(group)[0]);
		const allDefectsOrganized = new ProductDefectController().organizeVendorProductsDefects(defectsToAnalyze);

		const {
			straightToStock: allStraightToStock,
			afterTestingWithServiceCase: allWithServiceCase,
			afterTestingWithWarrantyClaim: allWithWarrantyClaim,
		} = allDefectsOrganized;

		const vendors = allVendors.map((vendor) => {
			const vendorProducts = testedProducts.filter((product) => product.batch?.vendor?.id === vendor.id);
			const vendorProductIds = vendorProducts.map((p) => p.id);

			const vendorDefectStats = {
				straightToStock: getDefectsCountByProductIds(allStraightToStock, vendorProductIds),
				afterTestingWithServiceCase: getDefectsCountByProductIds(allWithServiceCase, vendorProductIds),
				afterTestingWithWarrantyClaim: getDefectsCountByProductIds(allWithWarrantyClaim, vendorProductIds),
			};

			const vendorProductsWithDefects = vendorProducts.filter((product) => product.productDefects.length > 0);

			const categoryStats = categories.map((category) => {
				const productsInCategory = vendorProducts.filter((product) => product.productCategory?.id === category.id);
				const productIdsInCategoryForVendor = productsInCategory.map((p) => p.id);

				return {
					categoryId: category.id,
					total: productsInCategory.length,
					defects: productsInCategory.filter((p) => p.productDefects.length > 0).length,
					defectStats: {
						straightToStock: getDefectsCountByProductIds(allStraightToStock, productIdsInCategoryForVendor),
						afterTestingWithServiceCase: getDefectsCountByProductIds(allWithServiceCase, productIdsInCategoryForVendor),
						afterTestingWithWarrantyClaim: getDefectsCountByProductIds(allWithWarrantyClaim, productIdsInCategoryForVendor),
					},
				};
			});

			return {
				id: vendor.id,
				name: vendor.name,
				totalTests: vendorProducts.length,
				totalDefects: vendorProductsWithDefects.length,
				categoryStats: categoryStats,
				defectStats: vendorDefectStats,
				byDefectType: new ProductDefectController().getDefectTypeBreakdown(
					allDefects.filter((def) => vendorProductIds.includes(def.productId)),
				),
			};
		});

		const data = {
			categories,
			vendors,
			totals: {
				byCategory: categories.map((category) => {
					const productsInCategory = testedProducts.filter((product) => product.productCategory?.id === category.id);
					const productIdsInCategory = productsInCategory.map((p) => p.id);

					return {
						categoryId: category.id,
						total: productsInCategory.length,
						defects: productsInCategory.filter((p) => p.productDefects.length > 0).length,
						defectStats: {
							straightToStock: getDefectsCountByProductIds(allStraightToStock, productIdsInCategory),
							afterTestingWithServiceCase: getDefectsCountByProductIds(allWithServiceCase, productIdsInCategory),
							afterTestingWithWarrantyClaim: getDefectsCountByProductIds(allWithWarrantyClaim, productIdsInCategory),
						},
						byDefectType: new ProductDefectController().getDefectTypeBreakdown(
							allDefects.filter((def) => productIdsInCategory.includes(def.productId)),
						),
					};
				}),
				total: testedProducts.length,
				defects: productsWithDefects.length,
				defectStats: {
					straightToStock: allStraightToStock.length,
					afterTestingWithServiceCase: allWithServiceCase.length,
					afterTestingWithWarrantyClaim: allWithWarrantyClaim.length,
				},
				byDefectType: new ProductDefectController().getDefectTypeBreakdown(allDefects),
			},
		};

		respond<'getVendorAnalytics'>(data);
	});

	router.get('/product', scopeMiddleware('admin'), async (req) => {
		const { startDate, endDate } = req.query;

		const [testedProducts] = await new ProductController().listWithProductAnalytics(
			listAll({
				filter: {
					'productTest.testedById': { ne: null },
					'productTest.testedAt': { gte: startDate, lt: endDate },
					'productCategory.name': { eq: ['Notebooky', 'Počítače', 'All-in-One', 'Monitory', 'Tablety', 'Telefony'] },
				},
			}),
		);

		const byCategory = groupByRambda((product) => product.productCategory?.name as string, testedProducts);

		const data = Object.entries(byCategory).map(([category, products]) => {
			const categoryData = new Map<string, { count: number; manufacturer: string; model: string; caseSize: string | undefined }>();
			const isComputer = category === 'Počítače';
			const isTabletOrTelephone = ['Tablety', 'Telefony'].includes(category);

			for (const product of products) {
				const attrMap = new Map(
					product.attributeValues.map((item) => [item.attributeValue.attribute.name, item.attributeValue.value]),
				);
				const manufacturerAttribute = isTabletOrTelephone ? '[Mobil / Tablet] Výrobce' : 'Značka (výrobce)';
				const modelAttribute = isTabletOrTelephone ? '[Mobil / Tablet] Model' : 'Model';
				const manufacturer = attrMap.get(manufacturerAttribute) ?? '';
				const model = attrMap.get(modelAttribute) ?? '';
				const caseSize = isComputer ? attrMap.get('Provedení') ?? '' : '';

				const identifier = filterUndefined([manufacturer, model, caseSize]).join(' ');

				categoryData.set(identifier, {
					count: (categoryData.get(identifier)?.count ?? 0) + 1,
					manufacturer: manufacturer.toString(),
					model: model.toString(),
					caseSize: caseSize?.toString() ?? undefined,
				});
			}

			return {
				category,
				data: Array.from(categoryData.entries())
					.sort(([, a], [, b]) => b.count - a.count)
					.map(([identifier, details]) => ({
						identifier,
						...details,
					})),
			};
		});

		respond<'getProductAnalytics'>(data);
	});

	router.get('/aging-stock', scopeMiddleware('admin'), async () => {
		const [envelopesWithProductsForSale] = await new ProductEnvelopeController().listWithProducts(
			listAll({
				filter: {
					'products.status': { eq: 'FOR_SALE' },
				},
			}),
		);

		const relevantEnvelopes = envelopesWithProductsForSale
			.map((envelope) => {
				const stockAmount = envelope.products.filter((product) => product.status === 'FOR_SALE').length;

				return {
					...envelope,
					stockAmount,
				};
			})
			.filter((envelope) => envelope.stockAmount >= 5);

		const agingStockReport: z.infer<typeof agingStockAnalytics> = [];

		await Promise.all(
			relevantEnvelopes.map(async (envelope) => {
				const daysWithoutMovement = await new ProductEnvelopeController().getDaysWithoutMovement(envelope.id);
				const tempo = await new ProductEnvelopeController().calculateEnvelopeTempo(envelope.id);

				const isNotSelling = daysWithoutMovement != null && daysWithoutMovement >= 60;
				const hasBadTempo = tempo != null && tempo < 1;

				if (isNotSelling || hasBadTempo) {
					const envelopeCode = formatEnvelopeCode(envelope.productCategory.codePrefix)(envelope.code);
					const attributesOverview = await new ProductEnvelopeController().getEnvelopeAttributeValuesOverview(envelope, 'stock');
					const { averageBuyPrice, envelopeSalePrice } = await new ProductEnvelopeController().getPriceInfo(envelope.id);
					const category = envelope.productCategory.name ?? NOT_AVAILABLE;

					const match = agingStockReport.find((item) => item.category === category);
					const envelopeData = {
						envelopeId: envelope.id,
						envelopeCode,
						envelopeName: envelope.name,
						attributesOverview,
						averageBuyPrice: averageBuyPrice.toNumber(),
						envelopeSalePrice: envelopeSalePrice.toNumber(),
						daysWithoutMovement: daysWithoutMovement ?? null,
						tempo,
						stockAmount: envelope.stockAmount,
					};

					if (match) {
						match.data.push(envelopeData);
					} else {
						agingStockReport.push({
							category: category,
							data: [envelopeData],
						});
					}
				}
			}),
		);

		respond<'getAgingStockAnalytics'>(agingStockReport);
	});

	router.get('/batch/:batchId', scopeMiddleware('admin'), async (req) => {
		const { batchId } = req.params;

		const [batchProductDefects, batchProductsCount] = await Promise.all([
			new ProductDefectController().findByBatchForAnalytics(batchId).then((res) => res),
			new ProductController().countProductsInBatch(batchId).then((res) => res),
		]);

		const data = new ProductDefectController().getClosedBatchCounts(batchProductDefects, batchProductsCount.tested);

		respond<'getClosedBatchAnalytics'>({ ...data, testedProducts: batchProductsCount.tested });
	});

	return router;
};

const getDefectsCountByProductIds = (defects: ProductDefect[], productIds: Product['id'][]) => {
	return defects.filter((defect) => productIds.includes(defect.productId)).length;
};
