import { publicApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { publicCustomerClaimRouter } from './public/customerClaim';
import { publicEcommerceOrderRouter } from './public/ecommerceOrder';
import { publicProductRouter } from './public/product';

export const publicRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(publicApi);

	router.use('/order', publicEcommerceOrderRouter(ctx));
	router.use('/product', publicProductRouter(ctx));
	router.use('/customer-claim', publicCustomerClaimRouter(ctx));

	return router;
};
