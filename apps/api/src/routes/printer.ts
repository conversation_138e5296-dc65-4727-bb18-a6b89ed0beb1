import { createARecord, deleteARecord } from '@pocitarna-nx-2023/aws';
import { CA_CRT, DOMAIN_NAME, ONE_SECOND } from '@pocitarna-nx-2023/config';
import {
	Batch<PERSON>ontroller,
	listAll,
	PrinterController,
	ProductCategoryController,
	ProductCodeController,
	ProductController,
	ProductEnvelopeController,
	ServiceCaseController,
	ShipmentController,
	WarehousePositionController,
} from '@pocitarna-nx-2023/database';
import {
	chunk,
	filterUndefined,
	formatBatchCode,
	formatEnvelopeCode,
	formatProductCode,
	formatServiceCaseCode,
	formatShipmentCode,
	formatWarehousePositionCode,
	formatWarehousePositionName,
	getBatchLabels,
	getProductEnvelopeLabels,
	getProductLabels,
	getServiceLabels,
	getShipmentLabels,
	getSnLabels,
	getWarehousePositionLabels,
	sleep,
} from '@pocitarna-nx-2023/utils';
import { printerApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import axios from 'axios';
// eslint-disable-next-line import/default
import PDFMerger from 'pdf-merger-js';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const printerRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(printerApi);

	router.post('/', scopeMiddleware('admin'), async (req) => {
		const printer = await new PrinterController().create(req.body);

		await createARecord(`${printer.id}.printer`, printer.ip);

		respond<'createPrinter'>(printer);
	});

	router.get('/batch-delivery/:batchId', scopeMiddleware('home'), async (req, res) => {
		const batch = await new BatchController().findById(req.params.batchId);
		if (!batch) return res.status(404).json();

		const [codes] = await new ProductCodeController().listByBatch(req.params.batchId, getListProps());
		const batchCode = formatBatchCode(batch.code);

		const productLabels = getProductLabels(codes.map((code) => formatProductCode(code)));

		const pallets = batch.pallets;
		const palletLabels = getBatchLabels(batchCode, pallets);

		respond<'getPrintLabelsBatchDelivery'>([...productLabels, ...palletLabels]);
	});

	router.get('/product-envelope/bulk', scopeMiddleware('home'), async (req) => {
		const { productEnvelopeIds } = req.query;

		const envelopes = await Promise.all(
			productEnvelopeIds.map(async (productEnvelopeId) => {
				const productEnvelope = await new ProductEnvelopeController().resolveRecord(productEnvelopeId);
				if (!productEnvelope) return;

				return {
					code: formatEnvelopeCode(productEnvelope.productCategory.codePrefix)(productEnvelope.code),
					name: productEnvelope.name,
					grade: productEnvelope.grade?.name ?? '',
				};
			}),
		);

		const productEnvelopeLabels = getProductEnvelopeLabels(filterUndefined(envelopes));

		return respond<'getBulkPrintLabelsProductEnvelope'>(productEnvelopeLabels);
	});

	router.get('/product-envelope/:productEnvelopeId', scopeMiddleware('home'), async (req, res) => {
		const productEnvelope = await new ProductEnvelopeController().findById(req.params.productEnvelopeId);
		if (!productEnvelope) return res.status(404).json();

		const productCategory = await new ProductCategoryController().findById(productEnvelope.productCategory.id);
		if (!productCategory) return res.status(404).json();

		const productEnvelopeLabels = getProductEnvelopeLabels([
			{
				code: formatEnvelopeCode(productCategory.codePrefix)(productEnvelope.code),
				name: productEnvelope.name,
				grade: productEnvelope.grade?.name ?? '',
			},
		]);

		respond<'getPrintLabelsProductEnvelope'>(productEnvelopeLabels);
	});

	router.get('/batch/:batchId/small-test', scopeMiddleware('home'), async (req) => {
		const { productEnvelopeIds } = req.query;
		const labels: string[] = [];
		const [productsInEnvelopes] = await new ProductController().list(
			listAll({ filter: { productEnvelopeId: { eq: productEnvelopeIds }, batchId: { eq: req.params.batchId } } }),
		);

		for (const product of productsInEnvelopes) {
			const envelope = product.productEnvelope;
			if (!envelope) continue;

			labels.push(
				...getProductEnvelopeLabels([
					{
						code: formatEnvelopeCode(envelope.productCategory.codePrefix)(envelope.code),
						name: envelope.name,
						grade: product.grade?.name ?? '',
					},
				]),
			);
		}

		return respond<'getBatchSmallTestPrintLabels'>(labels);
	});

	router.get('/shipment/:shipmentId', scopeMiddleware('home'), async (req, res) => {
		const shipment = await new ShipmentController().findById(req.params.shipmentId);
		if (!shipment) return res.status(404).json();

		const shipmentLabels = getShipmentLabels([formatShipmentCode(shipment.code)]);

		respond<'getPrintLabelsShipment'>(shipmentLabels);
	});

	router.get('/service-case/:serviceCaseId/product/:productId', scopeMiddleware('home'), async (req, res) => {
		const serviceCase = await new ServiceCaseController().findById(req.params.serviceCaseId);
		if (!serviceCase) return res.status(404).json();

		const product = await new ProductController().findById(req.params.productId);
		if (!product) return res.status(404).json();

		if (!product.batchId) return res.status(404).json();
		const batch = await new BatchController().findById(product.batchId);
		if (!batch) return res.status(404).json();

		const batchCode = formatBatchCode(batch.code);

		const serviceCaseLabels = getServiceLabels(batchCode, [formatServiceCaseCode(serviceCase.code)]);

		respond<'getPrintLabelsService'>(serviceCaseLabels);
	});

	router.get('/product/:productId', scopeMiddleware('home'), async (req, res) => {
		const product = await new ProductController().findById(req.params.productId);
		if (!product) return res.status(404).json();

		const productLabels = getProductLabels([formatProductCode(product.code)]);

		respond<'getPrintLabelsProduct'>(productLabels);
	});

	router.get('/product/:productId/sn', scopeMiddleware('home'), async (req, res) => {
		const product = await new ProductController().findById(req.params.productId);
		if (!product) return res.status(404).json();

		const snLabels = getSnLabels([product.sn.split('|')]);

		respond<'getPrintLabelsProductSn'>(snLabels);
	});

	router.get('/warehousePosition/bulk', scopeMiddleware('home'), async (req) => {
		const { warehousePositionIds } = req.query;
		const [warehousePositions] = await new WarehousePositionController().list(
			listAll({ filter: { id: { eq: warehousePositionIds } } }),
		);

		const positionLabels = getWarehousePositionLabels(
			warehousePositions.map((warehousePosition) => ({
				code: formatWarehousePositionCode(warehousePosition.code),
				positionName: formatWarehousePositionName(warehousePosition),
			})),
		);

		respond<'getBulkPrintLabelsWarehousePosition'>(positionLabels);
	});

	router.get('/warehousePosition/:warehousePositionId', scopeMiddleware('stock'), async (req, res) => {
		const warehousePosition = await new WarehousePositionController().findById(req.params.warehousePositionId);
		if (!warehousePosition) return res.status(404).json();

		const positionLabels = getWarehousePositionLabels([
			{
				code: formatWarehousePositionCode(warehousePosition.code),
				positionName: formatWarehousePositionName(warehousePosition),
			},
		]);

		respond<'getPrintLabelsShipment'>(positionLabels);
	});

	router.get('/', scopeMiddleware('admin', 'batchDelivery', 'serviceRead', 'serviceWrite', 'testRead', 'productTest'), async () => {
		const [printers, getCount] = await new PrinterController().list(getListProps());
		respondWithPaging<'getAllPrinters'>(printers, await getCount());
	});

	router.get(
		'/:printerId',
		scopeMiddleware('admin', 'batchDelivery', 'serviceRead', 'serviceWrite', 'testRead', 'productTest'),
		async (req, res) => {
			const printer = await new PrinterController().findById(req.params.printerId);
			if (!printer) return res.status(404).json();

			respond<'getPrinter'>(printer);
		},
	);

	router.get('/:printerId/crt', scopeMiddleware('admin'), async (req, res) => {
		const printer = await new PrinterController().findById(req.params.printerId);
		if (!printer || !printer.crt) return res.status(404).json();

		res.setHeader('Content-Type', 'application/x-x509-ca-cert');
		res.setHeader('Content-Disposition', `attachment; filename=HTTPS_CERT.NRD`);
		res.send(`${printer.crt}${CA_CRT}`);
	});

	router.get('/:printerId/key', scopeMiddleware('admin'), async (req, res) => {
		const printer = await new PrinterController().findById(req.params.printerId);
		if (!printer || !printer.key) return res.status(404).json();

		res.setHeader('Content-Type', 'application/x-x509-ca-cert');
		res.setHeader('Content-Disposition', `attachment; filename=HTTPS_KEY.NRD`);
		res.send(printer.key);
	});

	router.get('/:printerId/nginx-conf', scopeMiddleware('admin'), async (req, res) => {
		const printer = await new PrinterController().findById(req.params.printerId);
		if (!printer || !printer.key) return res.status(404).json();
		const data = `
server {
    listen 443 ssl;
    server_name ${printer.id}.printer.(dev|staging|prod).${DOMAIN_NAME};
    ssl_certificate /etc/nginx/localcerts/HTTPS_CERT.NRD;
    ssl_certificate_key /etc/nginx/localcerts/HTTPS_KEY.NRD;

    location / {
        access_log off;
        proxy_pass http://${printer.ip}:8182;
        proxy_http_version 1.1;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}`;

		res.setHeader('Content-Type', 'text/plain');
		res.setHeader('Content-Disposition', `attachment; filename=wss.conf`);
		res.send(data);
	});

	router.patch('/:printerId', scopeMiddleware('admin'), async (req, res) => {
		const printer = await new PrinterController().update(req.params.printerId, req.body);
		if (!printer) return res.status(404).json();

		await createARecord(`${printer.id}.printer`, printer.ip);

		respond<'updatePrinter'>(printer);
	});

	router.delete('/:printerId', scopeMiddleware('admin'), async (req) => {
		const { printerId } = req.params;
		const result = await new PrinterController().delete(printerId);

		await deleteARecord(`${printerId}.printer`);

		respond<'deletePrinter'>(result);
	});

	router.post('/pdf', scopeMiddleware('home'), async (req) => {
		const { zpl } = req.body;
		const individualLabels = zpl
			.replace(/\n/g, '')
			.split('^XZ^XA')
			.map((label) => `${label.startsWith('^XA') ? '' : '^XA'}${label}${label.endsWith('^XZ') ? '' : '^XZ'}`);
		const chunks = chunk(individualLabels, 50);
		const pdfBuffers = [];

		for (const labels of chunks) {
			const response = await axios({
				method: 'post',
				url: 'https://api.labelary.com/v1/printers/8dpmm/labels/2.4409448819x1.1417322835/',
				data: labels.join(''),
				headers: {
					'Content-Type': 'application/x-www-form-urlencoded',
					Accept: 'application/pdf',
				},
				responseType: 'arraybuffer',
			});

			pdfBuffers.push(response.data);
			await sleep(ONE_SECOND);
		}

		// Merge pdfs
		const pdf = new PDFMerger();
		for (const buffer of pdfBuffers) {
			await pdf.add(buffer);
		}
		const result = await pdf.saveAsBuffer();

		respond<'exportLabels'>(result.toString('base64'));
	});

	return router;
};
