import { triggerOrderUpdate, triggerShoptetExport } from '@pocitarna-nx-2023/aws';
import { APP_URL } from '@pocitarna-nx-2023/config';
import {
	AttributeController,
	AttributeValueController,
	EcommerceOrderController,
	EcommerceOrderItemController,
	listAll,
	ProductController,
	ProductEnvelopeCodeController,
	ProductEnvelopeController,
	UserController,
	WarehousePositionController,
} from '@pocitarna-nx-2023/database';
import { formatEnvelopeCode, formatProductCode, formatWarehousePositionName, stripCode, uniques } from '@pocitarna-nx-2023/utils';
import { productEnvelopeApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const productEnvelopeRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(productEnvelopeApi);

	router.post('/', scopeMiddleware('envelopeWrite'), async (req) => {
		const attributeValues = req.body.attributeValues;
		const textValues = attributeValues.filter((item) => !!item.value && !item.attributeValueId);

		await Promise.all(
			textValues.map(async (item) => {
				if (!item.value) return;
				const attributeValue = await new AttributeController().createTextValue(item.attributeId, String(item.value));
				item.attributeValueId = attributeValue.id; // Intentional side effect
			}),
		);

		await new ProductEnvelopeController().create({ ...req.body, attributeValues });
		respond<'createProductEnvelope'>(true);
	});

	router.get('/', scopeMiddleware('batchRead', 'batchWrite', 'productRead', 'productWrite'), async () => {
		const [envelopes, getCount] = await new ProductEnvelopeController().list(getListProps());
		respondWithPaging<'getAllProductEnvelopes'>(envelopes, await getCount());
	});

	router.get('/existing-match', scopeMiddleware('productRead', 'productWrite'), async (req) => {
		const attributeValues = req.query.attributeValues;

		const existingMatch = await new ProductEnvelopeController().findDuplicateEnvelope(attributeValues, getListProps());

		respond<'getProductEnvelopeByAttributeValues'>(existingMatch);
	});

	router.post('/:productEnvelopeId/shoptet', scopeMiddleware('admin'), async (req) => {
		await triggerShoptetExport(req.params.productEnvelopeId);
		respond<'triggerShoptetExport'>(true);
	});

	router.get('/ready-for-stock', scopeMiddleware('productRead', 'productWrite', 'stock'), async () => {
		const envelopes = await new ProductEnvelopeController().findByProductStatus('STOCK', 'stock');
		const envelopeIds = envelopes.map(({ id }) => id);
		const [products] = await new ProductController().list(
			listAll({ filter: { productEnvelopeId: { eq: envelopeIds }, status: { eq: ['STOCK', 'FOR_SALE'] } } }),
		);
		envelopes.forEach((envelope) => {
			envelope.products = products.filter(({ productEnvelopeId }) => productEnvelopeId === envelope.id);
		});

		respond<'getProductEnvelopesReadyForStock'>(envelopes);
	});

	router.get(
		'/:productEnvelopeId',
		scopeMiddleware('productRead', 'productWrite', 'serviceRead', 'serviceWrite', 'stock', 'warrantyClaimRead', 'warrantyClaimWrite'),
		async (req, res) => {
			const productEnvelope = await new ProductEnvelopeController().findById(req.params.productEnvelopeId);
			if (!productEnvelope) return res.status(404).json();
			respond<'getProductEnvelope'>(productEnvelope);
		},
	);

	router.get('/code/:productEnvelopeCode', scopeMiddleware('productRead'), async (req, res) => {
		const order = await new ProductEnvelopeController().findByCode(stripCode(req.params.productEnvelopeCode));
		if (!order) return res.status(404).json();
		respond<'getProductEnvelopeByCode'>(order);
	});

	router.get('/code/:productEnvelopeCodeId', scopeMiddleware('productRead', 'testRead', 'productTest', 'batchRead'), async (req, res) => {
		const productEnvelopeCode = await new ProductEnvelopeCodeController().findById(req.params.productEnvelopeCodeId);
		if (!productEnvelopeCode) return res.status(404).json();
		respond<'getProductEnvelopeCode'>(productEnvelopeCode);
	});

	router.get('/:productEnvelopeId/products', scopeMiddleware('productRead', 'productWrite'), async (req) => {
		const [products, getCount] = await new ProductController().findByEnvelope(req.params.productEnvelopeId, getListProps());
		respondWithPaging<'getProductEnvelopeProducts'>(products, await getCount());
	});

	router.get('/:productEnvelopeId/products/count', scopeMiddleware('productRead', 'productWrite'), async (req) => {
		const count = await new ProductController().count({
			filter: { productEnvelopeId: { eq: req.params.productEnvelopeId }, ...getListProps().filter },
		});

		respond<'getProductEnvelopeProductsCount'>(count);
	});

	router.get('/:productEnvelopeId/prices', scopeMiddleware('productRead', 'productWrite', 'stock'), async (req) => {
		const products = await new ProductEnvelopeController().getProducts(req.params.productEnvelopeId);
		const productPrices = products.map(({ productPrice }) => productPrice).flat();

		respond<'getProductEnvelopePrices'>(productPrices);
	});

	router.patch('/bulk/sale-price-by-margin', scopeMiddleware('productWrite', 'envelopeWrite'), async (req) => {
		const { margin } = req.body.data;
		await Promise.all(
			req.body.ids.map(async (id) => {
				return await new ProductEnvelopeController().updatePriceByMargin(id, margin);
			}),
		);

		respond<'bulkUpdateProductEnvelopeSalePriceByMargin'>(true);
	});

	router.patch('/:productEnvelopeId', scopeMiddleware('productWrite', 'envelopeWrite'), async (req, res) => {
		const attributeValues = req.body.attributeValues ?? [];
		const textValues = attributeValues.filter((item) => !!item.value && !item.attributeValueId);

		await Promise.all(
			textValues.map(async (item) => {
				if (!item.value) return;
				const attributeValue = await new AttributeController().createTextValue(item.attributeId, String(item.value));
				item.attributeValueId = attributeValue.id; // Intentional side effect
			}),
		);

		await new ProductEnvelopeController().update(req.params.productEnvelopeId, { ...req.body, attributeValues });

		const productEnvelope = await new ProductEnvelopeController().findById(req.params.productEnvelopeId);

		if (!productEnvelope) return res.status(404).json();

		respond<'updateProductEnvelope'>(productEnvelope);
	});

	router.get('/:productEnvelopeId/attribute-value', scopeMiddleware('productRead', 'productWrite'), async (req) => {
		const [data, getCount] = await new AttributeValueController().listByProductEnvelope(req.params.productEnvelopeId, getListProps());

		respondWithPaging<'getProductEnvelopeAttributeValues'>(data, await getCount());
	});

	router.put('/:productEnvelopeId/attribute-value', scopeMiddleware('productWrite', 'envelopeWrite'), async (req, res) => {
		const productEnvelope = await new ProductEnvelopeController().findById(req.params.productEnvelopeId);
		if (!productEnvelope) return res.status(404).json();

		const products = await new ProductEnvelopeController().getProducts(productEnvelope.id);
		if (products.length === 0) return res.status(404).json();

		const { attributeValues } = req.body;
		const existingMatch = await new ProductEnvelopeController().findDuplicateEnvelope(
			attributeValues,
			listAll({
				filter: {
					type: { eq: productEnvelope.type },
					gradeId: { eq: productEnvelope.gradeId },
					productType: { eq: productEnvelope.productType },
				},
			}),
		);

		if (existingMatch) {
			return res.status(409).json({
				_error: {
					message: `Duplicate product envelope ${formatEnvelopeCode(existingMatch.productCategory.codePrefix)(existingMatch.code)}`,
				},
			});
		}

		await new ProductEnvelopeController().updateAttributeValues(productEnvelope.id, attributeValues);

		respond<'updateProductEnvelopeAttributeValues'>(productEnvelope);
	});

	router.post('/stock', scopeMiddleware('stock', 'envelopeWrite'), async (req) => {
		const productIds: string[] = [];
		const orderIds: string[] = [];
		for (const { envelopeId, salePrice, standardPrice } of req.body) {
			const data = { salePrice, ...(standardPrice != null && standardPrice > 0 ? { standardPrice } : {}) };

			await new ProductEnvelopeController().update(envelopeId, data);

			const [products] = await new ProductController().list(
				listAll({ filter: { productEnvelopeId: { eq: envelopeId }, status: { eq: 'STOCK' } } }),
			);
			productIds.push(...products.map((product) => product.id));
			await Promise.all(products.map((product) => new ProductController().update(product, { status: 'FOR_SALE' })));

			const [emptyEcommerceOrderItems] = await new EcommerceOrderItemController().findUnassignedByEnvelope(envelopeId);
			await Promise.all(
				emptyEcommerceOrderItems.map(async (item, index) => {
					const product = products.at(index);
					if (!product) return;

					const order = await new EcommerceOrderController().findById(item.ecommerceOrderId);
					if (!order) return;

					await new EcommerceOrderItemController().update(item, { productId: product.id });

					await new EcommerceOrderController().handleProductInOrder({
						order,
						productId: product.id,
					});
					products.splice(index, 1);
				}),
			);

			await triggerShoptetExport(envelopeId);
		}

		await Promise.all(
			uniques(orderIds).map(async (orderId) => {
				await triggerOrderUpdate(orderId);
			}),
		);

		await new ProductController().prepareStockReport(uniques(productIds));

		respond<'stockProductEnvelopes'>({ products: productIds, productEnvelopes: req.body.map(({ envelopeId }) => envelopeId) });
	});

	router.get('/code/:productEnvelopeCode/products', async (req, res) => {
		const productEnvelope = await new ProductEnvelopeController().findByCode(stripCode(req.params.productEnvelopeCode));
		if (!productEnvelope) return res.status(404).json();

		const products = await new ProductEnvelopeController().getProducts(
			productEnvelope.id,
			getListProps({ status: { eq: ['FOR_SALE', 'RESERVED'] } }),
		);

		const data = await Promise.all(
			products.map(async (product) => {
				let warehousePosition = null;
				let ecommerceOrder = null;
				let pickedBy = null;
				if (['RESERVED', 'SOLD'].includes(product.status)) {
					ecommerceOrder = await new EcommerceOrderController().findByProduct(product);
				}
				if (product.warehousePositionId) {
					warehousePosition = await new WarehousePositionController().findById(product.warehousePositionId);
				}
				if (product.pickedById) {
					pickedBy = await new UserController().findById(product.pickedById);
				}

				return {
					code: req.params.productEnvelopeCode,
					pcn: formatProductCode(product.code),
					sn: product.sn
						.split('|')
						.map((sn) => sn.trim())
						.filter((sn) => sn !== '')
						.join(', '),
					warehousePosition: warehousePosition
						? `${warehousePosition.warehouse.name} - ${formatWarehousePositionName(warehousePosition)}`
						: null,
					pickedAt: product.pickedAt,
					pickedBy: pickedBy?.name ?? null,
					ecommerceOrder: ecommerceOrder?.code ?? null,
					url: `${APP_URL}/product/envelope/${productEnvelope.id}`,
				};
			}),
		);
		return respond<'getProductEnvelopeCodeProducts'>(data);
	});

	return router;
};
