import { triggerStockDrain } from '@pocitarna-nx-2023/aws';
import { STAGE } from '@pocitarna-nx-2023/config';
import { webhookApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import crypto from 'crypto';
import { respond } from '../utils/respond';

const signatureKey = STAGE === 'prod' ? '17690b2d0a597f1dc716b7eb0d4d410d' : 'e46e3c0758f6abad1a8d5162291ca270';
const calculateHash = (body: Record<string, unknown>) => crypto.createHmac('sha1', signatureKey).update(JSON.stringify(body)).digest('hex');

export const webhookRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(webhookApi);

	router.post('/shoptet-job-finished', async (req, res) => {
		const shoptetHash = req.headers['shoptet-webhook-signature'];
		const ourHash = calculateHash(req.body);
		if (!shoptetHash || !ourHash || shoptetHash !== ourHash) return res.status(401).json();

		const jobId = req.body.eventInstance;
		if (jobId) {
			await triggerStockDrain(jobId);
		}

		respond<'shoptetJobFinished'>(true);
	});

	return router;
};
