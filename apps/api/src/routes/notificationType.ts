import { NotificationTypeController } from '@pocitarna-nx-2023/database';
import { notificationTypeApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respondWithPaging } from '../utils/respond';

export const notificationTypeRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(notificationTypeApi);

	router.get('/', scopeMiddleware('admin'), async () => {
		const [notificationTypes, getCount] = await new NotificationTypeController().list(getListProps());
		respondWithPaging<'getNotificationTypes'>(notificationTypes, await getCount());
	});

	return router;
};
