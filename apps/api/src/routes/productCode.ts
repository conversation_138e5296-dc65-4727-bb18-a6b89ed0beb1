import { NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import {
	AttributeController,
	AttributeValueController,
	listAll,
	listOne,
	type Product,
	ProductCodeController,
	ProductController,
	ProductEnvelopeController,
} from '@pocitarna-nx-2023/database';
import { reader } from '@pocitarna-nx-2023/reader';
import { formatEnvelopeCode, formatProductCode, stripCode, uniquesBy } from '@pocitarna-nx-2023/utils';
import { productCodeApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { respond } from '../utils/respond';

export const productCodeRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(productCodeApi);

	router.get(
		'/:productCodeId',
		scopeMiddleware('productRead', 'productWrite', 'testRead', 'productTest', 'batchCheck', 'batchDelivery', 'batchRead', 'batchWrite'),
		async (req, res) => {
			const productCode = await new ProductCodeController().findById(req.params.productCodeId);
			if (!productCode) return res.status(404).json();
			respond<'getProductCode'>(productCode);
		},
	);

	router.get('/:productCodeCode/expedition', async (req, res) => {
		const product = await new ProductController().findByCode(stripCode(req.params.productCodeCode));
		if (!product || !product.productEnvelopeId) return res.status(404).json();

		const envelope = await new ProductEnvelopeController().findById(product.productEnvelopeId);
		if (!envelope) return res.status(404).json();

		const [[pnAttributeValue]] = await new ProductController().getAttributeValues(
			product.id,
			listOne({
				filter: {
					'productAttributeValue.type': { eq: 'resolved' },
					'attribute.displayName': { eq: ['PN'] },
					temporary: { eq: false },
				},
			}),
		);

		const payload = {
			pcn: formatProductCode(product?.code),
			code: formatEnvelopeCode(envelope.productCategory.codePrefix)(envelope.code),
			sn: product.sn
				.split('|')
				.map((sn) => sn.trim())
				.filter((sn) => sn !== '')
				.join(', '),
			pn: pnAttributeValue?.value.toString() ?? '',
			grade: `Kategorie ${product.grade?.name ?? NOT_AVAILABLE}`,
		};
		respond<'getProductCodeExpedition'>(payload);
	});

	router.get(
		'/code/:productCodeCode',
		scopeMiddleware('productRead', 'productWrite', 'testRead', 'productTest', 'batchCheck', 'batchDelivery', 'batchRead', 'batchWrite'),
		async (req, res) => {
			const productCode = await new ProductCodeController().findByCode(req.params.productCodeCode);
			if (!productCode) return res.status(404).json();
			respond<'getProductCodeByCode'>(productCode);
		},
	);

	router.get('/:productCodeId/product', scopeMiddleware('testRead', 'productTest'), async (req, res) => {
		const product = await new ProductCodeController().findProduct(req.params.productCodeId);
		if (!product) return res.status(404).json();
		respond<'getProductCodeProduct'>(product);
	});

	router.post('/:productCodeId/attribute-value-loader', scopeMiddleware('productTest'), async (req, res) => {
		const { productCodeId } = req.params;
		const productCode = await new ProductCodeController().findById(productCodeId);
		if (!productCode || !productCode.batch) return res.status(400).json();

		const { fileIds, forceModelOverride } = req.body;
		const data = (await Promise.all(fileIds.map((fileId) => reader(fileId)))).filter((data) => data !== false);
		if (data.length === 0) return res.status(500).json();

		const attributeValues = uniquesBy(
			data.flatMap((data) => data?.attributeValues),
			'attributeId',
		);
		const productProps = data
			.flatMap((data) => data?.props)
			.reduce<Partial<Product>>((acc, props) => {
				(Object.keys(props) as unknown as (keyof Product)[]).forEach((key) => {
					if (props[key]) {
						// @ts-expect-error - 🤷‍♂️
						acc[key] = props[key];
					}
				});
				return acc;
			}, {});

		// If product has been already found by previous reader, we can just reuse it
		const assignedProduct = await new ProductCodeController().findProduct(req.params.productCodeId);
		if (assignedProduct) {
			const newProduct = await new ProductController().update(assignedProduct, { ...productProps, code: { id: productCodeId } });
			await new ProductController().addFiles(newProduct.id, fileIds, 'reader');
			await new ProductController().setAttributeValue(newProduct.id, attributeValues, ['reader', 'resolved']);
			return respond<'loadAttributeValuesForProductCodeFromReader'>(true);
		}

		if (!productProps.sn) return res.status(404).json();

		const matchResult = await new ProductController().findBySnInBatchWithMatchInfo(
			productCode.batch.id,
			productProps.sn.toUpperCase(),
			{
				filter: { status: { eq: 'TO_TEST' } },
			},
		);

		if (!matchResult) return res.status(404).json({ sn: productProps.sn.toUpperCase() });

		const { product: foundProduct, matchType } = matchResult;
		const product = await new ProductController().findWithAttributeValues(foundProduct.id);
		if (!product) return res.status(404).json({ sn: productProps.sn.toUpperCase() });

		if (matchType === 'exact') {
			const newProduct = await new ProductController().update(product, { ...productProps, code: { id: productCodeId } });
			await new ProductController().addFiles(newProduct.id, fileIds, 'reader');
			await new ProductController().setAttributeValue(newProduct.id, attributeValues, ['reader', 'resolved']);
			return respond<'loadAttributeValuesForProductCodeFromReader'>(true);
		}

		if (forceModelOverride) {
			const newProduct = await new ProductController().update(product, { ...productProps, code: { id: productCodeId } });
			await new ProductController().addFiles(newProduct.id, fileIds, 'reader');
			await new ProductController().setAttributeValue(newProduct.id, attributeValues, ['reader', 'resolved']);
			return respond<'loadAttributeValuesForProductCodeFromReader'>(true);
		}

		if (!product.productCategoryId) return res.status(404).json({ sn: productProps.sn.toUpperCase() });

		const [categoryAttributes] = await new AttributeController().listByCategory(product.productCategoryId, listAll());

		const modelAttribute = categoryAttributes.find((item) => item.displayName === 'Model');
		const modelAttributeValueFromReader = attributeValues.find((attributeValue) => attributeValue.attributeId === modelAttribute?.id);
		const modelAttributeValueFromProduct = product.attributeValues.find(
			(item) => item.type === 'resolved' && item.attributeValue.attribute.id === modelAttribute?.id,
		);

		if (modelAttributeValueFromReader && modelAttributeValueFromProduct) {
			const modelsMatch = modelAttributeValueFromReader.attributeValueId === modelAttributeValueFromProduct?.attributeValueId;

			if (!modelsMatch) {
				const [batchProductsWithReaderModel] = await new ProductController().findByAttributeValues(
					[modelAttributeValueFromReader.attributeValueId],
					listAll({ filter: { id: { ne: product.id }, batchId: { eq: product.batchId }, status: { eq: 'TO_TEST' } } }),
				);

				const readerModelValue = await new AttributeValueController().findById(modelAttributeValueFromReader.attributeValueId);
				const productModelValue = await new AttributeValueController().findById(modelAttributeValueFromProduct.attributeValueId);

				return res.status(300).json({
					readerModel: readerModelValue?.value.toString() ?? '',
					productModel: productModelValue?.value.toString() ?? '',
					matchingProducts: batchProductsWithReaderModel.map((product) => ({
						id: product.id,
						code: product.code?.code ?? null,
					})),
				});
			}
		}

		const newProduct = await new ProductController().update(product, { ...productProps, code: { id: productCodeId } });
		await new ProductController().addFiles(newProduct.id, fileIds, 'reader');
		await new ProductController().setAttributeValue(newProduct.id, attributeValues, ['reader', 'resolved']);
		respond<'loadAttributeValuesForProductCodeFromReader'>(true);
	});

	router.patch('/:productCodeId/files', scopeMiddleware('productTest', 'productWrite'), async (req) => {
		const fileIds = req.body;
		const productId = req.params.productCodeId;
		await new ProductController().addFiles(productId, fileIds);
		respond<'addFilesToProduct'>(true);
	});

	return router;
};
