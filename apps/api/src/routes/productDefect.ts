import { DefectTypeController, ProductDefectController, ServiceCaseController } from '@pocitarna-nx-2023/database';
import { productDefectApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const productDefectRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(productDefectApi);

	router.get('/', scopeMiddleware('serviceRead', 'serviceWrite', 'warrantyClaimRead', 'warrantyClaimWrite'), async () => {
		const [defects, getCount] = await new ProductDefectController().list(getListProps());

		return respondWithPaging<'getAllProductDefects'>(defects, await getCount());
	});

	router.get(
		'/:productId',
		scopeMiddleware('productRead', 'productWrite', 'testRead', 'productTest', 'batchCheck', 'batchDelivery', 'batchRead', 'batchWrite'),
		async (req) => {
			const [defects, getCount] = await new ProductDefectController().findByProduct(req.params.productId, getListProps());

			return respondWithPaging<'getProductDefects'>(defects, await getCount());
		},
	);

	router.post('/:productId', scopeMiddleware('batchCheck', 'productTest'), async (req, res) => {
		const { productId } = req.params;
		const { files = [], note, serviceCase, warrantyClaim, customerClaim } = req.body;

		const filesToSave = files.filter((file): file is { id: string } => Boolean(file && file.id)) ?? [];

		let serviceCaseToSave = undefined;

		if (serviceCase && 'id' in serviceCase) {
			serviceCaseToSave = serviceCase;
		}

		if (serviceCase && !('id' in serviceCase) && 'note' in serviceCase && 'type' in serviceCase) {
			const newServiceCase = await new ServiceCaseController().create({ productId, note: serviceCase.note, type: serviceCase.type });
			serviceCaseToSave = newServiceCase;
		}

		const createdDefect = await new ProductDefectController().create({
			note,
			files: filesToSave,
			product: { id: productId },
			defectType: req.body.defectType,
			...(serviceCaseToSave && { serviceCase: serviceCaseToSave }),
			...(warrantyClaim && { warrantyClaim }),
			...(customerClaim && { customerClaim }),
		});

		if (files.length > 0) {
			await new ProductDefectController().addFiles(createdDefect.id, files);
		}

		const defect = await new ProductDefectController().findById(createdDefect.id);
		if (!defect) return res.status(500).json();

		return respond<'createProductDefect'>(defect);
	});

	router.get(
		'/:defectId/defect-type',
		scopeMiddleware('serviceRead', 'serviceWrite', 'warrantyClaimRead', 'warrantyClaimWrite'),
		async (req, res) => {
			const defect = await new ProductDefectController().findById(req.params.defectId);
			if (!defect) return res.status(404).json();

			const defectType = await new DefectTypeController().findById(defect.defectType.id);
			if (!defectType) return res.status(404).json();

			respond<'getProductDefectType'>(defectType);
		},
	);

	router.patch('/:defectId', scopeMiddleware('batchCheck', 'productTest'), async (req) => {
		const { defectId } = req.params;
		const { files, ...props } = req.body;

		const defect = await new ProductDefectController().update(defectId, props);
		await new ProductDefectController().addFiles(defectId, files);

		return respond<'updateProductDefect'>(defect);
	});

	router.delete('/:productId/:defectId', scopeMiddleware('batchCheck', 'productTest'), async (req, res) => {
		const deleted = await new ProductDefectController().delete(req.params.defectId);

		if (!deleted) return res.status(500).send();

		return respond<'deleteProductDefect'>(undefined);
	});

	router.delete('/:productId/:defectId/files/:fileId', scopeMiddleware('batchCheck', 'productTest'), async (req, res) => {
		const entity = await new ProductDefectController().deleteFile(req.params.defectId, req.params.fileId);
		if (!entity) return res.status(500).send();

		return respond<'deleteProductDefectFile'>(true);
	});

	return router;
};
