import { ServiceTaskTypeController } from '@pocitarna-nx-2023/database';
import { serviceTaskTypeApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const serviceTaskTypeRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(serviceTaskTypeApi);

	router.post('/', scopeMiddleware('admin'), async (req, res) => {
		const serviceTaskType = await new ServiceTaskTypeController().create(req.body);

		if (!serviceTaskType) return res.status(500).json({ _error: { message: 'Could not create service task type' } });

		respond<'createServiceTaskType'>(serviceTaskType);
	});

	router.get(
		'/',
		scopeMiddleware(
			'admin',
			'serviceRead',
			'serviceWrite',
			'batchRead',
			'batchWrite',
			'testRead',
			'productTest',
			'warrantyClaimRead',
			'warrantyClaimWrite',
		),
		async () => {
			const [data, getCount] = await new ServiceTaskTypeController().list(getListProps());

			return respondWithPaging<'getServiceTaskTypes'>(data, await getCount());
		},
	);

	router.get(
		'/:serviceTaskTypeId',
		scopeMiddleware(
			'admin',
			'serviceRead',
			'serviceWrite',
			'batchRead',
			'batchWrite',
			'testRead',
			'productTest',
			'warrantyClaimRead',
			'warrantyClaimWrite',
		),
		async (req, res) => {
			const serviceTaskType = await new ServiceTaskTypeController().findById(req.params.serviceTaskTypeId);

			if (!serviceTaskType) {
				return res.status(404).json({ _error: { message: 'Service task type not found' } });
			}

			return respond<'getServiceTaskType'>(serviceTaskType);
		},
	);

	router.patch('/:serviceTaskTypeId', scopeMiddleware('admin'), async (req, res) => {
		const serviceTaskType = await new ServiceTaskTypeController().update(req.params.serviceTaskTypeId, req.body);
		if (!serviceTaskType) return res.status(404).json();

		respond<'updateServiceTaskType'>(serviceTaskType);
	});

	router.delete('/:serviceTaskTypeId', scopeMiddleware('admin'), async (req, res) => {
		const serviceTaskType = await new ServiceTaskTypeController().findById(req.params.serviceTaskTypeId);
		if (!serviceTaskType) return res.status(404).send();

		const result = await new ServiceTaskTypeController().delete(serviceTaskType.id);

		respond<'deleteServiceTaskType'>(result);
	});

	return router;
};
