import {
	Batch<PERSON>ontroller,
	BatchDefectController,
	CurrencyRateController,
	listAll,
	listOne,
	type Product,
	ProductCodeController,
	ProductController,
	ProductPriceController,
	ProductTestController,
	ServiceCaseController,
	useAuthentication,
	WarrantyClaimController,
} from '@pocitarna-nx-2023/database';
import { importClassificator, importer } from '@pocitarna-nx-2023/importer';
import { isCommonStatus, safeDivide, stripCode } from '@pocitarna-nx-2023/utils';
import { batchApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const batchRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(batchApi);

	router.post('/', scopeMiddleware('batchWrite'), async (req, res) => {
		const currencyRate = await new CurrencyRateController().getEURCurrencyRate();
		const batch = await new BatchController().create({ ...req.body, currencyRate });
		if (!batch) return res.status(500).json({ _error: { message: 'Could not create batch' } });

		respond<'createBatch'>(batch);
	});

	router.get(
		'/',
		scopeMiddleware(
			'batchRead',
			'batchWrite',
			'batchDelivery',
			'testRead',
			'productTest',
			'batchCheck',
			'warrantyClaimRead',
			'warrantyClaimWrite',
			'serviceRead',
			'serviceWrite',
			'stock',
			'productRead',
			'productWrite',
		),
		async () => {
			const [data, getCount] = await new BatchController().list(getListProps());

			return respondWithPaging<'getBatches'>(data, await getCount());
		},
	);

	router.post('/import/product', scopeMiddleware('batchWrite'), async (req, res) => {
		const { files, batchId } = req.body;
		const result = await importer(
			files.map((file) => file.id),
			batchId,
		);

		if (!result || result.length === 0) {
			return res.status(400).json({
				_error: {
					message: 'Importní soubor je ve špatném formátu. Nepodařilo se jej spárovat s žádným vzorovým importním souborem.',
				},
			});
		}

		const batch = await new BatchController().findById(batchId);
		if (!batch) return res.status(404).json();

		await new BatchController().update(batch, {
			status: batch.status === 'IMPORTING' ? 'AT_SUPPLIER' : batch.status,
		});
		await new BatchController().addFiles(
			batch.id,
			req.body.files.map((file) => file.id),
			'import',
		);

		respond<'importBatchProducts'>([batchId]);
	});

	router.post('/import', scopeMiddleware('batchWrite'), async (req, res) => {
		const variantManager = await importClassificator(req.body.files.map((file) => file.id));
		const currency = variantManager?.currency;
		const batchType = variantManager?.batchType;

		if (!currency)
			return res.status(400).json({
				_error: {
					message: 'Importní soubor je ve špatném formátu. Nepodařilo se jej spárovat s žádným vzorovým importním souborem.',
				},
			});

		const currencyRate = await new CurrencyRateController().getPriceCurrency({ currency });

		const batch = await new BatchController().create({
			status: 'IMPORTING',
			files: req.body.files,
			...(currencyRate && { currencyRate: { id: currencyRate.id } }),
			type: batchType ?? 'USED',
		});
		await new BatchController().addFiles(
			batch.id,
			req.body.files.map((file) => file.id),
			'import',
		);

		if (!batch) return res.status(500).json({ _error: { message: 'Could not create batch' } });

		respond<'importBatch'>(batch.id);
	});

	router.get(
		'/:batchId',
		scopeMiddleware(
			'batchRead',
			'batchWrite',
			'batchDelivery',
			'testRead',
			'productTest',
			'batchCheck',
			'warrantyClaimRead',
			'warrantyClaimWrite',
			'serviceRead',
			'serviceWrite',
			'stock',
			'productRead',
			'productWrite',
		),
		async (req, res) => {
			const batch = await new BatchController().findById(req.params.batchId);

			if (!batch) {
				return res.status(404).json({ _error: { message: 'Batch not found' } });
			}

			return respond<'getBatch'>(batch);
		},
	);

	router.get('/code/:batchCode', scopeMiddleware('batchRead'), async (req, res) => {
		const order = await new BatchController().findByCode(stripCode(req.params.batchCode));
		if (!order) return res.status(404).json();
		respond<'getBatchByCode'>(order);
	});

	router.get('/:batchId/defect', scopeMiddleware('batchCheck', 'batchRead', 'batchWrite', 'batchDelivery'), async (req) => {
		const [batchDefects, getCount] = await new BatchDefectController().findByBatch(req.params.batchId);
		return respondWithPaging<'getBatchDefects'>(batchDefects, await getCount());
	});

	router.patch('/bulk', scopeMiddleware('batchWrite', 'batchDelivery', 'productTest', 'batchCheck'), async (req) => {
		await Promise.all(
			req.body.ids.map(async (id) => {
				return await new BatchController().update(id, req.body.data);
			}),
		);

		return respond<'bulkUpdateBatch'>(true);
	});

	router.patch('/:batchId', scopeMiddleware('batchWrite', 'batchDelivery', 'productTest', 'batchCheck'), async (req, res) => {
		const batch = await new BatchController().findById(req.params.batchId);
		if (!batch) return res.status(404).json();

		const batchStatus = req.body.status;
		const vendorId = req.body.vendor?.id;
		const isNewStatus = batchStatus != null && batchStatus !== batch.status;
		const isNewVendor = vendorId != null && batch.vendor?.id !== vendorId;

		// Transition products
		if (isNewStatus && isCommonStatus(batchStatus)) {
			await new BatchController().updateProductsToNextStatus(req.params.batchId, batchStatus);
		}

		await new ProductPriceController().handleBatchChange(batch, req.body.deliveryPrice, req.body.currencyRate?.id);

		const newEta = vendorId != null && isNewVendor ? await new BatchController().getEtaOnBatchUpdate(batch, vendorId) : null;

		await new BatchController().update(batch, {
			...req.body,
			...(newEta && { eta: newEta }),
		});

		respond<'updateBatch'>(true);
	});

	router.patch('/:batchId/files', scopeMiddleware('productTest'), async (req) => {
		const fileIds = req.body;
		const batchId = req.params.batchId;
		await new BatchController().addFiles(batchId, fileIds);
		respond<'addFilesToBatch'>(true);
	});

	router.post('/:batchId/small-test', scopeMiddleware('productWrite', 'envelopeWrite'), async (req) => {
		await new BatchController().finishBatchSmallTest({ batchId: req.params.batchId, amountEnvelopesData: req.body });

		respond<'finishSmallTest'>(true);
	});

	router.patch('/:batchId/small-test', scopeMiddleware('productWrite', 'envelopeWrite'), async (req, res) => {
		const { envelopeId, productId, productCode, sn } = req.body;

		let productToProcess: Product | null = null;

		if (productId) {
			productToProcess = await new ProductController().resolveRecord(productId);
		} else {
			const [[productWithoutPcn]] = await new ProductController().findByBatch(
				req.params.batchId,
				listOne({
					filter: {
						productEnvelopeId: { eq: envelopeId },
						'code.code': { eq: null },
					},
				}),
			);
			productToProcess = productWithoutPcn;
		}

		if (!productToProcess) return res.status(404).json({ _error: { message: 'Product not found' } });

		const args =
			productCode != null
				? { batchId: req.params.batchId, product: productToProcess, productCode: stripCode(productCode), sn: null }
				: { batchId: req.params.batchId, product: productToProcess, sn: sn as string, productCode: null };

		try {
			const updatedProduct = await new BatchController().pairSmallTestProduct(args);

			if (!updatedProduct) return res.status(400).json({ _error: { message: 'Could not update product' } });

			respond<'pairSmallTestProduct'>(updatedProduct.id);
		} catch (error) {
			return res.status(400).json({ _error: { message: error instanceof Error ? error.message : 'Could not update product' } });
		}
	});

	router.post('/:batchId/products', scopeMiddleware('batchWrite'), async (req) => {
		const { totalPrice, productsCount, productCategoryId } = req.body;
		const perProductPrice = safeDivide(totalPrice, productsCount);

		for (const _product of Array.from({ length: productsCount })) {
			await new ProductCodeController().create({ batch: { id: req.params.batchId } });

			const productEntity = await new ProductController().create({
				status: 'AT_SUPPLIER',
				productCategory: productCategoryId ? { id: productCategoryId } : undefined,
				batch: { id: req.params.batchId },
			});

			await new ProductController().addBuyPrice(productEntity.id, perProductPrice ?? 0);
		}

		return respond<'createBatchProducts'>(true);
	});

	router.get(
		'/:batchId/products',
		scopeMiddleware('batchRead', 'batchWrite', 'batchCheck', 'batchDelivery', 'productRead', 'productWrite', 'testRead', 'productTest'),
		async (req) => {
			const [data, getCount] = await new ProductController().findByBatch(req.params.batchId, getListProps());
			return respondWithPaging<'getBatchProducts'>(data, await getCount());
		},
	);

	router.get('/:batchId/sn-duplicates', scopeMiddleware('batchRead', 'batchWrite', 'productRead', 'productWrite'), async (req) => {
		const duplicates = await new ProductController().getSNDuplicatesByBatch(req.params.batchId);

		return respond<'getBatchSnDuplicates'>(duplicates);
	});

	router.get(
		'/:batchId/product-tests',
		scopeMiddleware('batchRead', 'batchWrite', 'productRead', 'productWrite', 'testRead', 'productTest'),
		async (req) => {
			const productTests = await new ProductTestController().findByBatch(req.params.batchId, getListProps());
			return respond<'getBatchProductTests'>(productTests);
		},
	);

	router.get(
		'/:batchId/products/count',
		scopeMiddleware('batchRead', 'batchWrite', 'batchCheck', 'batchDelivery', 'testRead', 'productTest'),
		async (req) => {
			const count = await new ProductController().countProductsInBatch(req.params.batchId);

			return respond<'getBatchProductsCount'>(count);
		},
	);

	router.get('/:batchId/price', scopeMiddleware('batchRead', 'batchWrite', 'batchCheck', 'batchDelivery'), async (req) => {
		const batchPrice = await new BatchController().getPrice(req.params.batchId);
		return respond<'getBatchPrice'>(batchPrice);
	});

	router.delete('/:batchId/files/:fileId', scopeMiddleware('batchWrite', 'batchDelivery', 'batchCheck'), async (req, res) => {
		const entity = await new BatchController().deleteFile(req.params.batchId, req.params.fileId);
		if (!entity) return res.status(500).send();

		return respond<'deleteBatchFile'>(true);
	});

	router.get('/:batchId/products/code', scopeMiddleware('batchRead', 'batchWrite', 'testRead', 'productTest'), async (req) => {
		if (req.query.unassigned) {
			const [codes, getCount] = await new ProductCodeController().listUnassignedByBatch(req.params.batchId, getListProps());
			return respondWithPaging<'getBatchProductCodes'>(codes, await getCount());
		}

		const [codes, getCount] = await new ProductCodeController().listByBatch(req.params.batchId, getListProps());
		return respondWithPaging<'getBatchProductCodes'>(codes, await getCount());
	});

	router.patch('/bulk/product-test', scopeMiddleware('productTest'), async (req, res) => {
		const [products] = await new ProductController().findByBatch(req.body.ids, getListProps());
		if (products.length === 0) return res.status(304).json({ _error: { message: 'There are no products to update' } });

		await Promise.all(
			products.map(async ({ id }) => {
				const productTest = await new ProductTestController().findByProduct(id);
				if (!productTest) return;
				await new ProductTestController().update(productTest, req.body.data);
			}),
		);

		return respond<'bulkUpdateBatchProductTests'>(true);
	});

	router.post('/:batchId/products/code', scopeMiddleware('batchCheck'), async (req) => {
		const [defects] = await new BatchDefectController().findByBatch(req.params.batchId);
		const missingProducts = defects.reduce((acc, defect) => acc + defect.missing, 0);

		await new BatchController().handleMissingProductsWithCodeAssignment({
			batchId: req.params.batchId,
			expectedCount: missingProducts,
		});

		respond<'assignProductCodes'>(true);
	});

	// Timestamps and batch-status transitions
	router.post('/:batchId/delivery', scopeMiddleware('batchDelivery'), async (req) => {
		await new BatchController().handleStatusTransition({
			batchOrId: req.params.batchId,
			batchStatusTransitionTimestamp: 'deliveredAt',
			requestBody: req.body,
			newProductsStatus: req.body.status === 'TO_TEST' ? 'TO_TEST' : 'TO_CHECK',
		});

		return respond<'setBatchDeliveryDate'>(true);
	});

	router.post('/:batchId/check', scopeMiddleware('batchCheck'), async (req) => {
		await new BatchController().handleStatusTransition({
			batchOrId: req.params.batchId,
			batchStatusTransitionTimestamp: 'checkedAt',
			requestBody: req.body,
			newProductsStatus: 'TO_TEST',
		});

		return respond<'setBatchCheckDate'>(true);
	});

	router.post('/:batchId/check-sn', scopeMiddleware('batchCheck'), async (req) => {
		await new BatchController().handleStatusTransition({
			batchOrId: req.params.batchId,
			batchStatusTransitionTimestamp: 'checkedSnAt',
			requestBody: req.body,
			newProductsStatus: 'TO_TEST',
		});

		return respond<'setBatchCheckSnDate'>(true);
	});

	router.get('/:batchId/service-case', scopeMiddleware('batchRead', 'batchWrite'), async (req) => {
		const serviceCases = await new ServiceCaseController().getServiceCasesByBatch(req.params.batchId);

		return respond<'getBatchServiceCases'>(serviceCases);
	});

	router.get('/:batchId/warranty-claim', scopeMiddleware('batchRead', 'batchWrite'), async (req) => {
		const warrantyClaims = await new WarrantyClaimController().getWarrantyClaimsByBatch(req.params.batchId);

		return respond<'getBatchWarrantyClaims'>(warrantyClaims);
	});

	router.delete('/:batchId', scopeMiddleware('batchWrite'), async (req, res) => {
		const authentication = useAuthentication();
		const isAdmin = authentication?.role.scopes?.some((scope) => scope.name === 'admin');

		if (!isAdmin) {
			const batch = await new BatchController().findById(req.params.batchId);
			if (batch && !['AT_SUPPLIER', 'ON_THE_WAY'].includes(batch.status)) {
				return res.status(403).json();
			}
		}

		const [batchProducts] = await new ProductController().findByBatch(req.params.batchId, listAll());

		await Promise.all(
			batchProducts.map(async (product) => {
				return await new ProductController().delete(product.id);
			}),
		);
		const result = await new BatchController().delete(req.params.batchId);

		respond<'deleteBatch'>(result);
	});

	return router;
};
