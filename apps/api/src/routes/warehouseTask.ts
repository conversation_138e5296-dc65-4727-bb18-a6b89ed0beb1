import { COPY_SECTOR, STORE_SECTOR } from '@pocitarna-nx-2023/config';
import { ProductController, useAuthentication, WarehouseTaskController } from '@pocitarna-nx-2023/database';
import { warehouseTaskApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const warehouseTaskRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(warehouseTaskApi);

	router.get('/', scopeMiddleware('warehouseRead'), async () => {
		const [warehouseTasks, getCount] = await new WarehouseTaskController().list(getListProps());
		return respondWithPaging<'getWarehouseTasks'>(warehouseTasks, await getCount());
	});

	router.patch('/bulk', scopeMiddleware('warehouseWrite'), async (req) => {
		await Promise.all(
			req.body.ids.map(async (id) => {
				await new WarehouseTaskController().update(id, req.body.data);
			}),
		);

		return respond<'bulkUpdateWarehouseTasks'>(true);
	});

	router.post('/shift', scopeMiddleware('warehouseWrite'), async (req, res) => {
		const user = useAuthentication()?.user;
		if (!user) return res.status(401).json();

		if ('productIds' in req.body) {
			const { productIds } = req.body;
			await new WarehouseTaskController().createShift(productIds, user.id);
			return respond<'createShiftWarehouseTask'>(true);
		}

		const { productEnvelopeId, amount } = req.body;
		const [products] = await new ProductController().findByEnvelope(productEnvelopeId, {
			filter: { warehousePositionId: { ne: null }, status: { eq: 'FOR_SALE' } },
			page: { page: 1, limit: amount },
			sort: [
				`CASE WHEN "warehousePosition"."sector" = '${COPY_SECTOR}' THEN 1 ELSE 0 END`,
				`CASE WHEN "warehousePosition"."sector" = '${STORE_SECTOR}' THEN 1 ELSE 0 END`,
				'entity.createdAt',
				'entity.id',
			],
		});
		await new WarehouseTaskController().createShift(
			products.map((product) => product.id),
			user.id,
		);
		return respond<'createShiftWarehouseTask'>(true);
	});

	router.delete('/shift/:productId', scopeMiddleware('warehouseWrite'), async (req, res) => {
		const { productId } = req.params;
		const user = useAuthentication()?.user;
		if (!user) return res.status(401).json();

		const [warehouseTasks] = await new WarehouseTaskController().listOpenByProduct(productId, {
			filter: { type: { eq: 'SHIFT' }, userId: { eq: user.id } },
		});
		await new WarehouseTaskController().delete(warehouseTasks.map((task) => task.id));
		return respond<'deleteShiftWarehouseTask'>(true);
	});

	return router;
};
