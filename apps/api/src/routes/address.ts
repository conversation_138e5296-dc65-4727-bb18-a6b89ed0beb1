import { AddressController } from '@pocitarna-nx-2023/database';
import { addressApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { respond } from '../utils/respond';

export const addressRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(addressApi);

	router.get('/:addressId', scopeMiddleware('admin'), async (req, res) => {
		const address = await new AddressController().findById(req.params.addressId);

		if (!address) return res.status(404).json({ _error: { message: 'Address not found' } });

		respond<'getAddress'>(address);
	});

	return router;
};
