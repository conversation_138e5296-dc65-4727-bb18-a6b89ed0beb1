import { triggerEmail } from '@pocitarna-nx-2023/aws';
import { CustomerClaimCodeController, CustomerClaimController, FileController } from '@pocitarna-nx-2023/database';
import { generateCustomerClaimPdfProtocol } from '@pocitarna-nx-2023/pdf';
import { formatCustomerClaimCode } from '@pocitarna-nx-2023/utils';
import { customerClaimApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const customerClaimRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(customerClaimApi);

	router.post('/', scopeMiddleware('customerClaimWrite'), async (req) => {
		const customerClaim = await new CustomerClaimController().create(req.body);
		respond<'createCustomerClaim'>(customerClaim.id);
	});

	router.get('/', scopeMiddleware('customerClaimRead'), async () => {
		const [data, getCount] = await new CustomerClaimController().list(getListProps());

		return respondWithPaging<'getCustomerClaims'>(data, await getCount());
	});

	router.get('/code/:customerClaimCodeId', scopeMiddleware('customerClaimRead'), async (req, res) => {
		const customerClaimCode = await new CustomerClaimCodeController().findById(req.params.customerClaimCodeId);
		if (!customerClaimCode) return res.status(404).json();
		respond<'getCustomerClaimCode'>(customerClaimCode);
	});

	router.get('/:customerClaimId/protocol', async (req, res) => {
		const protocol = await new CustomerClaimController().getPdfProtocolFile(req.params.customerClaimId);
		if (!protocol) return res.status(404).json();
		const pdfUrl = await new FileController().getFileDownloadLink(protocol.file.id);
		respond<'getCustomerClaimPdfProtocolLink'>(pdfUrl);
	});

	router.get('/:customerClaimId', scopeMiddleware('customerClaimRead'), async (req, res) => {
		const customerClaim = await new CustomerClaimController().findById(req.params.customerClaimId);
		if (!customerClaim) return res.status(404).json();
		respond<'getCustomerClaim'>(customerClaim);
	});

	router.patch('/:customerClaimId', scopeMiddleware('customerClaimWrite'), async (req) => {
		await new CustomerClaimController().update(req.params.customerClaimId, req.body);

		if (req.body.status === 'RECEIVED' && req.body.receivedAt) {
			const customerClaim = await new CustomerClaimController().resolveRecord(req.params.customerClaimId);
			const pdfUrl = await generateCustomerClaimPdfProtocol(req.params.customerClaimId);
			const subject = `Protokol reklamace ${formatCustomerClaimCode(customerClaim.code)}`;
			const message = `<p>Dobrý den,</p><p>Poštovním dopravou Vám zasíláme protokol reklamace.</p>
				<p>Najděte svůj protokol <a href="${pdfUrl}">zde</a></p>`;

			await triggerEmail({
				recipients: customerClaim.contact.email,
				subject,
				message,
			});
		}

		respond<'updateCustomerClaim'>(true);
	});

	return router;
};
