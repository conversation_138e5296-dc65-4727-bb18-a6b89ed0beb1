/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { deleteSession, getSession, setSession } from '@pocitarna-nx-2023/aws';
import { ONE_WEEK } from '@pocitarna-nx-2023/config';
import { Authentication<PERSON>ontroller, UserController } from '@pocitarna-nx-2023/database';
import { azureApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { respond } from '../utils/respond';

export const azureRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(azureApi);

	router.post('/', async (req) => {
		const email = req.body.email === null ? undefined : req.body.email; // FIXME - undefined === null -> false
		const name = req.body.name ?? req.body.email ?? undefined;
		const image = req.body.image === null ? undefined : req.body.image;

		const isUser = await new UserController().findOneByEmail(email);

		if (isUser) {
			const auth = isUser.authentications.find((authentication) => authentication.type === 'azure-ad');

			if (!auth) {
				const newAuth = await new AuthenticationController().createAuthentication(isUser.id, {
					type: 'azure-ad',
					credentials: { account: {} },
				});
				return respond<'createUser'>({ ...isUser, id: newAuth.id });
			}

			return respond<'createUser'>({ ...isUser, id: auth.id });
		}

		const user = await new UserController().create({ email, name, image });

		const newAuth = await new AuthenticationController().createAuthentication(user.id, {
			type: 'azure-ad',
			credentials: { account: {} },
		});

		respond<'createUser'>({ ...user, id: newAuth.id });
	});

	router.get('/', async (req) => {
		const user = await new UserController().findOneByEmail(req.query.email);

		if (!user) return respond<'getUserByEmail'>(null); // FIXME - should return 404 instead
		const auth = user.authentications.find((authentication) => authentication.type === 'azure-ad');

		if (!auth) {
			const newAuth = await new AuthenticationController().createAuthentication(user.id, {
				type: 'azure-ad',
				credentials: { account: {} },
			});
			return respond<'getUserByEmail'>({ ...user, id: newAuth.id });
		}

		respond<'getUserByEmail'>({ ...user, id: auth.id });
	});

	router.get('/account/:provider/:accountId', async (req) => {
		const user = await new UserController().findOneByAccountProvider(req.params.provider, req.params.accountId);

		const auth = user?.authentications.find(
			(authentication) =>
				authentication.credentials.account!.provider === req.params.provider &&
				authentication.credentials.account!.providerAccountId === req.params.accountId,
		);

		if (!auth || !user) return respond<'getUserByAccount'>(null); // FIXME - should return 404 instead

		respond<'getUserByAccount'>({ ...user, id: auth.id });
	});

	router.get('/:userId', async (req) => {
		const auth = await new AuthenticationController().findByIdWithRelations(req.params.userId);

		const user = auth?.user;
		if (!user) return respond<'getAuthUser'>(null); // FIXME - should return 404 instead

		respond<'getAuthUser'>({
			email: user.email,
			name: user.name,
			image: user.image,
			role: auth.role ? [auth.role] : [],
			id: auth.id,
			userId: user.id,
			deletedAt: user.deletedAt,
		});
	});

	router.patch('/session', async (req, res) => {
		const session = await getSession(req.body.sessionToken);
		if (!session) return res.status(404).json();

		const authentication = await new AuthenticationController().findByIdWithRelations(session.authenticationId);
		if (!authentication || !authentication.user) return res.status(404).json();

		const date = new Date(Date.now() + ONE_WEEK);
		const expires = req.body.expires || date;
		const token = req.body.sessionToken || crypto.randomUUID();
		await setSession({ id: token, expires, authenticationId: authentication.id });

		respond<'updateSession'>({
			id: authentication.id,
			sessionToken: token,
			expires,
			role: authentication?.role.id,
			scopes: authentication?.role?.scopes?.map((scope) => scope.name) ?? [],
		});
	});

	router.patch('/:userId', async (req, res) => {
		const auth = await new AuthenticationController().findByIdWithRelations(req.params.userId);

		const user = auth?.user;
		if (!user) return res.status(404).json();

		const body = {
			...user,
			...req.body,
		};

		const updatedUser = await new UserController().update(user.id, {
			email: body.email,
			name: body.name === null ? undefined : body.name,
			image: user.image ? user.image : body.image,
		});

		respond<'updateUser'>(updatedUser);
	});

	router.delete('/:userId', async (req, res) => {
		const auth = await new AuthenticationController().findByIdWithRelations(req.params.userId);
		const user = auth?.user;
		if (!user) return res.status(404).json();

		await new UserController().delete(user.id);

		respond<'deleteUser'>(user);
	});

	router.post('/account', async (req) => {
		const auth = await new AuthenticationController().findByIdWithRelations(req.body.id);
		const user = auth?.user;
		if (!user) return respond<'linkAccount'>(null); // FIXME - should return 404 instead

		const authExists = user.authentications.find(
			(authentication) =>
				authentication.credentials.account!.provider === req.body.provider &&
				authentication.credentials.account!.providerAccountId === req.body.providerAccountId &&
				authentication.credentials.account!.type === req.body.type,
		);

		if (authExists) {
			return respond<'linkAccount'>({
				provider: authExists.credentials.account!.provider,
				providerAccountId: authExists.credentials.account!.providerAccountId,
				type: authExists.credentials.account!.type,
				id: authExists.id,
			});
		}

		const authentication = await new AuthenticationController().update(auth.id, {
			type: 'azure-ad',
			credentials: {
				account: {
					provider: req.body.provider,
					providerAccountId: req.body.providerAccountId,
					accessToken: req.body.access_token || crypto.randomUUID(),
					refreshToken: req.body.refresh_token || crypto.randomUUID(),
					type: req.body.type,
				},
			},
			user,
		});

		respond<'linkAccount'>({
			provider: req.body.provider,
			providerAccountId: req.body.providerAccountId,
			type: req.body.type,
			id: authentication!.id,
		});
	});

	router.delete('/account/:provider/:accountId', async (req, res) => {
		const user = await new UserController().findOneByAccountProvider(req.params.provider, req.params.accountId);
		if (!user) return res.status(404).json();

		const authentication = user.authentications.find(
			(authentication) =>
				authentication.credentials.account!.provider === req.params.provider &&
				authentication.credentials.account!.providerAccountId === req.params.accountId,
		);
		if (!authentication) return res.status(404).json();

		await new AuthenticationController().delete(authentication.id);

		respond<'unlinkAccount'>({
			provider: authentication.credentials.account!.provider,
			providerAccountId: authentication.credentials.account!.providerAccountId,
			type: authentication.credentials.account!.type,
			id: authentication.id,
		});
	});

	router.post('/session', async (req, res) => {
		const authentication = await new AuthenticationController().findByIdWithRelations(req.body.userId);
		if (!authentication || !authentication.user) return res.status(404).json();

		const date = new Date(Date.now() + ONE_WEEK);
		const expires = req.body.expires || date;
		const token = req.body.sessionToken || crypto.randomUUID();
		await setSession({ id: token, expires, authenticationId: authentication.id });

		respond<'createSession'>({
			id: req.body.userId,
			sessionToken: token,
			expires,
			role: authentication.role?.id,
			scopes: authentication.role?.scopes?.map((scope) => scope.name) ?? [],
		});
	});

	router.post('/session/verification-token', async (req, res) => {
		const authentication = await new AuthenticationController().findOneByVerificationToken(req.body.verificationToken);

		if (!authentication || !authentication.user) return res.status(404).json();

		const expires = new Date(Date.now() + ONE_WEEK);
		const token = crypto.randomUUID();
		await setSession({ id: token, expires, authenticationId: authentication.id });

		respond<'createSessionForVerificationToken'>({
			id: authentication.id,
			sessionToken: token,
			expires,
			role: authentication.role?.id,
			scopes: authentication.role?.scopes?.map((scope) => scope.name) ?? [],
			user: authentication.user,
		});
	});

	router.get('/session/:sessionToken', async (req, res) => {
		const session = await getSession(req.params.sessionToken);
		if (!session) return res.status(404).json();

		const authentication = await new AuthenticationController().findByIdWithRelations(session.authenticationId);
		if (!authentication || !authentication.user) return res.status(404).json();

		respond<'getSessionAndUser'>({
			user: { ...authentication.user, id: authentication.id },
			session: {
				sessionToken: session.id,
				expires: session.expires,
				id: authentication.id,
				role: authentication.role?.id,
				scopes: authentication.role?.scopes?.map((scope) => scope.name) ?? [],
			},
		});
	});

	router.delete('/session/:sessionToken', async (req) => {
		const result = await deleteSession(req.params.sessionToken);
		respond<'deleteSession'>(result);
	});

	router.post('/verification', async (req, res) => {
		const authentication = await new AuthenticationController().findOneByEmailWithUser(req.body.identifier);
		if (!authentication || !authentication.user) return res.status(404).json();

		const token = req.body.token || crypto.randomUUID();
		await new AuthenticationController().update(authentication.id, {
			...authentication,
			credentials: {
				...authentication.credentials,
				verificationToken: { token },
			},
		});

		respond<'createVerificationToken'>({
			email: authentication.user.email ?? '',
			emailVerified: new Date(),
			id: authentication.id,
			token,
		});
	});

	router.patch('/verification', async (req, res) => {
		const authentication = await new AuthenticationController().findOneByVerificationToken(req.body.token);
		if (!authentication || !authentication.user) return res.status(404).json();

		respond<'useVerificationToken'>({
			email: authentication.user.email ?? '',
			emailVerified: new Date(),
			id: authentication.id,
		});
	});

	// getUserByQR
	router.get('/qr/:code', async (req) => {
		const authentication = await new AuthenticationController().findOneByVerificationToken(req.params.code);
		if (!authentication) return respond<'getUserByQR'>(null); // FIXME - should return 404 instead

		respond<'getUserByQR'>({ ...authentication.user, id: authentication.id });
	});

	return router;
};
