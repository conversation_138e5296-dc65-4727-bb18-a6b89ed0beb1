import {
	AttributeController,
	AttributeValueConditionalAttributeController,
	ConditionalAttributeController,
	listAll,
} from '@pocitarna-nx-2023/database';
import { conditionalAttributeApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const conditionalAttributeRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(conditionalAttributeApi);

	router.get('/attribute', scopeMiddleware('admin', 'testRead', 'productTest', 'productAdmin'), async () => {
		const [attributes, getCount] = await new ConditionalAttributeController().listAttributes(getListProps());
		const children = await new ConditionalAttributeController().listChildAttributes(attributes.map(({ id }) => id));
		const result = attributes.map((attribute) => ({
			...attribute,
			children: children[attribute.id],
		}));
		respondWithPaging<'getConditionalAttributesAttributes'>(result, await getCount());
	});

	router.get('/attribute/:attributeId', scopeMiddleware('admin', 'testRead', 'productTest', 'productAdmin'), async (req) => {
		const { attributeId } = req.params;
		const { page, filter, sort } = getListProps();
		const [conditionalAttributes] = await new ConditionalAttributeController().list(
			listAll({ filter: { ...filter, 'attribute.id': { eq: attributeId } }, sort }),
		);

		respondWithPaging<'getConditionalAttributesForAttribute'>(
			page ? conditionalAttributes.slice((page.page - 1) * page.limit, page.page * page.limit) : conditionalAttributes,
			conditionalAttributes.length,
		);
	});

	router.get(
		'/:conditionalAttributeId/attribute-value',
		scopeMiddleware('admin', 'testRead', 'productTest', 'productAdmin'),
		async (req) => {
			const [attributeValueConditionalAttributes, getCount] = await new AttributeValueConditionalAttributeController().list(
				getListProps({ conditionalAttributeId: { eq: req.params.conditionalAttributeId } }),
			);

			respondWithPaging<'getAttributeValueConditionalAttributes'>(attributeValueConditionalAttributes, await getCount());
		},
	);

	router.delete('/attribute/:attributeId/attribute/:childAttributeId', scopeMiddleware('admin'), async (req) => {
		const { attributeId, childAttributeId } = req.params;
		await new AttributeValueConditionalAttributeController().unlinkAttributeByAttribute(attributeId, childAttributeId);

		respond<'deleteConditionalAttributeAttribute'>(true);
	});

	router.post('/', scopeMiddleware('admin', 'productAdmin'), async (req) => {
		await new ConditionalAttributeController().create(req.body);
		respond<'createConditionalAttribute'>(true);
	});

	router.patch('/:conditionalAttributeId', scopeMiddleware('admin', 'productAdmin'), async (req, res) => {
		const { conditionalAttributeId } = req.params;
		const conditionalAttribute = await new ConditionalAttributeController().update(conditionalAttributeId, req.body);
		if (!conditionalAttribute) return res.status(404).json();

		respond<'updateConditionalAttribute'>(conditionalAttribute);
	});

	router.delete('/:conditionalAttributeId', scopeMiddleware('admin', 'productAdmin'), async (req) => {
		const { conditionalAttributeId } = req.params;
		const result = await new ConditionalAttributeController().delete(conditionalAttributeId);

		respond<'deleteConditionalAttribute'>(result);
	});

	router.post('/:conditionalAttributeId/attribute-value', scopeMiddleware('admin', 'productAdmin'), async (req) => {
		const { conditionalAttributeId } = req.params;
		const { attributeId, attributeValueId, value } = req.body;
		await new AttributeValueConditionalAttributeController().unlinkAttribute(conditionalAttributeId, attributeId);
		if (attributeValueId) {
			await new AttributeValueConditionalAttributeController().link(conditionalAttributeId, attributeValueId);
		} else if (value) {
			const attributeValue = await new AttributeController().createTextValue(attributeId, value);
			await new AttributeValueConditionalAttributeController().link(conditionalAttributeId, attributeValue.id);
		}

		respond<'addConditionalAttributeAttributeValue'>(true);
	});

	router.delete('/:conditionalAttributeId/attribute-value/:attributeValueId', scopeMiddleware('admin', 'productAdmin'), async (req) => {
		const { conditionalAttributeId, attributeValueId } = req.params;
		await new AttributeValueConditionalAttributeController().unlink(conditionalAttributeId, attributeValueId);

		respond<'deleteConditionalAttributeAttributeValue'>(true);
	});

	return router;
};
