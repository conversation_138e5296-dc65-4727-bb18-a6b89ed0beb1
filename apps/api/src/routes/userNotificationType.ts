import { UserNotificationTypeController } from '@pocitarna-nx-2023/database';
import { userNotificationTypeApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const userNotificationTypeRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(userNotificationTypeApi);

	router.post('/:userId/:notificationTypeId', scopeMiddleware('admin'), async (req) => {
		await new UserNotificationTypeController().link(req.params.userId, req.params.notificationTypeId, req.body.deliveryMethod);

		respond<'createUserNotificationType'>(true);
	});

	router.get('/', scopeMiddleware('admin'), async () => {
		const [userNotificationTypes, getCount] = await new UserNotificationTypeController().list(getListProps());
		respondWithPaging<'getUserNotificationTypes'>(userNotificationTypes, await getCount());
	});

	router.delete('/:userId/:notificationTypeId', scopeMiddleware('admin'), async (req) => {
		await new UserNotificationTypeController().unlink(req.params.userId, req.params.notificationTypeId, req.body.deliveryMethod);

		respond<'deleteUserNotificationType'>(true);
	});

	return router;
};
