import { VendorController } from '@pocitarna-nx-2023/database';
import { vendorsApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const vendorsRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(vendorsApi);

	router.post('/', scopeMiddleware('admin'), async (req, res) => {
		const { defectTypes, ...data } = req.body;

		const vendor = await new VendorController().create(data);
		await new VendorController().setUncoveredDefectTypes(vendor.id, defectTypes);
		if (!vendor) return res.status(500).json();

		respond<'createVendor'>(vendor);
	});

	router.get('/', scopeMiddleware('admin', 'batchRead', 'batchWrite'), async () => {
		const [vendors, getCount] = await new VendorController().list(getListProps());
		respondWithPaging<'getAllVendors'>(vendors, await getCount());
	});

	router.get('/:vendorId', scopeMiddleware('home'), async (req, res) => {
		const vendor = await new VendorController().findById(req.params.vendorId);
		if (!vendor) return res.status(404).json();

		respond<'getVendor'>(vendor);
	});

	router.patch('/:vendorId', scopeMiddleware('admin'), async (req) => {
		const { defectTypes, ...data } = req.body;

		await new VendorController().update(req.params.vendorId, data);
		if (defectTypes) {
			await new VendorController().setUncoveredDefectTypes(req.params.vendorId, defectTypes);
		}

		respond<'updateVendor'>(true);
	});

	router.delete('/:vendorId', scopeMiddleware('admin'), async (req, res) => {
		const vendor = await new VendorController().findById(req.params.vendorId);
		if (!vendor) return res.status(404).send();

		const result = await new VendorController().delete(vendor.id);

		respond<'deleteVendor'>(result);
	});

	router.patch('/:vendorId/files', scopeMiddleware('admin'), async (req) => {
		const fileIds = req.body;
		const vendorId = req.params.vendorId;
		await new VendorController().addFiles(vendorId, fileIds);

		respond<'addFilesToVendor'>(true);
	});

	router.delete(
		'/:vendorId/files/:fileId',
		scopeMiddleware('serviceWrite', 'warrantyClaimWrite', 'productWrite', 'productTest'),
		async (req, res) => {
			const entity = await new VendorController().deleteFile(req.params.vendorId, req.params.fileId);
			if (!entity) return res.status(500).send();

			respond<'deleteVendorFile'>(true);
		},
	);

	return router;
};
