import { triggerOrderSync, triggerOrderUpdate } from '@pocitarna-nx-2023/aws';
import { listOne, ShoptetOrderSyncController } from '@pocitarna-nx-2023/database';
import { shoptetOrderSyncApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { respond } from '../utils/respond';

export const shoptetOrderSyncRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(shoptetOrderSyncApi);

	router.get('/', scopeMiddleware('orderRead'), async (_req, res) => {
		const [[latestSync]] = await new ShoptetOrderSyncController().list(
			listOne({
				filter: {
					finishedAt: { ne: null },
				},
			}),
		);

		if (!latestSync?.finishedAt) return res.status(404).json();

		respond<'getLatestShoptetOrderSync'>(latestSync);
	});

	router.post('/', scopeMiddleware('orderWrite'), async () => {
		await triggerOrderSync();
		respond<'triggerShoptetOrderSync'>(true);
	});

	router.post('/:ecommerceOrderId', scopeMiddleware('admin'), async (req) => {
		await triggerOrderUpdate(req.params.ecommerceOrderId);
		respond<'triggerOrderUpdate'>(true);
	});

	return router;
};
