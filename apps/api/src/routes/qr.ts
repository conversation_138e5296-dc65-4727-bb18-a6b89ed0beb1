import { AuthenticationController, useAuthentication } from '@pocitarna-nx-2023/database';
import { qrApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { respond } from '../utils/respond';

export const qrRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(qrApi);

	router.get('/', scopeMiddleware('home'), async (_req, res) => {
		const authentication = useAuthentication();
		if (!authentication) return res.status(404).json({ _error: { message: 'User not found' } });

		const qr = await new AuthenticationController().findVerificationTokenByUserId(authentication.user.id);
		if (!qr) return res.status(404).json({ _error: { message: 'Verification code not found' } });

		respond<'getQr'>(qr?.token);
	});

	router.get('/:userId', scopeMiddleware('admin'), async (req, res) => {
		const qr = await new AuthenticationController().findVerificationTokenByUserId(req.params.userId);
		if (!qr) return res.status(404).json({ _error: { message: 'Verification code not found' } });

		respond<'getQrByUser'>(qr.token);
	});

	return router;
};
