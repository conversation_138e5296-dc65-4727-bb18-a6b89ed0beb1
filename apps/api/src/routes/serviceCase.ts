import { ServiceCase<PERSON>ode<PERSON>ontroller, ServiceCaseController } from '@pocitarna-nx-2023/database';
import { serviceCaseApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { isFilteringBy } from '../utils/isFilteringBy';
import { respond, respondWithPaging } from '../utils/respond';

export const serviceCaseRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(serviceCaseApi);

	router.post('/', scopeMiddleware('serviceWrite', 'warrantyClaimWrite', 'productWrite', 'productTest'), async (req) => {
		const serviceCase = await new ServiceCaseController().create(req.body);
		respond<'createServiceCase'>(serviceCase);
	});

	router.get(
		'/',
		scopeMiddleware(
			'serviceRead',
			'serviceWrite',
			'warrantyClaimRead',
			'warrantyClaimWrite',
			'productRead',
			'productWrite',
			'testRead',
			'productTest',
		),
		async () => {
			// Actions cannot be added to the base queryBuilder (they would clash with the version queryBuilder loadActions),
			// hence we decided to adjust the main list method to choose a more specific queryBuilder for that specific use-case

			const listProps = getListProps();
			const isFilteringByAction = isFilteringBy(listProps, 'action');
			const isFilteringByModel = isFilteringBy(listProps, 'attributeValue.value');

			const [data, getCount] = isFilteringByAction
				? await new ServiceCaseController().listWithAction(listProps)
				: await new ServiceCaseController().listWithProduct(listProps, isFilteringByModel);

			return respondWithPaging<'getServiceCases'>(data, await getCount());
		},
	);

	router.get(
		'/:serviceCaseId',
		scopeMiddleware(
			'serviceRead',
			'serviceWrite',
			'warrantyClaimRead',
			'warrantyClaimWrite',
			'productRead',
			'productWrite',
			'testRead',
			'productTest',
		),
		async (req, res) => {
			const serviceCase = await new ServiceCaseController().findById(req.params.serviceCaseId);
			if (!serviceCase) return res.status(404).json();
			respond<'getServiceCase'>(serviceCase);
		},
	);

	router.get(
		'/code/:serviceCaseCodeId',
		scopeMiddleware('serviceRead', 'serviceWrite', 'productRead', 'testRead', 'productTest', 'batchRead'),
		async (req, res) => {
			const serviceCaseCode = await new ServiceCaseCodeController().findById(req.params.serviceCaseCodeId);
			if (!serviceCaseCode) return res.status(404).json();
			respond<'getServiceCaseCode'>(serviceCaseCode);
		},
	);

	router.patch('/bulk', scopeMiddleware('serviceWrite', 'warrantyClaimWrite', 'productWrite', 'productTest'), async (req) => {
		for (const id of req.body.ids) {
			await new ServiceCaseController().handleTransition(id, req.body.data);
		}

		return respond<'bulkUpdateServiceCases'>(true);
	});

	router.patch('/:serviceCaseId', scopeMiddleware('serviceWrite', 'warrantyClaimWrite', 'productWrite', 'productTest'), async (req) => {
		const { serviceCaseId } = req.params;

		if (!('status' in req.body)) {
			await new ServiceCaseController().update(serviceCaseId, {
				trackingCode: req.body.trackingCode,
			});

			return respond<'updateServiceCase'>(true);
		}

		await new ServiceCaseController().handleTransition(serviceCaseId, req.body);

		respond<'updateServiceCase'>(true);
	});

	router.patch(
		'/:serviceCaseId/files',
		scopeMiddleware('serviceWrite', 'warrantyClaimWrite', 'productWrite', 'productTest'),
		async (req) => {
			const fileIds = req.body;
			const serviceCaseId = req.params.serviceCaseId;
			await new ServiceCaseController().addFiles(serviceCaseId, fileIds);
			respond<'addFilesToServiceCase'>(true);
		},
	);

	router.delete(
		'/:serviceCaseId/files/:fileId',
		scopeMiddleware('serviceWrite', 'warrantyClaimWrite', 'productWrite', 'productTest'),
		async (req, res) => {
			const entity = await new ServiceCaseController().deleteFile(req.params.serviceCaseId, req.params.fileId);
			if (!entity) return res.status(500).send();

			respond<'deleteServiceCaseFile'>(true);
		},
	);

	return router;
};
