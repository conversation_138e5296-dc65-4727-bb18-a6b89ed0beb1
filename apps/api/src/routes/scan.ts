import { triggerOrderItemStatusUpdate } from '@pocitarna-nx-2023/aws';
import { CODE_PREFIX, ORDER_STATUS_PROCESSING } from '@pocitarna-nx-2023/config';
import {
	BatchController,
	EcommerceOrderItemController,
	ProductCategoryController,
	ProductCodeController,
	ProductController,
	ProductEnvelopeController,
	useAuthentication,
	WarehousePositionController,
	WarehouseTaskController,
} from '@pocitarna-nx-2023/database';
import { stripCode } from '@pocitarna-nx-2023/utils';
import { scanApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { respond } from '../utils/respond';

export const scanRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(scanApi);

	router.get(
		'/:scanCode',
		scopeMiddleware(
			'productRead',
			'productWrite',
			'productTest',
			'serviceRead',
			'serviceWrite',
			'warrantyClaimRead',
			'warrantyClaimWrite',
			'batchRead',
			'batchWrite',
			'warehouseRead',
		),
		async (req, res) => {
			const code = req.params.scanCode.toUpperCase();

			if (code.startsWith(`${CODE_PREFIX.PRODUCT}0`)) {
				const cleanCode = stripCode(code);
				const productCode = await new ProductCodeController().findByCode(cleanCode); // Need to separately fetch product code, because we can just find unassigned code
				if (!productCode) return res.status(404).json();

				const product = await new ProductController().findByProductCode(productCode);
				if (!product) return respond<'getByScanCode'>({ type: 'product', product: null });

				if (req.query.expedition || req.query.warehouse) {
					const [warehouseTasks] = await new WarehouseTaskController().listOpenByProduct(product.id, {
						filter: { type: { eq: 'PICKUP' } },
					});

					// I can expedite the product
					if (req.query.expedition && warehouseTasks.length > 0) {
						await new ProductController().pickup(product);
						const pairedOrderItem = await new EcommerceOrderItemController().findByProduct(product);

						if (pairedOrderItem) {
							await triggerOrderItemStatusUpdate(
								pairedOrderItem.ecommerceOrder.code,
								pairedOrderItem.shoptetItemIdentifier,
								ORDER_STATUS_PROCESSING,
							);
						}
						return respond<'getByScanCode'>({ type: 'product', product });
					}

					// No reservations, can move the product
					const isWarehouseManage = !!useAuthentication()?.role.scopes.find((scope) => scope.name === 'warehouseManage');
					if (req.query.warehouse && (warehouseTasks.length === 0 || isWarehouseManage)) {
						await new ProductController().pickup(product);
						return respond<'getByScanCode'>({ type: 'product', product });
					}

					// No action is valid, have to prevent it
					return respond<'getByScanCode'>({ type: 'product', product: null });
				}

				return respond<'getByScanCode'>({ type: 'product', product });
			}

			if (code.startsWith(`${CODE_PREFIX.WAREHOUSE_POSITION}0`)) {
				const cleanCode = stripCode(code);
				const warehousePosition = await new WarehousePositionController().findByCode(cleanCode);
				if (!warehousePosition) return respond<'getByScanCode'>({ type: 'warehousePosition', warehousePosition: null });

				return respond<'getByScanCode'>({ type: 'warehousePosition', warehousePosition });
			}

			const batchCodePrefixes = [CODE_PREFIX.BATCH, CODE_PREFIX.BATCH_DELIVERY, CODE_PREFIX.BATCH_CHECK, CODE_PREFIX.BATCH_TEST];
			if (batchCodePrefixes.some((prefix) => code.startsWith(`${prefix}0`))) {
				const cleanCode = stripCode(code);
				const batch = await new BatchController().findByCode(cleanCode);
				if (!batch) return res.status(404).json();

				return respond<'getByScanCode'>({ type: 'batch', batch });
			}

			const categoryPrefixes = await new ProductCategoryController().findCodePrefixes();
			if (categoryPrefixes.some((prefix) => code.startsWith(`${prefix}0`))) {
				const cleanCode = stripCode(code);
				const productEnvelope = await new ProductEnvelopeController().findByCode(cleanCode);
				if (!productEnvelope) return res.status(404).json();

				return respond<'getByScanCode'>({
					type: 'productEnvelope',
					productEnvelope,
				});
			}

			const product = await new ProductController().findBySn(code);
			if (!product) return res.status(404).json();

			respond<'getByScanCode'>({ type: 'product', product });
		},
	);

	return router;
};
