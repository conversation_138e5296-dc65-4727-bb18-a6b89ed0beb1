import { ProductCosmeticDefectController } from '@pocitarna-nx-2023/database';
import { productCosmeticDefectApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const productCosmeticDefectRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(productCosmeticDefectApi);

	router.get('/', scopeMiddleware('productTest'), async () => {
		const [data, getCount] = await new ProductCosmeticDefectController().list(getListProps());
		respondWithPaging<'getProductCosmeticDefects'>(data, await getCount());
	});

	router.post('/product/:productId/cosmeticArea/:cosmeticAreaId', scopeMiddleware('productTest'), async (req) => {
		const { cosmeticDefectId, isFix } = req.body;

		await new ProductCosmeticDefectController().link({
			productId: req.params.productId,
			cosmeticAreaId: req.params.cosmeticAreaId,
			cosmeticDefectIds: [cosmeticDefectId],
			isFix,
		});

		return respond<'addCosmeticDefectsToProductArea'>(true);
	});

	router.patch('/:productCosmeticDefectId/files', scopeMiddleware('admin'), async (req) => {
		const fileIds = req.body;
		await new ProductCosmeticDefectController().addFiles(req.params.productCosmeticDefectId, fileIds);

		respond<'addFilesToProductCosmeticDefect'>(true);
	});

	router.delete('/product/:productId/cosmeticDefect/:cosmeticDefectId', scopeMiddleware('productTest'), async (req) => {
		await new ProductCosmeticDefectController().unlink(req.params.productId, req.params.cosmeticDefectId);

		return respond<'deleteProductCosmeticDefect'>(true);
	});

	router.delete(
		'/:productCosmeticDefectId/files/:fileId',
		scopeMiddleware('serviceWrite', 'warrantyClaimWrite', 'productWrite', 'productTest'),
		async (req, res) => {
			const entity = await new ProductCosmeticDefectController().deleteFile(req.params.productCosmeticDefectId, req.params.fileId);
			if (!entity) return res.status(500).send();

			respond<'deleteProductCosmeticDefectFile'>(true);
		},
	);

	return router;
};
