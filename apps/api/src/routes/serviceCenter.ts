import { ServiceCenterController } from '@pocitarna-nx-2023/database';
import { serviceCenterApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const serviceCenterRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(serviceCenterApi);

	router.post('/', scopeMiddleware('admin', 'productWrite'), async (req, res) => {
		const serviceCenter = await new ServiceCenterController().create(req.body);
		if (!serviceCenter) return res.status(500).json();
		respond<'createServiceCenter'>(serviceCenter);
	});

	router.get('/', scopeMiddleware('admin', 'serviceRead', 'serviceWrite', 'productRead', 'productWrite'), async () => {
		const [serviceCenters, getCount] = await new ServiceCenterController().list(getListProps());
		respondWithPaging<'getServiceCenters'>(serviceCenters, await getCount());
	});

	router.get(
		'/:serviceCenterId',
		scopeMiddleware('admin', 'serviceRead', 'serviceWrite', 'productRead', 'productWrite'),
		async (req, res) => {
			const serviceCenter = await new ServiceCenterController().findById(req.params.serviceCenterId);
			if (!serviceCenter) return res.status(404).json();
			respond<'getServiceCenter'>(serviceCenter);
		},
	);

	router.patch('/:serviceCenterId', scopeMiddleware('admin'), async (req) => {
		const serviceCenter = await new ServiceCenterController().update(req.params.serviceCenterId, req.body);
		respond<'updateServiceCenter'>(serviceCenter);
	});

	router.delete('/:serviceCenterId', scopeMiddleware('admin'), async (req) => {
		const result = await new ServiceCenterController().delete(req.params.serviceCenterId);
		respond<'deleteServiceCenter'>(result);
	});

	return router;
};
