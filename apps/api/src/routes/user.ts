import { UserController } from '@pocitarna-nx-2023/database';
import { userApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { respond } from '../utils/respond';

export const userRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(userApi);

	router.get('/:userId', scopeMiddleware('home'), async (req, res) => {
		const user = await new UserController().findById(req.params.userId);
		if (!user) return res.status(404).json();

		respond<'getUser'>(user);
	});

	return router;
};
