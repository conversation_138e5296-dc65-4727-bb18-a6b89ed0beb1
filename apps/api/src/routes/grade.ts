import { GradeController } from '@pocitarna-nx-2023/database';
import { gradeApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const gradeRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(gradeApi);

	router.post('/', scopeMiddleware('admin'), async (req, res) => {
		const grade = await new GradeController().create(req.body);
		if (!grade) return res.status(500).json();

		respond<'createGrade'>(grade);
	});

	router.get('/', scopeMiddleware('admin', 'batchRead', 'batchWrite'), async () => {
		const [grades, getCount] = await new GradeController().list(getListProps());
		respondWithPaging<'getGrades'>(grades, await getCount());
	});

	router.get('/ranked', scopeMiddleware('admin', 'batchRead', 'batchWrite'), async () => {
		const rankedGrades = await new GradeController().listByRank();
		respond<'getRankedGrades'>(rankedGrades);
	});

	router.patch('/sequence', scopeMiddleware('admin'), async (req, res) => {
		const result = await new GradeController().updateSequence(req.body.gradeId, req.body.sequence);
		if (!result) return res.status(500).json();

		respond<'updateGradeSequence'>(result);
	});

	router.get('/:gradeId', scopeMiddleware('home'), async (req, res) => {
		const grade = await new GradeController().findById(req.params.gradeId);
		if (!grade) return res.status(404).json();

		respond<'getGrade'>(grade);
	});

	router.patch('/:gradeId', scopeMiddleware('admin'), async (req) => {
		await new GradeController().update(req.params.gradeId, req.body);

		respond<'updateGrade'>(true);
	});

	router.delete('/:gradeId', scopeMiddleware('admin'), async (req, res) => {
		const grade = await new GradeController().findById(req.params.gradeId);
		if (!grade) return res.status(404).send();

		const result = await new GradeController().delete(grade.id);

		respond<'deleteGrade'>(result);
	});

	return router;
};
