import {
	AttributeController,
	AttributeValueController,
	listAll,
	ProductCategoryAttributeController,
	ProductController,
} from '@pocitarna-nx-2023/database';
import { filterUndefined, uniques } from '@pocitarna-nx-2023/utils';
import { attributeApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const attributeRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(attributeApi);

	router.get('/', scopeMiddleware('home'), async () => {
		const [entities, getCount] = await new AttributeController().list(getListProps());
		respondWithPaging<'getAttributes'>(entities, await getCount());
	});

	router.post('/', scopeMiddleware('admin', 'productAdmin'), async (req) => {
		const entity = await new AttributeController().create(req.body);

		if (entity.dataType === 'boolean') {
			await new AttributeValueController(entity.id).create({ value: true });
			await new AttributeValueController(entity.id).create({ value: false });
		}

		respond<'createAttribute'>(entity);
	});

	router.get('/:attributeId', scopeMiddleware('home'), async (req, res) => {
		const entity = await new AttributeController().findById(req.params.attributeId);
		if (!entity) return res.status(404).json();

		respond<'getAttribute'>(entity);
	});

	router.get('/:attributeId/full', scopeMiddleware('home'), async (req, res) => {
		const entity = await new AttributeController().findFullById(req.params.attributeId);
		if (!entity) return res.status(404).json();

		respond<'getFullAttribute'>(entity);
	});

	router.patch('/:attributeId', scopeMiddleware('admin', 'productAdmin'), async (req, res) => {
		const entity = await new AttributeController().update(req.params.attributeId, req.body);
		if (!entity) return res.status(404).json();

		respond<'updateAttribute'>(true);
	});

	router.delete('/:attributeId', scopeMiddleware('admin', 'productAdmin'), async (req, res) => {
		const count = await new ProductCategoryAttributeController().count({ filter: { attributeId: { eq: req.params.attributeId } } });

		if (count > 0) {
			return res
				.status(500)
				.json({ _error: { message: 'Parametr nelze smazat, protože je přiřazen k některé z kategorií produktů.' } });
		}

		const status = await new AttributeController().delete(req.params.attributeId);
		respond<'deleteAttribute'>(status);
	});

	router.post('/:attributeId/name', scopeMiddleware('admin', 'productAdmin'), async (req) => {
		const entity = await new AttributeController().createAlternativeName(req.params.attributeId, req.body);
		respond<'createAttributeAlternativeName'>(entity);
	});

	router.put('/:attributeId/name/:attributeAlternativeNameId', scopeMiddleware('admin', 'productAdmin'), async (req, res) => {
		const entity = await new AttributeController().updateAlternativeName(
			req.params.attributeId,
			req.params.attributeAlternativeNameId,
			req.body,
		);
		if (!entity) return res.status(404).json();

		respond<'updateAttributeAlternativeName'>(entity);
	});

	router.delete('/:attributeId/name/:attributeAlternativeNameId', scopeMiddleware('admin', 'productAdmin'), async (req) => {
		const status = await new AttributeController().deleteAlternativeName(req.params.attributeId, req.params.attributeAlternativeNameId);
		respond<'deleteAttributeAlternativeName'>(status);
	});

	router.get('/:attributeId/value', scopeMiddleware('home'), async (req) => {
		const [entities, getCount] = await new AttributeController().listValues(req.params.attributeId, getListProps());
		respondWithPaging<'getAttributeValues'>(entities, await getCount());
	});

	router.post('/:attributeId/value', scopeMiddleware('admin', 'productTest', 'productAdmin'), async (req, res) => {
		const data = await new AttributeController().getOrCreatePermanentValue(req.params.attributeId, req.body.value);
		const entity = await new AttributeController().getValue(req.params.attributeId, data.id);
		if (!entity) return res.status(404).json();
		respond<'createAttributeValue'>(entity);
	});

	router.get('/:attributeId/value/:attributeValueId', scopeMiddleware('home'), async (req, res) => {
		const entity = await new AttributeController().getValue(req.params.attributeId, req.params.attributeValueId);
		if (!entity) return res.status(404).json();

		respond<'getAttributeValue'>(entity);
	});

	router.patch(
		'/:attributeId/value/:attributeValueId',
		scopeMiddleware('admin', 'productAdmin', 'productTest', 'productTestLead'),
		async (req, res) => {
			if (req.params.attributeValueId === req.body.attributeValueId) {
				const entity = await new AttributeController().updateValue(req.params.attributeId, req.params.attributeValueId, {
					value: req.body.value,
					temporary: false,
				});
				if (!entity) return res.status(404).json();
				return respond<'updateAttributeValue'>(entity);
			}

			const originalValue = await new AttributeValueController().findById(req.params.attributeValueId);
			if (!originalValue) return res.status(404).json();

			if (originalValue.temporary) {
				await new AttributeValueController(req.params.attributeId).createAlternative(req.body.attributeValueId, {
					value: req.body.value,
				});

				const entity = await new AttributeController().getValue(req.params.attributeId, req.body.attributeValueId);
				if (!entity) return res.status(404).json();
				return respond<'updateAttributeValue'>(entity);
			}

			res.status(400).json({ _error: { message: 'Are you sure, that the action you are trying to perform is allowed?' } });
		},
	);

	router.get('/:attributeId/value/:attributeValueId/products', scopeMiddleware('home'), async (req) => {
		const { attributeId, attributeValueId } = req.params;
		const attributeValues = await new AttributeValueController(attributeId).findProductsByAttributeValueId(attributeValueId);
		const productIds = uniques(filterUndefined(attributeValues.flatMap(({ products }) => products.map(({ product }) => product.id))));

		const [products] = await new ProductController().list(listAll({ filter: { id: { eq: productIds } } }));
		respond<'getAttributeValueProducts'>(products);
	});

	router.delete('/:attributeId/value/:attributeValueId', scopeMiddleware('admin', 'productAdmin'), async (req) => {
		const status = await new AttributeController().deleteValue(req.params.attributeId, req.params.attributeValueId);

		respond<'deleteAttributeValue'>(status);
	});

	router.post('/:attributeId/value/:attributeValueId/alternative', scopeMiddleware('admin', 'productAdmin'), async (req, res) => {
		const entity = await new AttributeController().createAlternative(req.params.attributeId, req.params.attributeValueId, req.body);
		if (!entity) return res.status(404).json();
		respond<'createAttributeValueAlternative'>(entity);
	});

	router.patch(
		'/:attributeId/value/:attributeValueId/alternative/:attributeValueAlternativeId',
		scopeMiddleware('admin', 'productAdmin'),
		async (req, res) => {
			const entity = await new AttributeController().updateAlternative(
				req.params.attributeId,
				req.params.attributeValueId,
				req.params.attributeValueAlternativeId,
				req.body,
			);
			if (!entity) return res.status(404).json();

			respond<'updateAttributeValueAlternative'>(entity);
		},
	);

	router.delete(
		'/:attributeId/value/:attributeValueId/alternative/:attributeValueAlternativeId',
		scopeMiddleware('admin', 'productAdmin'),
		async (req) => {
			const status = await new AttributeController().deleteAlternative(
				req.params.attributeId,
				req.params.attributeValueId,
				req.params.attributeValueAlternativeId,
			);

			respond<'deleteAttributeValueAlternative'>(status);
		},
	);

	return router;
};
