import { ServiceTaskController } from '@pocitarna-nx-2023/database';
import { serviceTaskApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { Decimal } from 'decimal.js';
import { scopeMiddleware } from '../middlewares/scope';
import { respond } from '../utils/respond';

export const serviceTaskRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(serviceTaskApi);

	router.post('/', scopeMiddleware('serviceWrite', 'admin', 'warrantyClaimWrite'), async (req) => {
		const { productDefectsIds, attributeValues, price, ...rest } = req.body;
		await new ServiceTaskController().create({
			...rest,
			price: new Decimal(price ?? 0),
			productDefects: productDefectsIds.map((id) => ({ id })),
			attributeValues:
				attributeValues?.map(({ attributeId, attributeValueId }) => ({
					id: attributeValueId,
					attribute: { id: attributeId },
				})) ?? [],
		});

		respond<'createServiceTask'>(true);
	});

	router.post('/bulk', scopeMiddleware('serviceWrite', 'admin', 'warrantyClaimWrite'), async (req) => {
		await new ServiceTaskController().createInBulk(req.body);

		return respond<'bulkCreateServiceTask'>(true);
	});

	router.patch('/:serviceTaskId', scopeMiddleware('serviceWrite', 'admin', 'warrantyClaimWrite'), async (req) => {
		const { serviceTaskId } = req.params;
		const { attributeValues, price, ...rest } = req.body;

		const updateData: Parameters<ServiceTaskController['update']>[1] = { ...rest };

		if (price != null) {
			updateData.price = new Decimal(price);
		}

		if (attributeValues != null) {
			updateData.attributeValues = attributeValues.map(({ attributeValueId }) => ({
				id: attributeValueId,
			}));
		}

		await new ServiceTaskController().update(serviceTaskId, updateData);

		respond<'updateServiceTask'>(true);
	});

	return router;
};
