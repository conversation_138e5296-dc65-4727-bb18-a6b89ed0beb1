import { list<PERSON><PERSON>, ProductController, ProductTaskController, ServiceTaskTypeController } from '@pocitarna-nx-2023/database';
import { productTaskApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond } from '../utils/respond';

export const productTaskRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(productTaskApi);

	router.post('/bulk', scopeMiddleware('batchWrite', 'productTest'), async (req, res) => {
		const serviceTaskType = await new ServiceTaskTypeController().findById(req.body.serviceTaskType);

		if (!serviceTaskType) {
			return res.status(404).json({ _error: { message: 'Service task type not found' } });
		}

		await Promise.all(
			req.body.products.map(
				async (productId: string) =>
					await new ProductTaskController().create({
						product: { id: productId },
						serviceTaskType: { id: req.body.serviceTaskType },
						note: req.body.note,
						price: serviceTaskType.price,
					}),
			),
		);

		respond<'createBulkProductTask'>(true);
	});

	router.get(
		'/product/:productId',
		scopeMiddleware('productRead', 'productWrite', 'testRead', 'productTest', 'batchRead'),
		async (req) => {
			const tasks = await new ProductTaskController().findByProduct(req.params.productId, getListProps());

			return respond<'getProductTasks'>(tasks);
		},
	);

	router.get('/batch/:batchId', scopeMiddleware('testRead', 'productTest'), async (req, res) => {
		const [products] = await new ProductController().findByBatch(req.params.batchId, listAll());
		if (!products) {
			return res.status(404).json({ _error: { message: 'Products not found' } });
		}

		const tasks = await new ProductTaskController().findByProduct(
			products.map((product) => product.id),
			getListProps(),
		);
		if (!tasks) {
			return res.status(404).json({ _error: { message: 'Product tasks not found' } });
		}

		return respond<'getBatchProductTasks'>(tasks);
	});

	router.patch('/:productTaskId', scopeMiddleware('batchWrite', 'productWrite', 'productTest', 'productTestLead'), async (req) => {
		await new ProductTaskController().update(req.params.productTaskId, req.body);
		respond<'updateProductTask'>(true);
	});

	router.delete('/:productTaskId', scopeMiddleware('batchWrite', 'productWrite', 'productTest'), async (req) => {
		const status = await new ProductTaskController().delete(req.params.productTaskId);
		respond<'deleteProductTask'>(status);
	});

	return router;
};
