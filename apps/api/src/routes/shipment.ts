import { ServiceCenterController, ShipmentController, ShipmentProductController, VendorController } from '@pocitarna-nx-2023/database';
import { arraysHaveSameContent } from '@pocitarna-nx-2023/utils';
import { shipmentApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const shipmentRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(shipmentApi);

	router.get('/', async () => {
		const [shipments, getCount] = await new ShipmentController().list(getListProps());
		respondWithPaging<'getShipments'>(shipments, await getCount());
	});

	router.get('/:shipmentId', async (req, res) => {
		const shipment = await new ShipmentController().findById(req.params.shipmentId);
		if (!shipment) return res.status(404).json();

		respond<'getShipment'>(shipment);
	});

	router.patch('/:shipmentId', scopeMiddleware('admin'), async (req, res) => {
		// TODO: handle addresses and contacts
		const shipment = await new ShipmentController().update(req.params.shipmentId, req.body);
		if (!shipment) return res.status(404).json();

		respond<'updateShipment'>(shipment);
	});

	router.patch('/:shipmentId/dispatch', scopeMiddleware('admin'), async (req, res) => {
		const { scannedProductsIds, unscannedProductsIds } = req.body;
		const shipment = await new ShipmentController().findById(req.params.shipmentId);
		if (!shipment) return res.status(404).json({ _error: { message: 'Shipment not found' } });

		const updatedShipment = await new ShipmentController().update(req.params.shipmentId, {
			dispatchedAt: new Date(),
			status: 'DISPATCHED',
		});

		const currentProductsIds = await new ShipmentController().getProductsIds(shipment);

		if (!arraysHaveSameContent(currentProductsIds, scannedProductsIds)) {
			const productsToRemove = currentProductsIds.filter((id) => !scannedProductsIds.includes(id));
			const unlinkPromises = productsToRemove.map((id) => new ShipmentProductController().unlink(updatedShipment.id, id));
			const linkPromises = scannedProductsIds.map((id) => new ShipmentProductController().link(updatedShipment.id, id));
			await Promise.all([...unlinkPromises, ...linkPromises]);
		}

		await new ShipmentController().transitionShipmentProducts(scannedProductsIds, 'SHIPMENT_DISPATCHED');

		if (unscannedProductsIds.length > 0) {
			await new ShipmentController().create({
				address: updatedShipment.address,
				contact: updatedShipment.contact,
				status: 'NEW',
				products: unscannedProductsIds.map((id) => ({ id })),
			});
		}

		respond<'dispatchShipment'>(updatedShipment);
	});

	router.get('/:shipmentId/recipient', async (req, res) => {
		const shipment = await new ShipmentController().findById(req.params.shipmentId);
		if (!shipment) return res.status(404).json();

		const addressId = shipment.address.id;
		const contactId = shipment.contact.id;

		const vendor = await new VendorController().findByAddressAndContact(addressId, contactId);
		if (vendor) return respond<'getShipmentRecipient'>(vendor);

		const serviceCenter = await new ServiceCenterController().findByAddressAndContact(addressId, contactId);
		if (serviceCenter) return respond<'getShipmentRecipient'>(serviceCenter);

		return res.status(404).json();
	});

	return router;
};
