import { ProductController, WarehousePositionController, WarehouseTaskController } from '@pocitarna-nx-2023/database';
import { warehousePositionApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const warehousePositionRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(warehousePositionApi);

	router.get('/', scopeMiddleware('warehouseRead'), async () => {
		const [warehousePositions, getCount] = await new WarehousePositionController().list(getListProps());
		respondWithPaging<'getWarehousePositions'>(warehousePositions, await getCount());
	});

	router.post('/', scopeMiddleware('warehouseManage'), async (req, res) => {
		const { warehouse, ...rest } = req.body;

		const conflictingPosition = await new WarehousePositionController().findConflictingPosition({
			warehouseId: warehouse?.id ?? '',
			...rest,
		});

		if (conflictingPosition) {
			return res.status(409).json({
				conflictingPosition,
			});
		}

		await new WarehousePositionController().create(req.body);

		respond<'createWarehousePosition'>(true);
	});

	router.put('/:warehousePositionId', scopeMiddleware('warehouseManage'), async (req, res) => {
		const { warehouse, sector, rack, shelf, box } = req.body;

		const conflictingPosition = await new WarehousePositionController().findConflictingPosition({
			warehouseId: warehouse?.id ?? '',
			sector: sector ?? null,
			rack: rack ?? null,
			shelf: shelf ?? null,
			box: box ?? null,
			warehousePositionId: req.params.warehousePositionId,
		});

		if (conflictingPosition) {
			return res.status(409).json({
				conflictingPosition,
			});
		}

		const warehousePosition = await new WarehousePositionController().update(req.params.warehousePositionId, req.body);

		if (!warehousePosition) return res.status(404).json();

		respond<'updateWarehousePosition'>(warehousePosition);
	});

	router.get('/:warehousePositionId', scopeMiddleware('warehouseRead'), async (req, res) => {
		const warehousePosition = await new WarehousePositionController().findById(req.params.warehousePositionId);

		if (!warehousePosition) return res.status(404).json();

		respond<'getWarehousePosition'>(warehousePosition);
	});

	router.delete('/:warehousePositionId', scopeMiddleware('warehouseManage'), async (req, res) => {
		const warehousePosition = await new WarehousePositionController().findById(req.params.warehousePositionId);

		if (!warehousePosition) return res.status(404).send();

		if (warehousePosition?.productCount && warehousePosition.productCount > 0) {
			return res.status(409).json({ _error: { message: 'V této pozici se momentálně nacházejí produkty, takže ji nelze smazat.' } });
		}

		const result = await new WarehousePositionController().delete(warehousePosition.id);

		respond<'deleteWarehousePosition'>(result);
	});

	router.get('/:warehousePositionId/products', scopeMiddleware('productRead', 'warehouseRead'), async (req) => {
		const [products, getCount] = await new ProductController().list(
			getListProps({ warehousePositionId: { eq: req.params.warehousePositionId } }),
		);
		respondWithPaging<'getWarehousePositionProducts'>(products, await getCount());
	});

	router.post('/:warehousePositionId/products', scopeMiddleware('productWrite', 'warehouseWrite'), async (req, res) => {
		const position = await new WarehousePositionController().findById(req.params.warehousePositionId);
		if (!position) return res.status(404).json();

		const { products, warehouseTasks } = req.body;

		await Promise.all(
			products.map(async (productId) => new ProductController().setWarehousePosition(productId, req.params.warehousePositionId)),
		);

		if (warehouseTasks.length > 0) {
			await new WarehouseTaskController().close(warehouseTasks);
		}

		respond<'addProductsToWarehousePosition'>(true);
	});

	return router;
};
