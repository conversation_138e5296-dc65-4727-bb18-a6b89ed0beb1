import { DEFAULT_ROLE_NAME } from '@pocitarna-nx-2023/config';
import { Authentication<PERSON><PERSON>roller, RoleController, ScopeController, UserController } from '@pocitarna-nx-2023/database';
import { authAdmin } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { randomUUID } from 'crypto';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const authAdminRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(authAdmin);

	router.get('/role', async () => {
		const [roles, getCount] = await new RoleController().listWithScopes(getListProps());
		respondWithPaging<'getRoles'>(roles, await getCount());
	});

	router.post('/user', async (req) => {
		const user = await new UserController().create({
			...req.body,
			name: req.body.name ?? undefined,
			image: req.body.image ?? undefined,
		});
		respond<'createUserAdmin'>(user);
	});

	router.put('/user/:userId', async (req) => {
		await new UserController().update(req.params.userId, {
			...req.body,
			name: req.body.name ?? undefined,
			image: req.body.image ?? undefined,
		});
		respond<'updateUserAdmin'>(true);
	});

	router.delete('/user/:userId', async (req) => {
		const user = await new UserController().delete(req.params.userId);
		respond<'deleteUserAdmin'>(user);
	});

	router.put('/user/:userId/authentication/:authenticationId', async (req, res) => {
		const entity = await new UserController().findById(req.params.userId);

		if (!entity?.authentications.some((auth) => auth.id === req.params.authenticationId)) return res.status(404).json();

		await new AuthenticationController().update(req.params.authenticationId, {
			role: { id: req.body.role },
			type: req.body.type,
		});

		respond<'updateUserAuthentication'>(true);
	});

	router.post('/role', async (req) => {
		const role = await new RoleController().create({ ...req.body, scopes: req.body.scopes.map((scope) => ({ id: scope })) });
		respond<'createRole'>(role);
	});

	router.delete('/role/:roleId', async (req) => {
		const affectedAuthentications = await new AuthenticationController().findByRole(req.params.roleId);
		const [[defaultRole]] = await new RoleController().list({ filter: { name: { eq: DEFAULT_ROLE_NAME } } });

		if (defaultRole != null && affectedAuthentications.length > 0) {
			await Promise.all(
				affectedAuthentications.map(async (authentication) => {
					return await new AuthenticationController().update(authentication.id, { role: { id: defaultRole.id } });
				}),
			);
		}

		const role = await new RoleController().delete(req.params.roleId);
		respond<'deleteRole'>(role);
	});

	router.put('/role/:roleId', async (req) => {
		const role = await new RoleController().update(req.params.roleId, {
			...req.body,
			scopes: req.body.scopes.map((scope) => ({ id: scope })),
		});
		respond<'updateRole'>(role);
	});

	router.get('/scope', async () => {
		const [scopes, getCount] = await new ScopeController().list(getListProps());
		respondWithPaging<'getScopes'>(scopes, await getCount());
	});

	router.get('/user', async () => {
		const [users, getCount] = await new UserController().list(getListProps());
		return respondWithPaging<'getUsers'>(users, await getCount());
	});

	router.get('/scope/:scopeName/user', async (req) => {
		const [users, getCount] = await new UserController().list(getListProps({ 'scope.name': { eq: req.params.scopeName } }));

		return respondWithPaging<'getUsersByScope'>(users, await getCount());
	});

	router.post('/user/:userId/authentication', async (req) => {
		if (req.body.type === 'azure-ad') {
			await new AuthenticationController().createAuthentication(req.params.userId, {
				type: req.body.type,
				role: { id: req.body.role },
				credentials: { account: { type: 'azure-ad' } },
			});
		}

		if (req.body.type === 'verificationCode') {
			await new AuthenticationController().createAuthentication(req.params.userId, {
				type: req.body.type,
				role: { id: req.body.role },
				credentials: { verificationToken: { token: randomUUID() } },
			});
		}

		if (req.body.type === 'password') {
			//TODO: finish if needed
		}

		respond<'createUserAuthentication'>(true);
	});

	router.delete('/user/:userId/authentication/:authenticationId', async (req, res) => {
		const entity = await new UserController().findById(req.params.userId);

		if (!entity?.authentications.some((auth) => auth.id === req.params.authenticationId)) return res.status(404).json();

		await new AuthenticationController().delete(req.params.authenticationId);
		respond<'deleteUserAuthentication'>(true);
	});

	return router;
};
