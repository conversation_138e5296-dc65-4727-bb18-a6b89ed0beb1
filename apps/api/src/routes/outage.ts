import { OutageController } from '@pocitarna-nx-2023/database';
import { publishInvalidation } from '@pocitarna-nx-2023/sse-server';
import { outageApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { respond } from '../utils/respond';

export const outageRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(outageApi);

	router.get('/', scopeMiddleware('admin'), async () => {
		const outage = await new OutageController().getCurrent();
		respond<'getOutageStatus'>(outage);
	});

	router.post('/', scopeMiddleware('admin'), async () => {
		const outage = await new OutageController().toggle();
		publishInvalidation('getOutageStatus');
		respond<'toggleOutageStatus'>(outage);
	});

	return router;
};
