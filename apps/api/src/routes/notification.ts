import { NotificationController, useAuthentication } from '@pocitarna-nx-2023/database';
import { notificationApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const notificationRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(notificationApi);

	router.get('/', scopeMiddleware('home'), async (_, res) => {
		const authentication = useAuthentication();
		const user = authentication?.user;
		if (!user) return res.status(401).json();

		const [notifications, getCount] = await new NotificationController().list(getListProps({ userId: { eq: user.id } }));
		respondWithPaging<'getNotifications'>(notifications, await getCount());
	});

	router.patch('/:notificationId', scopeMiddleware('home'), async (req, res) => {
		const { notificationId } = req.params;
		const authentication = useAuthentication();
		const user = authentication?.user;
		if (!user) return res.status(401).json();

		const notification = await new NotificationController().findById(notificationId);
		if (!notification) return res.status(404).json();
		if (notification.userId !== user.id) return res.status(403).json();

		await new NotificationController().update(notification, req.body);
		respond<'updateNotification'>(true);
	});

	return router;
};
