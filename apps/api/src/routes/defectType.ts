import { DefectTypeController } from '@pocitarna-nx-2023/database';
import { defectTypeApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const defectTypeRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(defectTypeApi);

	router.post('/', scopeMiddleware('admin'), async (req, res) => {
		const defectType = await new DefectTypeController().create(req.body);
		if (!defectType) return res.status(500).json({ _error: { message: 'Could not create defect type' } });

		respond<'createDefectType'>(true);
	});

	router.get(
		'/',
		scopeMiddleware(
			'admin',
			'serviceRead',
			'serviceWrite',
			'warrantyClaimRead',
			'warrantyClaimWrite',
			'testRead',
			'productTest',
			'productRead',
			'productWrite',
		),
		async () => {
			const [data, getCount] = await new DefectTypeController().list(getListProps());

			return respondWithPaging<'getDefectTypes'>(data, await getCount());
		},
	);

	router.get(
		'/:defectTypeId',
		scopeMiddleware(
			'admin',
			'serviceRead',
			'serviceWrite',
			'warrantyClaimRead',
			'warrantyClaimWrite',
			'testRead',
			'productTest',
			'productRead',
			'productWrite',
		),
		async (req, res) => {
			const defectType = await new DefectTypeController().findById(req.params.defectTypeId);

			if (!defectType) {
				return res.status(404).json({ _error: { message: 'Defect type not found' } });
			}

			return respond<'getDefectType'>(defectType);
		},
	);

	router.patch('/:defectTypeId', scopeMiddleware('admin'), async (req, res) => {
		const defectType = await new DefectTypeController().update(req.params.defectTypeId, req.body);
		if (!defectType) return res.status(404).json();

		respond<'updateDefectType'>(true);
	});

	router.delete('/:defectTypeId', scopeMiddleware('admin'), async (req, res) => {
		const defectType = await new DefectTypeController().findById(req.params.defectTypeId);
		if (!defectType) return res.status(404).send();

		const result = await new DefectTypeController().delete(defectType.id);

		respond<'deleteDefectType'>(result);
	});

	return router;
};
