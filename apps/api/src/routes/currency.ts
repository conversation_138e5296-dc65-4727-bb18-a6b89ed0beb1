import { CurrencyController, CurrencyRateController } from '@pocitarna-nx-2023/database';
import { currencyApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const currencyRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(currencyApi);

	router.get('/', scopeMiddleware('batchRead', 'batchWrite'), async () => {
		const [currencies] = await new CurrencyController().listWithLatestRates(getListProps());
		return respond<'getCurrencies'>(currencies);
	});

	router.get('/rate/:currencyRateId', scopeMiddleware('batchRead', 'batchWrite'), async (req, res) => {
		const currencyRate = await new CurrencyRateController().findById(req.params.currencyRateId);

		if (!currencyRate) return res.status(404).json({ _error: { message: 'Currency rate not found' } });

		return respond<'getCurrencyRate'>(currencyRate);
	});

	router.get('/:currencyCode/rate', scopeMiddleware('batchRead', 'batchWrite'), async (req) => {
		const [results, getCount] = await new CurrencyRateController().findRatesByCurrency(req.params.currencyCode, getListProps());

		return respondWithPaging<'getCurrencyRatesByCode'>(results, await getCount());
	});

	return router;
};
