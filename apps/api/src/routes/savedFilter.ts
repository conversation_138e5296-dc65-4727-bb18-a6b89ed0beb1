import { SavedFilterController, useAuthentication } from '@pocitarna-nx-2023/database';
import { savedFilterApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { respond } from '../utils/respond';

export const savedFilterRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(savedFilterApi);

	router.post('/', scopeMiddleware('home'), async (req, res) => {
		const authentication = useAuthentication();
		if (!authentication) return res.status(401).json();
		const userId = authentication.user.id;

		const savedFilter = await new SavedFilterController().create({ ...req.body, user: { id: userId } });

		if (!savedFilter) return res.status(500).json();

		respond<'createSavedFilter'>(savedFilter);
	});

	router.get('/', scopeMiddleware('home'), async (req, res) => {
		const authentication = useAuthentication();
		if (!authentication) return res.status(401).json();
		const userId = authentication.user.id;

		const [savedFilters] = await new SavedFilterController().listByUserAndEndpoint(userId, req.query.endpoint);
		respond<'getAllSavedFilters'>(savedFilters);
	});

	router.delete('/:savedFilterId', scopeMiddleware('home'), async (req, res) => {
		const savedFilter = await new SavedFilterController().findById(req.params.savedFilterId);

		if (!savedFilter) return res.status(404).send();

		const result = await new SavedFilterController().delete(savedFilter.id);

		respond<'deleteSavedFilter'>(result);
	});

	return router;
};
