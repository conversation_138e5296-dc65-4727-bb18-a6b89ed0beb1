import { CODE_PREFIX, PAGINATION_LIMIT } from '@pocitarna-nx-2023/config';
import {
	type Bat<PERSON>,
	BatchController,
	type EcommerceOrder,
	EcommerceOrderController,
	type Product,
	ProductController,
	type ProductEnvelope,
	ProductEnvelopeController,
	type ServiceCase,
	ServiceCaseController,
	VendorController,
	type WarrantyClaim,
	WarrantyClaimController,
} from '@pocitarna-nx-2023/database';
import { stripCode, stripCodePrefix, uniquesBy } from '@pocitarna-nx-2023/utils';
import { searchApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond } from '../utils/respond';

export const searchRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(searchApi);

	router.get('/', scopeMiddleware('home'), async (req) => {
		const query = req.query.query;

		if (!query) return respond<'search'>(null);

		const queryStripped = stripCode(query);
		const queryCodePrefix = stripCodePrefix(query);
		const hasKnownPrefix = !!queryCodePrefix && Object.values(CODE_PREFIX).includes(queryCodePrefix);
		const page = { page: 1, limit: PAGINATION_LIMIT };

		const [
			batchesByName,
			batchByCode,
			vendorsByName,
			productBySN,
			productsByPastSn,
			productsByCode,
			productEnvelopesByName,
			productEnvelopesByCode,
			serviceCasesByCode,
			warrantyClaimsByCode,
			ecommerceOrdersByCode,
		] = await Promise.all([
			// batchesByName
			new BatchController().list({ filter: { name: { eq: `%${query}%` } }, page }),

			// batchByCode
			queryStripped && (!queryCodePrefix || queryCodePrefix === CODE_PREFIX.BATCH)
				? new BatchController().list({
						filter: !queryCodePrefix
							? { 'code.code::varchar': { eq: `%${queryStripped}%` } }
							: { 'code.code': { eq: queryStripped } },
						page,
					})
				: new Promise((resolve) => resolve(null)),

			// vendorsByName
			new VendorController().list({ filter: { name: { eq: `%${query}%` } }, page }),

			// productBySN
			new ProductController().findBySn(query.toUpperCase()),

			// productsByPastSn
			new ProductController().list({ filter: { pastSn: { eq: `%${query}%` } }, page }),

			// productsByCode
			queryStripped && (!queryCodePrefix || queryCodePrefix === CODE_PREFIX.PRODUCT)
				? new ProductController().list({
						filter: !queryCodePrefix
							? { 'code.code::varchar': { eq: `%${queryStripped}%` } }
							: { 'code.code': { eq: queryStripped } },
						sort: ['code.code'],
					})
				: new Promise((resolve) => resolve(null)),

			// productEnvelopesByName
			new ProductEnvelopeController().list({ filter: { name: { eq: `%${query}%` } }, page }),

			// productEnvelopesByCode
			queryStripped && (!queryCodePrefix || !hasKnownPrefix)
				? new ProductEnvelopeController().list({
						filter: !queryCodePrefix
							? { 'code.code::varchar': { eq: `%${queryStripped}%` } }
							: { 'code.code': { eq: queryStripped } },
						sort: ['code.code'],
						page,
					})
				: new Promise((resolve) => resolve(null)),

			// serviceCasesByCode
			queryStripped && (!queryCodePrefix || queryCodePrefix === CODE_PREFIX.SERVICE_CASE)
				? new ServiceCaseController().list({
						filter: !queryCodePrefix
							? { 'code.code::varchar': { eq: `%${queryStripped}%` } }
							: { 'code.code': { eq: queryStripped } },
						sort: ['code.code'],
						page,
					})
				: new Promise((resolve) => resolve(null)),

			// warrantyClaimsByCode
			queryStripped && (!queryCodePrefix || queryCodePrefix === CODE_PREFIX.WARRANTY_CLAIM)
				? new WarrantyClaimController().list({
						filter: !queryCodePrefix
							? { 'code.code::varchar': { eq: `%${queryStripped}%` } }
							: { 'code.code': { eq: queryStripped } },
						sort: ['code.code'],
						page,
					})
				: new Promise((resolve) => resolve(null)),
			// ecommerceOrdersByCode
			queryStripped && !isNaN(Number(queryStripped))
				? new EcommerceOrderController().list({ filter: { code: { eq: `%${queryStripped}%` } }, sort: ['code'], page })
				: new Promise((resolve) => resolve(null)),
		]);

		const batchesByNameData = batchesByName[0];
		const batchByCodeData = Array.isArray(batchByCode) ? batchByCode[0] : [];
		const vendorByNameData = vendorsByName[0];
		const productsBySNData = productBySN ? [productBySN] : [];
		const productsByPastSnData = productsByPastSn[0];
		const productsByCodeData = Array.isArray(productsByCode) ? productsByCode[0] : [];
		const productEnvelopesByNameData = productEnvelopesByName[0];
		const productEnvelopesByCodeData = Array.isArray(productEnvelopesByCode) ? productEnvelopesByCode[0] : [];
		const serviceCasesByCodeData = Array.isArray(serviceCasesByCode) ? serviceCasesByCode[0] : [];
		const warrantyClaimsByCodeData = Array.isArray(warrantyClaimsByCode) ? warrantyClaimsByCode[0] : [];
		const ecommerceOrdersByCodeData = Array.isArray(ecommerceOrdersByCode) ? ecommerceOrdersByCode[0] : [];

		respond<'search'>({
			batches: uniquesBy([...batchesByNameData, ...(batchByCodeData as Batch[])], 'id'),
			vendors: vendorByNameData,
			products: uniquesBy([...productsBySNData, ...productsByPastSnData, ...(productsByCodeData as Product[])], 'id'),
			productEnvelopes: uniquesBy([...productEnvelopesByNameData, ...(productEnvelopesByCodeData as ProductEnvelope[])], 'id'),
			serviceCases: uniquesBy(serviceCasesByCodeData as ServiceCase[], 'id'),
			warrantyClaims: uniquesBy(warrantyClaimsByCodeData as WarrantyClaim[], 'id'),
			ecommerceOrders: uniquesBy(ecommerceOrdersByCodeData as EcommerceOrder[], 'id'),
		});
	});

	router.get('/product/envelope', scopeMiddleware('home'), async (req) => {
		const query = req.query.query;
		const listProps = getListProps();

		if (!query) return respond<'searchProductEnvelope'>([]);

		const queryStripped = stripCode(query);
		const queryCodePrefix = stripCodePrefix(query);
		const hasPrefix = !!queryCodePrefix;

		const [productEnvelopesByName, productEnvelopesByCode, productEnvelopesByEan, productEnvelopesByVendorCode] = await Promise.all([
			// productEnvelopesByName
			new ProductEnvelopeController().list({
				...listProps,
				filter: {
					...listProps.filter,
					name: { eq: `%${query}%` },
				},
			}),

			// productEnvelopesByCode
			queryStripped && (!queryCodePrefix || !hasPrefix)
				? new ProductEnvelopeController().list({
						...listProps,
						filter: {
							...listProps.filter,
							...(!queryCodePrefix
								? { 'code.code::varchar': { eq: `%${queryStripped}%` } }
								: { 'code.code': { eq: queryStripped } }),
						},
						sort: ['code.code'],
					})
				: new Promise((resolve) => resolve(null)),

			// productEnvelopesByEan
			new ProductEnvelopeController().findByEan(query.toUpperCase(), {
				...listProps,
				filter: listProps.filter,
			}),

			// productEnvelopesByVendorCode
			new ProductEnvelopeController().findByVendorCode(query.toUpperCase(), {
				...listProps,
				filter: listProps.filter,
			}),
		]);

		const productEnvelopesByNameData = productEnvelopesByName[0];
		const productEnvelopesByCodeData = Array.isArray(productEnvelopesByCode) ? productEnvelopesByCode[0] : [];

		respond<'searchProductEnvelope'>(
			uniquesBy(
				[
					...productEnvelopesByNameData,
					...(productEnvelopesByCodeData as ProductEnvelope[]),
					...(productEnvelopesByEan as ProductEnvelope[]),
					...(productEnvelopesByVendorCode as ProductEnvelope[]),
				],
				'id',
			),
		);
	});

	return router;
};
