import { ShoptetAttributeController } from '@pocitarna-nx-2023/database';
import { shoptetAttributeApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const shoptetAttributeRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(shoptetAttributeApi);

	router.get('/', scopeMiddleware('admin'), async (req) => {
		const { unassigned } = req.query;
		if (unassigned) {
			const [entities, getCount] = await new ShoptetAttributeController().listUnassigned(getListProps());
			return respondWithPaging<'listShoptetAttributes'>(entities, await getCount());
		}

		const [entities, getCount] = await new ShoptetAttributeController().list(getListProps());
		respondWithPaging<'listShoptetAttributes'>(entities, await getCount());
	});

	router.get('/:shoptetAttributeId', scopeMiddleware('admin'), async (req, res) => {
		const shoptetAttribute = await new ShoptetAttributeController().findById(req.params.shoptetAttributeId);
		if (!shoptetAttribute) return res.status(404).json();
		respond<'getShoptetAttribute'>(shoptetAttribute);
	});

	return router;
};
