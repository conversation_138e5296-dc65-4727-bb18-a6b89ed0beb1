import { CosmeticAreaController } from '@pocitarna-nx-2023/database';
import { cosmeticAreaApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const cosmeticAreaRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(cosmeticAreaApi);

	router.post('/', scopeMiddleware('admin'), async (req, res) => {
		const cosmeticArea = await new CosmeticAreaController().create(req.body);
		if (!cosmeticArea) return res.status(500).json();

		respond<'createCosmeticArea'>(true);
	});

	router.get('/', scopeMiddleware('admin', 'batchRead', 'batchWrite'), async () => {
		const [cosmeticAreas, getCount] = await new CosmeticAreaController().list(getListProps());
		respondWithPaging<'getCosmeticAreas'>(cosmeticAreas, await getCount());
	});

	router.get('/:cosmeticAreaId', scopeMiddleware('home'), async (req, res) => {
		const cosmeticArea = await new CosmeticAreaController().findById(req.params.cosmeticAreaId);
		if (!cosmeticArea) return res.status(404).json();

		respond<'getCosmeticArea'>(cosmeticArea);
	});

	router.patch('/:cosmeticAreaId', scopeMiddleware('admin'), async (req) => {
		await new CosmeticAreaController().update(req.params.cosmeticAreaId, req.body);

		respond<'updateCosmeticArea'>(true);
	});

	router.delete('/:cosmeticAreaId', scopeMiddleware('admin'), async (req, res) => {
		const cosmeticArea = await new CosmeticAreaController().findById(req.params.cosmeticAreaId);
		if (!cosmeticArea) return res.status(404).send();

		const result = await new CosmeticAreaController().delete(cosmeticArea.id);

		respond<'deleteCosmeticArea'>(result);
	});

	return router;
};
