import { IS_PROD, IS_STAGING, STAGE } from '@pocitarna-nx-2023/config';
import * as Sentry from '@sentry/node';
import { nodeProfilingIntegration } from '@sentry/profiling-node';

if (IS_STAGING || IS_PROD) {
	Sentry.init({
		dsn: 'https://<EMAIL>/4507792460742736',
		environment: `${STAGE}-api`,
		integrations: [
			nodeProfilingIntegration(),
			Sentry.expressIntegration(),
			Sentry.postgresIntegration(),
			Sentry.zodErrorsIntegration(),
		],
		tracesSampler: (context) =>
			!['GET /', 'GET /auth/:userId', 'GET /auth/session/:sessionToken', 'PATCH /auth/session', 'GET /sse'].includes(context.name),
		tracesSampleRate: 0.25,
		profileSessionSampleRate: 0.25,
	});
}
