import type { Request<PERSON><PERSON><PERSON>, Router } from 'express';

// This is type unsafe, because zod<PERSON> wraps express router but requires its type to also match type of context, which I cannot guarantee universally.
// We only ever access internal workings of express router, so it should be safe to recast the type
// Layers inside the router are not typed anyway 😭
export const errorBoundary = (router: unknown) => {
	const expressRouter = router as Router;
	expressRouter.stack.forEach((layer) => {
		if (layer.route) {
			layer.route.stack.forEach(handleLayer);
		} else if (layer.name === 'router') {
			errorBoundary(layer.handle);
		} else {
			handleLayer(layer);
		}
	});
};

const handleLayer = (layer: Router['stack'][number]) => {
	if (layer.handle && layer.handle.constructor.name === 'AsyncFunction') {
		const original = layer.handle;
		const handle: RequestHandler = async (req, res, next) => {
			try {
				await original(req, res, next);
			} catch (err) {
				next(err);
			}
		};
		layer.handle = handle;
	}
};
