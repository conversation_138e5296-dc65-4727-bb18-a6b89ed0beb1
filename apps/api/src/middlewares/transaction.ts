import { ENTITY_MANAGER_LABEL } from '@pocitarna-nx-2023/config';
import { Database } from '@pocitarna-nx-2023/database';
import type { RequestHandler } from 'express';
import * as httpContext from 'express-http-context';
import { type EntityManager } from 'typeorm';
import { asyncHandler } from '../utils/asyncHandler';

export const createTransactionMiddleware: RequestHandler = asyncHandler((req, res, next) => {
	if (req.method === 'GET' || req.method === 'HEAD' || req.method === 'OPTIONS') return next();

	return Database.transaction(async (entityManager) => {
		return new Promise<void>((resolve, reject) => {
			httpContext.set(ENTITY_MANAGER_LABEL, entityManager);
			res.on('error', (err) => reject(err));
			res.on('close', () => resolve());
			return next();
		});
	});
});

export const rollbackTransaction = async () => {
	const entityManager = httpContext.get(ENTITY_MANAGER_LABEL) as EntityManager | undefined;
	if (
		!entityManager ||
		!entityManager.queryRunner ||
		!entityManager.queryRunner.isTransactionActive ||
		entityManager.queryRunner.isReleased
	)
		return;

	return entityManager.queryRunner.rollbackTransaction();
};
