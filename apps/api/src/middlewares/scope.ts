import { type ScopeName } from '@pocitarna-nx-2023/config';
import { useAuthentication } from '@pocitarna-nx-2023/database';
import { type NextFunction, type Response } from 'express';

export const scopeMiddleware = (...scopes: ScopeName[]) => {
	return async (_req: unknown, res: Response, next: NextFunction) => {
		const authentication = useAuthentication();
		if (!authentication) return res.status(401).json({ _error: { message: 'User not found' } });

		const userScope = authentication?.role.scopes?.some((s) => s.name === 'admin' || scopes.includes(s.name));
		if (!userScope) return res.status(401).json({ _error: { message: 'Unauthorized' } });

		next();
	};
};
