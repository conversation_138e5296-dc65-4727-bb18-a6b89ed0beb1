import type { Request, RequestHandler, Response } from 'express';
import * as httpContext from 'express-http-context';

export const useRequest = (): Request => httpContext.get('request');
export const useResponse = (): Response => httpContext.get('response');

export const contextMiddleware: RequestHandler = (req, res, next) => {
	res.set('X-Clacks-Overhead', 'GNU Terry Pratchett');
	res.set('Connection', 'keep-alive');
	res.set('Keep-Alive', 'timeout=60');
	res.set('Cache-Control', 'no-cache, no-store, must-revalidate');
	httpContext.set('request', req);
	httpContext.set('response', res);
	next();
};
