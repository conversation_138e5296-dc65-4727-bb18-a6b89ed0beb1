import { safeDivide } from '@pocitarna-nx-2023/utils';
import { type ApiAliases, type ApiBody } from '@pocitarna-nx-2023/zodios';
import { useRequest, useResponse } from '../middlewares/contextMiddleware';
import { getPagingContext } from './getPagingContext';

const getCode = () => {
	const req = useRequest();
	if (req.method === 'DELETE') return 204;
	if (['POST', 'PUT'].includes(req.method)) return 201;
	return 200;
};

export const respond = <Alias extends ApiAliases>(data: ApiBody<Alias, false>) => {
	const res = useResponse();
	const code = getCode();
	const response = { _data: data, _status: code };
	res.status(code).json(response);
};

export const respondWithPaging = <Alias extends ApiAliases>(data: ApiBody<Alias, false>, total = 0) => {
	const res = useResponse();
	const code = getCode();
	const { page, limit } = getPagingContext();
	const totalPages = Math.max(Math.ceil(safeDivide(total, limit)), 1);
	const nextPage = page < totalPages ? page + 1 : null;
	const previousPage = page > 1 ? page - 1 : null;
	const response = { _status: code, _data: data, _paging: { page, total, limit, totalPages, nextPage, previousPage } };
	res.status(code).json(response);
};
