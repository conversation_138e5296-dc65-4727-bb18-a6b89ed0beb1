import { PAGINATION_LIMIT } from '@pocitarna-nx-2023/config';
import type { Api, Paging } from '@pocitarna-nx-2023/zodios';
import type { ZodiosQueryParamsByAlias } from '@zodios/core';
import { useRequest } from '../middlewares/contextMiddleware';

export const getPagingContext = (): Paging => {
	// Using healthcheck as dummy route to strongly type query
	const { page, limit } = useRequest().query as unknown as ZodiosQueryParamsByAlias<Api, 'healthcheck', false>;

	return { page: page ?? 1, limit: limit ?? PAGINATION_LIMIT };
};
