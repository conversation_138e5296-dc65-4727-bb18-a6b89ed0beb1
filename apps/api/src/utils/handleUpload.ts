import type { Request } from 'express';

export const handleUpload = (req: Request): Promise<Uint8Array> =>
	new Promise((resolve, reject) => {
		let body = new Uint8Array();

		req.on('error', (error) => reject(error));

		req.on('data', (chunk: Uint8Array) => {
			body = new Uint8Array([...body, ...chunk]);
		});

		req.on('end', () => resolve(body));
	});

export const handleUploadBase64 = (req: Request): Uint8Array => Buffer.from(req.body.data, 'base64');
