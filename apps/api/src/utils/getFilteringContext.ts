import type { Api, Filtering } from '@pocitarna-nx-2023/zodios';
import type { ZodiosQueryParamsByAlias } from '@zodios/core';
import { useRequest } from '../middlewares/contextMiddleware';

export const getFilteringContext = (): Filtering | undefined => {
	// Using healthcheck as dummy route to strongly type query
	const { filter } = useRequest().query as unknown as ZodiosQueryParamsByAlias<Api, 'healthcheck', false>;
	return filter;
};
