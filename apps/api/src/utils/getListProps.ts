import type { Filtering, ListProps } from '@pocitarna-nx-2023/zodios';
import { getFilteringContext } from './getFilteringContext';
import { getPagingContext } from './getPagingContext';
import { getSortingContext } from './getSortingContext';

export const getListProps = (customFilter?: Filtering): ListProps => {
	const baseFilter = getFilteringContext();
	const page = getPagingContext();
	const sort = getSortingContext();

	const filter = { ...(baseFilter ?? {}), ...(customFilter ?? {}) };

	return { filter, page, sort };
};
