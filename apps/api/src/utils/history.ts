import { PUBLIC_CUSTOMER_USER_NAME } from '@pocitarna-nx-2023/config';
import { type Action } from '@pocitarna-nx-2023/database';

export type HistoricalEntity = { action: Action };
export type HistoriesObject = Record<string, HistoricalEntity[]>;
export type MergedHistoriesResult<T extends HistoriesObject> = { action: Action; entities: T }[];

export const groupBySubsequentActions = <T extends HistoricalEntity>(entities: T[]): T[][] => {
	if (!entities.length) return [];

	const result: T[][] = [];
	let currentGroup: T[] = [entities[0]];

	for (let i = 1; i < entities.length; i++) {
		const currentEntity = entities[i];
		const previousEntity = entities[i - 1];

		if (
			currentEntity.action.method === previousEntity.action.method &&
			currentEntity.action.endpoint === previousEntity.action.endpoint
		) {
			currentGroup.push(currentEntity);
		} else {
			result.push(currentGroup);
			currentGroup = [currentEntity];
		}
	}

	if (currentGroup.length > 0) {
		result.push(currentGroup);
	}

	return result;
};

export const mergeHistoriesByAction = <T extends HistoriesObject>(histories: T, shouldGroup = false): MergedHistoriesResult<T> => {
	const allActions: {
		action: HistoricalEntity['action'];
		key: string;
		entities: HistoricalEntity[];
	}[] = [];
	const result: MergedHistoriesResult<T> = [];

	if (shouldGroup) {
		const groupedHistories: Record<string, HistoricalEntity[][]> = {};
		for (const [key, entities] of Object.entries(histories)) {
			groupedHistories[key] = groupBySubsequentActions(entities);
		}

		for (const [key, groups] of Object.entries(groupedHistories)) {
			if (groups.length === 0) continue;
			for (const group of groups) {
				if (group.length === 0) continue;
				allActions.push({
					action: group[0].action,
					key,
					entities: group,
				});
			}
		}
	} else {
		for (const [key, entities] of Object.entries(histories)) {
			if (entities.length === 0) continue;
			for (const entity of entities) {
				allActions.push({
					action: entity.action,
					key,
					entities: [entity],
				});
			}
		}
	}

	allActions.sort((a, b) => new Date(b.action.createdAt).getTime() - new Date(a.action.createdAt).getTime());

	for (const { action, key, entities } of allActions) {
		const existingAction = result.find(
			(item) =>
				item.action.method === action.method &&
				item.action.endpoint === action.endpoint &&
				item.action.createdAt.getTime() === action.createdAt.getTime(),
		);

		if (existingAction) {
			if (!existingAction.entities[key]) {
				// @ts-expect-error - I don't care :)
				existingAction.entities[key] = [];
			}
			existingAction.entities[key].push(...entities);
		} else {
			const newAction = {
				action,
				entities: {
					[key]: entities,
				} as T,
			};
			result.push(newAction);
		}
	}

	return result;
};

// Temporary hack to remove user-names before sending them to frontend
export const maskPublicHistory = (historyEntries: HistoricalEntity[]) => {
	return historyEntries.map((item) => ({
		...item,
		action: {
			...item.action,
			authentication: {
				...item.action.authentication,
				user: {
					...item.action.authentication.user,
					name: item.action.authentication.user.name === PUBLIC_CUSTOMER_USER_NAME ? PUBLIC_CUSTOMER_USER_NAME : 'Počítařná',
				},
			},
		},
	}));
};
