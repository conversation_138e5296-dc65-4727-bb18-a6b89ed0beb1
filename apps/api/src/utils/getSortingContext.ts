import type { Api, Sorting } from '@pocitarna-nx-2023/zodios';
import type { ZodiosQueryParamsByAlias } from '@zodios/core';
import { useRequest } from '../middlewares/contextMiddleware';

export const getSortingContext = (): Sorting | undefined => {
	// Using healthcheck as dummy route to strongly type query
	const { sort } = useRequest().query as unknown as ZodiosQueryParamsByAlias<Api, 'healthcheck', false>;
	return sort;
};
