FROM gitlab.superkoders.com:5050/sk/pocitarna-nx-2023/node:22-alpine AS builder

WORKDIR /app

COPY ["package*.json", "./"]
COPY .npm ./.npm

RUN npm ci --cache .npm --prefer-offline --no-audit --ignore-scripts --fund=false

COPY ["nx.json", "tsconfig.base.json", "./"]
COPY modules ./modules
COPY apps/api ./apps/api

RUN npx nx run api:build \
    && cd dist/apps/api \
	&& npm ci --cache ../../../.npm --prefer-offline --no-audit --ignore-scripts --fund=false

#

FROM gitlab.superkoders.com:5050/sk/pocitarna-nx-2023/node:22-alpine AS runner

WORKDIR /app

RUN apk add --no-cache curl openssl

COPY --from=builder /app/dist/apps/api .

CMD ["main.js"]
