import { execSync } from 'node:child_process';
import { rm } from 'node:fs/promises';
import * as os from 'node:os';
import * as path from 'node:path';
import AdmZip from 'adm-zip';
import * as plist from 'plist';

const isWin32 = process.platform === 'win32';
const LIB_PATH = path.join(__dirname, '/assets/libimobiledevice-win-x64.zip');
const LIB_TEMP_PATH = path.join(os.tmpdir(), 'libimobiledevice');

const DEVICE_INFO_PATH = isWin32 ? path.join(LIB_TEMP_PATH, 'ideviceinfo.exe') : 'ideviceinfo';
const DEVICE_DIAGNOSTICS_PATH = isWin32 ? path.join(LIB_TEMP_PATH, 'idevicediagnostics.exe') : 'idevicediagnostics';
const shell = isWin32 ? 'cmd.exe' : '/bin/sh';

type Keys = 'device' | 'disk' | 'battery' | 'legacyBattery' | 'regulatoryModelNumber';

export const getDeviceInfo = async () => {
	if (isWin32) {
		new AdmZip(LIB_PATH).extractAllTo(LIB_TEMP_PATH, true);
	}

	const commands: Record<Keys, string> = {
		device: `${DEVICE_INFO_PATH} --xml`,
		disk: `${DEVICE_INFO_PATH} --xml --domain com.apple.disk_usage`,
		battery: `${DEVICE_DIAGNOSTICS_PATH} ioregentry AppleSmartBattery`,
		legacyBattery: `${DEVICE_DIAGNOSTICS_PATH} ioregentry AppleARMPMUCharger`,
		regulatoryModelNumber: `${DEVICE_INFO_PATH} --key RegulatoryModelNumber`,
	};

	const result: Record<Keys, plist.PlistValue> = {
		device: {},
		disk: {},
		battery: {},
		legacyBattery: {},
		regulatoryModelNumber: {},
	};

	for (const key of Object.keys(commands)) {
		try {
			const stdout = execSync(commands[key as Keys], { shell, encoding: 'utf-8' })
				.toString()
				.trim()
				.replace(/\n\r/g, '');
			if (stdout.startsWith('<')) {
				result[key as Keys] = plist.parse(stdout);
			} else {
				result[key as Keys] = stdout;
			}
		} catch {
			console.warn('Command failed', commands[key as Keys]);
		}
	}

	if (isWin32) {
		try {
			await rm(LIB_TEMP_PATH, { recursive: true, force: true });
		} catch {
			// ignore
		}
	}

	return result;
};
