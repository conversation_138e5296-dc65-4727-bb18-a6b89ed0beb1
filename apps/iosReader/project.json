{"name": "iosReader", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/iosReader/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/iosReader", "main": "apps/iosReader/src/index.ts", "tsConfig": "apps/iosReader/tsconfig.app.json", "assets": ["apps/iosReader/src/assets"], "webpackConfig": "apps/iosReader/webpack.config.js", "outputFileName": "ios.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "iosReader:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "iosReader:build:development"}, "production": {"buildTarget": "iosReader:build:production"}}}, "package": {"cache": true, "executor": "nx:run-commands", "defaultConfiguration": "dev", "dependsOn": ["build"], "options": {"commands": ["cp dist/apps/iosReader/assets/env.${STAGE}.json dist/apps/iosReader/assets/env.json", "pkg dist/apps/iosReader/ios.js --targets node20-win-x64,node20-win-arm64,node20-macos-x64,node20-macos-arm64,node20-linux-x64,node20-linux-arm64 --out-path dist/apps/iosReader/bin"], "env": {"PKG_CACHE_PATH": "tmp/apps/iosReader"}, "parallel": false, "dependsOn": ["build"]}, "configurations": {"dev": {"env": {"STAGE": "dev"}}, "staging": {"env": {"STAGE": "staging"}}, "prod": {"env": {"STAGE": "prod"}}}}, "lint": {}, "typecheck": {}}}