{"name": "@pocitarna-nx-2023/photo", "version": "0.0.1", "private": true, "dependencies": {"@fortawesome/fontawesome-common-types": "*", "@fortawesome/fontawesome-svg-core": "*", "@fortawesome/pro-regular-svg-icons": "*", "@fortawesome/react-native-fontawesome": "*", "@react-native-cookies/cookies": "*", "@react-native/metro-config": "*", "@react-navigation/bottom-tabs": "*", "@react-navigation/drawer": "*", "@react-navigation/native": "*", "@react-navigation/native-stack": "*", "@sentry/react-native": "*", "@tanstack/react-query": "*", "@zodios/core": "*", "@zodios/react": "*", "async-mutex": "*", "axios": "*", "date-fns": "*", "date-fns-tz": "*", "decimal.js": "*", "deep-diff": "*", "eventsource": "*", "file-type": "*", "rambdax": "*", "react": "*", "react-native": "*", "react-native-config": "*", "react-native-datawedge-intents": "*", "react-native-gesture-handler": "*", "react-native-reanimated": "*", "react-native-safe-area-context": "*", "react-native-screens": "*", "react-native-sse": "*", "react-native-svg": "*", "react-native-svg-transformer": "*", "react-native-vision-camera": "*", "rooks": "*", "sonner": "*", "tslib": "*", "xlsx": "*", "zod": "*", "zustand": "*"}, "devDependencies": {}}