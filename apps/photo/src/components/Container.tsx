import { ONE_SECOND } from '@pocitarna-nx-2023/config';
import { useQueryClient } from '@tanstack/react-query';
import { type ComponentProps, type ReactElement, type ReactNode, useCallback, useState } from 'react';
import { FlatList, RefreshControl, ScrollView, View } from 'react-native';
import type { FlatListProps } from 'react-native/Libraries/Lists/FlatList';
import { type StyleProp } from 'react-native/Libraries/StyleSheet/StyleSheet';
import { type ViewStyle } from 'react-native/Libraries/StyleSheet/StyleSheetTypes';

type Props<T extends { id: string }> = {
	style?: StyleProp<ViewStyle>;
	nested?: boolean;
} & (
	| {
			data: NonNullable<FlatListProps<T>['data']>;
			children: (item: T) => ReactElement;
			fixed?: never;
	  }
	| {
			data?: undefined | null;
			children: ReactNode;
			fixed?: boolean;
	  }
);

export const Container = <T extends { id: string }>({ children, style, fixed, nested, data }: Props<T>) => {
	const queryClient = useQueryClient();
	const [refreshing, setRefreshing] = useState(false);
	const handleRefresh = useCallback(async () => {
		setRefreshing(true);
		await queryClient.invalidateQueries();
		setTimeout(() => {
			setRefreshing(false);
		}, ONE_SECOND);
	}, [queryClient]);

	const commonProps: ComponentProps<typeof ScrollView> = {
		style: { width: '100%', height: '100%', flex: 1 },
		contentContainerStyle: [{ gap: 16, padding: nested ? 0 : 16, minHeight: '100%' }, style],
		refreshControl: <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />,
		fadingEdgeLength: 64,
		showsHorizontalScrollIndicator: false,
		showsVerticalScrollIndicator: false,
	};

	if (!data) {
		if (fixed) return <View style={{ width: '100%', height: '100%', flex: 1, gap: 16, padding: nested ? 0 : 16 }}>{children}</View>;
		return <ScrollView {...commonProps}>{children}</ScrollView>;
	}
	return <FlatList {...commonProps} data={data} keyExtractor={(item) => item.id} renderItem={({ item }) => children(item)} />;
};
