import { type IconDefinition } from '@fortawesome/fontawesome-common-types';
import { FontAwesomeIcon, type FontAwesomeIconStyle } from '@fortawesome/react-native-fontawesome';
import { type FC, type PropsWithChildren } from 'react';
import { Pressable, Text } from 'react-native';
import { type StyleProp } from 'react-native/Libraries/StyleSheet/StyleSheet';
import { type ViewStyle } from 'react-native/Libraries/StyleSheet/StyleSheetTypes';
import { globalStyles } from '../app/globalStyles';

const defaultIconSize = 14;

type Props = PropsWithChildren<{
	icon?: IconDefinition;
	onPress?: () => void;
	variant?: 'primary' | 'danger' | 'outline';
	disabled?: boolean;
	style?: StyleProp<ViewStyle & { fontSize?: number }>;
}>;

export const Button: FC<Props> = ({ icon, variant = 'primary', onPress, disabled = false, style, children }) => {
	const iconSize = (Array.isArray(style) ? style : [style]).reduce((acc, style) => {
		if (style && 'fontSize' in style) return style.fontSize ?? acc;
		return acc;
	}, defaultIconSize);

	return (
		<Pressable
			style={({ pressed }) => [
				globalStyles.button,
				variant === 'primary' && globalStyles.buttonPrimary,
				variant === 'primary' && pressed && globalStyles.buttonPrimaryHover,
				variant === 'danger' && globalStyles.buttonDanger,
				variant === 'danger' && pressed && globalStyles.buttonDangerHover,
				variant === 'outline' && globalStyles.buttonOutline,
				variant === 'outline' && pressed && globalStyles.buttonOutlineHover,
				disabled && globalStyles.disabled,
				icon && !children && globalStyles.iconButton,
				style,
			]}
			onPress={onPress}
			disabled={disabled}
		>
			{icon && (
				<FontAwesomeIcon
					icon={icon}
					size={iconSize}
					style={
						[
							children && {
								marginLeft: -iconSize,
							},
							variant === 'primary' && globalStyles.buttonTextPrimary,
							variant === 'danger' && globalStyles.buttonTextDanger,
							variant === 'outline' && globalStyles.buttonTextOutline,
						] as FontAwesomeIconStyle
					}
				/>
			)}
			{children && (
				<Text
					style={[
						globalStyles.buttonText,
						variant === 'primary' && globalStyles.buttonTextPrimary,
						variant === 'danger' && globalStyles.buttonTextDanger,
						variant === 'outline' && globalStyles.buttonTextOutline,
					]}
				>
					{children}
				</Text>
			)}
		</Pressable>
	);
};
