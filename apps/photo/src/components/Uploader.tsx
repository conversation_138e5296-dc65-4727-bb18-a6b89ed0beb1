import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useCallback, useEffect, useState } from 'react';
import { Alert } from 'react-native';
import { MessageBar } from '../app/MessageBar';
import { type UploadStackFile, useAppState } from '../app/state';

export const Uploader: FC = () => {
	const [uploading, setUploading] = useState<boolean>(false);

	const uploadStack = useAppState((state) => state.uploadStack);
	const removeFromUploadStack = useAppState((state) => state.removeFromUploadStack);

	const { mutate: uploadBase64 } = apiHooks.useUploadBase64();

	const blobToBase64 = (blob: Blob): Promise<string> => {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.onerror = reject;
			reader.onload = () => {
				resolve(String(reader.result));
			};
			reader.readAsDataURL(blob);
		});
	};

	const uploadFile = useCallback(
		async (file: UploadStackFile) => {
			const { path, ...props } = file;
			const imageResponse = await fetch(path);
			const imageBlob = await imageResponse.blob();
			const data = await blobToBase64(imageBlob);
			return uploadBase64(
				{ ...props, name: new Date().toISOString() + '.jpg', data: data.split(',')[1] },
				{
					onSuccess: () => {
						removeFromUploadStack(file.path);
					},
					onError: () => {
						Alert.alert(
							'Nahrání fotky se nezdařilo...',
							undefined,
							[
								{
									text: 'Zavřít a pokračovat',
									onPress: () => removeFromUploadStack(file.path),
								},
								{
									text: 'Zkusit znovu',
									onPress: () => uploadFile(file),
								},
							],
							{ cancelable: true, onDismiss: () => removeFromUploadStack(file.path) },
						);
					},
					onSettled: () => setUploading(false),
				},
			);
		},
		[removeFromUploadStack, uploadBase64],
	);

	useEffect(() => {
		if (uploading || uploadStack.length === 0) return;
		setUploading(true);
		uploadFile(uploadStack[0]);
	}, [uploadStack, uploading, uploadFile]);

	if (uploadStack.length === 0) return null;
	return (
		<MessageBar zIndex={1000}>
			Nahrávám {uploadStack.length} soubor{uploadStack.length === 1 ? '' : uploadStack.length < 5 ? 'y' : 'ů'}...
		</MessageBar>
	);
};
