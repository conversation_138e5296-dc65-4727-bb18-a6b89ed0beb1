import { useUserHasScope } from './useUserHasScope';

export const useIsPhotoCapable = () => {
	const isBatchWrite = useUserHasScope('batchWrite');
	const isBatchCheck = useUserHasScope('batchCheck');
	const isBatchDelivery = useUserHasScope('batchDelivery');
	const isProductWrite = useUserHasScope('productWrite');
	const isProductTest = useUserHasScope('productTest');
	const isServiceWrite = useUserHasScope('serviceWrite');
	const isWarrantyClaimWrite = useUserHasScope('warrantyClaimWrite');

	return isBatchWrite || isBatchCheck || isBatchDelivery || isProductWrite || isProductTest || isServiceWrite || isWarrantyClaimWrite;
};
