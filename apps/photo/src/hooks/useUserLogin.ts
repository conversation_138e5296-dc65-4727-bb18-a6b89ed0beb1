import { apiClient, apiHooks } from '@pocitarna-nx-2023/zodios-client';
import <PERSON><PERSON><PERSON>anager from '@react-native-cookies/cookies';
import * as Sentry from '@sentry/react-native';
import { useEffect } from 'react';
import { ToastAndroid } from 'react-native';
import { useAppState } from '../app/state';

export const useUserLogin = (scanCode: string) => {
	const { mutate: logIn, isLoading } = apiHooks.useCreateSessionForVerificationToken();

	const setAuth = useAppState((state) => state.setAuth);

	useEffect(() => {
		if (!scanCode) return;

		logIn(
			{
				verificationToken: scanCode,
			},
			{
				onSuccess: (data) => {
					CookieManager.set(apiClient.baseURL ?? '', {
						name: 'next-auth.session-token',
						value: data._data.sessionToken,
						path: '/',
					});

					setAuth(data._data);
					Sentry.setUser({
						id: data._data.user.id ?? undefined,
						username: data._data.user.name ?? undefined,
						email: data._data.user.email ?? undefined,
					});
				},
				onError: () => {
					ToastAndroid.showWithGravity(`Přihlášení se nezdařilo`, ToastAndroid.LONG, ToastAndroid.CENTER);
				},
			},
		);
	}, [logIn, scanCode, setAuth]);

	return isLoading;
};
