import { type InventoryItemScan } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useCallback, useRef, useState } from 'react';
import { useDebounce } from 'rooks';
import { vibrateError } from '../utils/vibrate';

const DELAY_BEFORE_BATCH_MS = 1000;
const DELAY_AFTER_REQUEST_MS = 1000;
const GENERIC_ERROR_MESSAGE = 'Chyba při dávkovém skenování';

type ScanItem = InventoryItemScan & { id: string };

export const useScanQueue = (inventoryId: string, warehousePositionId?: string) => {
	const [queue, setQueue] = useState<ScanItem[]>([]);
	const bufferRef = useRef<ScanItem[]>([]);
	const processingRef = useRef(false);

	const { mutate: scanInventoryItems } = apiHooks.useScanInventoryItem({
		params: { inventoryId },
		queries: { warehousePositionId },
	});

	const debouncedProcessBuffer = useDebounce(() => {
		if (processingRef.current || bufferRef.current.length === 0) return;

		processingRef.current = true;
		const itemsToScan = [...bufferRef.current];
		bufferRef.current = [];

		scanInventoryItems(
			itemsToScan.map((item) => item.pcn),
			{
				onSuccess: (response) => {
					if (response._data.find((r) => r.status === 'ERROR')) {
						vibrateError();
					}

					setQueue((prev) =>
						prev.map((item) => {
							const result = response._data.find((r) => r.pcn === item.pcn);
							if (!result) return item;
							return {
								...item,
								status: result.status,
								error: result.status !== 'ERROR' ? undefined : result.error ?? GENERIC_ERROR_MESSAGE,
							};
						}),
					);
				},
				onError: (error) => {
					console.error('Batch scan error:', error);
					vibrateError();

					setQueue((prev) =>
						prev.map((item) =>
							itemsToScan.some((b) => b.pcn === item.pcn) ? { ...item, status: 'ERROR', error: GENERIC_ERROR_MESSAGE } : item,
						),
					);
				},
				onSettled: () => {
					processingRef.current = false;
					if (bufferRef.current.length > 0) {
						setTimeout(() => debouncedProcessBuffer(), DELAY_AFTER_REQUEST_MS);
					}
				},
			},
		);
	}, DELAY_BEFORE_BATCH_MS);

	const addToQueue = useCallback(
		(pcn: string) => {
			if (queue.some((item) => item.pcn === pcn && ['OK', 'PENDING'].includes(item.status))) return;

			const newItem: ScanItem = { id: pcn, pcn, status: 'PENDING' };
			setQueue((prev) => {
				const pendingItems = prev.filter(({ status }) => status === 'PENDING');
				const restItems = prev.filter(({ status }) => status !== 'PENDING');
				const amount = Math.max(pendingItems.length, 20) + 1;
				return [...restItems, ...pendingItems, newItem].slice(-amount);
			});
			bufferRef.current.push(newItem);

			debouncedProcessBuffer();
		},
		[queue, debouncedProcessBuffer],
	);

	return {
		queue,
		addToQueue,
	};
};
