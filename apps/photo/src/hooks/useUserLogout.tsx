import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import CookieManager from '@react-native-cookies/cookies';
import * as Sentry from '@sentry/react-native';
import { useCallback } from 'react';
import { useAppState } from '../app/state';

export const useUserLogout = () => {
	const auth = useAppState((state) => state.auth);
	const setAuth = useAppState((state) => state.setAuth);

	const { mutate: logOut } = apiHooks.useDeleteSession({
		params: {
			sessionToken: auth?.sessionToken ?? '',
		},
	});

	return useCallback(() => {
		logOut(undefined, {
			onSuccess: () => {
				CookieManager.removeSessionCookies();
				setAuth(null);
				Sentry.setUser(null);
			},
			onError: () => {
				CookieManager.removeSessionCookies();
				setAuth(null);
				Sentry.setUser(null);
			},
		});
	}, [logOut, setAuth]);
};
