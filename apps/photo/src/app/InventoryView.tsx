import { NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { formatDateTime, formatInventoryCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useMemo } from 'react';
import { Pressable, StyleSheet, Text, View } from 'react-native';
import { Container } from '../components/Container';
import { useNavigation } from '../hooks/useNavigation';
import { globalStyles } from './globalStyles';

export const InventoryView = () => {
	const navigation = useNavigation();

	const { data: inventoriesData } = apiHooks.useGetInventories({
		queries: {
			filter: { status: { eq: 'OPEN' } },
		},
	});
	const inventories = useMemo(() => inventoriesData?._data ?? [], [inventoriesData?._data]);

	return (
		<Container data={inventories}>
			{(inventory) => (
				<Pressable
					style={({ pressed }) => [styles.item, pressed && globalStyles.buttonOutlineHover]}
					onPress={() => navigation.navigate('Warehouse/Inventory/Detail', { inventory })}
				>
					<Text style={styles.itemOdd}>No.:</Text>
					<Text style={styles.itemEven}>{formatInventoryCode(inventory.code)}</Text>
					{inventory.name && (
						<>
							<Text style={styles.itemOdd}>Název:</Text>
							<Text style={styles.itemEven}>{inventory.name}</Text>
						</>
					)}
					<Text style={styles.itemOdd}>Vytvořil:</Text>
					<View style={styles.itemEven}>
						<Text>{formatDateTime(inventory.createdAt)}</Text>
						<Text>{inventory.createdBy?.name ?? NOT_AVAILABLE}</Text>
					</View>
				</Pressable>
			)}
		</Container>
	);
};

const styles = StyleSheet.create({
	item: {
		...globalStyles.button,
		...globalStyles.buttonOutline,
		alignItems: 'flex-start',
		flexWrap: 'wrap',
	},
	itemOdd: {
		...globalStyles.buttonTextOutline,
		flexGrow: 1,
		flexBasis: '20%',
		fontWeight: 'bold',
	},
	itemEven: {
		...globalStyles.buttonTextOutline,
		flexGrow: 2,
		flexBasis: '60%',
	},
});
