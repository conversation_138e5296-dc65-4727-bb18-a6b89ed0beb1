import { faBarcodeScan } from '@fortawesome/pro-regular-svg-icons/faBarcodeScan';
import { type ComponentProps, type FC } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import type { StyleProp } from 'react-native/Libraries/StyleSheet/StyleSheet';
import type { ViewStyle } from 'react-native/Libraries/StyleSheet/StyleSheetTypes';
import { Button } from '../components/Button';
import { useAppState } from './state';
import { ZebraScanner } from './ZebraScanner';

export type ScannerProps = {
	handleCode: ((code: string) => void) | null;
};

type Props = {
	prompt?: string;
	style?: StyleProp<ViewStyle>;
	buttonStyle?: ComponentProps<typeof Button>['style'];
};

export const Scanner: FC<Props> = ({ prompt, style, buttonStyle }) => {
	const isZebraScannerAvailable = useAppState((state) => state.isZebraScannerAvailable);
	const setScannerActive = useAppState((state) => state.setScannerActive);
	const handleCode = useAppState((state) => state.handleCode);

	return (
		<View style={[styles.wrapper, style]}>
			{prompt && <Text>{prompt}</Text>}
			<Button
				icon={faBarcodeScan}
				onPress={() => setScannerActive(true)}
				disabled={!handleCode}
				style={[buttonStyle, { fontSize: 24 }]}
			/>
			{isZebraScannerAvailable && <ZebraScanner handleCode={handleCode} />}
		</View>
	);
};

const styles = StyleSheet.create({
	wrapper: {
		alignItems: 'center',
		justifyContent: 'center',
		gap: 4,
	},
});
