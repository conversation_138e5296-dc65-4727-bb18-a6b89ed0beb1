import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useEffect, useState } from 'react';
import { ToastAndroid } from 'react-native';
import { Container } from '../components/Container';
import { useScanCode } from '../hooks/useScanCode';
import { Scanner } from './Scanner';
import { useAppState } from './state';

export const LoginDeviceView = () => {
	const [scanCode, setScanCode] = useState<string>('');
	const { mutate: createSession } = apiHooks.useCreateSession(undefined, {
		onSuccess: () => {
			ToastAndroid.showWithGravity('Přihlášeno...', ToastAndroid.SHORT, ToastAndroid.CENTER);
		},
		onError: () => {
			ToastAndroid.showWithGravity(`Přihlášení se nezdařilo`, ToastAndroid.LONG, ToastAndroid.CENTER);
		},
	});
	const auth = useAppState((state) => state.auth);

	useScanCode(setScanCode);

	useEffect(() => {
		if (scanCode && auth) {
			createSession({ sessionToken: scanCode, userId: auth.id });
			setScanCode('');
		}
	}, [createSession, scanCode, auth]);

	return (
		<Container style={{ justifyContent: 'center' }}>
			<Scanner prompt="Naskenuj QR kód na zařízení" />
		</Container>
	);
};
