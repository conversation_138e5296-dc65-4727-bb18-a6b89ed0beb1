import { PHOTO_USAGE, PHOTO_USAGE_ENTITY } from '@pocitarna-nx-2023/config';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useCallback, useEffect, useState } from 'react';
import { Alert, ToastAndroid } from 'react-native';
import { Container } from '../components/Container';
import { useNavigation } from '../hooks/useNavigation';
import { useScanCode } from '../hooks/useScanCode';
import { Overlay } from './Overlay';
import { Scanner } from './Scanner';
import type { EntityType, UsageType } from './state';

export const ScannerView = () => {
	const navigation = useNavigation();
	const [scanCode, setScanCode] = useState<string>('');

	const {
		data: scanData,
		isFetching,
		isSuccess,
		isError,
	} = apiHooks.useGetByScanCode({ params: { scanCode } }, { enabled: !!scanCode, cacheTime: 0, keepPreviousData: false });

	useEffect(() => {
		const dataType = scanData?._data.type;
		if (scanData?._data && ((dataType === 'product' && scanData._data.product?.id) || dataType === 'batch')) {
			let id = '';
			if (dataType === 'product' && scanData._data.product?.id) {
				id = scanData._data.product.id;
			} else if (dataType === 'batch') {
				id = scanData._data.batch.id;
			}

			navigation.navigate('Camera', { entity: { id, type: dataType, code: scanCode } });
			setScanCode('');
			return;
		}

		if (!isFetching && (isSuccess || isError)) {
			ToastAndroid.showWithGravity('Nepodařilo se nalézt entitu dle kódu', ToastAndroid.LONG, ToastAndroid.CENTER);
			setScanCode('');
		}
	}, [isError, isFetching, isSuccess, navigation, scanCode, scanData?._data]);

	const onCodeScanned = useCallback(
		(value: string) => {
			let parsedCode = '';
			let entityId: string | null = null;
			let photoSessionId: string | null = null;
			let entityType: EntityType | null = null;
			let usageType: UsageType | null = null;

			if (value.startsWith('{')) {
				const data = JSON.parse(value);
				if ('entityId' in data) {
					parsedCode = data.entityCode;
					entityId = data.entityId ?? null;
					entityType = data.entityType ?? null;
				}

				if ('photoSessionId' in data) {
					parsedCode = data.entityCode;
					usageType = data.usage ?? null;
					photoSessionId = data.photoSessionId ?? null;
				}
			} else {
				parsedCode = value;
			}

			Alert.alert(
				'Kód entity:',
				parsedCode + (entityType ? ` (${PHOTO_USAGE_ENTITY[entityType]})` : usageType ? ` (${PHOTO_USAGE[usageType]})` : ''),
				[
					{
						text: 'Zavřít',
					},
					{
						text: 'Potvrdit',
						onPress: () => {
							setScanCode('');
							if (entityId && entityType && parsedCode) {
								navigation.navigate('Camera', { entity: { id: entityId, type: entityType, code: parsedCode } });
							} else if (photoSessionId && usageType && parsedCode) {
								navigation.navigate('Camera', {
									session: { id: photoSessionId, type: usageType },
									entity: { code: parsedCode },
								});
							} else {
								setScanCode(parsedCode);
							}
						},
					},
				],
				{ cancelable: true },
			);
		},
		[navigation],
	);

	useScanCode(onCodeScanned);

	return (
		<Container style={{ justifyContent: 'center' }}>
			<Scanner prompt="Načtěte QR kód produktu či várky" />
			{isFetching && <Overlay />}
		</Container>
	);
};
