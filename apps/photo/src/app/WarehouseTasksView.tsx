import { faArrowRight } from '@fortawesome/pro-regular-svg-icons/faArrowRight';
import { faBoxesPacking } from '@fortawesome/pro-regular-svg-icons/faBoxesPacking';
import { faScannerGun } from '@fortawesome/pro-regular-svg-icons/faScannerGun';
import { NOT_AVAILABLE, TO_STOCK_SECTOR } from '@pocitarna-nx-2023/config';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { Button } from '../components/Button';
import { Container } from '../components/Container';
import { useNavigation } from '../hooks/useNavigation';
import { useAppState } from './state';

export const WarehouseTasksView = () => {
	const user = useAppState((state) => state.auth?.user);
	const navigation = useNavigation();

	const { data: warehousePositionsData } = apiHooks.useGetWarehousePositions({
		queries: { filter: { sector: { eq: TO_STOCK_SECTOR } }, page: 1, limit: 1 },
	});
	const amountToStock = warehousePositionsData?._data.at(0)?.productCount;

	const { data: expeditionTasks } = apiHooks.useGetWarehouseTasks({
		queries: { filter: { type: { eq: 'PICKUP' }, status: { eq: 'OPEN' } }, page: 1, limit: 1 },
	});
	const amountToExpedite = expeditionTasks?._paging.total;

	const { data: shiftTasks } = apiHooks.useGetWarehouseTasks(
		{ queries: { filter: { type: { eq: 'SHIFT' }, status: { eq: 'OPEN' }, userId: { eq: user?.id } }, page: 1, limit: 1 } },
		{ enabled: !!user?.id },
	);
	const amountToShift = shiftTasks?._paging.total;

	return (
		<Container>
			<Button variant="outline" icon={faArrowRight} onPress={() => navigation.navigate('Warehouse/Store')}>
				{TO_STOCK_SECTOR} {'->'} Prodejní ({amountToStock ?? NOT_AVAILABLE})
			</Button>
			<Button variant="outline" icon={faBoxesPacking} onPress={() => navigation.navigate('Warehouse/Expedite')}>
				Prodejní {'->'} Expedice ({amountToExpedite ?? NOT_AVAILABLE})
			</Button>
			<Button variant="outline" icon={faScannerGun} onPress={() => navigation.navigate('Warehouse/Shift')}>
				Skladový seznam ({amountToShift ?? NOT_AVAILABLE})
			</Button>
		</Container>
	);
};
