import { type PHOTO_USAGE, type PHOTO_USAGE_ENTITY } from '@pocitarna-nx-2023/config';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { create } from 'zustand';

export type EntityType = keyof typeof PHOTO_USAGE_ENTITY;
export type UsageType = keyof typeof PHOTO_USAGE;
export type UploadStackFile = { id?: string; type?: EntityType; photoSessionId?: string; path: string };

type AppState = {
	auth: ApiBody<'createSessionForVerificationToken'> | null;
	setAuth: (auth: ApiBody<'createSessionForVerificationToken'> | null) => void;
	isZebraScannerAvailable: boolean;
	setZebraScannerAvailable: (isZebraScannerAvailable: boolean) => void;
	uploadStack: UploadStackFile[];
	addToUploadStack: (...files: UploadStackFile[]) => void;
	removeFromUploadStack: (...files: string[]) => void;
	handleCode: ((code: string) => void) | null;
	setHandleCode: (handleCode: ((code: string) => void) | null) => void;
	isScannerActive: boolean;
	setScannerActive: (isScannerActive: boolean) => void;
};

export const useAppState = create<AppState>()((set, get) => ({
	auth: null,
	setAuth: (auth) => set({ auth }),
	isZebraScannerAvailable: false,
	setZebraScannerAvailable: (isZebraScannerAvailable) => set({ isZebraScannerAvailable }),
	uploadStack: [],
	addToUploadStack: (...files) => set({ uploadStack: [...get().uploadStack, ...files] }),
	removeFromUploadStack: (...files) => set({ uploadStack: get().uploadStack.filter((file) => !files.includes(file.path)) }),
	handleCode: null,
	setHandleCode: (handleCode) => set({ handleCode }),
	isScannerActive: false,
	setScannerActive: (isScannerActive) => set({ isScannerActive }),
}));
