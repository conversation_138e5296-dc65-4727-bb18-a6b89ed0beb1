import { EIGHT_HOURS } from '@pocitarna-nx-2023/config';
import { type FC, type PropsWithChildren, useEffect, useRef } from 'react';
import { PanResponder, View } from 'react-native';

type Props = {
	callback: () => void;
};

export const UserInactivity: FC<PropsWithChildren<Props>> = ({ callback, children }) => {
	const timerId = useRef<any>(null);

	useEffect(() => {
		resetInactivityTimeout();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	const panResponder = useRef(
		PanResponder.create({
			onStartShouldSetPanResponderCapture: () => {
				resetInactivityTimeout();
				return false;
			},
		}),
	).current;

	const resetInactivityTimeout = () => {
		clearTimeout(timerId.current);
		timerId.current = setTimeout(() => {
			callback();
		}, EIGHT_HOURS);
	};

	return (
		<View style={{ flex: 1 }} {...panResponder.panHandlers}>
			{children}
		</View>
	);
};
