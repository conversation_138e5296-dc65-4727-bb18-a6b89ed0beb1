import { faCheck } from '@fortawesome/pro-regular-svg-icons/faCheck';
import { faSpinner } from '@fortawesome/pro-regular-svg-icons/faSpinner';
import { faXmark } from '@fortawesome/pro-regular-svg-icons/faXmark';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { CODE_PREFIX, EMPTY_VALUE } from '@pocitarna-nx-2023/config';
import { formatInventoryCode, formatWarehousePositionName } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useCallback, useEffect, useMemo, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Container } from '../components/Container';
import { useScanCode } from '../hooks/useScanCode';
import { useScanQueue } from '../hooks/useScanQueue';
import { vibrateWarning } from '../utils/vibrate';
import { COLORS, globalStyles } from './globalStyles';

type Props = {
	inventory: ApiBody<'getInventories'>[number];
};

export const InventoryDetailView: FC<Props> = ({ inventory }) => {
	const [warehousePosition, setWarehousePosition] = useState<ApiBody<'getWarehousePosition'> | null>(null);
	const [scanCode, setScanCode] = useState<string>('');
	const { queue, addToQueue } = useScanQueue(inventory.id, warehousePosition?.id);

	const { data: scanData } = apiHooks.useGetByScanCode(
		{ params: { scanCode } },
		{ enabled: !!scanCode && scanCode.startsWith(CODE_PREFIX.WAREHOUSE_POSITION), cacheTime: 0, keepPreviousData: false },
	);
	const fetchedWarehousePosition = useMemo(
		() => (scanData?._data?.type === 'warehousePosition' ? scanData?._data.warehousePosition : null),
		[scanData?._data],
	);

	const handleScan = useCallback(
		(value: string) => {
			if (value.startsWith(CODE_PREFIX.PRODUCT)) {
				addToQueue(value);
			} else if (value.startsWith(CODE_PREFIX.WAREHOUSE_POSITION)) {
				setScanCode(value);
			} else {
				vibrateWarning();
			}
		},
		[addToQueue],
	);

	useScanCode(handleScan);

	useEffect(() => {
		if (fetchedWarehousePosition && fetchedWarehousePosition.id !== warehousePosition?.id) {
			setWarehousePosition(fetchedWarehousePosition);
		}
	}, [fetchedWarehousePosition, warehousePosition?.id]);

	return (
		<Container fixed>
			<Text style={styles.title}>
				{formatInventoryCode(inventory.code)}
				{inventory.name && ` - ${inventory.name}`}
			</Text>

			{warehousePosition && (
				<Text>
					Produkty na pozici{' '}
					<Text style={globalStyles.bold}>
						{warehousePosition.warehouse.name} - {formatWarehousePositionName(warehousePosition)}
					</Text>{' '}
					({warehousePosition.productCount})
				</Text>
			)}

			<Container nested data={queue} style={{ gap: 2 }}>
				{(item) => (
					<View style={styles.item} key={item.pcn}>
						<Text>{item.pcn}</Text>
						{item.status === 'PENDING' && <FontAwesomeIcon icon={faSpinner} color={COLORS.warning} />}
						{item.status === 'OK' && <FontAwesomeIcon icon={faCheck} color={COLORS.success} />}
						{item.status === 'ERROR' && <FontAwesomeIcon icon={faXmark} color={COLORS.error} />}
						<Text>{item.error ?? EMPTY_VALUE}</Text>
					</View>
				)}
			</Container>
		</Container>
	);
};

const styles = StyleSheet.create({
	title: {
		fontSize: 20,
		fontWeight: 'bold',
	},
	item: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'flex-start',
		gap: 16,
	},
});
