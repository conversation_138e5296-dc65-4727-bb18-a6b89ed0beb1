import { faArrowUpWideShort } from '@fortawesome/pro-regular-svg-icons/faArrowUpWideShort';
import { faCheck } from '@fortawesome/pro-regular-svg-icons/faCheck';
import { faClock } from '@fortawesome/pro-regular-svg-icons/faClock';
import { faFilter } from '@fortawesome/pro-regular-svg-icons/faFilter';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import {
	CODE_PREFIX,
	EXPEDITION_SECTOR,
	MAX_POSITIVE_INTEGER,
	NOT_AVAILABLE,
	ONE_DAY,
	ORDER_STATUSES_FOR_WAREHOUSE_TASKS,
	PRIORITY_VALUES,
	TWO_DAYS,
} from '@pocitarna-nx-2023/config';
import { formatDateTime, formatProductCode, formatWarehousePositionName, stripCode } from '@pocitarna-nx-2023/utils';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Modal, Pressable, StyleSheet, Text, ToastAndroid, View } from 'react-native';
import { Button } from '../components/Button';
import { Container } from '../components/Container';
import { useScanCode } from '../hooks/useScanCode';
import { vibrateError } from '../utils/vibrate';
import { COLORS, globalStyles } from './globalStyles';
import { Overlay } from './Overlay';

const sortLabelMap = {
	priority: 'Podle priorit',
	position: 'Podle pozic skladu',
	time: 'Podle stáří',
};

export const WarehouseExpediteView = () => {
	const [sort, setSort] = useState<'priority' | 'position' | 'time'>('priority');
	const [status, setStatus] = useState<(typeof ORDER_STATUSES_FOR_WAREHOUSE_TASKS)[number] | null>(null);
	const [isSortModalVisible, setSortModalVisible] = useState(false);
	const [isFilterModalVisible, setFilterModalVisible] = useState(false);
	const [scanCode, setScanCode] = useState<string>('');
	const [products, setProducts] = useState<ApiBody<'getProduct'>[]>([]);
	const productIds = useMemo(() => products.map(({ id }) => id), [products]);
	const { data: warehousePositionsData } = apiHooks.useGetWarehousePositions({
		queries: {
			filter: {
				'warehouse.type': { eq: 'EXPEDITION' },
				sector: { eq: EXPEDITION_SECTOR },
				rack: { eq: null },
				shelf: { eq: null },
				box: { eq: null },
			},
			page: 1,
			limit: 1,
		},
	});
	const warehousePosition = useMemo(() => warehousePositionsData?._data.at(0), [warehousePositionsData?._data]);

	const {
		data: warehouseTasksData,
		invalidate,
		isFetching,
	} = apiHooks.useGetWarehouseTasks({
		queries: {
			filter: { type: { eq: 'PICKUP' }, status: { eq: 'OPEN' }, ...(status ? { 'ecommerceOrder.status': { eq: status } } : {}) },
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
			sort: [
				...(sort === 'priority'
					? [
							['priority', 'desc'],
							`CASE WHEN "entity"."createdAt" >= NOW() - INTERVAL '2 DAYS' THEN -1 ELSE 0 END`,
							`CASE WHEN "entity"."createdAt" >= NOW() - INTERVAL '1 DAY' THEN -1 ELSE 0 END`,
						]
					: []),
				...(sort === 'priority' || sort === 'position'
					? ['warehousePosition.sector', 'warehousePosition.rack', 'warehousePosition.shelf', 'warehousePosition.box']
					: []),
				'entity.createdAt',
				'entity.id',
			],
		},
	});
	const warehouseTasks = useMemo(() => warehouseTasksData?._data ?? [], [warehouseTasksData?._data]);

	const { data: scanData } = apiHooks.useGetByScanCode(
		{ params: { scanCode }, queries: { expedition: true } },
		{ enabled: !!scanCode, cacheTime: 0, keepPreviousData: false },
	);
	const fetchedProduct = useMemo(() => (scanData?._data?.type === 'product' ? scanData?._data.product : null), [scanData?._data]);

	const { mutate: addProductsToWarehousePosition, isLoading: isAddingProducts } = apiHooks.useAddProductsToWarehousePosition(
		{ params: { warehousePositionId: warehousePosition?.id ?? '' } },
		{
			onSuccess: () => {
				if (warehousePosition) {
					ToastAndroid.showWithGravity(
						`Produkt${products.length > 1 ? 'y' : ''} (${products.length}) byl${products.length > 1 ? 'y' : ''} přidán${products.length > 1 ? 'y' : ''} do skladu "${warehousePosition.warehouse.name}" na pozici ${formatWarehousePositionName(warehousePosition)}`,
						ToastAndroid.LONG,
						ToastAndroid.CENTER,
					);
					handleReset();
				}
			},
		},
	);

	useEffect(() => {
		if (fetchedProduct && !productIds.includes(fetchedProduct.id)) {
			setProducts((products) => [...products, fetchedProduct]);
		}
	}, [fetchedProduct, productIds]);

	const handleScan = useCallback(
		(value: string) => {
			if (value.startsWith(CODE_PREFIX.PRODUCT) && !!warehouseTasks.find(({ product }) => product.code?.code === stripCode(value))) {
				setScanCode(value);
			} else {
				vibrateError();
			}
		},
		[warehouseTasks],
	);

	useScanCode(handleScan);

	const handleReset = () => {
		setScanCode('');
		setProducts([]);
		invalidate();
	};

	return (
		<Container fixed>
			{isFetching && <Overlay style={{ top: 0, bottom: 0, right: 0, left: 0 }} />}
			<View style={styles.sortWrapper}>
				<Text style={[styles.muted, { flexGrow: 1 }]}>
					{status ?? 'Všechny'} • {sortLabelMap[sort]}
				</Text>
				<Button
					icon={faFilter}
					variant="outline"
					onPress={() => setFilterModalVisible((isFilterModalVisible) => !isFilterModalVisible)}
				/>
				<Modal
					transparent
					animationType="fade"
					visible={isFilterModalVisible}
					onDismiss={() => setFilterModalVisible(false)}
					onRequestClose={() => setFilterModalVisible(false)}
				>
					<Pressable onPress={() => setFilterModalVisible(false)} style={styles.modalContainer}>
						<View style={styles.modalContent}>
							<Button
								variant="outline"
								disabled={status === null}
								onPress={() => {
									setStatus(null);
									setFilterModalVisible(false);
								}}
							>
								Všechny
							</Button>
							{ORDER_STATUSES_FOR_WAREHOUSE_TASKS.map((orderStatus) => (
								<Button
									key={orderStatus}
									variant="outline"
									disabled={status === orderStatus}
									onPress={() => {
										setStatus(orderStatus);
										setFilterModalVisible(false);
									}}
								>
									{orderStatus}
								</Button>
							))}
						</View>
					</Pressable>
				</Modal>

				<Button
					icon={faArrowUpWideShort}
					variant="outline"
					onPress={() => setSortModalVisible((isSortModalVisible) => !isSortModalVisible)}
				/>
				<Modal
					transparent
					animationType="fade"
					visible={isSortModalVisible}
					onDismiss={() => setSortModalVisible(false)}
					onRequestClose={() => setSortModalVisible(false)}
				>
					<Pressable onPress={() => setSortModalVisible(false)} style={styles.modalContainer}>
						<View style={styles.modalContent}>
							{(['priority', 'position', 'time'] as const).map((sortOption) => (
								<Button
									key={sortOption}
									variant="outline"
									disabled={sort === sortOption}
									onPress={() => {
										setSort(sortOption);
										setSortModalVisible(false);
									}}
								>
									{sortLabelMap[sortOption]}
								</Button>
							))}
						</View>
					</Pressable>
				</Modal>
			</View>
			<View style={styles.dataWrapper}>
				<Container data={warehouseTasks} nested>
					{(warehouseTask) => {
						const isScanned = productIds.includes(warehouseTask.product.id);
						const olderThan2Days = warehouseTask.createdAt.getTime() <= Date.now() - TWO_DAYS;
						const olderThan1Day = warehouseTask.createdAt.getTime() <= Date.now() - ONE_DAY;
						const color = isScanned
							? COLORS.success
							: warehouseTask.priority >= PRIORITY_VALUES.HIGH || olderThan2Days
								? COLORS.error
								: warehouseTask.priority >= PRIORITY_VALUES.MEDIUM || olderThan1Day
									? COLORS.warning
									: COLORS.border;

						return (
							<View style={[styles.warehouseTask, { borderColor: color }]}>
								<FontAwesomeIcon icon={isScanned ? faCheck : faClock} color={color} size={16} />
								<View>
									<Text style={styles.muted}>{formatDateTime(warehouseTask.createdAt)}</Text>
									<Text style={globalStyles.bold}>
										{warehouseTask.product.warehousePosition
											? `${warehouseTask.product.warehousePosition.warehouse.name} - ${formatWarehousePositionName(warehouseTask.product.warehousePosition)}`
											: warehouseTask.product.pickedAt
												? `V přesunu - ${warehouseTask.product.pickedBy?.name ?? NOT_AVAILABLE}`
												: NOT_AVAILABLE}
									</Text>
									<Text style={globalStyles.bold}>{formatProductCode(warehouseTask.product.code)}</Text>
									<Text>{warehouseTask.product.productEnvelope?.name}</Text>
									{warehouseTask.ecommerceOrder && (
										<Text>
											Obj. {warehouseTask.ecommerceOrder.code}:{' '}
											{warehouseTask.ecommerceOrder.contact?.name ?? NOT_AVAILABLE}
										</Text>
									)}
								</View>
							</View>
						);
					}}
				</Container>
			</View>

			<View style={styles.footerWrapper}>
				<Button
					onPress={() => {
						const scannedWarehouseTasks = warehouseTasks
							.filter((task) => productIds.includes(task.product.id))
							.map((warehouseTask) => warehouseTask.id);
						addProductsToWarehousePosition({ products: productIds, warehouseTasks: scannedWarehouseTasks });
					}}
					disabled={!warehousePosition || products.length === 0 || isAddingProducts}
					style={{ flexGrow: 1 }}
				>
					Potvrdit přesun
				</Button>
			</View>
		</Container>
	);
};

const styles = StyleSheet.create({
	titleWrapper: {
		width: '100%',
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
	},
	dataWrapper: {
		flex: 1,
	},
	footerWrapper: {},
	title: {
		fontSize: 28,
		fontWeight: 'bold',
	},
	warehouseTask: {
		...globalStyles.button,
		...globalStyles.buttonOutline,
		alignItems: 'flex-start',
		justifyContent: 'flex-start',
	},
	muted: {
		fontSize: 12,
		color: COLORS.muted,
	},
	sortWrapper: {
		alignItems: 'center',
		justifyContent: 'flex-end',
		flexDirection: 'row',
		gap: 8,
	},
	modalContainer: {
		flex: 1,
		justifyContent: 'flex-start',
		alignItems: 'flex-end',
	},
	modalContent: {
		gap: 8,
		backgroundColor: COLORS.white,
		padding: 8,
		borderRadius: 6,
		marginTop: 115,
		marginRight: 16,
		boxShadow: '0 4px 6px -1px rgba(0, 0, 0 / 0.1), 0 2px 4px -2px rgba(0, 0, 0 / 0.1)',
	},
});
