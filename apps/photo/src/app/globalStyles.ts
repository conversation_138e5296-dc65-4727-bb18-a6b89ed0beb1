import { StyleSheet } from 'react-native';

export const COLORS = {
	text: '#454545',
	muted: '#64748b',
	primary: '#00adab',
	primaryHover: 'rgba(0,173,171,0.8)',
	white: '#ffffff',
	error: '#ef4444',
	errorHover: 'rgba(239,68,68,0.8)',
	warning: '#f59e0b',
	success: '#16a34a',
	border: '#e7e5e4',
	outlineHover: '#f5f5f4',
};

export const globalStyles = StyleSheet.create({
	scrollView: {
		padding: 20,
	},
	button: {
		paddingVertical: 8,
		paddingHorizontal: 16,
		backgroundColor: COLORS.primary,
		borderColor: COLORS.primary,
		borderRadius: 6,
		borderWidth: 1,
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'center',
		gap: 8,
		boxShadow: '0 1px 2px rgba(0, 0, 0 / 0.05)',
	},
	iconButton: {
		paddingVertical: 8,
		paddingHorizontal: 8,
	},
	buttonText: {
		fontSize: 14,
		fontWeight: 500,
	},
	buttonLarge: {
		paddingVertical: 16,
		paddingHorizontal: 56,
	},

	buttonPrimary: {
		backgroundColor: COLORS.primary,
		borderColor: COLORS.primary,
	},
	buttonPrimaryHover: {
		backgroundColor: COLORS.primaryHover,
	},
	buttonTextPrimary: {
		color: COLORS.white,
	},

	buttonDanger: {
		backgroundColor: COLORS.error,
		borderColor: COLORS.error,
	},
	buttonDangerHover: {
		backgroundColor: COLORS.errorHover,
	},
	buttonTextDanger: {
		color: COLORS.white,
	},

	buttonOutline: {
		backgroundColor: COLORS.white,
		borderColor: COLORS.border,
	},
	buttonOutlineHover: {
		backgroundColor: COLORS.outlineHover,
	},
	buttonTextOutline: {
		color: COLORS.text,
	},

	disabled: {
		opacity: 0.5,
	},
	bold: {
		fontWeight: 'bold',
	},
});
