import { faCheck } from '@fortawesome/pro-regular-svg-icons/faCheck';
import { faClock } from '@fortawesome/pro-regular-svg-icons/faClock';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { CODE_PREFIX, MAX_POSITIVE_INTEGER, NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { formatProductCode, formatWarehousePositionCode, formatWarehousePositionName } from '@pocitarna-nx-2023/utils';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Alert, StyleSheet, Text, ToastAndroid, View } from 'react-native';
import { Button } from '../components/Button';
import { Container } from '../components/Container';
import { useScanCode } from '../hooks/useScanCode';
import { vibrateError } from '../utils/vibrate';
import { COLORS, globalStyles } from './globalStyles';
import { useAppState } from './state';

export const WarehouseShiftView = () => {
	const user = useAppState((state) => state.auth?.user);
	const [scanCode, setScanCode] = useState<string>('');
	const [products, setProducts] = useState<ApiBody<'getProduct'>[]>([]);
	const productIds = useMemo(() => products.map(({ id }) => id), [products]);
	const [warehousePosition, setWarehousePosition] = useState<ApiBody<'getWarehousePosition'> | null>(null);

	const { data: warehouseTasksData, invalidate: invalidateWarehouseTasks } = apiHooks.useGetWarehouseTasks(
		{
			queries: {
				filter: { type: { eq: 'SHIFT' }, status: { eq: 'OPEN' }, userId: { eq: user?.id } },
				page: 1,
				limit: MAX_POSITIVE_INTEGER,
				sort: [
					'warehousePosition.sector',
					'warehousePosition.rack',
					'warehousePosition.shelf',
					'warehousePosition.box',
					'entity.createdAt',
					'entity.id',
				],
			},
		},
		{ enabled: !!user?.id },
	);
	const warehouseTasks = useMemo(() => warehouseTasksData?._data ?? [], [warehouseTasksData?._data]);
	const warehouseTasksPCNs = useMemo(() => warehouseTasks.map((task) => formatProductCode(task.product.code)), [warehouseTasks]);

	const { data: scanData } = apiHooks.useGetByScanCode(
		{ params: { scanCode }, queries: { warehouse: true } },
		{ enabled: !!scanCode, cacheTime: 0, keepPreviousData: false },
	);
	const invalidProduct = useMemo(() => scanData?._data?.type === 'product' && scanData?._data.product === null, [scanData?._data]);
	const fetchedProduct = useMemo(() => (scanData?._data?.type === 'product' ? scanData?._data.product : null), [scanData?._data]);
	const fetchedWarehousePosition = useMemo(
		() => (scanData?._data?.type === 'warehousePosition' ? scanData?._data.warehousePosition : null),
		[scanData?._data],
	);

	const { mutate: addProductsToWarehousePosition, isLoading: isAddingProducts } = apiHooks.useAddProductsToWarehousePosition(
		{ params: { warehousePositionId: warehousePosition?.id ?? '' } },
		{
			onSuccess: () => {
				invalidateWarehouseTasks();
				if (warehousePosition) {
					ToastAndroid.showWithGravity(
						`Produkt${products.length > 1 ? 'y' : ''} (${products.length}) byl${products.length > 1 ? 'y' : ''} přidán${products.length > 1 ? 'y' : ''} do skladu "${warehousePosition.warehouse.name}" na pozici ${formatWarehousePositionName(warehousePosition)}`,
						ToastAndroid.LONG,
						ToastAndroid.CENTER,
					);
					handleReset();
				}
			},
		},
	);

	useEffect(() => {
		if (invalidProduct) {
			vibrateError();
			Alert.alert(
				'Pozor!',
				`Produkt ${scanCode} nelze přesunout, protože je rezervovaný pro expedici. Vrať ho zpátky na místo!`,
				[
					{
						text: 'Rozumím',
						onPress: () => setScanCode(''),
					},
				],
				{ cancelable: true, onDismiss: () => setScanCode('') },
			);
		}
	}, [invalidProduct, scanCode]);

	useEffect(() => {
		if (fetchedProduct && !products.find((item) => item.id === fetchedProduct.id)) {
			setProducts((products) => [...products, fetchedProduct]);
		}
	}, [fetchedProduct, products]);

	useEffect(() => {
		if (fetchedWarehousePosition && fetchedWarehousePosition.id !== warehousePosition?.id) {
			setWarehousePosition(fetchedWarehousePosition);
		}
	}, [fetchedWarehousePosition, warehousePosition?.id]);

	const handleAddProductsToWarehousePosition = useCallback(() => {
		if (productIds.length > 0) {
			const scannedWarehouseTasks = warehouseTasks
				.filter((task) => productIds.includes(task.product.id))
				.map((warehouseTask) => warehouseTask.id);
			addProductsToWarehousePosition({
				products: productIds,
				warehouseTasks: scannedWarehouseTasks,
			});
		}
	}, [addProductsToWarehousePosition, productIds, warehouseTasks]);

	const handleScan = useCallback(
		(value: string) => {
			if (value.startsWith(CODE_PREFIX.PRODUCT) && !warehouseTasksPCNs.includes(value)) {
				setScanCode(value);
			} else if (value.startsWith(CODE_PREFIX.WAREHOUSE_POSITION)) {
				if (warehousePosition && formatWarehousePositionCode(warehousePosition.code) === value) {
					handleAddProductsToWarehousePosition();
				} else {
					setScanCode(value);
				}
			} else {
				vibrateError();
			}
		},
		[handleAddProductsToWarehousePosition, warehousePosition],
	);

	useScanCode(handleScan);

	const handleReset = () => {
		setScanCode('');
		setProducts([]);
		setWarehousePosition(null);
	};

	const placeholder = (
		<View style={styles.item}>
			<FontAwesomeIcon icon={faClock} color={COLORS.warning} size={16} />
			<Text>{NOT_AVAILABLE}</Text>
		</View>
	);

	return (
		<Container fixed>
			<Text style={styles.title}>Naskenujte pozici</Text>
			<View>
				{warehousePosition ? (
					<View style={[styles.item, styles.successItem]}>
						<FontAwesomeIcon icon={faCheck} color={COLORS.success} size={16} />
						<Text style={globalStyles.bold}>
							{warehousePosition.warehouse.name} - {formatWarehousePositionName(warehousePosition)}
						</Text>
					</View>
				) : (
					placeholder
				)}
			</View>

			<Text style={styles.title}>Naskenujte produkt(y)</Text>
			<View style={styles.dataWrapper}>
				<Container data={warehouseTasks} nested>
					{(warehouseTask) => {
						const isScanned = productIds.includes(warehouseTask.product.id);
						const color = isScanned ? COLORS.success : COLORS.border;

						return (
							<View style={[styles.warehouseTask, { borderColor: color }]}>
								<FontAwesomeIcon icon={isScanned ? faCheck : faClock} color={color} size={16} />
								<View>
									<Text style={globalStyles.bold}>
										{warehouseTask.product.warehousePosition
											? `${warehouseTask.product.warehousePosition.warehouse.name} - ${formatWarehousePositionName(warehouseTask.product.warehousePosition)}`
											: warehouseTask.product.pickedAt
												? `V přesunu - ${warehouseTask.product.pickedBy?.name ?? NOT_AVAILABLE}`
												: NOT_AVAILABLE}
									</Text>
									<Text style={globalStyles.bold}>{formatProductCode(warehouseTask.product.code)}</Text>
									<Text>{warehouseTask.product.productEnvelope?.name}</Text>
								</View>
							</View>
						);
					}}
				</Container>
			</View>

			<View style={styles.buttonWrapper}>
				<Button
					onPress={handleAddProductsToWarehousePosition}
					disabled={!warehousePosition || products.length === 0 || isAddingProducts}
					style={{ flexGrow: 1 }}
				>
					Přesunout
				</Button>
			</View>
		</Container>
	);
};

const styles = StyleSheet.create({
	titleWrapper: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
	},
	title: {
		fontSize: 20,
		...globalStyles.bold,
	},
	dataWrapper: {
		flex: 1,
	},
	warehouseTask: {
		...globalStyles.button,
		...globalStyles.buttonOutline,
		alignItems: 'flex-start',
		justifyContent: 'flex-start',
	},
	buttonWrapper: {
		flexDirection: 'row',
		gap: 8,
	},
	item: {
		...globalStyles.button,
		...globalStyles.buttonOutline,
		justifyContent: 'flex-start',
		alignItems: 'flex-start',
	},
	successItem: {
		borderColor: COLORS.success,
	},
});
