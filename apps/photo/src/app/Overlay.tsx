import type { FC } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import type { ViewStyle } from 'react-native/Libraries/StyleSheet/StyleSheetTypes';
import { COLORS } from './globalStyles';

type Props = {
	style?: ViewStyle;
};

export const Overlay: FC<Props> = ({ style }) => {
	return (
		<View style={[styles.overlay, style]}>
			<ActivityIndicator size="large" color={COLORS.primary} />
		</View>
	);
};
const styles = StyleSheet.create({
	overlay: {
		position: 'absolute',
		elevation: 10000,
		zIndex: 10000,
		top: -16,
		left: -16,
		right: -16,
		bottom: -16,
		backgroundColor: 'rgba(255, 255, 255, 0.8)',
		alignItems: 'center',
		justifyContent: 'center',
	},
});
