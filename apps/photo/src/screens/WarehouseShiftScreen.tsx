import { useIsFocused } from '@react-navigation/native';
import { useEffect } from 'react';
import { WarehouseShiftView } from '../app/WarehouseShiftView';
import { useNavigation } from '../hooks/useNavigation';
import { useUserHasScope } from '../hooks/useUserHasScope';

export const WarehouseShiftScreen = () => {
	const navigation = useNavigation();
	const isWarehouseWrite = useUserHasScope('warehouseWrite');
	const isFocused = useIsFocused();

	useEffect(() => {
		if (!isWarehouseWrite) {
			navigation.navigate('Home');
		}
	}, [isWarehouseWrite, navigation]);

	if (!isWarehouseWrite || !isFocused) return null;

	return <WarehouseShiftView />;
};
