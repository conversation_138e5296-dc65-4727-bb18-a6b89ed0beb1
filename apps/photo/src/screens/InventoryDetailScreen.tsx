import { useIsFocused } from '@react-navigation/native';
import { type NativeStackScreenProps } from '@react-navigation/native-stack';
import { type FC, useEffect } from 'react';
import { InventoryDetailView } from '../app/InventoryDetailView';
import { useNavigation } from '../hooks/useNavigation';
import { useUserHasScope } from '../hooks/useUserHasScope';
import type { DrawerRouterDefinition } from '../router/DrawerRouter';

type Props = NativeStackScreenProps<DrawerRouterDefinition, 'Warehouse/Inventory/Detail'>;

export const InventoryDetailScreen: FC<Props> = ({ route }) => {
	const navigation = useNavigation();
	const isWarehouseWrite = useUserHasScope('warehouseWrite');
	const isFocused = useIsFocused();

	useEffect(() => {
		if (!isWarehouseWrite) {
			navigation.navigate('Home');
		}
	}, [isWarehouseWrite, navigation]);

	if (!isWarehouseWrite || !isFocused) return null;

	return <InventoryDetailView {...route.params} />;
};
