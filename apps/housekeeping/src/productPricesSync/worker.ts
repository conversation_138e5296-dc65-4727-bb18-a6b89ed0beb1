import { withSentry, withSqsRecord } from '@pocitarna-nx-2023/aws';
import { ONE_SECOND, PRODUCT_PRICES_SYNC_ENDPOINT } from '@pocitarna-nx-2023/config';
import { ProductEnvelopeController, ProductPriceController, withAction, withDatabase } from '@pocitarna-nx-2023/database';
import { sleep } from '@pocitarna-nx-2023/utils';

export const handler = withSentry(
	'https://<EMAIL>/4509666423930960',
	withDatabase(
		withAction(
			PRODUCT_PRICES_SYNC_ENDPOINT,
			withSqsRecord(async (record) => {
				const parsedRecord = JSON.parse(record.body);

				if (typeof parsedRecord?.envelopeId !== 'string' || !['sale', 'standard'].includes(parsedRecord?.priceType)) {
					console.error('Could not sync products for envelope with payload', record.body);
					return;
				}

				const { envelopeId, priceType } = parsedRecord;

				const envelope = await new ProductEnvelopeController().findById(envelopeId);
				if (!envelope) throw new Error('Envelope not found');

				// eslint-disable-next-line no-console
				console.info(`Syncing ${priceType} price for envelope ${envelope.id}`);

				if (priceType === 'sale') {
					await new ProductPriceController().bulkUpdateSalePrices(envelopeId, Number(envelope.salePrice));
				} else {
					await new ProductPriceController().bulkUpdateStandardPrices(envelopeId, Number(envelope.standardPrice));
				}

				await sleep(ONE_SECOND);
			}),
		),
	),
);
