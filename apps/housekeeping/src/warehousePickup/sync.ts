import { withSentry } from '@pocitarna-nx-2023/aws';
import { ORDER_STATUSES_FOR_WAREHOUSE_TASKS, WAREHOUSE_PICKUP_SYNC_ENDPOINT } from '@pocitarna-nx-2023/config';
import { EcommerceOrderController, listAll, WarehouseTaskController, withAction, withDatabase } from '@pocitarna-nx-2023/database';
import { filterUndefined } from '@pocitarna-nx-2023/utils';

export const handler = withSentry(
	'https://<EMAIL>/4509666423930960',
	withDatabase(
		withAction(WAREHOUSE_PICKUP_SYNC_ENDPOINT, async () => {
			const [orders] = await new EcommerceOrderController().list(
				listAll({ filter: { status: { eq: ORDER_STATUSES_FOR_WAREHOUSE_TASKS } } }),
			);

			const [warehouseTasks] = await new WarehouseTaskController().list(listAll({ filter: { status: { eq: 'OPEN' } } }));
			await new WarehouseTaskController().delete(warehouseTasks.map(({ id }) => id));

			await Promise.all(
				orders.map(async (order) => {
					const productIds = filterUndefined(order.items.map(({ productId }) => productId));
					if (productIds.length > 0) {
						await new WarehouseTaskController().createPickup(productIds, order);
					}
				}),
			);
		}),
	),
);
