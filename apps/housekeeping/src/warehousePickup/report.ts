import { triggerEmail, withSentry } from '@pocitarna-nx-2023/aws';
import { DOMAIN_NAME, MATEJ_EMAIL } from '@pocitarna-nx-2023/config';
import { listAll, ProductController, UserController, withDatabase } from '@pocitarna-nx-2023/database';
import { filterUndefined, formatDateTime, formatProductCode, uniques } from '@pocitarna-nx-2023/utils';

export const handler = withSentry(
	'https://<EMAIL>/4509666423930960',
	withDatabase(async () => {
		const [products] = await new ProductController().list(listAll({ filter: { pickedAt: { ne: null } } }));
		if (products.length === 0) return;
		const pickedByIds = uniques(filterUndefined(products.map((product) => product.pickedById)));
		const [users] = await new UserController().list(listAll({ filter: { id: { eq: pickedByIds } } }));

		let emailBody = '';
		users.forEach((user) => {
			emailBody += `<h3>${user.name}</h3>`;
			emailBody += '<ul>';
			products
				.filter((product) => product.pickedById === user.id)
				.forEach((product) => {
					emailBody += '<li>';
					emailBody += `<a href="https://cmp.${DOMAIN_NAME}/product/${product.id}">${formatProductCode(product.code)}<a>`;
					if (product.pickedAt) {
						emailBody += ` - ${formatDateTime(product.pickedAt)}`;
					}
					emailBody += '</li>';
				});
			emailBody += '</ul>';
		});

		await triggerEmail({
			recipients: [MATEJ_EMAIL],
			subject: `Produkty stále v přesunu`,
			message: emailBody,
		});
	}),
);
