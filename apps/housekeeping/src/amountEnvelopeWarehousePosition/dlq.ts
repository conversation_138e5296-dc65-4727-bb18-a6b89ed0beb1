import { triggerEmail, withSentry, withSqsRecord } from '@pocitarna-nx-2023/aws';

export const handler = withSentry(
	'https://<EMAIL>/4509666423930960',
	withSqsRecord(async (record) => {
		await triggerEmail({
			recipients: ['<EMAIL>'],
			subject: "Assignment of warehouse position to amount envelope's products failed",
			message: `Failed to assign warehouse position to products in amount envelope for this payload: ${record.body}`,
		});
	}),
);
