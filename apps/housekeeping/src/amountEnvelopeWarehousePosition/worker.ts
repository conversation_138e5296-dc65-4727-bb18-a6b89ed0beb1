import { withSentry, withSqsRecord } from '@pocitarna-nx-2023/aws';
import {
	listAll,
	ProductController,
	ProductEnvelopeController,
	WarehousePositionController,
	withDatabase,
} from '@pocitarna-nx-2023/database';

// TODO: Need to decide how and when to trigger this
export const handler = withSentry(
	'https://<EMAIL>/4507848018690128',
	withDatabase(
		withSqsRecord(async (record) => {
			const { envelopeId, warehousePositionId } = JSON.parse(record.body);

			if (!envelopeId || !warehousePositionId) throw new Error('Invalid record');

			const envelope = await new ProductEnvelopeController().findById(envelopeId);
			if (!envelope || envelope.type !== 'AMOUNT') throw new Error('No amount envelope found with this id');

			const position = await new WarehousePositionController().findById(warehousePositionId);
			if (!position) throw new Error('Warehouse position not found');

			const [envelopeProducts] = await new ProductController().list(listAll({ filter: { productEnvelopeId: { eq: envelope.id } } }));
			await Promise.all(
				envelopeProducts.map(async (product) => {
					await new ProductController().setWarehousePosition(product.id, warehousePositionId);
				}),
			);
		}),
	),
);
