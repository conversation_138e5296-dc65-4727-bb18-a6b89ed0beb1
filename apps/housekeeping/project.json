{"name": "housekeeping", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/housekeeping/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"platform": "node", "outputPath": "dist/apps/housekeeping", "format": ["cjs"], "bundle": false, "main": "apps/housekeeping/src/main.ts", "tsConfig": "apps/housekeeping/tsconfig.app.json", "assets": ["apps/housekeeping/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {}, "production": {"esbuildOptions": {"sourcemap": false, "outExtension": {".js": ".js"}}}}}, "deploy": {"executor": "nx:run-commands", "defaultConfiguration": "staging", "configurations": {"staging": {"command": "nx run cdk:deploy:staging Housekeeping"}, "prod": {"command": "nx run cdk:deploy:prod Housekeeping"}}}, "serve": {"executor": "nx:run-commands", "options": {"commands": ["nx run cdk:synth:staging", "sam local start-lambda --template ./tmp/cdk.out/Housekeeping.template.json --env-vars ./lambda.dev.json --invoke-image gitlab.superkoders.com:5050/sk/pocitarna-nx-2023/lambda-runtime:latest", "nx run cdk:clean"], "parallel": false}}, "invoke:productPricesSync": {"executor": "nx:run-commands", "options": {"command": "aws lambda invoke --function-name ProductPricesSyncHandler --cli-read-timeout 900 --endpoint-url http://localhost:3001 --payload file://apps/housekeeping/src/productPricesSync/mock-event.json --cli-binary-format raw-in-base64-out --region eu-central-1 --no-verify-ssl /dev/null"}}, "invoke:amountEnvelopeWarehousePosition": {"executor": "nx:run-commands", "options": {"command": "aws lambda invoke --function-name AmountEnvelopeWarehousePositionHandler --cli-read-timeout 900 --endpoint-url http://localhost:3001 --payload file://apps/housekeeping/src/amountEnvelopeWarehousePosition/mock-event.json --cli-binary-format raw-in-base64-out --region eu-central-1 --no-verify-ssl /dev/null"}}, "lint": {}, "typecheck": {}}}