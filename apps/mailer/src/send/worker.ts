import { constructEmailMessage, downloadEmailFromBucket, send, withSentry } from '@pocitarna-nx-2023/aws';
import type { S3Event } from 'aws-lambda/trigger/s3';

export const handler = withSentry(
	'https://<EMAIL>/4507849339568208',
	async (event: S3Event) => {
		for (const record of event.Records) {
			const email = await downloadEmailFromBucket(record.s3.object.key);
			if (!email) throw new Error(`Email ${record.s3.object.key} does not exist`);
			const message = await constructEmailMessage(email);
			await send(email.recipients, message, email.cc);
		}
	},
);
