import { withSentry } from '@pocitarna-nx-2023/aws';
import type { SNSEvent } from 'aws-lambda';

// See: https://docs.aws.amazon.com/ses/latest/dg/notification-contents.html
export const handler = withSentry(
	'https://<EMAIL>/4507849339568208',
	async (event: SNSEvent) => {
		for (const record of event.Records) {
			const message = JSON.parse(record.Sns.Message);
			let block = false;

			if (
				message.notificationType === 'Bounce' &&
				(message.bounce.bounceType === 'Permanent' || message.bounce.bouncedRecipients?.[0]?.status === '5.4.4') // 5.4.4 - Invalid domain, failed DNS lookup
			) {
				console.warn('Bounce', message.mail.messageId, JSON.stringify(message.bounce));
				block = true;
			}

			if (message.notificationType === 'Complaint' && message.complaint.complaintFeedbackType !== 'not-spam') {
				console.warn('Complaint', message.mail.messageId, JSON.stringify(message.complaint));
				block = true;
			}

			if (block) {
				console.warn(`Should block recipients: ${message.mail.destination.join(', ')}`);
			}
		}
	},
);
