{"name": "mailer", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/mailer/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"platform": "node", "outputPath": "dist/apps/mailer", "format": ["cjs"], "bundle": true, "main": "apps/mailer/src/main.ts", "tsConfig": "apps/mailer/tsconfig.app.json", "assets": ["apps/mailer/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {}, "production": {"esbuildOptions": {"sourcemap": false, "outExtension": {".js": ".js"}}}}}, "deploy": {"executor": "nx:run-commands", "defaultConfiguration": "staging", "configurations": {"staging": {"command": "nx run cdk:deploy:staging Mail<PERSON>"}, "prod": {"command": "nx run cdk:deploy:prod Mailer"}}}, "serve": {"continuous": true, "executor": "nx:run-commands", "options": {"commands": ["nx run cdk:synth:staging", "sam local start-lambda --template ./tmp/cdk.out/Mailer.template.json --env-vars ./lambda.dev.json --invoke-image gitlab.superkoders.com:5050/sk/pocitarna-nx-2023/lambda-runtime:latest", "nx run cdk:clean"], "parallel": false}}, "invoke:send": {"executor": "nx:run-commands", "options": {"command": "aws lambda invoke --function-name Send --cli-read-timeout 900 --endpoint-url http://localhost:3001 --payload file://apps/mailer/src/send/mock-event.json --cli-binary-format raw-in-base64-out --region eu-central-1 --no-verify-ssl /dev/null"}}, "invoke:feedback": {"executor": "nx:run-commands", "options": {"command": "aws lambda invoke --function-name Feedback --cli-read-timeout 900 --endpoint-url http://localhost:3001 --payload file://apps/mailer/src/feedback/mock-event.json --cli-binary-format raw-in-base64-out --region eu-central-1 --no-verify-ssl /dev/null"}}, "lint": {}, "typecheck": {}}}