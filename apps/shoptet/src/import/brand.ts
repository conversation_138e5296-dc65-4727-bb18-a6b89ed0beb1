import { withSentry } from '@pocitarna-nx-2023/aws';
import { SHOPTET_IMPORT_BRAND_ENDPOINT } from '@pocitarna-nx-2023/config';
import { listAll, ShoptetBrandController, withAction, withDatabase } from '@pocitarna-nx-2023/database';
import { type Brand, createShoptetClient } from '@pocitarna-nx-2023/shoptet-client';

const shoptetClient = createShoptetClient('CZ');

const fetchBrands = async (page = 1): Promise<Brand[]> => {
	const response = await shoptetClient.brands.getListofBrands({ params: { page } });
	const brands = response.data.data.brands;
	if (page === response.data.data.paginator.pageCount) return brands;

	brands.push(...(await fetchBrands(page + 1)));
	return brands;
};

export const handler = withSentry(
	'https://<EMAIL>/4507848018690128',
	withDatabase(
		withAction(SHOPTET_IMPORT_BRAND_ENDPOINT, async () => {
			const brands = await fetchBrands();
			const [shoptetBrands] = await new ShoptetBrandController().list(listAll());

			// Sync shoptet brands
			for (const shoptetBrand of brands) {
				const shoptetBrandEntity = shoptetBrands.find((item) => item.shoptetId === shoptetBrand.guid);

				if (!shoptetBrandEntity) {
					await new ShoptetBrandController().create({
						shoptetId: shoptetBrand.guid,
						name: shoptetBrand.name,
						code: shoptetBrand.indexName,
					});
				} else {
					shoptetBrands.splice(shoptetBrands.indexOf(shoptetBrandEntity), 1);
					await new ShoptetBrandController().update(shoptetBrandEntity.id, {
						name: shoptetBrand.name,
						code: shoptetBrand.indexName,
					});
				}
			}

			// `shoptetBrands` is now only those that are not in the response
			if (shoptetBrands.length > 0) {
				const shoptetBrandIds = shoptetBrands.map((shoptetBrand) => shoptetBrand.id);
				await new ShoptetBrandController().delete(shoptetBrandIds);
			}
		}),
	),
);
