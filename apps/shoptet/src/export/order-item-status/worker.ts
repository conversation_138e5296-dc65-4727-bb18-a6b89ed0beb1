import { withSentry, withSqsRecord } from '@pocitarna-nx-2023/aws';
import { EcommerceOrderController, withDatabase } from '@pocitarna-nx-2023/database';
import { createShoptetClient } from '@pocitarna-nx-2023/shoptet-client';
import { z } from 'zod';
import { fetchOrderDetail } from '../../orders/utils';

const bodySchema = z.object({
	orderCode: z.string(),
	orderItemShoptetIdentifiers: z
		.union([z.coerce.number(), z.array(z.coerce.number())])
		.transform((val) => (Array.isArray(val) ? val : [val])),
	status: z.string(),
});

export const handler = withSentry(
	'https://<EMAIL>/4507848018690128',
	withDatabase(
		withSqsRecord(async (record) => {
			const parsedBody = bodySchema.safeParse(JSON.parse(record.body));

			if (!parsedBody.success) {
				console.error(parsedBody.error);
				throw new Error('Invalid body');
			}

			const { orderCode, orderItemShoptetIdentifiers, status } = parsedBody.data;

			const order = await new EcommerceOrderController().findByCode(orderCode);

			if (!order) throw new Error('Order not found');

			const client = createShoptetClient(order.country);
			const orderStatuses = await client.orders.getListOfOrderStatuses();
			const statusToApply = orderStatuses.data.data.statuses.find((item) => item.name === status);

			if (!statusToApply) throw new Error('Status to apply not found');

			const orderDetail = await fetchOrderDetail(orderCode, client);

			if (!orderDetail) throw new Error('Order detail not found');

			for (const identifier of orderItemShoptetIdentifiers) {
				const currentOrderItem = orderDetail.items.find((item) => item.itemId === identifier);

				if (!currentOrderItem || currentOrderItem.status.id === statusToApply.id) continue;

				const itemType = ['product', 'product-set'].includes(currentOrderItem.itemType)
					? currentOrderItem.itemType
					: currentOrderItem.productType && ['product', 'product-set'].includes(currentOrderItem.productType)
						? currentOrderItem.productType
						: 'product';

				await client.orderItems.updateOrderItem(orderCode, identifier.toString(), false, {
					data: { statusId: statusToApply.id, itemType },
				});
			}
		}),
	),
);
