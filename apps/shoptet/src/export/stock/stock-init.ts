import { withSentry } from '@pocitarna-nx-2023/aws';
import { createShoptetClient } from '@pocitarna-nx-2023/shoptet-client';

const shoptetClient = createShoptetClient('CZ');

export const handler = withSentry(
	'https://<EMAIL>/4507848018690128',
	async () => {
		const response = await shoptetClient.products.getListOfAllProducts(
			'perStockAmounts',
			undefined,
			undefined,
			undefined,
			undefined,
			undefined,
			undefined,
			'visible',
			undefined,
			undefined,
			undefined,
			undefined,
			undefined,
			'cmp',
		);

		// eslint-disable-next-line no-console
		console.log(response.data.data?.jobId);
	},
);
