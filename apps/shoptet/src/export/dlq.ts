import { triggerEmail, withSentry, withSqsRecord } from '@pocitarna-nx-2023/aws';
import { DOMAIN_NAME, ERROR_RECIPIENTS } from '@pocitarna-nx-2023/config';

export const handler = withSentry(
	'https://<EMAIL>/4507848018690128',
	withSqsRecord(async (record) => {
		await triggerEmail({
			recipients: ERROR_RECIPIENTS,
			subject: 'Shoptet export failed',
			message: `Failed to export product envelope <a href="https://cmp.${DOMAIN_NAME}/product/envelope/${record.body}">${record.body}</a>`,
		});
	}),
);
