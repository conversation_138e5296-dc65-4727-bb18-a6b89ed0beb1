import { withSentry } from '@pocitarna-nx-2023/aws';
import { SHOPTET_ORDERS_CLEANUP_ENDPOINT } from '@pocitarna-nx-2023/config';
import { listAll, ShoptetOrderSyncController, withAction, withDatabase } from '@pocitarna-nx-2023/database';
import { startOfDay, sub } from 'date-fns';

export const handler = withSentry(
	'https://<EMAIL>/4507848018690128',
	withDatabase(
		withAction(SHOPTET_ORDERS_CLEANUP_ENDPOINT, async () => {
			const toYesterday = startOfDay(sub(new Date(), { days: 1 }));
			const [oldRecords] = await new ShoptetOrderSyncController().list(listAll({ filter: { finishedAt: { lte: toYesterday } } }));
			await new ShoptetOrderSyncController().delete(oldRecords.map(({ id }) => id));
		}),
	),
);
