/* eslint-disable no-console */
import { SYSTEM_USER_AUTH_ID } from '@pocitarna-nx-2023/config';
import * as QRCode from 'qrcode';
import superagent from 'superagent';
import { parseEnv } from './parseEnv';

const { API_ORIGIN, NEXTAUTH_SECRET } = parseEnv();

const blobToBase64 = async (blob: Blob) => {
	const arrayBuffer = await blob.arrayBuffer();
	const buffer = Buffer.from(arrayBuffer);
	return buffer.toString('base64');
};

let isRunning = false;

export const getAllAndUpload = async (abortController: AbortController) => {
	if (isRunning) return;
	isRunning = true;
	try {
		const sessionResponse = await superagent
			.post(`${API_ORIGIN}/auth/session/verification-token`)
			.send({ verificationToken: SYSTEM_USER_AUTH_ID })
			.set('Content-Type', 'application/json')
			.set('x-auth-secret', NEXTAUTH_SECRET);

		if (!sessionResponse.ok) {
			throw new Error(`Failed to create session: ${sessionResponse.status}`);
		}

		const sessionData = sessionResponse.body as { _data: { sessionToken: string } };
		const sessionToken = sessionData?._data?.sessionToken;
		if (!sessionToken) return console.error('Nelze vytvořit sessionToken');

		console.info('Vyčítání produktu...');
		const result: { sysInfo: object | null; aggreg: object | null; pnp: object | null; hwInfo: Buffer | null; gpuz: Buffer | null } = {
			sysInfo: null,
			aggreg: null,
			pnp: null,
			hwInfo: null,
			gpuz: null,
		};

		await Promise.all([
			(async () => {
				console.info('Vyčítání systemInfa započalo...');
				const { getSystemInfo } = await import('./getSystemInfo');
				result.sysInfo = await getSystemInfo();
				console.info('Vyčítání systemInfa dokončeno.');
			})(),
			(async () => {
				if (process.platform === 'win32') {
					console.info('Vyčítání HWInfo započalo...');
					const { getHwInfo } = await import('./getHwInfo');
					result.hwInfo = await getHwInfo(abortController.signal);
					console.info('Vyčítání HWInfo dokončeno.');
				}
			})(),
			(async () => {
				if (process.platform === 'win32') {
					console.info('Vyčítání GPUz započalo...');
					const { getGpuz } = await import('./getGpuz');
					result.gpuz = await getGpuz(abortController.signal);
					console.info('Vyčítání GPUz dokončeno.');
				}
			})(),
			(async () => {
				if (process.platform === 'win32') {
					console.info('Vyčítání aggreg započalo...');
					const { getAggreg } = await import('./getAggreg');
					result.aggreg = await getAggreg();
					console.info('Vyčítání aggreg dokončeno.');
				}
			})(),
			(async () => {
				if (process.platform === 'win32') {
					console.info('Vyčítání pnp započalo...');
					const { getPnp } = await import('./getPnp');
					result.pnp = await getPnp();
					console.info('Vyčítání pnp dokončeno.');
				}
			})(),
		]);

		console.info('Zpracovávání dat produktu...');
		const { sysInfo, aggreg, pnp, hwInfo, gpuz } = result;
		const readers = [
			{
				fileName: 'systeminfo.json',
				type: 'application/json',
				data: sysInfo && new Blob([JSON.stringify(sysInfo)], { type: 'application/json' }),
			},
			{
				fileName: 'gpuz.xml',
				type: 'application/xml',
				data: gpuz && new Blob([gpuz as Buffer<ArrayBuffer>], { type: 'application/xml' }),
			},
			{
				fileName: 'pnp.json',
				type: 'application/json',
				data: pnp && new Blob([JSON.stringify(pnp)], { type: 'application/json' }),
			},
			{
				fileName: 'aggreg.json',
				type: 'application/json',
				data: aggreg && new Blob([JSON.stringify(aggreg)], { type: 'application/json' }),
			},
			{
				fileName: 'hwinfo.xml',
				type: 'application/xml',
				data: hwInfo && new Blob([hwInfo as Buffer<ArrayBuffer>], { type: 'application/xml' }),
			},
		];
		console.info('Vyčtení produktu dokončeno.');

		const fileIds = [];

		console.info('Nahrávání dat do CMP...');
		for (const reader of readers) {
			if (!reader.data) continue;

			try {
				const fileResponse = await superagent
					.post(`${API_ORIGIN}/files/upload/base64`)
					.set('Content-Type', 'application/json')
					.set('Cookie', `next-auth.session-token=${sessionToken}`)
					.send({ name: reader.fileName, data: await blobToBase64(reader.data) });

				const fileData = fileResponse.body as { _data: string };
				fileIds.push(fileData._data);
			} catch {
				throw new Error(`Nepodařio se nahrát soubor ${reader.fileName}`);
			}
		}

		try {
			const productResponse = await superagent
				.post(`${API_ORIGIN}/product/attribute-value-loader`)
				.set('Content-Type', 'application/json')
				.set('Cookie', `next-auth.session-token=${sessionToken}`)
				.send({ fileIds });

			const sn = (productResponse.body as { _data: string })._data;

			console.info('Nahrávání dat do CMP dokončeno.\n');
			console.info(await QRCode.toString(sn, { type: 'terminal' }));
			console.info('Načti QR kód SN do CMP pro dokončení testu produktu');
		} catch (error) {
			if ((<any>error).status === 404) {
				throw new Error(`Nepodařilo se nalézt produkt`);
			}
			throw new Error(`Nepodařilo se přiřadit parametry k produktu: ${(<any>error).status}`);
		}
	} catch (error) {
		console.error(error);
		console.info('Automatické zpracovávání dat zastaveno. Můžete jej spustit znovu pomocí klávesy Enter.');
	} finally {
		isRunning = false;
	}
};
