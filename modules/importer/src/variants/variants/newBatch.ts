import { RECOMMENDED_PRICE_IMPORT_NAME, STANDARD_PRICE_IMPORT_NAME } from '@pocitarna-nx-2023/config';
import {
	AttributeController,
	AttributeV<PERSON>ue<PERSON>ontroller,
	GradeController,
	listAll,
	listOne,
	type Product,
	ProductCategoryController,
	ProductCodeController,
	ProductController,
	ProductEnvelopeController,
} from '@pocitarna-nx-2023/database';
import { withoutVat } from '@pocitarna-nx-2023/utils';
import { Decimal } from 'decimal.js';
import * as XLSX from 'xlsx';
import { type Column, type Variant } from '../../types';
import { BaseVariantManager } from '../../utils/BaseVariantManager';

const QUANTITY = 'množství';
const BUY_PRICE = 'nákupní cena';
const CURRENCY = 'měna';
const COMMON_COLUMNS: Column[] = [
	{ name: QUANTITY, type: 'number', required: true },
	{ name: CURRENCY, type: 'string', required: true },
];
const PRICES_COLUMNS: Column[] = [
	{ name: STANDARD_PRICE_IMPORT_NAME, type: 'number', required: true },
	{ name: RECOMMENDED_PRICE_IMPORT_NAME, type: 'number', required: true },
	{ name: BUY_PRICE, type: 'number', required: true },
];

export class NewBatch extends BaseVariantManager {
	protected async buildVariant(): Promise<Variant> {
		return {
			extension: 'xlsx',
			name: 'New Batch',
			currency: 'CZK',
			productNumberColumn: '',
			quantityColumn: QUANTITY,
			columns: await this.buildColumns(),
			batchType: 'NEW',
		};
	}

	protected override async parseFile(file: Uint8Array): Promise<Record<string, unknown>[]> {
		const workbook = XLSX.read(file, { type: 'buffer' });

		const sheetNames = workbook.SheetNames;
		const categoryName = sheetNames[0];
		const category = await new ProductCategoryController().findByName(categoryName);

		if (category) {
			const [categoryAttributes] = await new AttributeController().listByCategory(category.id, {
				filter: { 'categoryAttributes.type': { eq: 'envelope' } },
				sort: [['categoryAttributes.sequence', 'asc']],
			});
			this.categoryData = { id: category.id, attributes: categoryAttributes };
		}

		return super.parseFile(file);
	}

	override async processRow(data: Record<string, unknown>) {
		const categoryId = this.categoryData?.id;
		const categoryAttributes = this.categoryData?.attributes ?? [];
		const productCanBeProcessed = Object.values(data).every((value) => value !== undefined && value !== '');
		const standardPrice = this.normalizePrice(data[STANDARD_PRICE_IMPORT_NAME]);
		const standardPriceWithoutVAT = new Decimal(withoutVat(standardPrice ?? 0));

		if (!productCanBeProcessed || !categoryId) return null;

		const attributeValues = await this.columnValues(data, [
			QUANTITY,
			RECOMMENDED_PRICE_IMPORT_NAME,
			STANDARD_PRICE_IMPORT_NAME,
			BUY_PRICE,
			CURRENCY,
		]);

		const [productAttributeValues] = await new AttributeValueController().list(
			listAll({
				filter: {
					id: { eq: attributeValues.map((value) => value.attributeValueId) },
				},
			}),
		);

		const matchingEnvelope = await new ProductEnvelopeController().findByAttributeValues(
			attributeValues.map((value) => value.attributeValueId),
			{
				filter: {
					productCategoryId: { eq: categoryId },
					type: { eq: 'NEW' },
				},
			},
		);

		if (
			matchingEnvelope &&
			standardPrice != null &&
			standardPrice > 0 &&
			!matchingEnvelope.standardPrice.equals(standardPriceWithoutVAT)
		) {
			await new ProductEnvelopeController().update(matchingEnvelope.id, {
				standardPrice: standardPriceWithoutVAT,
			});
		}

		const envelopeForProduct =
			matchingEnvelope ??
			(await new ProductEnvelopeController().create({
				name: new ProductEnvelopeController().createName(categoryAttributes, productAttributeValues),
				productCategory: { id: categoryId },
				type: 'NEW',
				attributeValues,
				standardPrice: standardPriceWithoutVAT,
			}));

		const aGrade = (await new GradeController().list(listOne({ filter: { name: { eq: 'A' } } })))?.[0]?.[0];
		if (!aGrade) throw new Error('Grade A not found');

		const product = await new ProductController().create({
			status: 'AT_SUPPLIER',
			batch: { id: this.batchId },
			files: this.files,
			productCategory: { id: categoryId },
			productEnvelope: { id: envelopeForProduct.id },
			grade: { id: aGrade.id },
			type: 'NEW',
		});

		await this.handlePricesAndCode(data, product.id);

		return product;
	}

	async handlePricesAndCode(data: Record<string, unknown>, productId: Product['id']) {
		const buyPrice = this.normalizePrice(data[BUY_PRICE]);
		const recommendedPrice = this.normalizePrice(data[RECOMMENDED_PRICE_IMPORT_NAME]);

		if (buyPrice && buyPrice > 0) await new ProductController().addBuyPrice(productId, buyPrice);

		if (recommendedPrice && recommendedPrice > 0) {
			await new ProductController().addRecommendedPrice(productId, withoutVat(recommendedPrice));
		}

		await new ProductCodeController().create({ batch: { id: this.batchId } });
	}

	async buildColumns() {
		const productCategories = await new ProductCategoryController().getAllFlattenedCategories();

		const allCategoriesColumns: Column[][] = [];

		await Promise.all(
			productCategories.map(async (productCategory) => {
				const categorySpecificColumns: Column[] = [];

				const [envelopeCreationAttributesForCategory] = await new AttributeController().listByCategory(productCategory.id, {
					filter: { 'categoryAttributes.type': { eq: 'envelope' }, displayName: { ne: ['Stav', 'Typ produktu'] } },
					sort: [['categoryAttributes.sequence', 'asc']],
				});

				envelopeCreationAttributesForCategory.forEach((attribute) => {
					const trimmedLowerCaseName = attribute.displayName.trim().toLowerCase();

					if (attribute.dataType === 'boolean') {
						categorySpecificColumns.push({ name: trimmedLowerCaseName, type: attribute.dataType, truthyValue: 'Ano' });
					}

					if (attribute.dataType === 'number') {
						categorySpecificColumns.push({ name: trimmedLowerCaseName, type: 'string' });
					}

					if (['string', 'text', 'multiselect'].includes(attribute.dataType)) {
						categorySpecificColumns.push({ name: trimmedLowerCaseName, type: 'string' });
					}
				});

				allCategoriesColumns.push([...COMMON_COLUMNS, ...categorySpecificColumns, ...PRICES_COLUMNS]);
			}),
		);

		return allCategoriesColumns;
	}

	normalizePrice(price: unknown) {
		return price && !isNaN(Number(price)) ? Number(price) : null;
	}
}
