/* eslint-disable no-console */
import {
	AttributeController,
	AttributeValueConditionalAttributeController,
	AttributeValueController,
	ConditionalAttributeController,
	Database,
	listAll,
} from '@pocitarna-nx-2023/database';
import { filterUndefined, type Primitive } from '@pocitarna-nx-2023/utils';
import { readFile } from 'fs/promises';
import { FileExtensionManager } from '../formats/FileExtensionsManager';
import type { Column } from '../types';

// const CPU_TYPE_ATTRIBUTE_NAME = 'cpu - typ';
const CPU_TURBO_BOOST_ATTRIBUTE_NAME = 'cpu - frekvence turbo mode';
const CPU_FREQUENCY_ATTRIBUTE_NAME = 'cpu - frekvence';
const CPU_LOGICAL_CORES_ATTRIBUTE_NAME = 'cpu - počet logických cpu';
const CPU_CORES_ATTRIBUTE_NAME = 'cpu - počet jader';

const columns = [
	{ name: 'cpu', type: 'custom', parse: (value) => value ?? '' },
	{ name: 'gpu integrovaná - model', type: 'custom', parse: (value) => value ?? '' },
	{ name: CPU_LOGICAL_CORES_ATTRIBUTE_NAME, type: 'custom', parse: (value) => Number(value || 0) },
	{ name: CPU_CORES_ATTRIBUTE_NAME, type: 'custom', parse: (value) => Number(value || 0) },
	{ name: 'cpu - označení', type: 'custom', parse: (value) => value ?? '' },
	{
		name: CPU_TURBO_BOOST_ATTRIBUTE_NAME,
		type: 'custom',
		parse: (value) => {
			if (!value) return '';
			const match = /\d+\.?\d*/.exec(value);
			return match ? Number(match[0]) : '';
		},
	},
	{
		name: CPU_FREQUENCY_ATTRIBUTE_NAME,
		type: 'custom',
		parse: (value) => {
			if (!value) return '';
			const match = /\d+\.?\d*/.exec(value);
			return match ? Number(match[0]) : '';
		},
	},
	// { name: CPU_TYPE_ATTRIBUTE_NAME, type: 'custom', parse: (value) => value?.replace(/\s+/g, ' ').trim() ?? '' },
] as const satisfies Column[];

type Key = (typeof columns)[number]['name'];

const attributeMapping: Partial<Record<Key, string>> = {
	cpu: 'CPU',
	'gpu integrovaná - model': 'GPU integrovaná - model',
	// [CPU_LOGICAL_CORES_ATTRIBUTE_NAME]: 'CPU - počet logických CPU',
	'cpu - počet jader': 'CPU - počet jader',
	'cpu - označení': 'CPU - označení',
	[CPU_TURBO_BOOST_ATTRIBUTE_NAME]: 'CPU - frekvence turbo mode',
	[CPU_FREQUENCY_ATTRIBUTE_NAME]: 'CPU - frekvence',
	// [CPU_TYPE_ATTRIBUTE_NAME]: 'CPU - typ',
};

const run = async () => {
	await Database.initialize();
	const file = await readFile(process.argv[2]);
	const rawData = (await new FileExtensionManager().importFile(file, columns)) as Record<Key, Primitive>[];

	const data = rawData.map((row) => {
		const processed = columns.reduce(
			(acc, { name, parse }) => {
				acc[name] = parse(String(row[name] ?? ''));
				return acc;
			},
			{} as Record<Key, Primitive>,
		);

		if (!processed[CPU_TURBO_BOOST_ATTRIBUTE_NAME] && processed[CPU_FREQUENCY_ATTRIBUTE_NAME]) {
			processed[CPU_TURBO_BOOST_ATTRIBUTE_NAME] = processed[CPU_FREQUENCY_ATTRIBUTE_NAME];
		}

		if (!processed[CPU_LOGICAL_CORES_ATTRIBUTE_NAME] && processed[CPU_CORES_ATTRIBUTE_NAME]) {
			processed[CPU_LOGICAL_CORES_ATTRIBUTE_NAME] = processed[CPU_CORES_ATTRIBUTE_NAME];
		}

		return processed;
	});

	const [attributes] = await new AttributeController().list(listAll({ filter: { name: { eq: Object.values(attributeMapping) } } }));
	const keyAttribute = attributes.find((attribute) => attribute.name === 'CPU');
	if (!keyAttribute) return;

	// Ensure all values exist
	for (const row of data) {
		for (const [key, value] of Object.entries(row)) {
			const attribute = attributes.find((attribute) => attribute.name === attributeMapping[key as Key]);
			if (!attribute) continue;
			if (value === '' || value === null || value === undefined || value === 0) continue;
			await new AttributeValueController().findOrCreate(attribute.id, value);
		}
	}

	// Propagate to conditional attributes
	for (const row of data) {
		const keyAttributeValue = await new AttributeValueController().findOrCreate(keyAttribute.id, row['cpu']);
		const conditionalAttributeValues = filterUndefined(
			await Promise.all(
				(Object.keys(attributeMapping) as Key[]).slice(1).map(async (key) => {
					if (!row[key]) return null;

					const attribute = attributes.find((attribute) => attribute.name === attributeMapping[key]);
					if (!attribute) return null;

					return new AttributeController().findValue(attribute.id, row[key]);
				}),
			),
		);

		console.log(`${keyAttributeValue.value} => ${conditionalAttributeValues.map(({ value }) => value).join(', ')}`);

		const conditionalAttribute = await new ConditionalAttributeController().findOrCreate(keyAttributeValue);
		await new AttributeValueConditionalAttributeController().unlinkAll(conditionalAttribute.id);
		await new AttributeValueConditionalAttributeController().link(
			conditionalAttribute.id,
			conditionalAttributeValues.map(({ id }) => id),
		);
	}

	// await assignCPUTypeAttributeValueToProductsAndEnvelopes({ CPUAttribute, CPUTypeAttribute });
};

run();

// const assignCPUTypeAttributeValueToProductsAndEnvelopes = async ({
// 	CPUAttribute,
// 	CPUTypeAttribute,
// }: {
// 	CPUAttribute: Attribute;
// 	CPUTypeAttribute: Attribute;
// }) => {
// 	const [CPUAttributeValues] = await new AttributeValueController().list(
// 		listAll({
// 			filter: {
// 				'attribute.id': { eq: CPUAttribute.id },
// 			},
// 		}),
// 	);
// 	const queryBuilder = new AttributeValueController()
// 		.getProductByAttributeValueIdQueryBuilder()
// 		.leftJoinAndSelect('product.productEnvelope', 'productEnvelope');
//
// 	const CPUAttributeValuesWithProducts = await new AttributeValueController().findProductsByAttributeValueId(
// 		CPUAttributeValues.map((item) => item.id),
// 		queryBuilder,
// 	);
//
// 	for (const CPUAttributeValueWithProducts of CPUAttributeValuesWithProducts) {
// 		const productsIds = filterUndefined(
// 			uniques(CPUAttributeValueWithProducts.products.flatMap((productAttributeValue) => productAttributeValue.product?.id)),
// 		);
//
// 		const CPUTypeAttributeValueToAssign = await new AttributeValueController().findOrCreate(
// 			CPUTypeAttribute.id,
// 			CPUAttributeValueWithProducts.value,
// 		);
//
// 		for (const productId of productsIds) {
// 			await new ProductController().setAttributeValue(
// 				productId,
// 				{
// 					attributeId: CPUTypeAttribute.id,
// 					attributeValueId: CPUTypeAttributeValueToAssign.id,
// 				},
// 				'resolved',
// 			);
// 			// eslint-disable-next-line no-console
// 			console.debug(`Added attribute value ${CPUAttributeValueWithProducts.value} to product ${productId}`);
// 		}
//
// 		const envelopesIds = filterUndefined(
// 			uniques(
// 				CPUAttributeValueWithProducts.products.flatMap(
// 					(productAttributeValue) => productAttributeValue.product?.productEnvelope?.id,
// 				),
// 			),
// 		);
//
// 		for (const envelopeId of envelopesIds) {
// 			const envelope = await new ProductEnvelopeController().findById(envelopeId);
//
// 			if (envelope) {
// 				await new ProductEnvelopeController().addAttributeValue(envelope.id, CPUTypeAttributeValueToAssign.id);
// 				// eslint-disable-next-line no-console
// 				console.debug(`Added attribute value ${CPUAttributeValueWithProducts.value} to product envelope ${envelope.id}`);
// 				// FIXME: won't this cause shoptet to rate-limit us?
// 				await triggerShoptetExport(envelopeId);
// 			}
// 		}
// 	}
// };
