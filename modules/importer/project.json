{"name": "importer", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "modules/importer/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/modules/importer", "main": "modules/importer/src/index.ts", "tsConfig": "modules/importer/tsconfig.lib.json", "assets": []}}, "import": {"executor": "nx:run-commands", "options": {"forwardAllArgs": true}, "configurations": {"amd": {"command": "npx ts-node --project {projectRoot}/tsconfig.lib.json {projectRoot}/src/cli/amd.ts"}, "pocitarna-cpus": {"command": "npx ts-node --project {projectRoot}/tsconfig.lib.json {projectRoot}/src/cli/pocitarna-cpus.ts"}, "pocitarna-conditional-attributes": {"command": "npx ts-node --project {projectRoot}/tsconfig.lib.json {projectRoot}/src/cli/pocitarna-conditional-attributes.ts"}, "cpu": {"command": "npx ts-node --project {projectRoot}/tsconfig.lib.json {projectRoot}/src/cli/cpu.ts"}}}, "lint": {}, "typecheck": {}}}