import * as XLSX from 'xlsx';
import { AttributeController, type AttributeValue, ConditionalAttributeController, Database, listAll } from '../src';

const attributeName = 'CPU';

const resolveCpuType = ({ value }: AttributeValue) => {
	if (typeof value !== 'string') return;
	if (value.startsWith('AMD')) {
		return /^(AMD (Athlon (Gold|PRO)|Ryzen( \d| Threadripper)?( PRO)?|Phenom II|.*?))\s/gi.exec(value)?.[1] ?? '';
	}

	if (value.startsWith('Intel')) {
		return (
			/^(Intel (Core (i\d|Ultra \d|Duo|2 \w+|\d)|Atom( x\d)?|Pentium( .| III| Gold| Silver)|Celeron( \w)?|Xeon( E\d)|.*?))(\s|-)/gi.exec(
				value,
			)?.[1] ?? ''
		);
	}

	return '';
};

const run = async () => {
	await Database.initialize();

	const attributes = await new AttributeController().findByName(attributeName);
	if (!attributes) return;
	const attribute = attributes[0];
	if (!attribute) return;

	const [conditionalAttributes] = await new ConditionalAttributeController().list(
		listAll({
			filter: { 'attribute.id': { eq: attribute.id } },
			sort: [
				['attributeValue.value', 'asc'],
				['conditionalAttribute.name', 'desc'],
			],
		}),
	);

	const data = conditionalAttributes.map(({ attributeValue, conditionalAttributeValues }) => ({
		[attributeValue.attribute.name]: attributeValue.value,
		...conditionalAttributeValues.reduce<Record<string, string>>((acc, { attributeValue }) => {
			acc[attributeValue.attribute.name] = attributeValue.value.toString();
			return acc;
		}, {}),
		['CPU - typ']: resolveCpuType(attributeValue),
	}));

	const workbook = XLSX.utils.book_new();
	const sheet = XLSX.utils.json_to_sheet(data);
	XLSX.utils.book_append_sheet(workbook, sheet, attributeName);
	XLSX.writeFile(workbook, `${attributeName}.xlsx`);
};

run();
