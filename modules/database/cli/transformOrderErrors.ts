import { readFileSync } from 'node:fs';
import * as XLSX from 'xlsx';
import { Database, type EcommerceOrder, EcommerceOrderController } from '../src';

const run = async () => {
	await Database.initialize();
	const lines = readFileSync('orders.log').toString('utf-8').split('\n');
	let order: EcommerceOrder | null = null;
	const result = [];

	for (const line of lines) {
		if (line.startsWith('INFO')) {
			const orderCode = line.split('orderCode ')[1];
			order = await new EcommerceOrderController().findByCode(orderCode);
		} else if (line.startsWith('ERROR')) {
			if (!order) continue;

			result.push({
				orderCode: order.code,
				orderUrl: order.shoptetUrl,
				orderStatus: order.status,
				message: line.split('\t')[1],
			});
		}
	}

	const workbook = XLSX.utils.book_new();
	const sheet = XLSX.utils.json_to_sheet(result);
	XLSX.utils.book_append_sheet(workbook, sheet);
	XLSX.writeFile(workbook, `orders.xlsx`);
};

run();
