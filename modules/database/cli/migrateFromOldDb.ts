/* eslint-disable no-console */
import { DataSource } from 'typeorm';
import * as entities from '../src/entity';
import { NamingStrategy } from '../src/utils/NamingStrategy';

const sourceDb = new DataSource({
	type: 'postgres',
	host: process.env.SOURCE_DB_HOST || 'localhost',
	port: parseInt(process.env.SOURCE_DB_PORT || '5432'),
	username: process.env.SOURCE_DB_USER || 'postgres',
	password: process.env.SOURCE_DB_PASS || 'rootroot',
	database: 'pocitarnanx2023_old4',
	synchronize: false,
	logging: ['info', 'warn', 'error'],
	namingStrategy: new NamingStrategy(),
});

const destDb = new DataSource({
	type: 'postgres',
	host: process.env.DEST_DB_HOST || 'localhost',
	port: parseInt(process.env.DEST_DB_PORT || '5432'),
	username: process.env.DEST_DB_USER || 'postgres',
	password: process.env.DEST_DB_PASS || 'rootroot',
	database: 'pocitarnanx2023',
	synchronize: false,
	entities,
	logging: ['info', 'warn', 'error'],
	namingStrategy: new NamingStrategy(),
});

async function getSourceTables(queryRunner) {
	const tables = await queryRunner.query(`
		SELECT table_name
		FROM information_schema.tables
		WHERE table_schema = 'public'
		  AND table_type = 'BASE TABLE'
		ORDER BY table_name
	`);
	return tables.map((t) => t.table_name);
}

async function getTableColumns(queryRunner, tableName) {
	const columns = await queryRunner.query(
		`
			SELECT column_name
			FROM information_schema.columns
			WHERE table_schema = 'public'
			  AND table_name = $1
			ORDER BY ordinal_position
		`,
		[tableName],
	);
	return columns.map((c) => c.column_name);
}

async function hasColumn(queryRunner, tableName, columnName) {
	const columns = await getTableColumns(queryRunner, tableName);
	return columns.includes(columnName);
}

async function getCompatibleColumns(sourceQueryRunner, destQueryRunner, sourceTableName, destTableName) {
	const sourceColumns = await getTableColumns(sourceQueryRunner, sourceTableName);
	const destColumns = await getTableColumns(destQueryRunner, destTableName);

	// Only include columns that exist in both source and destination tables
	return sourceColumns.map((columnName) => columnName.replace('Version', '')).filter((columnName) => destColumns.includes(columnName));
}

async function migrateVersionTable(sourceQueryRunner, destQueryRunner, versionTableName, versionIdMap) {
	console.log(`Migrating ${versionTableName}...`);

	// Determine base table name (remove "Version" suffix)
	const baseTableName = versionTableName.slice(0, -7);
	const historyTableName = `${baseTableName}History`;

	console.log(`  Base table: ${baseTableName}`);
	console.log(`  History table: ${historyTableName}`);

	// Get columns that exist in both source and destination tables
	const columns = await getCompatibleColumns(sourceQueryRunner, destQueryRunner, versionTableName, baseTableName);
	const columnsList = columns.map((c) => `"${c}"`).join(', ');

	// Check if the version table has a deletedAt column
	const hasDeletedAtColumn = await hasColumn(sourceQueryRunner, versionTableName, 'deletedAt');

	// Check if the base table has a deletedAt column
	const baseTableHasDeletedAtColumn = await hasColumn(sourceQueryRunner, baseTableName, 'deletedAt');

	// Get all versions from the table, conditionally excluding deleted records and those with deleted parents
	const allVersions = await sourceQueryRunner.query(`
		SELECT v.*
		FROM "${versionTableName}" v
			${hasDeletedAtColumn || baseTableHasDeletedAtColumn ? 'WHERE' : ''} ${hasDeletedAtColumn ? 'v."deletedAt" IS NULL' : ''}
			${hasDeletedAtColumn && baseTableHasDeletedAtColumn ? 'AND' : ''}
			${
				baseTableHasDeletedAtColumn
					? `NOT EXISTS (
			SELECT 1 FROM "${baseTableName}" b
			WHERE b.id = v."parentId" AND b."deletedAt" IS NOT NULL
		)`
					: ''
			}
		ORDER BY v."parentId", v."createdAt" DESC
	`);

	console.log(`  Found ${allVersions.length} total versions in ${versionTableName}`);

	// Use the versionIdMap to separate latest and older versions
	const latestVersions = [];
	const olderVersions = [];

	for (const version of allVersions) {
		const versionInfo = versionIdMap.get(version.id);
		if (versionInfo && versionInfo.isLatest) {
			latestVersions.push(version);
		}
		olderVersions.push(version);
	}

	console.log(`  Found ${latestVersions.length} latest versions to migrate to ${baseTableName}`);
	console.log(`  Found ${olderVersions.length} older versions to migrate to ${historyTableName}`);

	// Start a transaction
	await destQueryRunner.startTransaction();

	try {
		// Disable foreign key constraints but keep sequence incrementing
		await destQueryRunner.query(`SET session_replication_role = 'replica'`);

		// Insert latest versions into base table
		if (latestVersions.length > 0) {
			// Process records in chunks to avoid parameter limit
			const CHUNK_SIZE = 1000; // Adjust based on the number of columns
			let processedCount = 0;

			await destQueryRunner.query(`AlTER TABlE "${baseTableName}"
				DISABLE TRIGGER USER`);

			for (let i = 0; i < latestVersions.length; i += CHUNK_SIZE) {
				const chunk = latestVersions.slice(i, i + CHUNK_SIZE);
				console.log(
					`  Processing latest versions chunk ${i / CHUNK_SIZE + 1} of ${Math.ceil(latestVersions.length / CHUNK_SIZE)} (${chunk.length} records)`,
				);

				// Create placeholders for the INSERT query
				const placeholders = chunk
					.map((_, idx) => {
						const placeholderGroup = columns.map((_, colIdx) => `$${idx * columns.length + colIdx + 1}`).join(', ');
						return `(${placeholderGroup})`;
					})
					.join(', ');

				// Flatten all values for the query parameters
				const values = chunk.flatMap((version) =>
					columns.map((col) => {
						// Replace id with parentId for version tables
						if (col === 'id') {
							return version.parentId;
						}

						// Check if this column might contain a version ID that needs to be converted to a parent ID
						// Skip id and parentId columns as they are handled separately
						const value = version[col] ?? version[col.replace(/Id$/, 'VersionId')];
						if (col !== 'id' && col !== 'parentId' && col.endsWith('Id') && value !== null && value !== undefined) {
							const versionInfo = versionIdMap.get(value);
							if (versionInfo) {
								console.log(
									`    Converting version ID ${value} to parent ID ${versionInfo.parentId} in ${versionTableName}.${col}`,
								);
								return versionInfo.parentId;
							}
						}

						return value;
					}),
				);

				// Insert into base table
				await destQueryRunner.query(
					`
						INSERT INTO "${baseTableName}" (${columnsList})
						VALUES
						${placeholders}
						ON CONFLICT (id)
						DO NOTHING
					`,
					values,
				);

				processedCount += chunk.length;
				console.log(`  Processed ${processedCount} of ${latestVersions.length} latest version records`);
			}

			await destQueryRunner.query(`AlTER TABlE "${baseTableName}"
				ENABLE TRIGGER USER`);

			console.log(`  Inserted ${latestVersions.length} records into ${baseTableName}`);
		}

		// Insert older versions into history table
		if (olderVersions.length > 0) {
			// Get columns that exist in both source version table and destination history table
			const historyCompatibleColumns = await getCompatibleColumns(
				sourceQueryRunner,
				destQueryRunner,
				versionTableName,
				historyTableName,
			);
			// Add period column for history tables
			const historyColumns = [...historyCompatibleColumns, 'period'];
			const historyColumnsList = historyColumns.map((c) => `"${c}"`).join(', ');

			// Group versions by parentId to process them in order
			const versionsByParentId = {};
			olderVersions.forEach((version) => {
				if (!versionsByParentId[version.parentId]) {
					versionsByParentId[version.parentId] = [];
				}
				versionsByParentId[version.parentId].push(version);
			});

			// Process each group to set the correct period end dates
			const processedVersions = [];
			Object.values(versionsByParentId).forEach((versions: { createdAt: string }[]) => {
				// Sort versions by createdAt in ascending order (oldest first)
				versions.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

				// Process each version
				for (let i = 0; i < versions.length; i++) {
					const currentVersion = versions[i];
					const nextVersion = i < versions.length - 1 ? versions[i + 1] : null;

					// Add the version with its period
					processedVersions.push({
						...currentVersion,
						periodStart: currentVersion.createdAt,
						periodEnd: nextVersion ? nextVersion.createdAt : null,
					});
				}
			});

			// Process records in chunks to avoid parameter limit
			const CHUNK_SIZE = 1000; // Adjust based on the number of columns
			let processedCount = 0;

			await destQueryRunner.query(`AlTER TABlE "${historyTableName}"
				DISABLE TRIGGER USER`);

			for (let i = 0; i < processedVersions.length; i += CHUNK_SIZE) {
				const chunk = processedVersions.slice(i, i + CHUNK_SIZE);
				console.log(
					`  Processing older versions chunk ${i / CHUNK_SIZE + 1} of ${Math.ceil(processedVersions.length / CHUNK_SIZE)} (${chunk.length} records)`,
				);

				// Create placeholders for the INSERT query
				const placeholders = chunk
					.map((_, idx) => {
						const placeholderGroup = historyColumns
							.map((_, colIdx) => {
								// For the period column, use a tstzrange expression with start and end
								if (colIdx === historyColumns.length - 1) {
									return `tstzrange($${idx * (historyColumns.length + 1) + colIdx + 1}, $${idx * (historyColumns.length + 1) + historyColumns.length + 1}, '[)')`;
								}
								return `$${idx * (historyColumns.length + 1) + colIdx + 1}`;
							})
							.join(', ');
						return `(${placeholderGroup})`;
					})
					.join(', ');

				// Flatten all values for the query parameters, adding periodStart and periodEnd for the period column
				const values = chunk.flatMap((version) => [
					...historyCompatibleColumns.map((col) => {
						// Replace id with parentId for version tables
						if (col === 'id') {
							return version.parentId;
						}

						// Check if this column might contain a version ID that needs to be converted to a parent ID
						// Skip id and parentId columns as they are handled separately
						const value = version[col] ?? version[col.replace(/Id$/, 'VersionId')];
						if (col !== 'id' && col !== 'parentId' && col.endsWith('Id') && value !== null && value !== undefined) {
							const versionInfo = versionIdMap.get(value);
							if (versionInfo) {
								console.log(
									`    Converting version ID ${value} to parent ID ${versionInfo.parentId} in ${historyTableName}.${col}`,
								);
								return versionInfo.parentId;
							}
						}

						return value;
					}),
					version.periodStart, // Start of period
					version.periodEnd, // End of period (null for latest version)
				]);

				// Insert into history table
				await destQueryRunner.query(
					`
						INSERT INTO "${historyTableName}" (${historyColumnsList})
						VALUES
						${placeholders}
						ON CONFLICT (id, period)
						DO NOTHING
					`,
					values,
				);

				processedCount += chunk.length;
				console.log(`  Processed ${processedCount} of ${processedVersions.length} older version records`);
			}

			await destQueryRunner.query(`AlTER TABlE "${historyTableName}"
				ENABLE TRIGGER USER`);

			console.log(`  Inserted ${olderVersions.length} records into ${historyTableName}`);
		}

		// Re-enable foreign key constraints
		await destQueryRunner.query(`SET session_replication_role = 'origin'`);

		// Commit the transaction
		await destQueryRunner.commitTransaction();
		console.log(`  Migration of ${versionTableName} completed successfully`);
	} catch (error) {
		// Rollback the transaction on error
		await destQueryRunner.rollbackTransaction();

		// Re-enable foreign key constraints even if an error occurs
		await destQueryRunner.query(`SET session_replication_role = 'origin'`);

		console.error(`  ERROR migrating ${versionTableName}:`, error);
		process.exit(1);
	}
}

async function migrateRegularTable(sourceQueryRunner, destQueryRunner, tableName, versionIdMap) {
	const destTableName = tableName.endsWith('Version') ? tableName.slice(0, -7) : tableName;
	console.log(`Migrating regular table ${tableName} to ${destTableName}...`);

	// Get columns that exist in both source and destination tables
	const columns = await getCompatibleColumns(sourceQueryRunner, destQueryRunner, tableName, destTableName);
	const columnsList = columns.map((c) => `"${c}"`).join(', ');

	// Check if the table has a deletedAt column
	const hasDeletedAtColumn = await hasColumn(sourceQueryRunner, tableName, 'deletedAt');

	// Get all records from the source table, conditionally excluding deleted records
	let records = await sourceQueryRunner.query(`SELECT *
												 FROM "${tableName}" ${hasDeletedAtColumn ? 'WHERE "deletedAt" IS NULL' : ''}`);

	console.log(`  Found ${records.length} records to migrate ${hasDeletedAtColumn ? '(excluding deleted records)' : ''}`);

	if (records.length === 0) {
		console.log(`  No records to migrate for ${tableName}`);
		return;
	}

	// Check if any columns might contain version IDs that need to be converted to parent IDs
	// Typically, these would be foreign key columns ending with "Id"
	const potentialVersionIdColumns = columns.filter((col) => col.endsWith('Id') && col !== 'id' && !col.endsWith('parentId'));

	if (potentialVersionIdColumns.length > 0) {
		console.log(`  Checking for version IDs in columns: ${potentialVersionIdColumns.join(', ')}`);

		if (tableName === 'productAttributeValue') {
			console.log(`  Filtering productAttributeValue records to keep only those with latest version ID...`);
			const filteredRecords = [];

			// First filter records to keep only those with the latest version ID
			for (const record of records) {
				// Check if productId is a version ID and if it's the latest version
				if (record.productId && versionIdMap.has(record.productId)) {
					const versionInfo = versionIdMap.get(record.productId);
					if (!versionInfo.isLatest) {
						continue; // Skip this record as it doesn't have the latest version ID
					}
				}

				// Check if attributeValueId is a version ID and if it's the latest version
				if (record.attributeValueId && versionIdMap.has(record.attributeValueId)) {
					const versionInfo = versionIdMap.get(record.attributeValueId);
					if (!versionInfo.isLatest) {
						continue; // Skip this record as it doesn't have the latest version ID
					}
				}

				filteredRecords.push(record);
			}

			console.log(`  Filtered from ${records.length} to ${filteredRecords.length} records with latest version ID`);
			records = filteredRecords;
		}

		if (tableName === 'productEnvelopeAttributeValue') {
			console.log(`  Filtering productEnvelopeAttributeValue records to keep only those with latest version ID...`);
			const filteredRecords = [];

			// First filter records to keep only those with the latest version ID
			for (const record of records) {
				// Check if productEnvelopeId is a version ID and if it's the latest version
				if (record.productEnvelopeId && versionIdMap.has(record.productEnvelopeId)) {
					const versionInfo = versionIdMap.get(record.productEnvelopeId);
					if (!versionInfo.isLatest) {
						continue; // Skip this record as it doesn't have the latest version ID
					}
				}

				// Check if attributeValueId is a version ID and if it's the latest version
				if (record.attributeValueId && versionIdMap.has(record.attributeValueId)) {
					const versionInfo = versionIdMap.get(record.attributeValueId);
					if (!versionInfo.isLatest) {
						continue; // Skip this record as it doesn't have the latest version ID
					}
				}

				filteredRecords.push(record);
			}

			console.log(`  Filtered from ${records.length} to ${filteredRecords.length} records with latest version ID`);
			records = filteredRecords;
		}

		// First, convert all version IDs to parent IDs in the records array
		console.log(`  Converting version IDs to parent IDs for all records...`);
		for (const record of records) {
			for (const col of potentialVersionIdColumns) {
				const value = record[col] ?? record[col.replace(/Id$/, 'VersionId')];
				if (value !== null && value !== undefined) {
					const versionInfo = versionIdMap.get(value);
					if (versionInfo) {
						record[col] = versionInfo.parentId;
					}
				}
			}
		}

		// Now perform deduplication after version IDs have been converted to parent IDs
		// Special handling for productAttributeValue table - filter by latest version ID and deduplicate
		if (tableName === 'productAttributeValue') {
			// Then deduplicate based on productId + attributeValueId + type
			console.log(`  Deduplicating productAttributeValue records based on productId + attributeValueId + type...`);
			const uniqueMap = new Map();
			const uniqueRecords = [];

			for (const record of records) {
				const key = `${record.productId}_${record.attributeValueId}_${record.type}`;
				if (!uniqueMap.has(key)) {
					uniqueMap.set(key, true);
					uniqueRecords.push(record);
				}
			}

			console.log(`  Deduplicated from ${records.length} to ${uniqueRecords.length} records`);
			records = uniqueRecords.slice(); // Replace with deduplicated records
		}

		// Special handling for productEnvelopeAttributeValue table - filter by latest version ID and deduplicate
		if (tableName === 'productEnvelopeAttributeValue') {
			// Then deduplicate based on productEnvelopeId + attributeValueId
			console.log(`  Deduplicating productEnvelopeAttributeValue records based on productEnvelopeId + attributeValueId...`);
			const uniqueMap = new Map();
			const uniqueRecords = [];

			for (const record of records) {
				const key = `${record.productEnvelopeId}_${record.attributeValueId}`;
				if (!uniqueMap.has(key)) {
					uniqueMap.set(key, true);
					uniqueRecords.push(record);
				}
			}

			console.log(`  Deduplicated from ${records.length} to ${uniqueRecords.length} records`);
			records = uniqueRecords.slice(); // Replace with deduplicated records
		}
	}

	// Start a transaction
	await destQueryRunner.startTransaction();

	try {
		// Disable foreign key constraints but keep sequence incrementing
		await destQueryRunner.query(`SET session_replication_role = 'replica'`);

		// Process records in chunks to avoid parameter limit
		const CHUNK_SIZE = 1000; // Adjust based on the number of columns
		let processedCount = 0;

		await destQueryRunner.query(`AlTER TABlE "${destTableName}"
			DISABLE TRIGGER USER`);

		// Check if this is a table with jsonb columns that need special handling
		const hasJsonbColumns = destTableName === 'attributeValue' || destTableName === 'attributeValueAlternative';

		for (let i = 0; i < records.length; i += CHUNK_SIZE) {
			const chunk = records.slice(i, i + CHUNK_SIZE);
			console.log(`  Processing chunk ${i / CHUNK_SIZE + 1} of ${Math.ceil(records.length / CHUNK_SIZE)} (${chunk.length} records)`);

			// Create placeholders for the INSERT query
			const placeholders = chunk
				.map((_, idx) => {
					const placeholderGroup = columns.map((_, colIdx) => `$${idx * columns.length + colIdx + 1}`).join(', ');
					return `(${placeholderGroup})`;
				})
				.join(', ');

			// Flatten all values for the query parameters
			const values = chunk.flatMap((record) => {
				return columns.map((col) => {
					// Special handling for jsonb columns in specific tables
					if (hasJsonbColumns && col === 'value') {
						// Ensure the value is properly formatted as JSON
						// If it's already a string representation of JSON, use it as is
						// Otherwise, stringify it to ensure it's valid JSON
						const value = record[col];
						if (value === null || value === undefined) {
							return null;
						}

						try {
							// If it's already a string that looks like JSON, parse it to validate
							// and then stringify it again to ensure proper format
							if (typeof value === 'string' && (value.startsWith('{') || value.startsWith('['))) {
								return JSON.stringify(JSON.parse(value));
							}
							// Otherwise, just stringify the value
							return JSON.stringify(value);
						} catch (e) {
							// If parsing fails, just stringify the original value
							return JSON.stringify(value);
						}
					}

					return record[col] ?? record[col.replace(/Id$/, 'VersionId')];
				});
			});

			// Insert into destination table
			await destQueryRunner.query(
				`
					INSERT INTO "${destTableName}" (${columnsList})
					VALUES
					${placeholders}
					ON CONFLICT
					DO NOTHING
				`,
				values,
			);

			processedCount += chunk.length;
			console.log(`  Processed ${processedCount} of ${records.length} records`);
		}

		await destQueryRunner.query(`AlTER TABlE "${destTableName}"
			ENABLE TRIGGER USER`);

		// Re-enable foreign key constraints
		await destQueryRunner.query(`SET session_replication_role = 'origin'`);

		// Commit the transaction
		await destQueryRunner.commitTransaction();
		console.log(`  Migrated ${records.length} records to ${destTableName}`);
	} catch (error) {
		// Rollback the transaction on error
		await destQueryRunner.rollbackTransaction();

		// Re-enable foreign key constraints even if an error occurs
		await destQueryRunner.query(`SET session_replication_role = 'origin'`);

		console.error(`  ERROR migrating ${tableName}:`, error);
		process.exit(1);
	}
}

// Function to create a map of version IDs to parent IDs with latest version flag
async function createVersionIdMap(sourceQueryRunner, versionTables) {
	console.log('Creating version ID to parent ID map...');

	const versionIdMap = new Map();

	for (const versionTable of versionTables) {
		console.log(`  Processing ${versionTable} for version ID mapping...`);

		// Determine base table name (remove "Version" suffix)
		const baseTableName = versionTable.slice(0, -7);

		// Check if the version table has a deletedAt column
		const hasDeletedAtColumn = await hasColumn(sourceQueryRunner, versionTable, 'deletedAt');

		// Check if the base table has a deletedAt column
		const baseTableHasDeletedAtColumn = await hasColumn(sourceQueryRunner, baseTableName, 'deletedAt');

		// Get all versions from the table, excluding deleted records and those with deleted parents
		const versions = await sourceQueryRunner.query(`
			SELECT v.id, v."parentId", v."createdAt"
			FROM "${versionTable}" v
				${hasDeletedAtColumn || baseTableHasDeletedAtColumn ? 'WHERE' : ''} ${hasDeletedAtColumn ? 'v."deletedAt" IS NULL' : ''}
				${hasDeletedAtColumn && baseTableHasDeletedAtColumn ? 'AND' : ''}
				${
					baseTableHasDeletedAtColumn
						? `NOT EXISTS (
				SELECT 1 FROM "${baseTableName}" b
				WHERE b.id = v."parentId" AND b."deletedAt" IS NOT NULL
			)`
						: ''
				}
			ORDER BY v."parentId", v."createdAt" DESC
		`);

		console.log(`  Found ${versions.length} versions in ${versionTable}`);

		// Group versions by parentId
		const versionsByParentId = {};
		versions.forEach((version) => {
			if (!versionsByParentId[version.parentId]) {
				versionsByParentId[version.parentId] = [];
			}
			versionsByParentId[version.parentId].push(version);
		});

		// For each parentId, mark the latest version
		Object.values(versionsByParentId).forEach((groupVersions: any[]) => {
			// Sort by createdAt in descending order (newest first)
			groupVersions.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

			// Mark the first one as latest
			if (groupVersions.length > 0) {
				const latestVersion = groupVersions[0];
				versionIdMap.set(latestVersion.id, {
					parentId: latestVersion.parentId,
					isLatest: true,
					table: versionTable,
				});

				// Mark the rest as not latest
				for (let i = 1; i < groupVersions.length; i++) {
					versionIdMap.set(groupVersions[i].id, {
						parentId: groupVersions[i].parentId,
						isLatest: false,
						table: versionTable,
					});
				}
			}
		});
	}

	console.log(`Created version ID map with ${versionIdMap.size} entries`);
	return versionIdMap;
}

async function migrateData() {
	console.log('Starting migration from pocitarnanx2023_old to pocitarnanx2023...');

	try {
		// Initialize connections
		await sourceDb.initialize();
		console.log('Connected to source database');

		await destDb.initialize();
		console.log('Connected to destination database');

		const sourceQueryRunner = sourceDb.createQueryRunner();
		const destQueryRunner = destDb.createQueryRunner();

		// Get all tables from source database
		const tables = await getSourceTables(sourceQueryRunner);
		console.log(`Found ${tables.length} tables in source database`);

		// Separate Version tables and regular tables
		// Version tables are those ending with 'Version' that have a corresponding base table
		const versionBaseTables = tables.filter((t) => t.endsWith('Version') && tables.includes(t.slice(0, -7))).map((t) => t.slice(0, -7));
		const versionTables = versionBaseTables.map((t) => `${t}Version`);

		// Tables ending with 'Version' that don't have a base table are considered regular tables
		const versionTablesWithoutBase = tables.filter((t) => t.endsWith('Version') && !tables.includes(t.slice(0, -7)));

		const regularTables = tables.filter(
			(t) =>
				// Include regular tables (not ending with Version or History)
				(!t.endsWith('Version') && !t.endsWith('History') && !versionBaseTables.includes(t) && t !== 'migrations') ||
				// Include Version tables without base tables
				versionTablesWithoutBase.includes(t),
		);

		console.log(`Found ${versionTables.length} Version tables and ${regularTables.length} regular tables`);

		// Create version ID to parent ID map before starting migration
		const versionIdMap = await createVersionIdMap(sourceQueryRunner, versionTables);

		// Migrate Version tables first
		for (const versionTable of versionTables) {
			await migrateVersionTable(sourceQueryRunner, destQueryRunner, versionTable, versionIdMap);
		}

		// Then migrate regular tables
		for (const regularTable of regularTables) {
			await migrateRegularTable(sourceQueryRunner, destQueryRunner, regularTable, versionIdMap);
		}

		console.log('Migration completed successfully');
	} catch (error) {
		console.error('Migration failed:', error);
	} finally {
		// Close connections
		if (sourceDb.isInitialized) await sourceDb.destroy();
		if (destDb.isInitialized) await destDb.destroy();
		console.log('Database connections closed');
	}
}

// Run the migration
migrateData().catch(console.error);
