import type { QueryRunner } from 'typeorm';

type DatabaseColumn = {
	column_name: string;
	udt_name: string;
	character_maximum_length: number | null;
	is_nullable: string;
};

// See https://github.com/nearform/temporal_tables/releases/tag/v1.0.1
export const setupHistoryProcedures = async (queryRunner: QueryRunner) => {
	await queryRunner.query(`
CREATE OR REPLACE FUNCTION versioning()
RETURNS TRIGGER AS $$
DECLARE
  sys_period text;
  history_table text;
  manipulate jsonb;
  mitigate_update_conflicts text;
  ignore_unchanged_values bool;
  include_current_version_in_history bool;
  commonColumns text[];
  time_stamp_to_use timestamptz;
  range_lower timestamptz;
  existing_range tstzrange;
  newVersion record;
  oldVersion record;
  user_defined_system_time text;
BEGIN
  -- set custom system time if exists
  BEGIN
    SELECT current_setting('user_defined.system_time') INTO user_defined_system_time;
    IF NOT FOUND OR (user_defined_system_time <> '') IS NOT TRUE THEN
      time_stamp_to_use := CURRENT_TIMESTAMP;
    ELSE
      SELECT TO_TIMESTAMP(
          user_defined_system_time,
          'YYYY-MM-DD HH24:MI:SS.MS.US'
      ) INTO time_stamp_to_use;
    END IF;
    EXCEPTION WHEN OTHERS THEN
      time_stamp_to_use := CURRENT_TIMESTAMP;
  END;

  sys_period := TG_ARGV[0];
  history_table := TG_ARGV[1];
  mitigate_update_conflicts := TG_ARGV[2];
  ignore_unchanged_values := COALESCE(TG_ARGV[3],'false');
  include_current_version_in_history := COALESCE(TG_ARGV[4],'false');

  IF ignore_unchanged_values AND TG_OP = 'UPDATE' THEN
    IF NEW IS NOT DISTINCT FROM OLD THEN
      RETURN OLD;
    END IF;
  END IF;

  IF TG_OP = 'UPDATE' OR TG_OP = 'DELETE' OR (include_current_version_in_history = 'true' AND TG_OP = 'INSERT') THEN
    IF include_current_version_in_history <> 'true' THEN
      -- Ignore rows already modified in the current transaction
      IF OLD.xmin::text = (txid_current() % (2^32)::bigint)::text THEN
        IF TG_OP = 'DELETE' THEN
          RETURN OLD;
        END IF;

        RETURN NEW;
      END IF;
    END IF;

    -- If we we are performing an update or delete we might want to optionally mitigate update conflicts
    IF TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN
      EXECUTE format('SELECT $1.%I', sys_period) USING OLD INTO existing_range;

      range_lower := lower(existing_range);

      IF mitigate_update_conflicts = 'true' THEN
      -- mitigate update conflicts
        IF range_lower >= time_stamp_to_use THEN
          time_stamp_to_use := range_lower + interval '1 microseconds';
        END IF;
      END IF;
    END IF;

    WITH history AS
      (SELECT attname
      FROM   pg_attribute
      WHERE  attrelid = history_table::regclass
      AND    attnum > 0
      AND    NOT attisdropped),
      main AS
      (SELECT attname
      FROM   pg_attribute
      WHERE  attrelid = TG_RELID
      AND    attnum > 0
      AND    NOT attisdropped)
    SELECT array_agg(quote_ident(history.attname)) INTO commonColumns
      FROM history
      INNER JOIN main
      ON history.attname = main.attname
      AND history.attname != sys_period;
    -- skip version if it would be identical to the previous version
    IF ignore_unchanged_values AND TG_OP = 'UPDATE' AND array_length(commonColumns, 1) > 0 THEN      EXECUTE 'SELECT ROW($1.' || array_to_string(commonColumns , ', $1.') || ')'
        USING NEW
        INTO newVersion;
      EXECUTE 'SELECT ROW($1.' || array_to_string(commonColumns , ', $1.') || ')'
        USING OLD
        INTO oldVersion;
      IF newVersion IS NOT DISTINCT FROM oldVersion THEN
        RETURN NEW;
      END IF;
    END IF;
-- If we are including the current version in the history and the operation is an update or delete, we need to update the previous version in the history table
    IF include_current_version_in_history = 'true' THEN
      IF TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN
        EXECUTE (
          'UPDATE ' ||
          history_table ||
          ' SET ' ||
          quote_ident(sys_period) ||
          ' = tstzrange($2, $3, ''[)'')' ||
          ' WHERE (' ||
          array_to_string(commonColumns , ',') ||
          ') IS NOT DISTINCT FROM ($1.' ||
          array_to_string(commonColumns, ',$1.') ||
          ') AND ' ||
          quote_ident(sys_period) ||
          ' = $1.' ||
          quote_ident(sys_period)
        )
          USING OLD, range_lower, time_stamp_to_use;
      END IF;
      -- If we are including the current version in the history and the operation is an insert or update, we need to insert the current version in the history table
      IF TG_OP = 'UPDATE' OR TG_OP = 'INSERT' THEN
        EXECUTE ('INSERT INTO ' ||
          history_table ||
          '(' ||
          array_to_string(commonColumns , ',') ||
          ',' ||
          quote_ident(sys_period) ||
          ') VALUES ($1.' ||
          array_to_string(commonColumns, ',$1.') ||
          ',tstzrange($2, NULL, ''[)''))')
          USING NEW, time_stamp_to_use;
      END IF;
    ELSE
      EXECUTE ('INSERT INTO ' ||
      history_table ||
      '(' ||
      array_to_string(commonColumns , ',') ||
      ',' ||
      quote_ident(sys_period) ||
      ') VALUES ($1.' ||
      array_to_string(commonColumns, ',$1.') ||
      ',tstzrange($2, $3, ''[)''))')
       USING OLD, range_lower, time_stamp_to_use;
    END IF;
  END IF;

  IF TG_OP = 'UPDATE' OR TG_OP = 'INSERT' THEN
    manipulate := jsonb_set('{}'::jsonb, ('{' || sys_period || '}')::text[], to_jsonb(tstzrange(time_stamp_to_use, null, '[)')));

    RETURN jsonb_populate_record(NEW, manipulate);
  END IF;

  RETURN OLD;
END;
$$ LANGUAGE plpgsql;`);

	const entities = queryRunner.connection.entityMetadatas.filter(
		(entity) => entity.tableType === 'regular' && entity.tableName.endsWith('History'),
	);

	await Promise.all(
		entities.map(async (entity) => {
			const historyTableName = entity.tableName;
			const tableName = historyTableName.slice(0, -7);

			await queryRunner.query(`
				CREATE TABLE IF NOT EXISTS "${historyTableName}" AS SELECT * FROM "${tableName}" WHERE false;
			`);
			await queryRunner.query(`
				CREATE INDEX IF NOT EXISTS "${historyTableName}_id" ON "${historyTableName}" ("id");
			`);
			await queryRunner.query(`
				CREATE UNIQUE INDEX IF NOT EXISTS "${historyTableName}_id_period" ON "${historyTableName}" ("id", "period");
			`);

			// Check if source table schema has changed and update history table accordingly
			const sourceColumns = (await queryRunner.query(`
				SELECT column_name, udt_name, character_maximum_length, is_nullable
				FROM information_schema.columns
				WHERE table_name = '${tableName}' AND table_schema = 'public'
				ORDER BY ordinal_position;
			`)) as DatabaseColumn[];

			const historyColumns = (await queryRunner.query(`
				SELECT column_name, udt_name, character_maximum_length, is_nullable
				FROM information_schema.columns
				WHERE table_name = '${historyTableName}' AND table_schema = 'public'
				ORDER BY ordinal_position;
			`)) as DatabaseColumn[];

			// Create maps for easier comparison
			const sourceColumnMap = new Map<string, DatabaseColumn>(sourceColumns.map((col) => [col.column_name, col]));
			const historyColumnMap = new Map<string, DatabaseColumn>(historyColumns.map((col) => [col.column_name, col]));

			// Find columns to add or alter (in source but not in history or with different data type)
			for (const [columnName, sourceCol] of sourceColumnMap.entries()) {
				// Prepare data type definition
				let dataTypeDefinition = sourceCol.udt_name;
				if (sourceCol.character_maximum_length) {
					dataTypeDefinition += `(${sourceCol.character_maximum_length})`;
				}

				if (!historyColumnMap.has(columnName)) {
					// Column exists in source but not in history - add it
					await queryRunner.query(`
						ALTER TABLE "${historyTableName}"
							ADD COLUMN "${columnName}" "${dataTypeDefinition}";
					`);
				} else {
					// Column exists in both tables - check if data type or nullability has changed
					const historyCol = historyColumnMap.get(columnName)!; // Non-null assertion because we checked with has()
					let historyDataType = historyCol.udt_name;
					if (historyCol.character_maximum_length) {
						historyDataType += `(${historyCol.character_maximum_length})`;
					}

					if (dataTypeDefinition !== historyDataType) {
						// Handle data type changes
						await queryRunner.query(`
							ALTER TABLE "${historyTableName}"
								ALTER COLUMN "${columnName}" TYPE "${dataTypeDefinition}" USING "${columnName}"::"${dataTypeDefinition}";
						`);
					}
				}
			}

			// We don't remove columns from history table to preserve historical data even if the source table structure changes

			await queryRunner.query(`
				CREATE OR REPLACE TRIGGER versioning
				BEFORE INSERT OR UPDATE OR DELETE
				ON "${tableName}"
				FOR EACH ROW
				EXECUTE PROCEDURE versioning('period','"${historyTableName}"', true, true, true);
			`);
		}),
	);
};
