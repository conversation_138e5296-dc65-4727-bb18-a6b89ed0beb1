import type { QueryRunner } from 'typeorm';

export const setupActionProcedures = async (queryRunner: QueryRunner) => {
	const entities = queryRunner.connection.entityMetadatas.filter(
		(entity) =>
			entity.tableType === 'regular' &&
			!entity.tableName.endsWith('History') &&
			!!entity.columns.find((column) => column.databaseNameWithoutPrefixes === 'actionId'),
	);

	await queryRunner.query(`
		CREATE OR REPLACE FUNCTION "assignAction"() RETURNS TRIGGER AS $$
			BEGIN
				NEW."actionId" = (SELECT "id" FROM "action" WHERE "txId" = pg_current_xact_id()::varchar LIMIT 1);
				RETURN NEW;
			END;
		$$ LANGUAGE plpgsql;
	`);

	await Promise.all(
		entities.map(async (entity) => {
			await queryRunner.query(`
			CREATE OR REPLACE TRIGGER "assignAction"
			BEFORE INSERT OR UPDATE
			ON "${entity.tableName}"
			FOR EACH ROW
			EXECUTE FUNCTION "assignAction"();
		`);
		}),
	);
};
