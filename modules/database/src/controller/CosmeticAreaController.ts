import { CosmeticArea, type ProductCategory } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listAll } from '../utils/list';

export class CosmeticAreaController extends BaseController<CosmeticArea> {
	constructor() {
		super(CosmeticArea);
	}

	protected override getQueryBuilder() {
		return super.getQueryBuilder().leftJoinAndSelect('entity.productCategory', 'productCategory');
	}

	async findByCategory(productCategoryId: ProductCategory['id']) {
		const [results] = await this.list(listAll({ filter: { productCategoryId: { eq: productCategoryId } } }));
		return results;
	}
}
