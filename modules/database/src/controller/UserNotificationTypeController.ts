import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { type NotificationType, type User, UserNotificationType } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listAll } from '../utils/list';

export class UserNotificationTypeController extends BaseController<UserNotificationType> {
	constructor() {
		super(UserNotificationType);
	}

	protected override getQueryBuilder() {
		return super.getQueryBuilder().innerJoinAndSelect('entity.user', 'user');
	}

	async listByUser(userId: UserNotificationType['userId']) {
		return this.list(listAll({ filter: { userId: { eq: userId } } }));
	}

	async listByNotificationType(notificationTypeId: NotificationType['id'], props: ListProps = {}) {
		return this.list(listAll({ filter: { ...props.filter, notificationTypeId: { eq: notificationTypeId } } }));
	}

	async link(userId: User['id'], notificationTypeId: NotificationType['id'], deliveryMethod: UserNotificationType['deliveryMethod']) {
		return this.create({ user: { id: userId }, notificationType: { id: notificationTypeId }, deliveryMethod });
	}

	async unlink(userId: User['id'], notificationTypeId: NotificationType['id'], deliveryMethod: UserNotificationType['deliveryMethod']) {
		const result = await this.repository.delete({
			user: { id: userId },
			notificationType: { id: notificationTypeId },
			deliveryMethod,
		});

		return !!result.affected;
	}
}
