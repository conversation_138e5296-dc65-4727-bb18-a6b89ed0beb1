import { downlaodFileFromBucket, getFilePresignedUrl, uploadFileToBucket } from '@pocitarna-nx-2023/aws';
import { determineMimeFromFilename, handleError, uniquesBy } from '@pocitarna-nx-2023/utils';
import type { EntityWithFiles, ListProps } from '@pocitarna-nx-2023/zodios';
import { fromBuffer } from 'file-type';
import type { MimeType } from 'file-type/core';
import {
	type Batch,
	type BatchDefect,
	File,
	type Inventory,
	type Product,
	type ProductCosmeticDefect,
	type ProductDefect,
	type ServiceCase,
	type Vendor,
	type WarrantyClaim,
} from '../entity';
import { BaseController } from '../utils/BaseController';
import { listAll } from '../utils/list';
import {
	BatchController,
	BatchDefectController,
	ProductCodeController,
	ProductController,
	ProductCosmeticDefectController,
	ProductDefectController,
	ServiceCaseController,
	VendorController,
	WarrantyClaimController,
} from '.';

export class FileController extends BaseController<File> {
	constructor() {
		super(File);
	}

	override getQueryBuilder() {
		return super.getQueryBuilder(this.includeActionMiddleware);
	}

	private async getProductQueryBuilder() {
		return this.getQueryBuilder().innerJoin('entity.products', 'fileProduct').innerJoin('fileProduct.product', 'product');
	}

	private async getProductDefectQueryBuilder() {
		return this.getQueryBuilder()
			.innerJoin('entity.productDefects', 'fileProductDefect')
			.innerJoin('fileProductDefect.productDefect', 'productDefect');
	}

	private async getBatchDefectQueryBuilder() {
		return this.getQueryBuilder()
			.innerJoin('entity.batchDefects', 'fileBatchDefect')
			.innerJoin('fileBatchDefect.batchDefect', 'batchDefect');
	}

	private async getBatchQueryBuilder() {
		return this.getQueryBuilder().innerJoin('entity.batches', 'fileBatch').innerJoin('fileBatch.batch', 'batch');
	}

	private async getServiceCaseQueryBuilder() {
		return this.getQueryBuilder()
			.innerJoin('entity.serviceCases', 'fileServiceCase')
			.innerJoin('fileServiceCase.serviceCase', 'serviceCase');
	}

	private async getWarrantyClaimQueryBuilder() {
		return this.getQueryBuilder()
			.innerJoin('entity.warrantyClaims', 'fileWarrantyClaim')
			.innerJoin('fileWarrantyClaim.warrantyClaim', 'warrantyClaim');
	}

	private async getVendorQueryBuilder() {
		return this.getQueryBuilder().innerJoin('entity.vendors', 'fileVendor').innerJoin('fileVendor.vendor', 'vendor');
	}

	private async getInventoryQueryBuilder() {
		return this.getQueryBuilder().innerJoin('entity.inventories', 'fileInventory').innerJoin('fileInventory.inventory', 'inventory');
	}

	private async getProductCosmeticDefectQueryBuilder() {
		return this.getQueryBuilder()
			.innerJoin('entity.productCosmeticDefects', 'fileProductCosmeticDefect')
			.innerJoin('fileProductCosmeticDefect.productCosmeticDefect', 'productCosmeticDefect')
			.innerJoin('productCosmeticDefect.cosmeticDefect', 'cosmeticDefect');
	}

	async getFileDownloadLink(fileId: string) {
		try {
			return await getFilePresignedUrl(fileId);
		} catch (error) {
			return handleError(error, 'Could not create download link');
		}
	}

	async downloadFile(fileId: string) {
		try {
			const downloadedFile = await downlaodFileFromBucket(fileId);
			return downloadedFile.Body?.transformToByteArray();
		} catch (error) {
			return handleError(error, 'Could not download file from bucket');
		}
	}

	async saveFile(body: Uint8Array, name: string, photoSessionId?: string) {
		try {
			const fileTypeResult = await fromBuffer(body);

			const mime = (fileTypeResult?.mime ?? determineMimeFromFilename(name)) as MimeType;
			if (!mime) throw new Error('Mime type is necessary to save a file');

			const file = await this.create({ mime, name, photoSessionId, preview: await this.generatePreview(body, mime) });

			try {
				await uploadFileToBucket(file.id, mime, body, name);
			} catch (error) {
				await this.delete(file.id);
				handleError(error, 'Could not save file');
			}

			return file;
		} catch (error) {
			return handleError(error, 'Failed to convert file body to Uint8Array');
		}
	}

	async listBatchFiles(batchId: Batch['id'], props: ListProps) {
		return this.list({ ...props, filter: { ...props.filter, 'batch.id': { eq: batchId } } }, await this.getBatchQueryBuilder());
	}

	async listProductFiles(productId: Product['id'], props: ListProps) {
		return this.list({ ...props, filter: { ...props.filter, 'product.id': { eq: productId } } }, await this.getProductQueryBuilder());
	}

	async listProductImages(productId: Product['id'], props: ListProps) {
		return this.listProductFiles(productId, {
			...props,
			filter: {
				...props.filter,
				mime: {
					eq: '%image%',
				},
			},
		});
	}

	async listBatchDefectFiles(batchDefectId: BatchDefect['id'], props: ListProps) {
		return this.list(
			{ ...props, filter: { ...props.filter, 'batchDefect.id': { eq: batchDefectId } } },
			await this.getBatchDefectQueryBuilder(),
		);
	}

	async listProductDefectFiles(productDefectId: ProductDefect['id'], props: ListProps) {
		return this.list(
			{ ...props, filter: { ...props.filter, 'productDefect.id': { eq: productDefectId } } },
			await this.getProductDefectQueryBuilder(),
		);
	}

	async listServiceCaseFiles(serviceCaseId: ServiceCase['id'], props: ListProps) {
		return this.list(
			{ ...props, filter: { ...props.filter, 'serviceCase.id': { eq: serviceCaseId } } },
			await this.getServiceCaseQueryBuilder(),
		);
	}

	async listWarrantyClaimFiles(warrantyClaimId: WarrantyClaim['id'], props: ListProps) {
		return this.list(
			{ ...props, filter: { ...props.filter, 'warrantyClaim.id': { eq: warrantyClaimId } } },
			await this.getWarrantyClaimQueryBuilder(),
		);
	}

	async listVendorFiles(vendorId: Vendor['id'], props: ListProps) {
		return this.list({ ...props, filter: { ...props.filter, 'vendor.id': { eq: vendorId } } }, await this.getVendorQueryBuilder());
	}

	async listInventoryFiles(inventoryId: Inventory['id'], props: ListProps) {
		return this.list(
			{ ...props, filter: { ...props.filter, 'inventory.id': { eq: inventoryId } } },
			await this.getInventoryQueryBuilder(),
		);
	}

	async listProductCosmeticDefectFiles(productCosmeticDefectId: ProductCosmeticDefect['id'], props: ListProps) {
		return this.list(
			{ ...props, filter: { ...props.filter, 'productCosmeticDefect.id': { eq: productCosmeticDefectId } } },
			await this.getProductCosmeticDefectQueryBuilder(),
		);
	}

	async listFilesByPhotoSession(photoSessionId: string) {
		const [files] = await this.list(listAll({ filter: { photoSessionId: { eq: photoSessionId } } }));
		return files;
	}

	async listUploadedFiles(photoSessionId?: string, photoIds?: string[]) {
		const files = [];
		if (photoSessionId) {
			const [photoSessionFiles] = await this.list(listAll({ filter: { photoSessionId: { eq: photoSessionId } } }));
			files.push(...photoSessionFiles);
		}
		if (photoIds?.length) {
			const [idFiles] = await this.list(listAll({ filter: { id: { eq: photoIds } } }));
			files.push(...idFiles);
		}
		files.sort((a, b) => (a.createdAt < b.createdAt ? 1 : -1));
		return uniquesBy(files, 'id');
	}

	async handleFileAssociation({ id, type, savedFileId }: { id: string; type: EntityWithFiles; savedFileId: File['id'] }) {
		if (type === 'batch') {
			await new BatchController().addFiles(id, savedFileId);
		}
		if (type === 'batchDefect') {
			await new BatchDefectController().addFiles(id, savedFileId);
		}
		if (type === 'product') {
			await new ProductController().addFiles(id, savedFileId);
		}
		if (type === 'productCode') {
			await new ProductCodeController().addFiles(id, savedFileId);
		}
		if (type === 'productDefect') {
			await new ProductDefectController().addFiles(id, savedFileId);
		}
		if (type === 'productCosmeticDefect') {
			await new ProductCosmeticDefectController().addFiles(id, savedFileId);
		}
		if (type === 'serviceCase') {
			await new ServiceCaseController().addFiles(id, savedFileId);
		}
		if (type === 'vendor') {
			await new VendorController().addFiles(id, savedFileId);
		}
		if (type === 'warrantyClaim') {
			await new WarrantyClaimController().addFiles(id, savedFileId);
		}
	}

	async rotate(body: Uint8Array, mime: MimeType, rotation: number) {
		if (mime.startsWith('image/')) {
			const sharp = await import('sharp').then((mod) => mod.default);
			return sharp(body, { failOn: 'none' }).rotate(rotation).toBuffer();
		}

		return body;
	}

	async generatePreview(body: Uint8Array, mime: MimeType) {
		if (mime.startsWith('image/')) {
			const sharp = await import('sharp').then((mod) => mod.default);
			const previewBuffer = await sharp(body, { failOn: 'none' }).resize(96, 96).toBuffer();
			return `data:${mime};base64,${previewBuffer.toString('base64')}`;
		}

		return '';
	}
}
