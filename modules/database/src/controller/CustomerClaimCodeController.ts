import { CustomerClaimCode } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listOne } from '../utils/list';

export class CustomerClaimCodeController extends BaseController<CustomerClaimCode> {
	constructor() {
		super(CustomerClaimCode);
	}

	async findByCode(code: CustomerClaimCode['code']) {
		const [[item]] = await this.list(listOne({ filter: { code: { eq: code } } }));
		return item;
	}
}
