import { type SingleOrArray } from '@pocitarna-nx-2023/utils';
import { In } from 'typeorm';
import { type CosmeticDefect, type ProductCategory, ProductCategoryCosmeticDefect } from '../entity';
import { BaseController } from '../utils/BaseController';

export class ProductCategoryCosmeticDefectController extends BaseController<ProductCategoryCosmeticDefect> {
	constructor() {
		super(ProductCategoryCosmeticDefect);
	}

	link(
		productCategoryId: ProductCategory['id'],
		cosmeticDefectIds: SingleOrArray<CosmeticDefect['id']>,
	): Promise<ProductCategoryCosmeticDefect[]> {
		const data = (Array.isArray(cosmeticDefectIds) ? cosmeticDefectIds : [cosmeticDefectIds]).map((cosmeticDefectId) => ({
			productCategory: { id: productCategoryId },
			cosmeticDefect: { id: cosmeticDefectId },
		}));

		return this.bulkCreate(data);
	}

	async unlink(productCategoryId: ProductCategory['id'], cosmeticDefectIds: SingleOrArray<CosmeticDefect['id']>) {
		const result = await this.repository.delete({
			productCategory: { id: productCategoryId },
			cosmeticDefect: { id: Array.isArray(cosmeticDefectIds) ? In(cosmeticDefectIds) : cosmeticDefectIds },
		});

		return !!result.affected;
	}

	async unlinkAll(cosmeticDefectId: CosmeticDefect['id']) {
		const result = await this.repository.delete({
			cosmeticDefect: { id: cosmeticDefectId },
		});

		return !!result.affected;
	}
}
