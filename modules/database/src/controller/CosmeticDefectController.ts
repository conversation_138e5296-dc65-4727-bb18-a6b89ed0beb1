import { type Repository } from 'typeorm';
import { type CosmeticArea, CosmeticDefect, type ProductCategory } from '../entity';
import { type QueryBuilderMiddleware } from '../types';
import { BaseController } from '../utils/BaseController';
import { CosmeticAreaCosmeticDefectController } from './CosmeticAreaCosmeticDefectController';
import { ProductCategoryCosmeticDefectController } from './ProductCategoryCosmeticDefectController';

export class CosmeticDefectController extends BaseController<CosmeticDefect> {
	constructor() {
		super(CosmeticDefect);
	}

	protected override getQueryBuilder(...middlewares: QueryBuilderMiddleware<CosmeticDefect>[]) {
		return super
			.getQueryBuilder(...middlewares)
			.leftJoinAndSelect('entity.cosmeticAreaCosmeticDefects', 'cosmeticAreaCosmeticDefect')
			.leftJoinAndSelect('cosmeticAreaCosmeticDefect.cosmeticArea', 'cosmeticArea')
			.leftJoinAndSelect('cosmeticArea.productCategory', 'productCategory')
			.leftJoinAndSelect('entity.productCategoryCosmeticDefects', 'productCategoryCosmeticDefect')
			.innerJoinAndSelect('entity.grade', 'grade');
	}

	override async create(
		data: Parameters<Repository<CosmeticDefect>['create']>[0] & {
			productCategories: ProductCategory['id'][];
			cosmeticAreas: CosmeticArea['id'][];
		},
	) {
		const { productCategories, cosmeticAreas, ...rest } = data;

		const cosmeticDefect = await super.create(rest);

		await Promise.all([
			...productCategories.map((productCategoryId) =>
				new ProductCategoryCosmeticDefectController().link(productCategoryId, cosmeticDefect.id),
			),
			...cosmeticAreas.map((cosmeticAreaId) => new CosmeticAreaCosmeticDefectController().link(cosmeticAreaId, cosmeticDefect.id)),
		]);

		return cosmeticDefect;
	}

	override async update(
		idOrRecord: CosmeticDefect | CosmeticDefect['id'],
		data: Parameters<Repository<CosmeticDefect>['update']>[1] & {
			productCategories: ProductCategory['id'][];
			cosmeticAreas: CosmeticArea['id'][];
		},
	) {
		const { productCategories, cosmeticAreas, ...rest } = data;
		const currentDefect = await this.resolveRecord(idOrRecord);
		const cosmeticDefect = await super.update(idOrRecord, rest);

		await new ProductCategoryCosmeticDefectController().unlinkAll(currentDefect.id);
		await new CosmeticAreaCosmeticDefectController().unlinkAll(currentDefect.id);

		await Promise.all([
			...productCategories.map((productCategoryId) =>
				new ProductCategoryCosmeticDefectController().link(productCategoryId, cosmeticDefect.id),
			),
			...cosmeticAreas.map((cosmeticAreaId) => new CosmeticAreaCosmeticDefectController().link(cosmeticAreaId, cosmeticDefect.id)),
		]);

		return cosmeticDefect;
	}
}
