import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { FileVendorHistory, type Vendor } from '../../entity';
import { type QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class FileVendorHistoryController extends BaseHistoryController<FileVendorHistory> {
	constructor() {
		super(FileVendorHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<FileVendorHistory>[]) {
		return super.getQueryBuilder(...middlewares).innerJoinAndSelect('entity.file', 'file');
	}

	async listByVendorId(vendorId: Vendor['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		return this.listQuery({ ...props, filter: { ...props?.filter, vendorId: { eq: vendorId } } }, queryBuilder);
	}
}
