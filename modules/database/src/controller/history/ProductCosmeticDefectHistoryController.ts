import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { type Product, ProductCosmeticDefectHistory } from '../../entity';
import { type QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class ProductCosmeticDefectHistoryController extends BaseHistoryController<ProductCosmeticDefectHistory> {
	constructor() {
		super(ProductCosmeticDefectHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<ProductCosmeticDefectHistory>[]) {
		return super
			.getQueryBuilder(...middlewares)
			.innerJoinAndSelect('entity.cosmeticDefect', 'cosmeticDefect')
			.innerJoinAndSelect('entity.cosmeticArea', 'cosmeticArea');
	}

	async listByProductId(productId: Product['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		return this.listQuery({ ...props, filter: { ...props?.filter, productId: { eq: productId } } }, queryBuilder);
	}
}
