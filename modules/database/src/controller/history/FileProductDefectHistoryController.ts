import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { FileProductDefectHistory, type ProductDefect } from '../../entity';
import { type QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class FileProductDefectHistoryController extends BaseHistoryController<FileProductDefectHistory> {
	constructor() {
		super(FileProductDefectHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<FileProductDefectHistory>[]) {
		return super.getQueryBuilder(...middlewares).innerJoinAndSelect('entity.file', 'file');
	}

	async listByProductDefectId(productDefectId: ProductDefect['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		return this.listQuery({ ...props, filter: { ...props?.filter, productDefectId: { eq: productDefectId } } }, queryBuilder);
	}
}
