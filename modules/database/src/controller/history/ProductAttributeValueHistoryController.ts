import type { ListProps } from '@pocitarna-nx-2023/zodios';
import { type Product, ProductAttributeValueHistory } from '../../entity';
import type { QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class ProductAttributeValueHistoryController extends BaseHistoryController<ProductAttributeValueHistory> {
	constructor() {
		super(ProductAttributeValueHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<ProductAttributeValueHistory>[]) {
		return super
			.getQueryBuilder(...middlewares)
			.innerJoinAndSelect('entity.attributeValue', 'attributeValue')
			.innerJoinAndSelect('attributeValue.attribute', 'attribute');
	}

	async listByProductId(id: Product['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		return this.listQuery({ ...props, filter: { ...props?.filter, productId: { eq: id } } }, queryBuilder);
	}
}
