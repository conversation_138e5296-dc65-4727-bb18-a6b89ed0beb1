import { ProductEnvelopeHistory } from '../../entity';
import type { QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class ProductEnvelopeHistoryController extends BaseHistoryController<ProductEnvelopeHistory> {
	constructor() {
		super(ProductEnvelopeHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<ProductEnvelopeHistory>[]) {
		return super.getQueryBuilder(...middlewares).innerJoinAndSelect('entity.code', 'code');
	}
}
