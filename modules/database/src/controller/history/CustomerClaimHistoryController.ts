import { CustomerClaimHistory } from '../../entity';
import { type QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class CustomerClaimHistoryController extends BaseHistoryController<CustomerClaimHistory> {
	constructor() {
		super(CustomerClaimHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<CustomerClaimHistory>[]) {
		return super
			.getQueryBuilder(...middlewares)
			.innerJoinAndSelect('entity.code', 'code')
			.leftJoinAndSelect('entity.notes', 'note')
			.leftJoinAndSelect('entity.messages', 'message')
			.leftJoinAndSelect('entity.address', 'address')
			.leftJoinAndSelect('entity.contact', 'contact')
			.leftJoinAndSelect('entity.ecommerceOrderItem', 'ecommerceOrderItem')
			.leftJoinAndSelect('ecommerceOrderItem.product', 'product')
			.leftJoinAndSelect('ecommerceOrderItem.ecommerceOrder', 'ecommerceOrder');
	}
}
