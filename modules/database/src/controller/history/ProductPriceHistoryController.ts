import { ProductPriceHistory } from '../../entity';
import type { QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class ProductPriceHistoryController extends BaseHistoryController<ProductPriceHistory> {
	constructor() {
		super(ProductPriceHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<ProductPriceHistory>[]) {
		return super
			.getQueryBuilder(...middlewares)
			.innerJoinAndSelect('entity.currencyRate', 'currencyRate')
			.innerJoinAndSelect('currencyRate.currency', 'currency');
	}
}
