import type { ListProps } from '@pocitarna-nx-2023/zodios';
import { type CustomerClaim, type Product, ProductDefectHistory, type ServiceCase, type WarrantyClaim } from '../../entity';
import type { QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class ProductDefectHistoryController extends BaseHistoryController<ProductDefectHistory> {
	constructor() {
		super(ProductDefectHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<ProductDefectHistory>[]) {
		return super.getQueryBuilder(...middlewares).leftJoinAndSelect('entity.defectType', 'defectType');
	}

	async listByProductId(id: Product['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		const safeProps = props ?? {};
		return this.listQuery({ ...safeProps, filter: { ...safeProps?.filter, productId: { eq: id } } }, queryBuilder);
	}

	async listByServiceCaseId(id: ServiceCase['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		const safeProps = props ?? {};
		return this.listQuery({ ...safeProps, filter: { ...safeProps?.filter, serviceCaseId: { eq: id } } }, queryBuilder);
	}

	async listByCustomerClaimId(id: CustomerClaim['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		const safeProps = props ?? {};
		return this.listQuery({ ...safeProps, filter: { ...safeProps?.filter, customerClaimId: { eq: id } } }, queryBuilder);
	}

	async listByWarrantyClaimId(id: WarrantyClaim['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		const safeProps = props ?? {};
		return this.listQuery({ ...safeProps, filter: { ...safeProps?.filter, warrantyClaimId: { eq: id } } }, queryBuilder);
	}
}
