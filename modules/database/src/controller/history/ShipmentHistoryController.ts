import { ShipmentHistory } from '../../entity';
import type { QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class ShipmentHistoryController extends BaseHistoryController<ShipmentHistory> {
	constructor() {
		super(ShipmentHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<ShipmentHistory>[]) {
		return super.getQueryBuilder(...middlewares).innerJoinAndSelect('entity.code', 'code');
	}
}
