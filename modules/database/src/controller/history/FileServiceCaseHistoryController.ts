import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { FileServiceCaseHistory, type ServiceCase } from '../../entity';
import { type QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class FileServiceCaseHistoryController extends BaseHistoryController<FileServiceCaseHistory> {
	constructor() {
		super(FileServiceCaseHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<FileServiceCaseHistory>[]) {
		return super.getQueryBuilder(...middlewares).innerJoinAndSelect('entity.file', 'file');
	}

	async listByServiceCaseId(serviceCaseId: ServiceCase['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		return this.listQuery({ ...props, filter: { ...props?.filter, serviceCaseId: { eq: serviceCaseId } } }, queryBuilder);
	}
}
