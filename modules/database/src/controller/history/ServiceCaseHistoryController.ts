import { ServiceCaseHistory } from '../../entity';
import type { QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class ServiceCaseHistoryController extends BaseHistoryController<ServiceCaseHistory> {
	constructor() {
		super(ServiceCaseHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<ServiceCaseHistory>[]) {
		return super.getQueryBuilder(...middlewares).innerJoinAndSelect('entity.code', 'code');
	}
}
