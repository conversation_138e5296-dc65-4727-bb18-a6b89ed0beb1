import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { FileProductCodeHistory, type ProductCode } from '../../entity';
import { type QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class FileProductCodeHistoryController extends BaseHistoryController<FileProductCodeHistory> {
	constructor() {
		super(FileProductCodeHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<FileProductCodeHistory>[]) {
		return super.getQueryBuilder(...middlewares).innerJoinAndSelect('entity.file', 'file');
	}

	async listByProductCodeId(productCodeId: ProductCode['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		return this.listQuery({ ...props, filter: { ...props?.filter, productCodeId: { eq: productCodeId } } }, queryBuilder);
	}
}
