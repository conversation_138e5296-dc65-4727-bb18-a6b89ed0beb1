import { VendorHistory } from '../../entity';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class VendorHistoryController extends BaseHistoryController<VendorHistory> {
	constructor() {
		super(VendorHistory);
	}

	override getQueryBuilder() {
		return super
			.getQueryBuilder()
			.leftJoinAndSelect('entity.defectTypes', 'vendorDefectType')
			.leftJoinAndSelect('vendorDefectType.defectType', 'defectType');
	}
}
