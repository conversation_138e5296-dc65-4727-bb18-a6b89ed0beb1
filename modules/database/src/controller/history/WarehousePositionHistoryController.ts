import { WarehousePositionHistory } from '../../entity';
import { type QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class WarehousePositionHistoryController extends BaseHistoryController<WarehousePositionHistory> {
	constructor() {
		super(WarehousePositionHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<WarehousePositionHistory>[]) {
		return super
			.getQueryBuilder(...middlewares)
			.leftJoinAndSelect('entity.code', 'code')
			.innerJoinAndSelect('entity.warehouse', 'warehouse');
	}
}
