import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { FileProductHistory, type Product } from '../../entity';
import { type QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class FileProductHistoryController extends BaseHistoryController<FileProductHistory> {
	constructor() {
		super(FileProductHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<FileProductHistory>[]) {
		return super.getQueryBuilder(...middlewares).innerJoinAndSelect('entity.file', 'file');
	}

	async listByProductId(productId: Product['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		return this.listQuery({ ...props, filter: { ...props?.filter, productId: { eq: productId } } }, queryBuilder);
	}
}
