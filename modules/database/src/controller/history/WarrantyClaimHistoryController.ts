import { WarrantyClaimHistory } from '../../entity';
import type { QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class WarrantyClaimHistoryController extends BaseHistoryController<WarrantyClaimHistory> {
	constructor() {
		super(WarrantyClaimHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<WarrantyClaimHistory>[]) {
		return super.getQueryBuilder(...middlewares).innerJoinAndSelect('entity.code', 'code');
	}
}
