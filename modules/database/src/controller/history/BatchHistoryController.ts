import { BatchHistory } from '../../entity';
import { type QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class BatchHistoryController extends BaseHistoryController<BatchHistory> {
	constructor() {
		super(BatchHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<BatchHistory>[]) {
		return super
			.getQueryBuilder(...middlewares)
			.innerJoinAndSelect('entity.currencyRate', 'currencyRate')
			.innerJoinAndSelect('currencyRate.currency', 'currency')
			.innerJoinAndSelect('entity.code', 'code');
	}
}
