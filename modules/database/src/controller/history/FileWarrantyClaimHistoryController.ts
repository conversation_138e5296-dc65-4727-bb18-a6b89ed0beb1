import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { FileWarrantyClaimHistory, type WarrantyClaim } from '../../entity';
import { type QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class FileWarrantyClaimHistoryController extends BaseHistoryController<FileWarrantyClaimHistory> {
	constructor() {
		super(FileWarrantyClaimHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<FileWarrantyClaimHistory>[]) {
		return super.getQueryBuilder(...middlewares).innerJoinAndSelect('entity.file', 'file');
	}

	async listByWarrantyClaimId(warrantyClaimId: WarrantyClaim['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		return this.listQuery({ ...props, filter: { ...props?.filter, warrantyClaimId: { eq: warrantyClaimId } } }, queryBuilder);
	}
}
