import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { type BatchDefect, FileBatchDefectHistory } from '../../entity';
import { type QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class FileBatchDefectHistoryController extends BaseHistoryController<FileBatchDefectHistory> {
	constructor() {
		super(FileBatchDefectHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<FileBatchDefectHistory>[]) {
		return super.getQueryBuilder(...middlewares).innerJoinAndSelect('entity.file', 'file');
	}

	async listByBatchDefectId(batchDefectId: BatchDefect['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		return this.listQuery({ ...props, filter: { ...props?.filter, batchDefectId: { eq: batchDefectId } } }, queryBuilder);
	}
}
