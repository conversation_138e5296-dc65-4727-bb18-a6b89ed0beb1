import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { type Batch, FileBatchHistory } from '../../entity';
import { type QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class FileBatchHistoryController extends BaseHistoryController<FileBatchHistory> {
	constructor() {
		super(FileBatchHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<FileBatchHistory>[]) {
		return super.getQueryBuilder(...middlewares).innerJoinAndSelect('entity.file', 'file');
	}

	async listByBatchId(batchId: Batch['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		return this.listQuery({ ...props, filter: { ...props?.filter, batchId: { eq: batchId } } }, queryBuilder);
	}
}
