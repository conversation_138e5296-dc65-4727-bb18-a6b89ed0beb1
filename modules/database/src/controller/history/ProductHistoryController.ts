import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { type Product, ProductHistory, type WarehousePosition } from '../../entity';
import type { QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';
import { listAll } from '../../utils/list';

export class ProductHistoryController extends BaseHistoryController<ProductHistory> {
	constructor() {
		super(ProductHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<ProductHistory>[]) {
		return super.getQueryBuilder(...middlewares).leftJoinAndSelect('entity.code', 'code');
	}

	async listByWarehousePositionId(
		warehousePositionId: WarehousePosition['id'],
		props?: ListProps,
		queryBuilder = this.getQueryBuilder(),
	) {
		return this.listQuery({ ...props, filter: { ...props?.filter, warehousePositionId: { eq: warehousePositionId } } }, queryBuilder);
	}

	async getWarehousePositionProductHistory(warehousePositionId: WarehousePosition['id'], productIds: Product['id'][]) {
		const queryBuilder = this.getQueryBuilder()
			.innerJoin('productHistory', 'ph1', 'ph1.id = entity.id')
			.where('ph1.warehousePositionId IS NOT NULL')
			.andWhere('entity.warehousePositionId IS DISTINCT FROM ph1.warehousePositionId')
			.andWhere('LOWER(entity.period) >= UPPER(ph1.period)')
			.andWhere('ph1.warehousePositionId = :warehousePositionId', { warehousePositionId })
			.andWhere('ph1.id IN (:...productIds)', { productIds })
			.orderBy('LOWER(entity.period)', 'ASC');

		return await this.listQuery(listAll(), queryBuilder);
	}
}
