import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { type SingleOrArray } from '@pocitarna-nx-2023/utils';
import { type File, FileProduct, type Product } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listAll } from '../utils/list';

export class FileProductController extends BaseController<FileProduct> {
	constructor() {
		super(FileProduct);
	}

	override getQueryBuilder() {
		return super.getQueryBuilder().leftJoinAndSelect('entity.file', 'file');
	}

	link(
		productId: Product['id'],
		fileIds: SingleOrArray<File['id'] | { id: File['id'] }>,
		props: Omit<Parameters<this['create']>[0], 'product' | 'file'>,
	) {
		return Promise.all(
			(Array.isArray(fileIds) ? fileIds : [fileIds]).map((fileId, index) =>
				this.create({
					...props,
					sequence: (props.sequence ?? 0) + index,
					product: { id: productId },
					file: typeof fileId === 'string' ? { id: fileId } : fileId,
				}),
			),
		);
	}

	async unlink(productId: Product['id'], fileId: File['id'] | { id: File['id'] }) {
		await this.updateSequence(productId, fileId, MAX_POSITIVE_INTEGER);
		const result = await this.repository.delete({
			product: { id: productId },
			file: typeof fileId === 'string' ? { id: fileId } : fileId,
		});

		return !!result.affected;
	}

	async updateSequence(productId: Product['id'], fileId: File['id'] | { id: File['id'] }, sequence: number) {
		fileId = typeof fileId === 'string' ? fileId : fileId.id;
		const [records] = await this.list(listAll({ filter: { productId: { eq: productId } } }), this.getQueryBuilder());

		const selectedRecords = records.filter((record) => record.file.id === fileId);
		const originalSequence = selectedRecords[0]?.sequence ?? -1;
		const changedRecords: FileProduct[] = [];

		if (originalSequence === sequence || sequence === -1 || originalSequence === -1) return true; // No change

		// Moving file up
		if (originalSequence > sequence) {
			records
				.filter((record) => record.sequence >= sequence && record.sequence < originalSequence)
				.forEach((record) => {
					record.sequence = record.sequence + 1;
					changedRecords.push(record);
				});
		}

		// Moving file down
		if (originalSequence < sequence) {
			records
				.filter((record) => record.sequence <= sequence && record.sequence > originalSequence)
				.forEach((record) => {
					record.sequence = record.sequence - 1;
					changedRecords.push(record);
				});
		}

		selectedRecords.forEach((record) => {
			record.sequence = sequence;
			changedRecords.push(record);
		});

		await this.repository.save(changedRecords);
		return true;
	}
}
