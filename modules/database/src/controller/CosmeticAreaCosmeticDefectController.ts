import { type SingleOrArray } from '@pocitarna-nx-2023/utils';
import { In } from 'typeorm';
import { type CosmeticArea, CosmeticAreaCosmeticDefect, type CosmeticDefect } from '../entity';
import { BaseController } from '../utils/BaseController';

export class CosmeticAreaCosmeticDefectController extends BaseController<CosmeticAreaCosmeticDefect> {
	constructor() {
		super(CosmeticAreaCosmeticDefect);
	}

	link(
		cosmeticAreaId: CosmeticArea['id'],
		cosmeticDefectIds: SingleOrArray<CosmeticDefect['id']>,
	): Promise<CosmeticAreaCosmeticDefect[]> {
		const data = (Array.isArray(cosmeticDefectIds) ? cosmeticDefectIds : [cosmeticDefectIds]).map((cosmeticDefectId) => ({
			cosmeticArea: { id: cosmeticAreaId },
			cosmeticDefect: { id: cosmeticDefectId },
		}));

		return this.bulkCreate(data);
	}

	async unlink(cosmeticAreaId: CosmeticArea['id'], cosmeticDefectIds: SingleOrArray<CosmeticDefect['id']>) {
		const result = await this.repository.delete({
			cosmeticArea: { id: cosmeticAreaId },
			cosmeticDefect: { id: Array.isArray(cosmeticDefectIds) ? In(cosmeticDefectIds) : cosmeticDefectIds },
		});

		return !!result.affected;
	}

	async unlinkAll(cosmeticDefectId: CosmeticDefect['id']) {
		const result = await this.repository.delete({
			cosmeticDefect: { id: cosmeticDefectId },
		});

		return !!result.affected;
	}
}
