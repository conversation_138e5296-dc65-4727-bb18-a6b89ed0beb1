import { type SingleOrArray } from '@pocitarna-nx-2023/utils';
import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { In } from 'typeorm';
import { type CosmeticArea, type CosmeticDefect, type CustomerClaim, CustomerClaimCosmeticDefect, type File } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listAll } from '../utils/list';
import { FileCustomerClaimCosmeticDefectController } from './FileCustomerClaimCosmeticDefectController';

export class CustomerClaimCosmeticDefectController extends BaseController<CustomerClaimCosmeticDefect> {
	constructor() {
		super(CustomerClaimCosmeticDefect);
	}

	protected override getQueryBuilder() {
		return super
			.getQueryBuilder()
			.leftJoinAndSelect('entity.cosmeticDefect', 'cosmeticDefect')
			.leftJoinAndSelect('cosmeticDefect.cosmeticAreaCosmeticDefects', 'cosmeticAreaCosmeticDefect')
			.leftJoinAndSelect('entity.files', 'files');
	}

	async findByCustomerClaim(customerClaimId: CustomerClaim['id'], props: ListProps) {
		const [results] = await this.list(listAll({ ...props, filter: { ...props.filter, customerClaimId: { eq: customerClaimId } } }));
		return results;
	}

	link({
		customerClaimId,
		cosmeticAreaId,
		cosmeticDefectIds,
	}: {
		customerClaimId: CustomerClaim['id'];
		cosmeticAreaId: CosmeticArea['id'];
		cosmeticDefectIds: SingleOrArray<CosmeticDefect['id']>;
	}): Promise<CustomerClaimCosmeticDefect[]> {
		const data = (Array.isArray(cosmeticDefectIds) ? cosmeticDefectIds : [cosmeticDefectIds]).map((cosmeticDefectId) => ({
			customerClaim: { id: customerClaimId },
			cosmeticArea: { id: cosmeticAreaId },
			cosmeticDefect: { id: cosmeticDefectId },
		}));

		return this.bulkCreate(data);
	}

	async unlink(customerClaimId: CustomerClaim['id'], cosmeticDefectIds: SingleOrArray<CosmeticDefect['id']>) {
		const result = await this.repository.delete({
			customerClaim: { id: customerClaimId },
			cosmeticDefect: { id: Array.isArray(cosmeticDefectIds) ? In(cosmeticDefectIds) : cosmeticDefectIds },
		});

		return !!result.affected;
	}

	async unlinkAll(customerClaimId: CustomerClaim['id']) {
		const result = await this.repository.delete({
			customerClaim: { id: customerClaimId },
		});

		return !!result.affected;
	}

	listFiles(customerClaimCosmeticDefectId: CustomerClaimCosmeticDefect['id']) {
		return new FileCustomerClaimCosmeticDefectController().list(
			listAll({ filter: { customerClaimCosmeticDefectId: { eq: customerClaimCosmeticDefectId } }, sort: ['sequence'] }),
		);
	}

	async addFiles(
		customerClaimCosmeticDefectId: CustomerClaimCosmeticDefect['id'],
		fileIds: SingleOrArray<File['id'] | { id: File['id'] }>,
	) {
		const [files] = await this.listFiles(customerClaimCosmeticDefectId);
		const maxSequence = files.reduce((acc, file) => Math.max(acc, file.sequence), 0);
		const fileIdsToAdd = (Array.isArray(fileIds) ? fileIds : [fileIds])
			.map((fileId) => (typeof fileId === 'string' ? fileId : fileId.id))
			.filter((fileId) => !files.some((file) => file.fileId === fileId));
		return new FileCustomerClaimCosmeticDefectController().link(customerClaimCosmeticDefectId, fileIdsToAdd, {
			sequence: maxSequence + 1,
		});
	}

	async deleteFile(customerClaimCosmeticDefectId: CustomerClaimCosmeticDefect['id'], fileId: File['id'] | { id: File['id'] }) {
		return new FileCustomerClaimCosmeticDefectController().unlink(customerClaimCosmeticDefectId, fileId);
	}
}
