import { type SingleOrArray } from '@pocitarna-nx-2023/utils';
import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { In } from 'typeorm';
import { type CosmeticArea, type CosmeticDefect, type File, type Product, ProductCosmeticDefect } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listAll } from '../utils/list';
import { FileProductCosmeticDefectController } from './FileProductCosmeticDefectController';

export class ProductCosmeticDefectController extends BaseController<ProductCosmeticDefect> {
	constructor() {
		super(ProductCosmeticDefect);
	}

	protected override getQueryBuilder() {
		return super
			.getQueryBuilder()
			.leftJoinAndSelect('entity.cosmeticDefect', 'cosmeticDefect')
			.leftJoinAndSelect('cosmeticDefect.cosmeticAreaCosmeticDefects', 'cosmeticAreaCosmeticDefect')
			.leftJoinAndSelect('cosmeticDefect.grade', 'grade')
			.leftJoinAndSelect('entity.files', 'files');
	}

	async findByProduct(productId: Product['id'], props: ListProps) {
		const [results] = await this.list(listAll({ ...props, filter: { ...props.filter, productId: { eq: productId } } }));
		return results;
	}

	link({
		productId,
		cosmeticAreaId,
		cosmeticDefectIds,
		isFix,
	}: {
		productId: Product['id'];
		cosmeticAreaId: CosmeticArea['id'];
		cosmeticDefectIds: SingleOrArray<CosmeticDefect['id']>;
		isFix?: boolean;
	}): Promise<ProductCosmeticDefect[]> {
		const data = (Array.isArray(cosmeticDefectIds) ? cosmeticDefectIds : [cosmeticDefectIds]).map((cosmeticDefectId) => ({
			product: { id: productId },
			cosmeticArea: { id: cosmeticAreaId },
			cosmeticDefect: { id: cosmeticDefectId },
			isFix: isFix ?? false,
		}));

		return this.bulkCreate(data);
	}

	async unlink(productId: Product['id'], cosmeticDefectIds: SingleOrArray<CosmeticDefect['id']>) {
		const result = await this.repository.delete({
			product: { id: productId },
			cosmeticDefect: { id: Array.isArray(cosmeticDefectIds) ? In(cosmeticDefectIds) : cosmeticDefectIds },
		});

		return !!result.affected;
	}

	async unlinkAll(productId: Product['id']) {
		const result = await this.repository.delete({
			product: { id: productId },
		});

		return !!result.affected;
	}

	listFiles(productCosmeticDefectId: ProductCosmeticDefect['id']) {
		return new FileProductCosmeticDefectController().list(
			listAll({ filter: { productCosmeticDefectId: { eq: productCosmeticDefectId } }, sort: ['sequence'] }),
		);
	}

	async addFiles(productCosmeticDefectId: ProductCosmeticDefect['id'], fileIds: SingleOrArray<File['id'] | { id: File['id'] }>) {
		const [files] = await this.listFiles(productCosmeticDefectId);
		const maxSequence = files.reduce((acc, file) => Math.max(acc, file.sequence), 0);
		const fileIdsToAdd = (Array.isArray(fileIds) ? fileIds : [fileIds])
			.map((fileId) => (typeof fileId === 'string' ? fileId : fileId.id))
			.filter((fileId) => !files.some((file) => file.fileId === fileId));
		return new FileProductCosmeticDefectController().link(productCosmeticDefectId, fileIdsToAdd, { sequence: maxSequence + 1 });
	}

	async deleteFile(productCosmeticDefectId: ProductCosmeticDefect['id'], fileId: File['id'] | { id: File['id'] }) {
		return new FileProductCosmeticDefectController().unlink(productCosmeticDefectId, fileId);
	}
}
