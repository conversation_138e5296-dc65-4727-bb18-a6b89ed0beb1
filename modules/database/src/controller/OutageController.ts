import { Outage } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listOne } from '../utils/list';
import { useAuthentication } from '../utils/useAuthentication';

export class OutageController extends BaseController<Outage> {
	constructor() {
		super(Outage);
	}

	override getQueryBuilder() {
		return super.getQueryBuilder().innerJoinAndSelect('entity.createdBy', 'createdBy');
	}

	async getCurrent() {
		const [[outage]] = await this.list(listOne({ filter: { endedAt: { eq: null } }, sort: [['createdAt', 'desc']] }));
		if (!outage) return null;
		return outage;
	}

	async toggle() {
		const current = await this.getCurrent();
		if (current) {
			const user = useAuthentication()?.user;
			await this.update(current.id, { endedAt: new Date(), endedBy: user ? { id: user.id } : null });
			return this.create({ status: current.status === 'ok' ? 'outage' : 'ok' });
		}

		return this.create({ status: 'ok' });
	}
}
