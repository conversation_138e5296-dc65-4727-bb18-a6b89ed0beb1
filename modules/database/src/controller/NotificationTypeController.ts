import { NotificationType } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listOne } from '../utils/list';

export class NotificationTypeController extends BaseController<NotificationType> {
	constructor() {
		super(NotificationType);
	}

	async findByName(name: NotificationType['name']) {
		const [[item]] = await this.list(listOne({ filter: { name: { eq: name } } }));
		return item;
	}
}
