import { triggerShoptetExport } from '@pocitarna-nx-2023/aws';
import {
	type AttributeValueType,
	COPY_SECTOR,
	EMPTY_VALUE,
	type ProductFileType,
	type ProductStatus,
	STORE_SECTOR,
	TO_CLAIM_SECTOR,
	TO_STOCK_SECTOR,
	TO_TEST_SECTOR,
} from '@pocitarna-nx-2023/config';
import {
	calculateGradePrice,
	checkIfDiskCapacitiesMatch,
	filterUndefined,
	getProductStatusesAbove,
	getProductStatusesBelow,
	groupBy,
	type SingleOrArray,
	splitAttributesByDisplayName,
	uniques,
	uniquesBy,
	withIdLock,
	withoutVat,
} from '@pocitarna-nx-2023/utils';
import { type Filtering, type ListProps, type ProductWarranties } from '@pocitarna-nx-2023/zodios';
import { addYears, isBefore } from 'date-fns';
import { Decimal } from 'decimal.js';
import { Brackets, type Repository } from 'typeorm';
import { type Primitive } from 'zod';
import {
	type AttributeValue,
	type Batch,
	type File,
	type Grade,
	Product,
	type ProductAttributeValue,
	type ProductCategory,
	type ProductCode,
	type ProductDefect,
	type ProductEnvelope,
	type ProductTask,
	type ProductTest,
	type ServiceTask,
	type WarehousePosition,
} from '../entity';
import type { QueryBuilderMiddleware } from '../types';
import { BaseController } from '../utils/BaseController';
import { listAll, listOne } from '../utils/list';
import { generateStockReport } from '../utils/reports';
import { useAuthentication } from '../utils/useAuthentication';
import {
	AttributeController,
	AttributeValueController,
	BatchController,
	CurrencyRateController,
	DefectTypeController,
	FileProductController,
	GradeController,
	NotificationController,
	ProductAttributeValueController,
	ProductCodeController,
	ProductCosmeticDefectController,
	ProductDefectController,
	ProductEnvelopeController,
	ProductPriceController,
	ProductPriceHistoryController,
	ProductTestController,
	ServiceCaseController,
	VendorController,
	WarehouseController,
	WarehousePositionController,
	WarehouseTaskController,
	WarrantyClaimController,
	WarrantyController,
} from '.';

export class ProductController extends BaseController<Product> {
	constructor() {
		super(Product);
	}

	protected override getQueryBuilder(...middlewares: QueryBuilderMiddleware<Product>[]) {
		return super.getQueryBuilder(
			this.includeCommonMiddleware,
			this.includeProductEnvelopeMiddleware,
			this.includeProductTestMiddleware,
			this.includeDefectsMiddleware,
			this.includeTasksMiddleware,
			...middlewares,
		);
	}

	protected getBatchQueryBuilder(...middlewares: QueryBuilderMiddleware<Product>[]) {
		return super.getQueryBuilder(
			this.includeCommonMiddleware,
			this.includeProductEnvelopeMiddleware,
			this.includeProductTestMiddleware,
			this.includeDefectsMiddleware,
			this.includeTasksMiddleware,
			this.includeImportAttributeValuesMiddleware,
			...middlewares,
		);
	}

	protected getVendorAnalyticsQueryBuilder(...middlewares: QueryBuilderMiddleware<Product>[]) {
		return super.getQueryBuilder(
			this.includeCommonMiddleware,
			this.includeProductTestMiddleware,
			this.includeDefectsAnalyticsMiddleware,
			this.includeVendorMiddleware,
			...middlewares,
		);
	}

	protected getProductAnalyticsQueryBuilder(...middlewares: QueryBuilderMiddleware<Product>[]) {
		return super.getQueryBuilder(
			this.includeCommonMiddleware,
			this.includeProductTestMiddleware,
			this.includeProductStatisticsAttributesMiddleware,
			...middlewares,
		);
	}

	protected getProductAttributeValuesQueryBuilder(...middlewares: QueryBuilderMiddleware<Product>[]) {
		return this.getQueryBuilder(this.includeAttributeValuesMiddleware, ...middlewares);
	}

	protected includeCommonMiddleware: QueryBuilderMiddleware<Product> = (qb) =>
		qb
			.leftJoinAndSelect('entity.code', 'code')
			.leftJoinAndSelect('entity.productCategory', 'productCategory')
			.leftJoinAndSelect('entity.productPrice', 'productPrice')
			.leftJoinAndSelect('entity.grade', 'grade')
			.leftJoinAndSelect('entity.warehousePosition', 'warehousePosition')
			.leftJoinAndSelect('warehousePosition.warehouse', 'warehouse');

	protected includeProductTestMiddleware: QueryBuilderMiddleware<Product> = (qb) =>
		qb
			.leftJoinAndSelect('entity.productTest', 'productTest')
			.leftJoinAndSelect('productTest.testedBy', 'testedBy')
			.leftJoinAndSelect('productTest.conditionalAttribute', 'conditionalAttribute');

	protected includeProductEnvelopeMiddleware: QueryBuilderMiddleware<Product> = (qb) =>
		qb
			.leftJoinAndSelect('entity.productEnvelope', 'productEnvelope')
			.leftJoinAndSelect('productEnvelope.code', 'productEnvelopeCode')
			.leftJoinAndSelect('entity.productEnvelopeAssignedBy', 'productEnvelopeAssignedBy')
			.leftJoinAndSelect('productEnvelope.productCategory', 'productEnvelopeProductCategory');

	protected includeDefectsMiddleware: QueryBuilderMiddleware<Product> = (qb) =>
		qb
			.leftJoinAndSelect('entity.productDefects', 'productDefect')
			.leftJoinAndSelect('productDefect.defectType', 'defectType')
			.leftJoinAndSelect('productDefect.serviceTask', 'serviceTask')
			.leftJoinAndSelect('serviceTask.serviceTaskType', 'productDefectServiceTaskType')
			.leftJoinAndSelect('productDefect.vendorTask', 'vendorTask')
			.leftJoinAndSelect('vendorTask.serviceTaskType', 'productDefectVendorTaskType');

	protected includeDefectsAnalyticsMiddleware: QueryBuilderMiddleware<Product> = (qb) =>
		qb
			.leftJoinAndSelect('entity.productDefects', 'productDefect')
			.leftJoinAndSelect('productDefect.defectType', 'defectType')
			.leftJoinAndSelect('productDefect.serviceCase', 'serviceCase')
			.leftJoinAndSelect('productDefect.warrantyClaim', 'warrantyClaim');

	protected includeTasksMiddleware: QueryBuilderMiddleware<Product> = (qb) =>
		qb.leftJoinAndSelect('entity.productTasks', 'productTask').leftJoinAndSelect('productTask.serviceTaskType', 'serviceTaskType');

	protected includeAttributeValuesMiddleware: QueryBuilderMiddleware<Product> = (qb) =>
		qb
			.leftJoinAndSelect('entity.attributeValues', 'productAttributeValue')
			.leftJoinAndSelect('productAttributeValue.attributeValue', 'attributeValue')
			.leftJoinAndSelect('attributeValue.attribute', 'attribute');

	protected includeProductStatisticsAttributesMiddleware: QueryBuilderMiddleware<Product> = (qb) =>
		qb
			.innerJoinAndSelect('entity.attributeValues', 'relevantProductAttributeValue')
			.innerJoinAndSelect('relevantProductAttributeValue.attributeValue', 'relevantAttributeValue')
			.innerJoinAndSelect('relevantAttributeValue.attribute', 'relevantAttribute')
			.andWhere('relevantAttribute.displayName IN (:...displayNames)', {
				displayNames: ['Značka (výrobce)', 'Model', 'Provedení'],
			})
			.andWhere('relevantProductAttributeValue.type = :type', { type: 'resolved' })
			.andWhere('relevantAttributeValue.temporary = :temporary', { temporary: false });

	protected includeImportAttributeValuesMiddleware: QueryBuilderMiddleware<Product> = (qb) =>
		qb
			.leftJoinAndSelect(
				'entity.attributeValues',
				'productAttributeValue',
				'productAttributeValue.type = :type AND productAttributeValue.productId = entity.id',
				{ type: 'import' },
			)
			.leftJoinAndSelect('productAttributeValue.attributeValue', 'attributeValue')
			.leftJoinAndSelect('attributeValue.attribute', 'attribute');

	protected includeVendorMiddleware: QueryBuilderMiddleware<Product> = (qb) =>
		qb.innerJoinAndSelect('entity.batch', 'batch').innerJoinAndSelect('batch.vendor', 'vendor');

	protected getSNQueryBuilder(sn: Product['sn'], allowUnknown = false) {
		return this.getQueryBuilder().andWhere(
			new Brackets((qb) => {
				qb.where('entity.sn = :sn1', { sn1: sn });
				qb.orWhere('entity.sn LIKE :sn2', { sn2: `%${sn}%` });
				qb.orWhere(`:sn3 LIKE '%' || entity.sn || '%'`, { sn3: sn });
				if (allowUnknown) {
					qb.orWhere(`entity.sn = ''`);
				} else {
					qb.andWhere(`entity.sn != ''`);
				}

				return qb;
			}),
		);
	}

	override async create(data: Parameters<Repository<Product>['create']>[0]): Promise<Product> {
		const productEntity = await super.create({
			...data,
			pastSn: data.sn,
		});

		const { batch, productEnvelope, batchId, productEnvelopeId } = data;

		const batchIdToUse = batch?.id ?? batchId;
		const productEnvelopeIdToUse = productEnvelope?.id ?? productEnvelopeId;
		const batchEntity = batchIdToUse ? await new BatchController().findById(batchIdToUse) : null;
		const isNewBatch = batchEntity?.type === 'NEW';

		if (!isNewBatch) {
			await new ProductTestController().create({ product: { id: productEntity.id } });
		}

		if (batchEntity) {
			const batchWarrantyDeadline = batchEntity?.warranty;
			if (batchWarrantyDeadline) {
				await new WarrantyController().create({
					product: { id: productEntity.id },
					type: 'BUY',
					expiredAt: batchWarrantyDeadline,
				});
			}
		}

		if (productEnvelopeIdToUse) {
			await this.handleProductEnvelopeAssignment({ productId: productEntity.id, newProductEnvelopeId: productEnvelopeIdToUse });
		}

		return productEntity;
	}

	override async update(idOrRecord: Product['id'] | Product, data: Parameters<Repository<Product>['update']>[1]) {
		const product = await this.resolveRecord(idOrRecord);
		const oldStatus = product.status;
		const isProductStocking = oldStatus === 'STOCK' && data.status === 'FOR_SALE';
		const preSoldStatuses = getProductStatusesBelow('SOLD');
		const shouldSetSoldAt = data.status === 'SOLD' && !product.soldAt;
		const shouldClearSoldAt = product.soldAt && data.status && preSoldStatuses.includes(data.status as ProductStatus);
		const warehousePosition =
			typeof data.status === 'string' // if changing status
				? await this.getWarehousePositionOnUpdate(product, data.status)
				: undefined;
		const currentEnvelopeId = product?.productEnvelope?.id;
		const newEnvelopeId =
			data.productEnvelope && 'id' in data.productEnvelope && typeof data.productEnvelope.id === 'string'
				? data.productEnvelope.id
				: null;
		const envelopeHasChanged = Boolean(newEnvelopeId && newEnvelopeId !== currentEnvelopeId);
		const newGradeId = data.grade && 'id' in data.grade && typeof data.grade.id === 'string' ? data.grade.id : null;
		const gradeHasChanged = Boolean(newGradeId && newGradeId !== product?.gradeId);

		const newProduct = await super.update(product, {
			...data,
			...(shouldSetSoldAt ? { soldAt: new Date() } : {}),
			...(shouldClearSoldAt ? { soldAt: null } : {}),
			pastSn:
				data.pastSn ??
				uniques(
					product.pastSn
						.split('|')
						.concat((data.sn ? (typeof data.sn === 'string' ? data.sn : data.sn()) : '').split('|'), product.sn.split('|'))
						.map((sn) => sn.toUpperCase()),
				).join('|'),
			...(warehousePosition !== undefined ? { warehousePosition, pickedAt: null, pickedBy: null } : {}),
		});

		if (data.status === 'TO_TEST' && oldStatus !== 'TO_TEST') {
			const productTest = await new ProductTestController().findByProduct(product.id);
			if (productTest) {
				await new ProductTestController().update(productTest.id, { testedAt: null, deadlineAt: null, testedBy: undefined });
			}
		}

		if (data.status === 'SOLD' && oldStatus === 'SOLD') {
			const warrantyDeadline = addYears(new Date(), 2);

			await new WarrantyController().create({
				product: { id: product.id },
				type: 'SELL',
				expiredAt: warrantyDeadline,
			});
		}

		// Delete open pickup tasks if the product that was in an order changes status
		if (data.status && !['SOLD', 'RESERVED'].includes(data.status as ProductStatus) && ['SOLD', 'RESERVED'].includes(oldStatus)) {
			const [openPickupTasks] = await new WarehouseTaskController().listOpenByProduct(product.id, {
				filter: { type: { eq: 'PICKUP' } },
			});
			await new WarehouseTaskController().delete(openPickupTasks.map(({ id }) => id));
		}

		const userId = useAuthentication()?.user.id;
		if (
			userId &&
			data.status &&
			newProduct?.productEnvelopeId != null &&
			!isProductStocking &&
			oldStatus !== data.status &&
			(oldStatus === 'FOR_SALE' || data.status === 'FOR_SALE')
		) {
			await new NotificationController().notify({
				data: {
					body: 'Karta produktu by se měla exportovat do Shoptetu.',
					href: `/product/envelope/${newProduct.productEnvelopeId}`,
				},
				userId,
				notificationType: 'SHOPTET_EXPORT',
			});
		}

		if (((data.code as { id: string } | undefined)?.id || data.codeId) && !product.codeId) {
			const [files] = await new ProductCodeController().listFiles(
				(data.codeId as string | undefined) ?? (data.code as { id: string }).id,
			);
			await new ProductController().addFiles(
				newProduct.id,
				files.map((item) => item.fileId),
			);
		}

		// Update product prices if envelope has changed
		if (envelopeHasChanged) {
			await this.handleProductEnvelopeAssignment({
				productId: product.id,
				newProductEnvelopeId: newEnvelopeId as string,
				currentProductEnvelopeId: currentEnvelopeId,
			});
		}

		// Update product price if grade has changed
		if (gradeHasChanged) {
			await this.applyGradeRecommenededPrice(product.id, newGradeId as string);
		}

		return newProduct;
	}

	override async delete(ids: SingleOrArray<Product['id']>) {
		const [products] = await new ProductController().list(listAll({ filter: { id: { eq: ids } } }));
		if (!products || products.length === 0) return false;

		const eligibleProducts = products.filter((product) => ['AT_SUPPLIER', 'ON_THE_WAY'].includes(product.status));
		const productsWithoutCodes = eligibleProducts.filter((product) => !product.code && product.batchId);
		const batchIds = filterUndefined(uniques(productsWithoutCodes.map((product) => product.batchId)));
		const batchCodesCache: Record<Batch['id'], ProductCode['id'][]> = {};
		for (const batchId of batchIds) {
			const [batchProductCodes] = await new ProductCodeController().listUnassignedByBatch(batchId, listAll());
			batchCodesCache[batchId] = batchProductCodes.map((productCode) => productCode.id);
		}

		const productCodeIdsFromProducts = eligibleProducts.map((product) => product.codeId);
		const productCodeIdsFromBatches = productsWithoutCodes.map((product) =>
			product.batchId ? batchCodesCache[product.batchId].pop() : undefined,
		);
		await new ProductCodeController().free(filterUndefined([...productCodeIdsFromProducts, ...productCodeIdsFromBatches]));

		const productEnvelopeIds = filterUndefined(eligibleProducts.map((product) => product.productEnvelopeId));
		await Promise.all(productEnvelopeIds.map((productEnvelopeId) => triggerShoptetExport(productEnvelopeId)));

		return super.delete(eligibleProducts.map((product) => product.id));
	}

	async listWithCosmeticDefects(props: ListProps) {
		const queryBuilder = this.getQueryBuilder()
			.leftJoinAndSelect('entity.productCosmeticDefects', 'productCosmeticDefect')
			.leftJoinAndSelect('productCosmeticDefect.cosmeticDefect', 'cosmeticDefect');

		return await this.list(props, queryBuilder);
	}

	async addSalePrice(productId: Product['id'], amount: number) {
		const amountToSave = new Decimal(amount);
		const currencyRate = await this.getProductCurrencyRateFromBatch(productId);

		return await new ProductPriceController().findOrCreate({
			productId,
			priceType: 'SELL',
			currencyRate,
			value: amountToSave,
		});
	}

	async addDiscount({
		productId,
		amount,
		...rest
	}: {
		productId: Product['id'];
		amount: number | Decimal;
		productTaskId?: ProductTask['id'];
		serviceTaskId?: ServiceTask['id'];
	}) {
		const value = amount instanceof Decimal ? amount : new Decimal(amount);

		return new ProductPriceController().create({
			product: { id: productId },
			type: 'DISCOUNT',
			value,
			...rest,
		});
	}

	async addServicePrice({
		productId,
		amount,
		productTaskId,
		serviceTaskId,
	}: {
		productId: Product['id'];
		amount: number | Decimal;
		productTaskId?: ProductTask['id'];
		serviceTaskId?: ServiceTask['id'];
	}) {
		const value = amount instanceof Decimal ? amount : new Decimal(amount);

		return new ProductPriceController().create({
			product: { id: productId },
			type: 'SERVICE',
			value,
			productTaskId,
			serviceTaskId,
		});
	}

	async addCustomerClaimPrice({
		productId,
		amount,
		serviceTaskId,
	}: {
		productId: Product['id'];
		amount: number | Decimal;
		serviceTaskId?: ServiceTask['id'];
	}) {
		const value = amount instanceof Decimal ? amount : new Decimal(amount);

		return new ProductPriceController().create({
			product: { id: productId },
			type: 'CUSTOMER_CLAIM',
			value,
			serviceTaskId,
		});
	}

	async addBuyPrice(productId: Product['id'], amount: number) {
		const currencyRate = await this.getProductCurrencyRateFromBatch(productId);
		const amountToDecimal = new Decimal(amount);
		const amountToSave = currencyRate == null ? amountToDecimal : amountToDecimal.times(new Decimal(currencyRate.rate));
		return await new ProductPriceController().findOrCreate({
			productId,
			priceType: 'BUY',
			currencyRate,
			value: amountToSave,
		});
	}

	async addStandardPrice(productId: Product['id'], amount: number) {
		return await new ProductPriceController().findOrCreate({
			productId,
			priceType: 'STANDARD',
			value: new Decimal(withoutVat(amount)),
		});
	}

	async addRecommendedPrice(productId: Product['id'], amount: number) {
		return await new ProductPriceController().findOrCreate({
			productId,
			priceType: 'RECOMMENDED',
			value: new Decimal(withoutVat(amount)),
		});
	}

	async getProductWarranties(productId: Product['id']): Promise<ProductWarranties> {
		const [buyWarranty] = await new WarrantyController().findByProductAndType(productId, 'BUY');
		const [saleWarranty] = await new WarrantyController().findByProductAndType(productId, 'SELL');
		const serviceWarranties = (await new WarrantyController().findByProductAndType(productId, 'SERVICE')) ?? [];

		return {
			buyWarranty,
			saleWarranty,
			serviceWarranties,
		};
	}

	async findByBatch(batchIds: SingleOrArray<Batch['id']>, props?: ListProps) {
		return this.list({ ...props, filter: { ...props?.filter, batchId: { eq: batchIds } } }, this.getBatchQueryBuilder());
	}

	async findByEnvelope(envelopeIds: SingleOrArray<ProductEnvelope['id']>, props?: ListProps) {
		return this.list({ ...props, filter: { ...props?.filter, productEnvelopeId: { eq: envelopeIds } } });
	}

	async findOldestOrderableProductInEnvelope(envelopeId: ProductEnvelope['id'], props?: ListProps) {
		const commonFilter: Filtering = {
			...props?.filter,
			productEnvelopeId: { eq: envelopeId },
			status: { eq: 'FOR_SALE' },
		};

		const [[oldestProductInStockWarehouse]] = await this.list(
			listOne({
				sort: [['createdAt', 'asc']],
				filter: {
					...commonFilter,
					'warehousePosition.warehouse.type': { eq: 'STOCK' },
					'warehousePosition.sector': { ne: [STORE_SECTOR, COPY_SECTOR] },
				},
			}),
		);

		if (oldestProductInStockWarehouse) return oldestProductInStockWarehouse;

		const [[oldestProductAnywhereElse]] = await this.list(
			listOne({
				sort: [['createdAt', 'asc']],
				filter: {
					...commonFilter,
					'warehousePosition.sector': { ne: [STORE_SECTOR, COPY_SECTOR] },
				},
			}),
		);

		if (oldestProductAnywhereElse) return oldestProductAnywhereElse;

		const [[oldestProductInSensitivePositions]] = await this.list(
			listOne({
				sort: [['createdAt', 'asc']],
				filter: {
					...commonFilter,
					'warehousePosition.warehouse.type': { eq: 'STOCK' },
					'warehousePosition.sector': { eq: [STORE_SECTOR, COPY_SECTOR] },
				},
			}),
		);

		if (oldestProductInSensitivePositions) return oldestProductInSensitivePositions;

		const [[oldestProductWithoutPosition]] = await this.list(
			listOne({
				sort: [['createdAt', 'asc']],
				filter: commonFilter,
			}),
		);

		return oldestProductWithoutPosition ?? null;
	}

	async findBySn(sn: Product['sn'], props?: ListProps, allowUnknown = false) {
		const [[product]] = await this.list(
			listOne({ ...props, sort: [...(props?.sort ?? []), ['sn', 'desc']] }),
			this.getSNQueryBuilder(sn, allowUnknown),
		);

		if (!product) return null;
		return product;
	}

	async findBySnWithUnknown(sn: Product['sn'], props?: ListProps) {
		return this.findBySn(sn, props, true);
	}

	async findBySnInBatch(batchId: Batch['id'], sn: Product['sn'], props?: ListProps, allowUnknown = false) {
		return this.findBySn(sn, { ...props, filter: { ...props?.filter, batchId: { eq: batchId } } }, allowUnknown);
	}

	async findBySnInBatchWithMatchInfo(batchId: Batch['id'], sn: Product['sn'], props?: ListProps) {
		const exactMatch = await this.findBySnInBatch(batchId, sn, props, false);
		if (exactMatch) {
			return { product: exactMatch, matchType: 'exact' as const };
		}

		const fallbackMatch = await this.findBySnInBatch(batchId, sn, props, true);
		if (fallbackMatch) {
			return { product: fallbackMatch, matchType: 'fallback' as const };
		}

		return null;
	}

	async findEmptyInBatch(batchId: Batch['id']) {
		const [[product]] = await this.list(
			listOne(),
			this.getQueryBuilder()
				.innerJoin('entity.batch', 'batch')
				.leftJoin('entity.attributeValues', 'attributeValues')
				.where('"batch"."id" = :batchId', { batchId })
				.andWhere('"attributeValues" IS NULL'),
		);

		if (!product) return null;
		return product;
	}

	async countProductsInBatch(batchId: Batch['id']) {
		const filter: ListProps['filter'] = { batchId: { eq: batchId } };
		const [notTested, notVerified, tested] = await Promise.all([
			// Intentionally using super.getQueryBuilder because we don't need the joins here
			this.count({ filter: { ...filter, status: { eq: getProductStatusesBelow('TESTED') } } }, super.getQueryBuilder()),
			this.count({ filter: { ...filter, status: { eq: 'TESTED' } } }, super.getQueryBuilder()),
			this.count({ filter: { ...filter, status: { eq: getProductStatusesAbove('TESTED') } } }, super.getQueryBuilder()),
		] as const);

		return {
			notTested,
			notVerified,
			tested,
			total: notTested + notVerified + tested,
		};
	}

	async getProductCurrencyRateFromBatch(productId: Product['id']) {
		const product = await this.findById(productId);
		if (!product) return undefined;

		if (!product.batchId) return await new CurrencyRateController().getCZKCurrencyRate();

		const batch = await new BatchController().findById(product.batchId);

		if (!batch || !batch.currencyRateId) return await new CurrencyRateController().getCZKCurrencyRate();

		return (
			(await new CurrencyRateController().findById(batch.currencyRateId)) ?? (await new CurrencyRateController().getCZKCurrencyRate())
		);
	}

	async findByCode(code: ProductCode['code']) {
		const [result] = await this.list(listOne(), this.getQueryBuilder().where('code.code = :code', { code }));
		if (result.length === 0) return null;
		return result[0];
	}

	async findByProductCode(productCode: ProductCode) {
		const [result] = await this.list(listOne({ filter: { codeId: { eq: productCode.id } } }));
		if (result.length === 0) return null;
		return result[0];
	}

	async addAttributeValue(
		productId: Product['id'],
		attributeValueIds: SingleOrArray<AttributeValue['id']>,
		type: AttributeValueType,
	): Promise<ProductAttributeValue[]> {
		// FIXME - typescript doesn't know that link accepts both arrays and single values
		if (Array.isArray(attributeValueIds)) {
			return new ProductAttributeValueController().link(productId, attributeValueIds, { type });
		}
		return new ProductAttributeValueController().link(productId, attributeValueIds, { type });
	}

	async removeAttributeValue(productId: Product['id'], attributeValueIds: SingleOrArray<AttributeValue['id']>, type: AttributeValueType) {
		// FIXME - typescript doesn't know that link accepts both arrays and single values
		if (Array.isArray(attributeValueIds)) {
			return new ProductAttributeValueController().unlink(productId, attributeValueIds, type);
		}
		return new ProductAttributeValueController().unlink(productId, attributeValueIds, type);
	}

	async getAttributeValues(...props: Parameters<AttributeController['listByProduct']>) {
		return new AttributeController().listByProduct(...props);
	}

	async resolveAttributeValues(
		productId: Product['id'],
		attributeValues: { attributeId: string; attributeValueId: string; isFix: boolean }[],
	) {
		await new ProductAttributeValueController().unlinkAll(productId, 'resolved');

		await Promise.all(
			attributeValues.map(
				async ({ attributeValueId, isFix }) =>
					await new ProductAttributeValueController().link(productId, attributeValueId, {
						type: 'resolved',
						isFix: isFix ?? false,
					}),
			),
		);
	}

	async setAttributeValue(
		productId: Product['id'],
		attributeValues: SingleOrArray<{ attributeId: string; attributeValueId: string }>,
		types: SingleOrArray<ProductAttributeValue['type']> = 'resolved',
		autofill = false,
		lock = false,
		productTaskId?: ProductTask['id'],
		isFix = false,
	) {
		attributeValues = Array.isArray(attributeValues) ? attributeValues : [attributeValues];

		if (attributeValues.length === 0) return;

		const product = await this.findById(productId);

		if (!product?.productCategoryId) return;

		// Filter the attributeValues received to only include the ones that are in the category
		const [categoryAttributes] = await new AttributeController().listByCategory(product.productCategoryId, listAll());
		const attributeValuesToProcess = attributeValues.filter((value) =>
			categoryAttributes.some((attribute) => attribute.id === value.attributeId),
		);

		const [oldProductAttributeValues] = await new ProductAttributeValueController().list(
			listAll({
				filter: {
					productId: { eq: productId },
					'attribute.id': { eq: uniques(attributeValuesToProcess.map(({ attributeId }) => attributeId)) },
					type: { eq: types },
				},
			}),
		);

		types = Array.isArray(types) ? types : [types];

		for (const type of types) {
			const filteredOldProductAttributeValues = oldProductAttributeValues.filter((item) => item.type === type);

			if (filteredOldProductAttributeValues.length > 0) {
				await new ProductAttributeValueController().unlink(
					productId,
					filteredOldProductAttributeValues.map(({ attributeValue }) => attributeValue.id),
					type,
				);
			}

			await new ProductAttributeValueController().link(
				productId,
				attributeValuesToProcess.map(({ attributeValueId }) => attributeValueId),
				{ type, autofill, lock, productTaskId, isFix },
			);
		}
	}

	async changeCategory(productId: Product['id'], categoryId: ProductCategory['id']) {
		return this.update(productId, { productCategory: { id: categoryId } });
	}

	async createMismatchDefect(productId: string, conflicts: AttributeValue[][], warrantyClaimId: string) {
		const defectType = await new DefectTypeController().findByName('Neshodující se parametry');

		await new ProductDefectController().create({
			product: { id: productId },
			defectType: { id: defectType.id },
			warrantyClaim: { id: warrantyClaimId },
			source: 'TESTING',
			note: conflicts
				.map(
					(group) =>
						`${group[0].attribute.displayName}: ${group
							.sort((a, b) => a.products[0].type.localeCompare('import') - b.products[0].type.localeCompare('import'))
							.map(
								(attributeValue) =>
									`${attributeValue.products[0].type === 'import' ? 'z importu' : 'výsledná hodnota'} ${attributeValue.value} ${attributeValue.attribute.unit}`,
							)
							.join(', ')}`,
				)
				.join('\n'),
		});
	}

	async handleProductDefectsAndGetNextStatus(batch: Batch, productDefects: ProductDefect[]): Promise<ProductStatus> {
		const productId = productDefects[0].productId;
		const [productBuyWarranty] = await new WarrantyController().findByProductAndType(productId, 'BUY');
		const productIsInWarranty = productBuyWarranty.expiredAt && isBefore(new Date(), productBuyWarranty.expiredAt);

		const defectTypes = filterUndefined(
			uniquesBy(
				productDefects.map(({ defectType }) => defectType),
				'id',
			),
		);

		const vendor = batch?.vendorId ? await new VendorController().findById(batch.vendorId) : null;
		const someDefectsAreCovered = defectTypes.some((curr) => !vendor?.defectTypes?.some(({ defectType }) => defectType.id === curr.id));

		if (productIsInWarranty && someDefectsAreCovered) {
			const warrantyClaim = await new WarrantyClaimController().create({});
			await Promise.all(
				productDefects.map(async ({ id }) => {
					await new ProductDefectController().update(id, { warrantyClaim: { id: warrantyClaim.id } });
				}),
			);
			return 'WARRANTY_CLAIM';
		} else {
			const serviceCase = await new ServiceCaseController().create({});
			await Promise.all(
				productDefects.map(async ({ id }) => {
					await new ProductDefectController().update(id, { serviceCase: { id: serviceCase.id } });
				}),
			);
			return 'SERVICE';
		}
	}

	async handleClosingTest({
		product,
		batch,
		attributeValues,
		preventWarrantyClaim,
	}: {
		product: Product;
		batch: Batch;
		attributeValues: AttributeValue[];
		preventWarrantyClaim: boolean;
	}): Promise<ProductStatus> {
		if (!product.productCategoryId) throw new Error('Product is not in a category');
		const [productDefects] = await new ProductDefectController().findByProduct(product.id, listAll());
		const [compareAttributes] = await new AttributeController().listByCategory(product.productCategoryId, {
			filter: { 'categoryAttributes.type': { eq: 'compare' } },
		});
		const compareAttributeIds = compareAttributes.map(({ id }) => id);
		const compareAttributeValues = attributeValues.filter(({ attribute }) => compareAttributeIds.includes(attribute.id));

		const [diskAttributeValues, otherAttributeValues] = splitAttributesByDisplayName(
			compareAttributeValues,
			(item) => item.attribute.displayName,
		);
		const diskCapacitiesMatch = checkIfDiskCapacitiesMatch(
			diskAttributeValues,
			(item) => item.products.some((product) => product.type === 'service'),
			(item) => item.products.some((product) => product.type === 'import'),
			(item) => item.products.some((product) => product.type === 'resolved'),
		);
		const toCheckForConflicts = diskCapacitiesMatch ? otherAttributeValues : compareAttributeValues;

		const attributeGroups = groupBy(toCheckForConflicts, (item) => item.attribute.id);

		const productHasDefects = productDefects.filter((defect) => !defect.serviceTaskId).length > 0;
		const canIgnoreMismatch = product.productTest.ignoreMismatch;
		const hasTestMismatches = product.productTest.hasTestMismatches;

		if (hasTestMismatches && !preventWarrantyClaim && !canIgnoreMismatch) {
			const warrantyClaim = await new WarrantyClaimController().create({});
			const conflictingAttributeValues = await new ProductController().checkAttributeValuesConflicts(attributeGroups, 'testing');
			await this.createMismatchDefect(product.id, conflictingAttributeValues, warrantyClaim.id);

			if (productHasDefects) {
				await Promise.all(
					productDefects.map(async ({ id }) => {
						await new ProductDefectController().update(id, { warrantyClaim: { id: warrantyClaim.id } });
					}),
				);
			}

			return 'WARRANTY_CLAIM';
		}

		if (productHasDefects) {
			return await this.handleProductDefectsAndGetNextStatus(batch, productDefects);
		}

		// Here it means that the product has no issues whatsoever
		return 'STOCK';
	}

	async getBatch(recordOrId: Product | Product['id']) {
		const product = await this.resolveRecord(recordOrId);

		return new BatchController().findById(product.batchId ?? '');
	}

	listFiles(productId: Product['id'], props?: ListProps) {
		return new FileProductController().list(
			listAll({ ...props, filter: { ...props?.filter, productId: { eq: productId } }, sort: ['sequence'] }),
		);
	}

	async addFiles(productId: Product['id'], fileIds: SingleOrArray<File['id'] | { id: File['id'] }>, type: ProductFileType = 'regular') {
		const [files] = await this.listFiles(productId);
		const maxSequence = files.reduce((acc, file) => Math.max(acc, file.sequence), 0);
		const fileIdsToAdd = (Array.isArray(fileIds) ? fileIds : [fileIds])
			.map((fileId) => (typeof fileId === 'string' ? fileId : fileId.id))
			.filter((fileId) => !files.some((file) => file.fileId === fileId));
		return new FileProductController().link(productId, fileIdsToAdd, { sequence: maxSequence + 1, type });
	}

	async deleteFile(productId: Product['id'], fileId: File['id'] | { id: File['id'] }) {
		return new FileProductController().unlink(productId, fileId);
	}

	async findByDefect(defectId: ProductDefect['id']) {
		const defect = await new ProductDefectController().findById(defectId);
		if (!defect) return null;
		return await new ProductController().findById(defect.productId);
	}

	async handleBuyBack(productId: Product['id']) {
		const buyPrice = await new ProductPriceController().findOrCreate({ productId, priceType: 'BUY' });
		await new ProductPriceController().findOrCreate({
			productId,
			priceType: 'SELL',
			value: buyPrice?.value,
		});

		const targetStatus = await new ServiceCaseController().determineProductTargetStatus(productId, 'CLOSED', 'SOLD');

		return new ProductController().update(productId, { status: targetStatus });
	}

	async findByWarehousePosition(warehousePositionId: WarehousePosition['id']) {
		const queryBuilder = this.getQueryBuilder()
			.innerJoinAndSelect('entity.warehousePosition', 'warehousePosition')
			.where('warehousePosition.id = :warehousePositionId', {
				warehousePositionId,
			});

		return this.list(listAll(), queryBuilder);
	}

	async findByAttributeValues(attributeValueIds: AttributeValue['id'][], props: ListProps) {
		const queryBuilder = this.getQueryBuilder();
		const filter: Filtering = {};

		for (const [index, id] of attributeValueIds.entries()) {
			queryBuilder.innerJoin('entity.attributeValues', `productAttributeValue${index}`);
			filter[`productAttributeValue${index}.attributeValueId`] = { eq: id };
		}

		return this.list({ ...props, filter: { ...props.filter, ...filter } }, queryBuilder);
	}

	async listWithVendorAnalytics(props: ListProps) {
		return this.list(props, this.getVendorAnalyticsQueryBuilder());
	}

	async listWithProductAnalytics(props: ListProps) {
		return this.list(props, this.getProductAnalyticsQueryBuilder());
	}

	async findWithAttributeValues(id: Product['id']) {
		return this.findById(id, this.getProductAttributeValuesQueryBuilder());
	}

	async handleImportAttributeValuesUpdate(
		productId: Product['id'],
		attributeValues: { attributeId: string; attributeValueId?: string; value?: Primitive }[],
	) {
		const attributeIds = attributeValues.map(({ attributeId }) => attributeId);

		const [existingProductAttributeValues] = await new ProductAttributeValueController().list(
			listAll({
				filter: {
					type: { eq: ['import', 'resolved'] },
					productId: { eq: productId },
					'attribute.id': { eq: [...attributeIds] },
				},
			}),
		);

		const existingImportAttributeValues = existingProductAttributeValues.filter((attributeValue) => attributeValue.type === 'import');
		const existingResolvedAttributeValues = existingProductAttributeValues.filter(
			(attributeValue) => attributeValue.type === 'resolved',
		);

		const importUpdates: { attributeId: string; attributeValueId?: string; value?: Primitive }[] = [];
		const importAndResolvedUpdates: { attributeId: string; attributeValueId?: string; value?: Primitive }[] = [];

		for (const attributeValue of attributeValues) {
			const { attributeId } = attributeValue;
			const importAttributeValue = existingImportAttributeValues.find(
				({ attributeValue }) => attributeValue.attribute.id === attributeId,
			);
			const resolvedAttributeValue = existingResolvedAttributeValues.find(
				({ attributeValue }) => attributeValue.attribute.id === attributeId,
			);

			if (resolvedAttributeValue?.attributeValue.id === importAttributeValue?.attributeValue.id) {
				importAndResolvedUpdates.push(attributeValue);
			} else {
				importUpdates.push(attributeValue);
			}
		}

		await Promise.all([
			this.setAttributeValue(
				productId,
				importUpdates.filter((value): value is { attributeValueId: string; attributeId: string } => !!value.attributeValueId),
				['import'],
			),

			this.setAttributeValue(
				productId,
				importAndResolvedUpdates.filter(
					(value): value is { attributeValueId: string; attributeId: string } => !!value.attributeValueId,
				),
				['import', 'resolved'],
			),
		]);
	}

	async prepareStockReport(productIds: Product['id'][]) {
		return await generateStockReport(productIds);
	}

	async getWarehousePositionOnUpdate(product: Product, newStatus: ProductStatus): Promise<WarehousePosition | null | undefined> {
		const oldStatus = product.status;
		if (oldStatus === newStatus) return;

		const DELETE_WAREHOUSE_POSITION_STATUSES = ['SOLD', 'RETURNED', 'MISSING'];

		const shouldDeleteWarehousePosition =
			product.warehousePositionId != null &&
			DELETE_WAREHOUSE_POSITION_STATUSES.includes(newStatus) &&
			!DELETE_WAREHOUSE_POSITION_STATUSES.includes(oldStatus);
		if (shouldDeleteWarehousePosition) return null;

		// After-check
		if (oldStatus === 'ON_THE_WAY' && newStatus === 'TO_TEST') {
			const testingWarehouse = await new WarehouseController().findByType('TESTING');
			if (!testingWarehouse) throw new Error('Testing warehouse not available');
			return new WarehousePositionController().findOrCreate({
				warehouse: { id: testingWarehouse.id },
				sector: TO_TEST_SECTOR,
			});
		}

		// New batches after small test
		if (oldStatus === 'TO_CHECK' && newStatus === 'STOCK') {
			const testingWarehouse = await new WarehouseController().findByType('TESTING');
			if (!testingWarehouse) throw new Error('Testing warehouse not available');
			return new WarehousePositionController().findOrCreate({
				warehouse: { id: testingWarehouse.id },
				sector: TO_STOCK_SECTOR,
			});
		}

		// After-testing
		if (oldStatus === 'TO_TEST' && ['STOCK', 'TESTED'].includes(newStatus)) {
			const testingWarehouse = await new WarehouseController().findByType('TESTING');
			if (!testingWarehouse) throw new Error('Testing warehouse not available');
			return new WarehousePositionController().findOrCreate({
				warehouse: { id: testingWarehouse.id },
				sector: TO_STOCK_SECTOR,
			});
		}

		// When testing results in claim or service
		if (['TESTED', 'TO_TEST'].includes(oldStatus) && ['WARRANTY_CLAIM', 'SERVICE'].includes(newStatus)) {
			const serviceWarehouse = await new WarehouseController().findByType('SERVICE');
			if (!serviceWarehouse) throw new Error('Service warehouse not available');
			return new WarehousePositionController().findOrCreate({
				warehouse: { id: serviceWarehouse.id },
				sector: TO_CLAIM_SECTOR,
			});
		}

		return;
	}

	async checkProductAttributeValuesMismatches({ product, attributeValues }: { product: Product; attributeValues: AttributeValue[] }) {
		if (!product.productCategoryId) throw new Error('Product is not in a category');

		const [compareAttributes] = await new AttributeController().listByCategory(product.productCategoryId, {
			filter: { 'categoryAttributes.type': { eq: 'compare' } },
		});
		const compareAttributeIds = compareAttributes.map(({ id }) => id);
		const compareAttributeValues = attributeValues.filter(({ attribute }) => compareAttributeIds.includes(attribute.id));

		const [diskAttributeValues, otherAttributeValues] = splitAttributesByDisplayName(
			compareAttributeValues,
			(item) => item.attribute.displayName,
		);
		const diskCapacitiesMatch = checkIfDiskCapacitiesMatch(
			diskAttributeValues,
			(item) => item.products.some((product) => product.type === 'service'),
			(item) => item.products.some((product) => product.type === 'import'),
			(item) => item.products.some((product) => product.type === 'resolved'),
		);
		const toCheckForConflicts = diskCapacitiesMatch ? otherAttributeValues : compareAttributeValues;

		const attributeGroups = groupBy(toCheckForConflicts, (item) => item.attribute.id);

		const testingConflicts = await this.checkAttributeValuesConflicts(attributeGroups, 'testing');
		const stockConflicts = await this.checkAttributeValuesConflicts(attributeGroups, 'stock');

		return {
			hasTestMismatches: testingConflicts.length > 0,
			hasStockMismatches: stockConflicts.length > 0,
		};
	}

	async checkAttributeValuesConflicts(attributeGroups: AttributeValue[][], scope: 'stock' | 'testing') {
		const isStockScope = scope === 'stock';

		return attributeGroups.filter((attributeValues) => {
			const serviceAttributeValue = isStockScope
				? null
				: attributeValues.find((attributeValue) => attributeValue.products.some((product) => product.type === 'service'));
			const importAttributeValue = attributeValues.find((attributeValue) =>
				attributeValue.products.some((product) => product.type === 'import'),
			);
			const resolvedAttributeValue = attributeValues.find((attributeValue) =>
				attributeValue.products.some((product) => product.type === 'resolved'),
			);

			const compareAgainst = serviceAttributeValue ?? importAttributeValue;

			if (!compareAgainst || !resolvedAttributeValue) return false;

			const hasPlaceholderValue = compareAgainst.value === EMPTY_VALUE;

			if (hasPlaceholderValue) return false;

			if (compareAgainst.value === resolvedAttributeValue.value) return false;
			if (compareAgainst.value === 'SSD' && ['NVMe', 'SATA'].includes(resolvedAttributeValue.value as string)) return false; // FIXME - is there a way we don't have to hardcode this?

			return true;
		});
	}

	async handleAttributeValuesMismatchFlags(productId: Product['id']) {
		const product = await new ProductController().findById(productId);
		if (!product) return;

		const [attributeValues] = await new AttributeController().listByProduct(product.id, listAll());

		const { hasTestMismatches, hasStockMismatches } = await new ProductController().checkProductAttributeValuesMismatches({
			product,
			attributeValues,
		});

		const updates: Partial<ProductTest> = {
			...(product.productTest.ignoreMismatch ? { ignoreMismatch: false } : {}),
			...(product.productTest.hasTestMismatches !== hasTestMismatches ? { hasTestMismatches } : {}),
			...(product.productTest.hasStockMismatches !== hasStockMismatches ? { hasStockMismatches } : {}),
		};

		if (Object.keys(updates).length > 0) {
			await new ProductTestController().update(product.productTest, updates);
		}
	}

	async setWarehousePosition(productOrId: Product | Product['id'], warehousePositionId: WarehousePosition['id'] | null) {
		const product = await this.resolveRecord(productOrId);
		if (product.warehousePositionId === warehousePositionId) return;
		return this.update(product, { warehousePositionId, pickedBy: null, pickedAt: null });
	}

	async pickup(productOrId: Product | Product['id']) {
		const user = useAuthentication()?.user;
		return this.update(productOrId, { warehousePosition: null, pickedById: user?.id ?? null, pickedAt: new Date() });
	}

	async handleProductEnvelopeAssignment({
		productId,
		newProductEnvelopeId,
		currentProductEnvelopeId,
	}: {
		productId: Product['id'];
		newProductEnvelopeId: ProductEnvelope['id'];
		currentProductEnvelopeId?: ProductEnvelope['id'];
	}) {
		const product = await this.resolveRecord(productId);
		const currentEnvelope = currentProductEnvelopeId
			? await new ProductEnvelopeController().resolveRecord(currentProductEnvelopeId)
			: null;
		const newEnvelope = await new ProductEnvelopeController().resolveRecord(newProductEnvelopeId);

		if (['AMOUNT', 'NEW'].includes(newEnvelope.type)) {
			// For new and amount envelopes, remove all resolved and import attribute values, only use envelope-specific ones
			await new ProductAttributeValueController().unlinkAll(productId, 'resolved');
			await new ProductAttributeValueController().unlinkAll(productId, 'import');
		}

		const categoryHasChanged = product.productCategoryId !== newEnvelope.productCategoryId;

		const [productEnvelopeAttributeValues] = await new AttributeValueController().listByProductEnvelope(
			newProductEnvelopeId,
			listAll(),
		);

		await Promise.all(
			productEnvelopeAttributeValues.map(async (attributeValue) => {
				await new ProductController().setAttributeValue(
					productId,
					{
						attributeId: attributeValue.attribute.id,
						attributeValueId: attributeValue.id,
					},
					['resolved', 'import'],
				);
			}),
		);

		// Prices
		const { envelopeSalePrice, envelopeStandardPrice } = await new ProductEnvelopeController().getPriceInfo(newEnvelope.id);

		if (envelopeSalePrice.greaterThan(0)) {
			await new ProductPriceController().findOrCreate({
				productId: product.id,
				priceType: 'SELL',
				value: envelopeSalePrice,
			});
		}

		if (envelopeStandardPrice.greaterThan(0)) {
			await new ProductPriceController().findOrCreate({
				productId: product.id,
				priceType: 'STANDARD',
				value: envelopeStandardPrice,
			});
		}

		// If the envelope is of amount type and the previous envelope was not, free up PCNs. Using mutex lock to allow product updates in parallel while freeing unique PCNs.
		if (newEnvelope.type === 'AMOUNT' && currentEnvelope?.type !== 'AMOUNT' && product.batchId) {
			await withIdLock(product.batchId, async () => {
				const productCodeId = product.codeId;

				if (productCodeId) {
					await new ProductCodeController().free(productCodeId);
				} else {
					const [[unpairedProductCode]] = await new ProductCodeController().listUnassignedByBatch(
						product.batchId as string,
						listAll(),
					);

					if (unpairedProductCode) {
						await new ProductCodeController().free(unpairedProductCode.id);
					}
				}
			});
		}

		// Product update
		await this.update(productId, {
			productEnvelope: { id: newProductEnvelopeId },
			productEnvelopeAssignedAt: new Date(),
			productEnvelopeAssignedBy: useAuthentication()?.user ?? null,
			...(categoryHasChanged ? { productCategory: { id: newEnvelope.productCategoryId } } : {}),
		});
	}

	async applyGradeRecommenededPrice(productId: Product['id'], gradeId: Grade['id']) {
		const [recommendedPrice] = await new ProductPriceController().findByProductAndType(productId, 'RECOMMENDED');

		if (!recommendedPrice) return;

		const recommendedPriceHistory = await new ProductPriceHistoryController().listById(recommendedPrice.id);
		const originalRecommendedPrice = recommendedPriceHistory.at(-1);
		const currentValue = (originalRecommendedPrice ?? recommendedPrice).value;
		const newGrade = await new GradeController().resolveRecord(gradeId);

		if (!currentValue || !newGrade) return;

		await new ProductPriceController().update(recommendedPrice.id, {
			value: calculateGradePrice(currentValue, newGrade),
		});
	}

	async validateProductCosmeticDefectsPictures(productId: Product['id']) {
		const productCosmeticDefects = await new ProductCosmeticDefectController().findByProduct(productId, listAll());
		const allRelevantDefectsHaveImage = productCosmeticDefects.every((defect) => {
			if (!defect.cosmeticDefect?.pictureRequired) return true;
			return defect.files.length > 0;
		});

		if (!allRelevantDefectsHaveImage) throw new Error('Nahrajte prosím fotografii pro všechny relevantní kosmetické vady.');
	}

	async getSNDuplicatesByBatch(batchId: Batch['id']): Promise<
		Array<{
			productId: Product['id'];
			duplicateProductId: Product['id'];
			duplicateBatchId: Batch['id'];
		}>
	> {
		return this.repository
			.createQueryBuilder('product')
			.select('product.id', 'productId')
			.addSelect('duplicateProduct.id', 'duplicateProductId')
			.addSelect('duplicateProduct.batchId', 'duplicateBatchId')
			.innerJoin(
				Product,
				'duplicateProduct',
				`(duplicateProduct.sn = product.sn
				OR duplicateProduct.sn LIKE CONCAT('%', product.sn, '%')
				OR product.sn LIKE CONCAT('%', duplicateProduct.sn, '%'))`,
			)
			.where('product.batchId = :batchId', { batchId })
			.andWhere("product.sn != ''")
			.andWhere("duplicateProduct.sn != ''")
			.andWhere("product.sn != '-'")
			.andWhere("duplicateProduct.sn != '-'")
			.andWhere('duplicateProduct.id != product.id')
			.getRawMany();
	}

	async markAsMissing(productId: Product['id'], productCodeId?: ProductCode['id']) {
		const defectType = await new DefectTypeController().findByName('Chybějící produkt');

		await new ProductController().update(productId, {
			...(productCodeId ? { code: { id: productCodeId } } : {}),
			status: 'MISSING',
		});

		const warrantyClaim = await new WarrantyClaimController().create({ note: 'Chybějící produkt.' });

		await new ProductDefectController().create({
			product: { id: productId },
			defectType: { id: defectType.id },
			warrantyClaim: { id: warrantyClaim.id },
			source: 'TESTING',
		});
	}
}
