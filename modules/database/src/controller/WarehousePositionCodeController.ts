import type { Repository } from 'typeorm';
import { WarehousePositionCode } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listOne } from '../utils/list';

export class WarehousePositionCodeController extends BaseController<WarehousePositionCode> {
	constructor() {
		super(WarehousePositionCode);
	}

	override getQueryBuilder() {
		return super.getQueryBuilder().leftJoinAndSelect('entity.warehousePosition', 'warehousePosition');
	}

	override async create(data: Parameters<Repository<WarehousePositionCode>['create']>[0]) {
		const warehousePositionCode = await this.getNextFree();
		if (warehousePositionCode) {
			return (await this.update(warehousePositionCode, data)) as WarehousePositionCode;
		}

		return super.create(data);
	}

	async findByCode(code: WarehousePositionCode['code']) {
		const [[item]] = await this.list(listOne({ filter: { code: { eq: code } } }));
		return item;
	}

	async getNextFree() {
		const [[code]] = await this.list(listOne({ filter: { 'warehousePosition.id': { eq: null } } }));
		if (!code) return null;
		return code;
	}
}
