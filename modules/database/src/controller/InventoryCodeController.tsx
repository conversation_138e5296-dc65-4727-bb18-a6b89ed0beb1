import type { Repository } from 'typeorm';
import { InventoryCode } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listOne } from '../utils/list';

export class InventoryCodeController extends BaseController<InventoryCode> {
	constructor() {
		super(InventoryCode);
	}

	override getQueryBuilder() {
		return super.getQueryBuilder().leftJoinAndSelect('entity.inventory', 'inventory');
	}

	override async create(data: Parameters<Repository<InventoryCode>['create']>[0]) {
		const inventoryCode = await this.getNextFree();
		if (inventoryCode) {
			return (await this.update(inventoryCode, data)) as InventoryCode;
		}

		return super.create(data);
	}

	async findByCode(code: InventoryCode['code']) {
		const [[item]] = await this.list(listOne({ filter: { code: { eq: code } } }));
		return item;
	}

	async getNextFree() {
		const [[code]] = await this.list(listOne({ filter: { 'inventory.id': { eq: null } } }));
		if (!code) return null;
		return code;
	}
}
