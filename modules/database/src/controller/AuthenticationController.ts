import { DEFAULT_ROLE_NAME } from '@pocitarna-nx-2023/config';
import { JsonContains } from 'typeorm';
import { Authentication, listAll, type Role, RoleController } from '..';
import { BaseController } from '../utils/BaseController';

export class AuthenticationController extends BaseController<Authentication> {
	constructor() {
		super(Authentication);
	}

	override async delete(id: Authentication['id']) {
		await this.update(id, { deletedAt: new Date() });
		return true;
	}

	/**
	 * RELATIONS: role.scopes, user[authentications]
	 */
	async findByIdWithRelations(id: string) {
		return this.repository.findOne({
			select: {
				id: true,
				type: true,
				deletedAt: true,
				credentials: {},
				role: {
					id: true,
					name: true,
					description: true,
					scopes: {
						id: true,
						name: true,
						description: true,
					},
					defaultUrl: true,
				},
				user: {
					id: true,
					email: true,
					name: true,
					image: true,
					deletedAt: true,
					authentications: {
						id: true,
						type: true,
						credentials: {},
					},
				},
			},
			where: { id },
			relations: { role: { scopes: true }, user: { authentications: true } },
		});
	}

	/**
	 * RELATIONS:  user
	 */
	async findOneByEmailWithUser(email: string) {
		return this.repository.findOne({
			select: ['id', 'credentials', 'deletedAt', 'type', 'user'],
			where: { user: { email: email } },
			relations: { user: true },
		});
	}

	/**
	 * RELATIONS:  user, role, scopes
	 */
	async findOneByVerificationToken(verificationToken: string) {
		return this.repository.findOne({
			select: ['id', 'credentials', 'type', 'user', 'role'],
			where: { credentials: JsonContains({ verificationToken: { token: verificationToken } }) },
			relations: { user: true, role: { scopes: true } },
		});
	}

	async createAuthentication(userId: string, authentication: Parameters<(typeof this.repository)['save']>[0]) {
		const [[defaultRole]] = await new RoleController().list({ filter: { name: { eq: DEFAULT_ROLE_NAME } } });
		return this.repository.save({ role: defaultRole, ...authentication, user: { id: userId } });
	}

	async findVerificationTokenByUserId(userId: string) {
		const authentications = await this.repository.find({
			select: ['id', 'credentials', 'deletedAt', 'type'],
			where: { type: 'verificationCode', user: { id: userId } },
			order: { createdAt: 'DESC' },
		});

		const authentication = authentications.filter((authentication) => !authentication.deletedAt).at(0);
		if (!authentication) return null;

		return authentication.credentials.verificationToken ?? null;
	}

	async findByRole(roleId: Role['id']) {
		const queryBuilder = this.getQueryBuilder().innerJoin('entity.role', 'role', 'role.id = :roleId', {
			roleId,
		});

		const [matches] = await this.list(listAll(), queryBuilder);

		return matches;
	}
}
