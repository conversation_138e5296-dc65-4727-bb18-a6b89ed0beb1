import type { Repository } from 'typeorm';
import { type Attribute, type AttributeValue, ConditionalAttribute, type Product } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listAll, listOne } from '../utils/list';
import { AttributeController, AttributeValueConditionalAttributeController, AttributeValueController } from './index';

export class ConditionalAttributeController extends BaseController<ConditionalAttribute> {
	constructor() {
		super(ConditionalAttribute);
	}

	override getQueryBuilder() {
		return super
			.getQueryBuilder()
			.innerJoinAndSelect('entity.attributeValue', 'attributeValue')
			.innerJoinAndSelect('attributeValue.attribute', 'attribute')
			.leftJoinAndSelect('entity.conditionalAttributeValues', 'conditionalAttributeValues')
			.leftJoinAndSelect('conditionalAttributeValues.attributeValue', 'conditionalAttributeValue')
			.leftJoinAndSelect('conditionalAttributeValue.attribute', 'conditionalAttribute');
	}

	getChildQueryBuilder() {
		return super
			.getQueryBuilder()
			.innerJoinAndSelect('entity.attributeValue', 'attributeValue')
			.innerJoinAndSelect('attributeValue.attribute', 'attribute')
			.leftJoinAndSelect('entity.conditionalAttributeValues', 'conditionalAttributeValues')
			.leftJoinAndSelect('conditionalAttributeValues.attributeValue', 'conditionalAttributeValue')
			.leftJoinAndSelect('conditionalAttributeValue.attribute', 'conditionalAttribute')
			.distinctOn(['attribute.id', 'conditionalAttribute.id']);
	}

	override async update(
		idOrRecord: ConditionalAttribute | ConditionalAttribute['id'],
		data: Parameters<Repository<ConditionalAttribute>['update']>[1],
	) {
		const record = await this.resolveRecord(idOrRecord);

		const [conditionalAttributeValues] = await new AttributeValueConditionalAttributeController().list(
			listAll({ filter: { conditionalAttributeId: { eq: record.id } } }),
		);

		return super.update(record, { ...data, conditionalAttributeValues });
	}

	listAttributes(...props: Parameters<AttributeController['listConditional']>) {
		return new AttributeController().listConditional(...props);
	}

	async listChildAttributes(attributeId: Attribute['id']): Promise<Record<string, Attribute[]>>;
	async listChildAttributes(attributeIds: Attribute['id'][]): Promise<Record<string, Attribute[]>>;
	async listChildAttributes(attributeIds: Attribute['id'] | Attribute['id'][]): Promise<Record<string, Attribute[]>> {
		const [children] = await new ConditionalAttributeController().list(
			listAll({
				filter: { 'attribute.id': { eq: attributeIds } },
				sort: [
					['attribute.id', 'asc'],
					['conditionalAttribute.id', 'asc'],
				],
			}),
			this.getChildQueryBuilder(),
		);
		return children.reduce<Record<string, Attribute[]>>((acc, { attributeValue, conditionalAttributeValues }) => {
			(acc[attributeValue.attribute.id] = acc[attributeValue.attribute.id] ?? []).push(
				...conditionalAttributeValues.map(({ attributeValue }) => attributeValue.attribute),
			);
			return acc;
		}, {});
	}

	async findByAttributeValue(attributeValueId: AttributeValue['id']) {
		const [[conditionalAttribute]] = await this.list(listOne({ filter: { attributeValueId: { eq: attributeValueId } } }));
		if (!conditionalAttribute) return null;
		return conditionalAttribute;
	}

	async createProductTemplate(productId: Product['id'], attributeId: Attribute['id'], conditionalAttributeIds: Attribute['id'][]) {
		const [attributeValues] = await new AttributeValueController().listByProduct(productId, listAll());
		const resolvedAttributeValues = attributeValues.filter((item) => item.products.some((item) => item.type === 'resolved'));
		if (resolvedAttributeValues.length === 0) return;

		const keyAttributeValue = resolvedAttributeValues.find((attributeValue) => attributeValue.attribute.id === attributeId);
		if (!keyAttributeValue) return;

		const existingTemplate = await this.findByAttributeValue(keyAttributeValue.id);
		if (existingTemplate) return;

		const conditionalAttribute = await this.create({ attributeValue: keyAttributeValue });
		await new AttributeValueConditionalAttributeController().link(
			conditionalAttribute.id,
			resolvedAttributeValues
				.filter(({ id, attribute }) => conditionalAttributeIds.includes(attribute.id) && id !== keyAttributeValue.id)
				.map(({ id }) => id),
		);

		return conditionalAttribute;
	}

	async findOrCreate(attributeValue: AttributeValue) {
		const existingConditionalAttribute = await this.findByAttributeValue(attributeValue.id);

		return (
			existingConditionalAttribute ??
			(await new ConditionalAttributeController().create({
				attributeValue,
			}))
		);
	}
}
