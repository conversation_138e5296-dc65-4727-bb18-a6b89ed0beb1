import { ShoptetBrand } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listOne } from '../utils/list';

export class ShoptetBrandController extends BaseController<ShoptetBrand> {
	constructor() {
		super(ShoptetBrand);
	}

	async findByName(name: string) {
		// Not using filter because I need the transformation
		const [[brand]] = await this.list(listOne(), this.getQueryBuilder().where('UPPER(entity.name) = UPPER(:name)', { name }));
		if (!brand) return null;
		return brand;
	}
}
