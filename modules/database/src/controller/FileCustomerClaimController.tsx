import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { type SingleOrArray } from '@pocitarna-nx-2023/utils';
import { type CustomerClaim, type File, FileCustomerClaim } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listAll } from '../utils/list';

export class FileCustomerClaimController extends BaseController<FileCustomerClaim> {
	constructor() {
		super(FileCustomerClaim);
	}

	override getQueryBuilder() {
		return super.getQueryBuilder().leftJoinAndSelect('entity.file', 'file');
	}

	link(
		customerClaimId: CustomerClaim['id'],
		fileIds: SingleOrArray<File['id'] | { id: File['id'] }>,
		props: Omit<Parameters<this['create']>[0], 'customerClaim' | 'file'>,
	) {
		return Promise.all(
			(Array.isArray(fileIds) ? fileIds : [fileIds]).map((fileId, index) =>
				this.create({
					...props,
					sequence: (props.sequence ?? 0) + index,
					customerClaim: { id: customerClaimId },
					file: typeof fileId === 'string' ? { id: fileId } : fileId,
				}),
			),
		);
	}

	async unlink(customerClaimId: CustomerClaim['id'], fileId: File['id'] | { id: File['id'] }) {
		await this.updateSequence(customerClaimId, fileId, MAX_POSITIVE_INTEGER);
		const result = await this.repository.delete({
			customerClaim: { id: customerClaimId },
			file: typeof fileId === 'string' ? { id: fileId } : fileId,
		});

		return !!result.affected;
	}

	async updateSequence(customerClaimId: CustomerClaim['id'], fileId: File['id'] | { id: File['id'] }, sequence: number) {
		fileId = typeof fileId === 'string' ? fileId : fileId.id;
		const [records] = await this.list(listAll({ filter: { customerClaimId: { eq: customerClaimId } } }), this.getQueryBuilder());

		const selectedRecords = records.filter((record) => record.file.id === fileId);
		const originalSequence = selectedRecords[0]?.sequence ?? -1;
		const changedRecords: FileCustomerClaim[] = [];

		if (originalSequence === sequence || sequence === -1 || originalSequence === -1) return true; // No change

		// Moving file up
		if (originalSequence > sequence) {
			records
				.filter((record) => record.sequence >= sequence && record.sequence < originalSequence)
				.forEach((record) => {
					record.sequence = record.sequence + 1;
					changedRecords.push(record);
				});
		}

		// Moving file down
		if (originalSequence < sequence) {
			records
				.filter((record) => record.sequence <= sequence && record.sequence > originalSequence)
				.forEach((record) => {
					record.sequence = record.sequence - 1;
					changedRecords.push(record);
				});
		}

		selectedRecords.forEach((record) => {
			record.sequence = sequence;
			changedRecords.push(record);
		});

		await this.repository.save(changedRecords);
		return true;
	}
}
