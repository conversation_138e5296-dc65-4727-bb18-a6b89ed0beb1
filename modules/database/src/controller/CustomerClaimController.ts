import { filterUndefined, type SingleOrArray, uniques } from '@pocitarna-nx-2023/utils';
import { type Repository } from 'typeorm';
import { CustomerClaim, type File, type FileCustomerClaim, type Product } from '../entity';
import { type QueryBuilderMiddleware } from '../types';
import { BaseController } from '../utils/BaseController';
import { listAll, listOne } from '../utils/list';
import {
	AddressController,
	ContactController,
	CustomerClaimMessageController,
	EcommerceOrderItemController,
	FileCustomerClaimController,
	NoteController,
	ProductController,
	ProductDefectController,
} from '.';
import { CustomerClaimCodeController } from './CustomerClaimCodeController';

export class CustomerClaimController extends BaseController<CustomerClaim> {
	constructor() {
		super(CustomerClaim);
	}

	protected override getQueryBuilder(...middlewares: QueryBuilderMiddleware<CustomerClaim>[]) {
		return super
			.getQueryBuilder(...middlewares)
			.innerJoinAndSelect('entity.code', 'code')
			.innerJoinAndSelect('entity.createdBy', 'createdBy')
			.leftJoinAndSelect('entity.notes', 'note')
			.leftJoinAndSelect('note.createdBy', 'noteCreatedBy')
			.leftJoinAndSelect('entity.messages', 'message')
			.innerJoinAndSelect('entity.address', 'address')
			.innerJoinAndSelect('entity.contact', 'contact')
			.innerJoinAndSelect('entity.ecommerceOrderItem', 'ecommerceOrderItem')
			.innerJoinAndSelect('ecommerceOrderItem.product', 'product')
			.leftJoinAndSelect('product.code', 'productCode')
			.innerJoinAndSelect('ecommerceOrderItem.ecommerceOrder', 'ecommerceOrder');
	}

	override async create(
		data: Parameters<Repository<CustomerClaim>['save']>[0] & {
			note?: string;
			message: string;
			files?: string[];
		},
	): Promise<CustomerClaim> {
		if (!data.contact || !data.address) throw new Error('Contact is required');
		const contactToUse = await new ContactController().findOrCreate(data.contact, 'strict');
		const addressToUse = await new AddressController().findOrCreate(data.address);
		const { productDefects, note, message, files, ...rest } = data;

		const code = await new CustomerClaimCodeController().create({});
		const customerClaim = await super.create({ ...rest, code, contact: { id: contactToUse.id }, address: { id: addressToUse.id } });
		const ecommerceOrderItem = data.ecommerceOrderItem?.id
			? await new EcommerceOrderItemController().findById(data.ecommerceOrderItem.id)
			: null;

		if (!ecommerceOrderItem || !ecommerceOrderItem.productId) throw new Error('Cannot create customer claim on this order item');

		await new ProductController().update(ecommerceOrderItem.productId, { status: 'CUSTOMER_CLAIM' });

		if (!customerClaim) throw new Error('Could not create customer claim');

		if (note) {
			await new NoteController().create({ content: note, customerClaim: { id: customerClaim.id } });
		}

		if (files && files.length > 0) {
			await new CustomerClaimController().addFiles(customerClaim.id, files);
		}

		await new CustomerClaimMessageController().create({
			message,
			contact: { id: contactToUse.id },
			customerClaim: { id: customerClaim.id },
		});

		await Promise.all(
			(productDefects ?? []).map(async (productDefect) => {
				if (productDefect.id) {
					await new ProductDefectController().update(productDefect.id, { customerClaim: { id: customerClaim.id } });
				}
			}),
		);

		return customerClaim;
	}

	override async update(
		idOrRecord: CustomerClaim['id'] | CustomerClaim,
		data: Parameters<Repository<CustomerClaim>['update']>[1] & { productId: string },
	) {
		const currentClaim = await this.resolveRecord(idOrRecord);
		const currentStatus = currentClaim.status;
		const [productDefectsWithCustomerClaims] = await new ProductDefectController().list(
			listAll({
				filter: {
					productId: { eq: data.productId },
					customerClaimId: { ne: null },
				},
			}),
		);

		const otherCustomerClaimIds = filterUndefined(
			uniques(productDefectsWithCustomerClaims.map((defect) => defect.customerClaimId).filter((id) => id !== currentClaim.id)),
		);

		const [otherOpenClaims] = await this.list(
			listAll({
				filter: {
					id: { eq: otherCustomerClaimIds },
					status: { ne: 'RESOLVED' },
				},
			}),
		);

		if (currentStatus !== 'RESOLVED' && data.status === 'RESOLVED' && otherOpenClaims.length === 0) {
			await new ProductController().update(data.productId, { status: 'SOLD' });
		}

		return await super.update(idOrRecord, data);
	}

	async getCustomerClaimByProduct(productId: Product['id']) {
		const [[productDefect]] = await new ProductDefectController().list(
			listOne({ filter: { productId: { eq: productId }, customerClaimId: { ne: null } } }),
		);
		if (!productDefect) return null;

		if (!productDefect.customerClaimId) return null;

		return this.findById(productDefect.customerClaimId);
	}

	listFiles(customerClaimId: CustomerClaim['id']) {
		return new FileCustomerClaimController().list(
			listAll({ filter: { customerClaimId: { eq: customerClaimId } }, sort: ['sequence'] }),
		);
	}

	getPdfProtocolFileName(customerClaimId: CustomerClaim['id']) {
		return `Customer_Claim_Pdf_Protocol_${customerClaimId}.pdf`;
	}

	async getPdfProtocolFile(customerClaimId: CustomerClaim['id']): Promise<FileCustomerClaim | null> {
		const [[protocolFile]] = await new FileCustomerClaimController().list(
			listOne({
				filter: { customerClaimId: { eq: customerClaimId }, 'file.name': { eq: this.getPdfProtocolFileName(customerClaimId) } },
			}),
		);

		return protocolFile ?? null;
	}

	async addFiles(customerClaimId: CustomerClaim['id'], fileIds: SingleOrArray<File['id'] | { id: File['id'] }>) {
		const [files] = await this.listFiles(customerClaimId);
		const maxSequence = files.reduce((acc, file) => Math.max(acc, file.sequence), 0);
		const fileIdsToAdd = (Array.isArray(fileIds) ? fileIds : [fileIds])
			.map((fileId) => (typeof fileId === 'string' ? fileId : fileId.id))
			.filter((fileId) => !files.some((file) => file.fileId === fileId));
		return new FileCustomerClaimController().link(customerClaimId, fileIdsToAdd, { sequence: maxSequence + 1 });
	}

	async deleteFile(customerClaimId: CustomerClaim['id'], fileId: File['id'] | { id: File['id'] }) {
		return new FileCustomerClaimController().unlink(customerClaimId, fileId);
	}
}
