import { type AttributeValueType, NO_MATCH_ATTRIBUTE } from '@pocitarna-nx-2023/config';
import { type Primitive, type SingleOrArray, uniquesBy } from '@pocitarna-nx-2023/utils';
import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { Brackets, type Repository } from 'typeorm';
import {
	Attribute,
	AttributeNameAlternative,
	AttributeValue,
	AttributeValueAlternative,
	type Product,
	type ProductCategory,
	type ProductEnvelope,
	type ServiceTask,
	ShoptetAttribute,
} from '../entity';
import type { QueryBuilderMiddleware } from '../types';
import { BaseController } from '../utils/BaseController';
import { listAll, listOne } from '../utils/list';
import { ProductCategoryController, ProductController, ServiceTaskController } from '.';

type AttributeValuesChangeData = {
	oldAttributeValueId: AttributeValue['id'];
	newAttributeValueId: AttributeValue['id'];
	type: AttributeValueType;
};

export class AttributeController extends BaseController<Attribute> {
	constructor() {
		super(Attribute);
	}

	protected override getQueryBuilder(...middlewares: QueryBuilderMiddleware<Attribute>[]) {
		return super.getQueryBuilder(...middlewares).leftJoinAndSelect('entity.shoptetAttribute', 'shoptetAttribute');
	}

	private getCategoryQueryBuilder() {
		return this.getQueryBuilder().innerJoinAndSelect('entity.categoryAttributes', 'categoryAttributes');
	}

	private getConditionalQueryBuilder() {
		return this.getQueryBuilder()
			.innerJoin('entity.values', 'attributeValues')
			.innerJoin('attributeValues.conditionalAttributes', 'conditionalAttributes');
	}

	override async update(idOrRecord: Attribute['id'] | Attribute, data: Parameters<Repository<Attribute>['update']>[1]) {
		const attribute = await this.resolveRecord(idOrRecord);

		if (
			data.shoptetAttribute &&
			typeof data.shoptetAttribute !== 'function' &&
			data.shoptetAttribute.id !== attribute.shoptetAttributeId
		) {
			const [attributeValues] = await this.listValues(attribute?.id, { filter: { shoptetId: { ne: null } } });
			await Promise.all(
				attributeValues.map((attributeValue) => new AttributeValueController().update(attributeValue, { shoptetId: null })),
			);
		}
		return super.update(attribute, data);
	}

	async listValues(attributeId: Attribute['id'], ...props: Parameters<AttributeValueController['list']>) {
		return new AttributeValueController(attributeId).list(...props);
	}

	async listValuesWithAlternatives(attributeId: Attribute['id'], ...props: Parameters<AttributeValueController['listWithAlternatives']>) {
		return new AttributeValueController(attributeId).listWithAlternatives(...props);
	}

	async createValue(attributeId: Attribute['id'], ...props: Parameters<AttributeValueController['create']>) {
		return new AttributeValueController(attributeId).create(...props);
	}

	async getValue(attributeId: Attribute['id'], ...props: Parameters<AttributeValueController['findById']>) {
		return new AttributeValueController(attributeId).findById(...props);
	}

	async updateValue(attributeId: Attribute['id'], ...props: Parameters<AttributeValueController['update']>) {
		return new AttributeValueController(attributeId).update(...props);
	}

	async deleteValue(attributeId: Attribute['id'], ...props: Parameters<AttributeValueController['delete']>) {
		return new AttributeValueController(attributeId).delete(...props);
	}

	async listAlternatives(attributeId: Attribute['id'], ...props: Parameters<AttributeValueController['listAlternatives']>) {
		return new AttributeValueController(attributeId).listAlternatives(...props);
	}

	async createAlternative(attributeId: Attribute['id'], ...props: Parameters<AttributeValueController['createAlternative']>) {
		return new AttributeValueController(attributeId).createAlternative(...props);
	}

	async getAlternative(attributeId: Attribute['id'], ...props: Parameters<AttributeValueController['getAlternative']>) {
		return new AttributeValueController(attributeId).getAlternative(...props);
	}

	async updateAlternative(attributeId: Attribute['id'], ...props: Parameters<AttributeValueController['updateAlternative']>) {
		return new AttributeValueController(attributeId).updateAlternative(...props);
	}

	async deleteAlternative(attributeId: Attribute['id'], ...props: Parameters<AttributeValueController['deleteAlternative']>) {
		return new AttributeValueController(attributeId).deleteAlternative(...props);
	}

	async listByCategory(productCategoryIds: SingleOrArray<ProductCategory['id']>, props: ListProps) {
		return this.list(
			{
				...props,
				filter: { ...props.filter, 'categoryAttributes.productCategoryId': { eq: productCategoryIds } },
				sort: [...(props.sort ?? []), 'categoryAttributes.sequence'],
			},
			this.getCategoryQueryBuilder(),
		);
	}

	createAlternativeName(attributeId: Attribute['id'], data: Partial<AttributeNameAlternative>) {
		return new AttributeNameAlternativeController(attributeId).create(data);
	}

	listByProduct(...props: Parameters<AttributeValueController['listByProduct']>) {
		return new AttributeValueController().listByProduct(...props);
	}

	listConditional(props: ListProps) {
		return this.list(props, this.getConditionalQueryBuilder());
	}

	updateAlternativeName(attributeId: Attribute['id'], ...props: Parameters<AttributeNameAlternativeController['update']>) {
		return new AttributeNameAlternativeController(attributeId).update(...props);
	}

	deleteAlternativeName(attributeId: Attribute['id'], ...props: Parameters<AttributeNameAlternativeController['delete']>) {
		return new AttributeNameAlternativeController(attributeId).delete(...props);
	}

	getAlternativeName(attributeId: Attribute['id'], ...props: Parameters<AttributeNameAlternativeController['findById']>) {
		return new AttributeNameAlternativeController(attributeId).findById(...props);
	}

	async listAlternativeNames(attributeId: Attribute['id']) {
		return new AttributeNameAlternativeController(attributeId).findByAttribute();
	}

	async findByName(name: Attribute['name']): Promise<Attribute[] | null> {
		const [attributes] = await this.list(listAll({ filter: { name: { eq: name } } }));
		const alternativeName = (await new AttributeNameAlternativeController('').findByName(name))?.map((item) => item.parent) ?? [];

		const result = [...attributes, ...alternativeName];

		if (result.length > 0) return uniquesBy(result, 'id');

		return null;
	}

	async findFullById(id: Attribute['id']) {
		const result = await this.listQuery(
			listAll({ filter: { id: { eq: id } } }),
			this.getQueryBuilder()
				.leftJoinAndSelect('entity.alternativeNames', 'attributeNameAlternative')
				.leftJoinAndSelect('entity.values', 'attributeValue')
				.leftJoinAndSelect('attributeValue.alternatives', 'attributeValueAlternative'),
		);
		if (result.length === 0) return null;
		return result[0];
	}

	async findValue(attributeId: Attribute['id'], ...props: Parameters<AttributeValueController['findByValue']>) {
		return new AttributeValueController(attributeId).findByValue(...props);
	}

	async createTextValue(attributeId: Attribute['id'], ...props: Parameters<AttributeValueController['createTextValue']>) {
		return new AttributeValueController(attributeId).createTextValue(...props);
	}

	async findValueInAllAttributes(...props: Parameters<AttributeValueController['findByValue']>): Promise<{
		attributeId: string;
		attributeValueId: string;
	} | null> {
		const value = await new AttributeValueController().findByValue(...props);
		if (value) return { attributeId: value.attribute.id, attributeValueId: value.id };

		return null;
	}

	async getOrCreateValue(nameOrAttribute: string | { attributeId: string }, value: Primitive, temporary = true) {
		let attributes: { id: string }[] | null;
		if (typeof nameOrAttribute === 'string') {
			attributes = await this.findByName(nameOrAttribute);
		} else {
			attributes = [{ id: nameOrAttribute.attributeId }];
		}

		if (!attributes || attributes.length === 0) {
			throw new Error(`Attribute not found ${nameOrAttribute}, ${value}`);
		}

		return Promise.all(
			attributes.map(async ({ id }) => {
				const attributeValue = await this.findValue(id, value);

				if (attributeValue) return { attributeValueId: attributeValue.id, attributeId: id };

				const attribute = await this.findById(id);
				if (!attribute) throw new Error(`Attribute not found ${nameOrAttribute}, ${value}`);

				const newAttributeValue = await this.createValue(id, {
					value,
					temporary: attribute.dataType === 'text' ? false : temporary,
				});

				return { attributeValueId: newAttributeValue.id, attributeId: id };
			}),
		);
	}

	/**
	 * If screenSize is provided, the category is determined by the screen size, otherwise the default category is used
	 */
	async findCategoryAttributes(attributeIds: Attribute['id'][], screenSize?: number) {
		const [categoryAttributes] = await this.list(
			listAll({ filter: { id: { eq: attributeIds } } }),
			super
				.getQueryBuilder()
				.innerJoinAndSelect('entity.categoryAttributes', 'categoryAttributes')
				.innerJoinAndSelect('categoryAttributes.productCategory', 'productCategory')
				.where('"categoryAttributes".type = :type', { type: 'default' })
				.addGroupBy('entity.id')
				.addGroupBy('categoryAttributes.id')
				.addGroupBy('productCategory.id')
				.orderBy('COUNT("productCategory".id)', 'DESC'),
		);

		const categoryAttributesCount = categoryAttributes.reduce(
			(acc, curr) => {
				curr.categoryAttributes.forEach((categoryAttribute) => {
					const productCategory = categoryAttribute.productCategory;
					if (productCategory.id in acc) acc[productCategory.id] += 1;
					else acc[productCategory.id] = 1;
				});
				return acc;
			},
			{} as Record<ProductCategory['id'], number>,
		);

		const categoryId = Object.entries(categoryAttributesCount).sort((a, b) => b[1] - a[1])[0]?.[0] ?? null;
		let category = categoryId ? await new ProductCategoryController().findById(categoryId) : null;

		if (!category) return { attributes: [], category: null };

		if (screenSize && screenSize > 19 && category.name === 'Notebooky') {
			category = await new ProductCategoryController().findByName('All-in-One');
		} else if (screenSize && screenSize <= 19 && category.name === 'All-in-One') {
			category = await new ProductCategoryController().findByName('Notebooky');
		}

		if (!category) return { attributes: [], category: null };

		const [attributes] = await this.listByCategory(
			category.id,
			listAll({ filter: { 'categoryAttributes.attributeId': { eq: attributeIds }, 'categoryAttributes.type': { eq: 'testing' } } }),
		);

		return { attributes, category };
	}

	async getOrCreatePermanentValue(attributeId: string, value: Primitive) {
		const attribute = await this.findById(attributeId);

		if (!attribute) {
			throw new Error(`Attribute not found ${attributeId}, ${value}`);
		}

		const attributeValue = await this.findValue(attribute.id, value);

		if (attributeValue) return this.updateValue(attribute.id, attributeValue.id, { temporary: false });

		return this.createValue(attribute.id, {
			value,
			temporary: false,
		});
	}

	async getNames() {
		const [attributes] = await this.list(listAll());

		const attributeNames = attributes.map((attribute) => ({ name: attribute.name, attributeId: attribute.id }));
		const alternativeAttributeNames = (await this.listAlternativeNames(''))[0].map((attribute) => ({
			name: attribute.name,
			attributeId: attribute.parent.id,
		}));

		return [...attributeNames, ...alternativeAttributeNames];
	}

	async findProductsByValue(value: AttributeValue['value'], batchId: string) {
		return new AttributeValueController().findProductsByValue(value, batchId);
	}

	async findOrCreate(attributeName: Attribute['name']) {
		const existingAttribute = await this.findByName(attributeName);

		return existingAttribute?.[0] ?? (await this.create({ name: attributeName, displayName: attributeName }));
	}

	async findOrCreateNoMatchAttribute() {
		return this.findOrCreate(NO_MATCH_ATTRIBUTE);
	}
}

export class AttributeNameAlternativeController extends BaseController<AttributeNameAlternative> {
	private readonly attributeId: Attribute['id'];

	constructor(attributeId: Attribute['id']) {
		super(AttributeNameAlternative);
		this.attributeId = attributeId;
	}

	override getQueryBuilder() {
		const queryBuilder = super.getQueryBuilder();

		queryBuilder.innerJoinAndMapOne('entity.parent', Attribute, 'parent', 'parent.id = entity.parentId');

		return queryBuilder;
	}

	override async create(data: Parameters<Repository<AttributeNameAlternative>['create']>[0]) {
		return super.create({ ...data, parent: { id: this.attributeId } });
	}

	async findByName(name: AttributeNameAlternative['name']): Promise<AttributeNameAlternative[] | null> {
		const [attributeNames] = await this.list(listAll({ filter: { name: { eq: name } } }));

		return attributeNames[0] ? attributeNames : null;
	}

	async findByAttribute() {
		if (this.attributeId === '') {
			return this.list(listAll());
		}

		return this.list(listAll({ filter: { parentId: { eq: this.attributeId } } }));
	}
}

export class AttributeValueController extends BaseController<AttributeValue> {
	private readonly attributeId?: Attribute['id'];

	constructor(attributeId?: Attribute['id']) {
		super(AttributeValue);
		this.attributeId = attributeId;
	}

	override getQueryBuilder() {
		const queryBuilder = super.getQueryBuilder();

		queryBuilder.innerJoinAndMapOne(
			'entity.attribute',
			Attribute,
			'attribute',
			`attribute.id = entity.attributeId${this.attributeId ? ' AND attribute.id = :attributeId' : ''}`,
			{ attributeId: this.attributeId },
		);

		return queryBuilder;
	}

	private getAlternativesQueryBuilder() {
		return this.getQueryBuilder().leftJoin('entity.alternatives', 'alternatives');
	}

	override async update(idOrRecord: AttributeValue['id'] | AttributeValue, data: Parameters<Repository<AttributeValue>['update']>[1]) {
		const attributeValue = await this.resolveRecord(idOrRecord);

		if (data.value && data.value !== attributeValue.value) {
			await new AttributeValueAlternativeController(this.attributeId, attributeValue.id).create({ value: attributeValue.value });
			data.shoptetId = null;
		}

		return super.update(attributeValue, data);
	}

	getProductEnvelopeQueryBuilder() {
		return this.getQueryBuilder()
			.innerJoinAndSelect('entity.productEnvelopes', 'productEnvelopeAttributeValue')
			.innerJoinAndSelect('productEnvelopeAttributeValue.productEnvelope', 'productEnvelope');
	}

	getProductEnvelopeWithShoptetAttributesQueryBuilder() {
		return this.getProductEnvelopeQueryBuilder().leftJoinAndMapOne(
			'attribute.shoptetAttribute',
			ShoptetAttribute,
			'shoptetAttribute',
			'shoptetAttribute.id = attribute."shoptetAttributeId"',
		);
	}

	private getProductQueryBuilder() {
		return this.getQueryBuilder()
			.innerJoinAndSelect('entity.products', 'productAttributeValue')
			.innerJoinAndSelect('productAttributeValue.product', 'product');
	}

	getProductByAttributeValueIdQueryBuilder() {
		return this.getQueryBuilder()
			.leftJoinAndSelect('entity.products', 'productAttributeValue')
			.leftJoinAndSelect('productAttributeValue.product', 'product');
	}

	private getProductBasedOnValueQueryBuilder(value: AttributeValue['value'], batchId: string) {
		return this.getQueryBuilder()
			.leftJoinAndSelect('entity.alternatives', 'alternative')
			.leftJoinAndSelect('entity.products', 'productAttributeValue')
			.leftJoinAndSelect('productAttributeValue.product', 'product')
			.leftJoinAndSelect('product.batch', 'batch')
			.where(`batch.id = :batchId`, { batchId })
			.andWhere(`"product"."id" IS NOT NULL`)
			.andWhere(
				new Brackets((qb) =>
					qb.where(`:value LIKE entity.value ->> 0`, { value }).orWhere(`:value LIKE alternative.value ->> 0`, { value }),
				),
			);
	}

	async listByProductEnvelope(
		productEnvelopeIds: SingleOrArray<ProductEnvelope['id']>,
		props: ListProps,
		queryBuilder = this.getProductEnvelopeQueryBuilder(),
	) {
		return this.list({ ...props, filter: { ...props.filter, 'productEnvelope.id': { eq: productEnvelopeIds } } }, queryBuilder);
	}

	async listByProductEnvelopeWithShoptetAttributes(productEnvelopeId: ProductEnvelope['id'], props: ListProps) {
		return this.listByProductEnvelope(productEnvelopeId, props, this.getProductEnvelopeWithShoptetAttributesQueryBuilder());
	}

	async listByProduct(productId: Product['id'] | Product['id'][], props: ListProps) {
		return this.list({ ...props, filter: { ...props.filter, 'product.id': { eq: productId } } }, this.getProductQueryBuilder());
	}

	async listWithTestedProductsByAttributeValue(value: string) {
		return this.list(
			listAll({ filter: { 'value->>0': { eq: `%${value}%` }, 'product.status': { eq: 'TESTED' } } }),
			this.getProductByAttributeValueIdQueryBuilder(),
		);
	}

	override async create(data: Parameters<Repository<AttributeValue>['create']>[0]) {
		if (!this.attributeId && !data.attribute?.id) throw new Error('Attribute id is required');

		const attribute = await new AttributeController().findById(this.attributeId ?? data.attribute?.id ?? '');
		if (!attribute) throw new Error(`Attribute not found ${this.attributeId}`);
		const normalizedValue = data.value ? await this.normalizeValue(data.value) : data.value;

		return super.create({ ...data, value: normalizedValue, attribute: { id: attribute.id } });
	}

	async listWithAlternatives(props: ListProps) {
		return this.list(props, this.getAlternativesQueryBuilder());
	}

	async listAlternatives(valueId: AttributeValue['id'], ...props: Parameters<AttributeValueAlternativeController['list']>) {
		return new AttributeValueAlternativeController(this.attributeId, valueId).list(...props);
	}

	async createAlternative(valueId: AttributeValue['id'], ...props: Parameters<AttributeValueAlternativeController['create']>) {
		const { value } = props[0];
		if (!value) return;

		const attributeValue = await this.findById(valueId);
		if (!value || !attributeValue || attributeValue.temporary || attributeValue.value.toString() === value.toString()) return;

		const exitingAttributeValue = await new AttributeValueController(this.attributeId).findByValue(value, {
			filter: { temporary: { eq: false } },
		});
		if (exitingAttributeValue) return;

		const [validTemporaryValues] = await this.list(
			listAll({ filter: { 'value->>0': { eq: value }, temporary: { eq: true } } }),
			this.getProductByAttributeValueIdQueryBuilder(),
		);

		const changesPerProduct = validTemporaryValues.reduce<Record<AttributeValue['id'], AttributeValuesChangeData[]>>((acc, item) => {
			item.products.forEach(({ product, type }) => {
				if (!product) return;
				(acc[product.id] = acc[product.id] ?? []).push({
					oldAttributeValueId: item.id,
					newAttributeValueId: valueId,
					type,
				});
			});
			return acc;
		}, {});

		await Promise.all(
			Object.entries(changesPerProduct).map(async ([productId, data]) => {
				await this.swapAttributeValues(productId, data);
			}),
		);

		await this.delete(validTemporaryValues.map(({ id }) => id));
		return new AttributeValueAlternativeController(this.attributeId, valueId).create(...props);
	}

	async createTextValue(value: string) {
		const existingAttributeValue = await this.findByValue(value);
		if (existingAttributeValue) return existingAttributeValue;

		return this.create({ value, temporary: false });
	}

	async getAlternative(valueId: AttributeValue['id'], ...props: Parameters<AttributeValueAlternativeController['findById']>) {
		return new AttributeValueAlternativeController(this.attributeId, valueId).findById(...props);
	}

	async updateAlternative(valueId: AttributeValue['id'], ...props: Parameters<AttributeValueAlternativeController['update']>) {
		return new AttributeValueAlternativeController(this.attributeId, valueId).update(...props);
	}

	async deleteAlternative(valueId: AttributeValue['id'], ...props: Parameters<AttributeValueAlternativeController['delete']>) {
		return new AttributeValueAlternativeController(this.attributeId, valueId).delete(...props);
	}

	async findByValue(value: AttributeValue['value'], props: Omit<ListProps, 'page'> = {}): Promise<AttributeValue | null> {
		const searchValue = await this.normalizeValue(value);

		const [[attributeValue]] = await this.list(
			listOne(props),
			this.getQueryBuilder().where(`entity.value ->> 0 = :value`, { value: searchValue }),
		);
		if (attributeValue) return attributeValue;

		const attributeValueAlternative = await new AttributeValueAlternativeController(this.attributeId).findByValue(searchValue);
		if (attributeValueAlternative) return attributeValueAlternative.parent;

		return null;
	}

	async findProductsByAttributeValueId(
		attributeValueId: AttributeValue['id'] | AttributeValue['id'][],
		qb = this.getProductByAttributeValueIdQueryBuilder(),
	) {
		const [attributes] = await this.list(listAll({ filter: { id: { eq: attributeValueId } } }), qb);
		return attributes;
	}

	async findProductsByValue(value: AttributeValue['value'], batchId: string) {
		const [attributes] = await this.list(listAll(), this.getProductBasedOnValueQueryBuilder(value, batchId));
		return attributes;
	}

	async handleUnmatchedAttributeValue(value: string, fallbackAttributeId: AttributeValue['id']) {
		const [[alreadyExistingValue]] = await this.list(
			listOne(),
			this.getQueryBuilder()
				.where('attribute.id = :fallbackAttributeId', { fallbackAttributeId })
				.andWhere(`:value LIKE entity.value ->> 0`, { value }),
		);

		if (alreadyExistingValue) return null;

		const newAttributeValue = await new AttributeController().createValue(fallbackAttributeId, {
			value: value,
			temporary: true,
		});

		return { attributeId: fallbackAttributeId, attributeValueId: newAttributeValue.id };
	}

	async swapAttributeValues(productOrId: Product | Product['id'], changesData: AttributeValuesChangeData[]) {
		await Promise.all(
			changesData.map(async (item) => {
				await new ProductController().removeAttributeValue(
					typeof productOrId === 'string' ? productOrId : productOrId.id,
					item.oldAttributeValueId,
					item.type,
				);
				await new ProductController().addAttributeValue(
					typeof productOrId === 'string' ? productOrId : productOrId.id,
					item.newAttributeValueId,
					item.type,
				);
			}),
		);
	}

	async listByServiceTask(serviceTaskId: ServiceTask['id'], props: ListProps) {
		const serviceTask = await new ServiceTaskController().findById(serviceTaskId);
		return this.list(
			props,
			this.getQueryBuilder().innerJoin('entity.serviceTasks', 'serviceTask', 'serviceTask.id = :serviceTaskId', {
				serviceTaskId: serviceTask?.id,
			}),
		);
	}

	async findOrCreate(attributeId: Attribute['id'], value: Primitive) {
		const existingValue = await new AttributeController().findValue(attributeId, value);

		return existingValue ?? (await this.create({ attribute: { id: attributeId }, value }));
	}

	async normalizeValue(value: AttributeValue['value']) {
		const attribute = this.attributeId ? await new AttributeController().findById(this.attributeId) : null;

		let normalizedValue = value;

		if (attribute?.unit && typeof normalizedValue === 'string' && normalizedValue.endsWith(attribute.unit)) {
			normalizedValue = normalizedValue.slice(0, -attribute.unit.length);
		}
		if (typeof normalizedValue === 'string') {
			normalizedValue = normalizedValue.trim();
		}

		return normalizedValue;
	}
}

export class AttributeValueAlternativeController extends BaseController<AttributeValueAlternative> {
	private readonly attributeId?: Attribute['id'];
	private readonly valueId?: AttributeValue['id'];

	constructor(attributeId?: Attribute['id'], valueId?: AttributeValue['id']) {
		super(AttributeValueAlternative);
		this.attributeId = attributeId;
		this.valueId = valueId;
	}

	override getQueryBuilder() {
		const queryBuilder = super.getQueryBuilder();

		queryBuilder
			.innerJoinAndMapOne(
				'entity.parent',
				AttributeValue,
				'parent',
				'parent.id = entity.parentId' + (this.valueId ? ' AND parent.id = :valueId' : ''),
				{
					valueId: this.valueId,
				},
			)
			.innerJoinAndMapOne(
				'parent.attribute',
				Attribute,
				'attribute',
				'attribute.id = parent.attributeId AND attribute.id = :attributeId',
				{ attributeId: this.attributeId },
			);

		return queryBuilder;
	}

	override async create(data: Parameters<Repository<AttributeValueAlternative>['create']>[0]) {
		return super.create({ ...data, parent: { id: this.valueId } });
	}

	async findByValue(value: AttributeValueAlternative['value']): Promise<AttributeValueAlternative | null> {
		const [[attributeValues]] = await this.list(listOne(), this.getQueryBuilder().where(`:value LIKE entity.value ->> 0`, { value }));

		return attributeValues ?? null;
	}
}
