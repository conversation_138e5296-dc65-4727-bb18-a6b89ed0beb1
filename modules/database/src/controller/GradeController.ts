import { Grade } from '../entity';
import { type QueryBuilderMiddleware } from '../types';
import { BaseController } from '../utils/BaseController';
import { listAll } from '../utils/list';

export class GradeController extends BaseController<Grade> {
	constructor() {
		super(Grade);
	}

	protected override getQueryBuilder(...middlewares: QueryBuilderMiddleware<Grade>[]) {
		return super.getQueryBuilder(...middlewares).orderBy('entity.sequence', 'ASC');
	}

	async listByRank() {
		const [grades] = await this.list(
			listAll({
				sort: ['sequence'],
			}),
		);

		return grades;
	}

	async updateSequence(gradeId: Grade['id'], sequence: number) {
		const [records] = await this.list(listAll());

		const selectedRecords = records.filter((record) => record.id === gradeId);
		const originalSequence = selectedRecords[0]?.sequence ?? -1;
		const changedRecords: Grade[] = [];

		if (originalSequence === sequence || sequence === -1 || originalSequence === -1) return true; // No change

		// Moving up
		if (originalSequence > sequence) {
			records
				.filter((record) => record.sequence >= sequence && record.sequence < originalSequence)
				.forEach((record) => {
					record.sequence = record.sequence + 1;
					changedRecords.push(record);
				});
		}

		// Moving down
		if (originalSequence < sequence) {
			records
				.filter((record) => record.sequence <= sequence && record.sequence > originalSequence)
				.forEach((record) => {
					record.sequence = record.sequence - 1;
					changedRecords.push(record);
				});
		}

		selectedRecords.forEach((record) => {
			record.sequence = sequence;
			changedRecords.push(record);
		});

		await this.repository.save(changedRecords);
		return true;
	}
}
