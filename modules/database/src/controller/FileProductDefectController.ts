import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { type SingleOrArray } from '@pocitarna-nx-2023/utils';
import { type File, FileProductDefect, type ProductDefect } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listAll } from '../utils/list';

export class FileProductDefectController extends BaseController<FileProductDefect> {
	constructor() {
		super(FileProductDefect);
	}

	override getQueryBuilder() {
		return super.getQueryBuilder().leftJoinAndSelect('entity.file', 'file');
	}

	link(
		productDefectId: ProductDefect['id'],
		fileIds: SingleOrArray<File['id'] | { id: File['id'] }>,
		props: Omit<Parameters<this['create']>[0], 'productDefect' | 'file'>,
	) {
		return Promise.all(
			(Array.isArray(fileIds) ? fileIds : [fileIds]).map((fileId, index) =>
				this.create({
					...props,
					sequence: (props.sequence ?? 0) + index,
					productDefect: { id: productDefectId },
					file: typeof fileId === 'string' ? { id: fileId } : fileId,
				}),
			),
		);
	}

	async unlink(productDefectId: ProductDefect['id'], fileId: File['id'] | { id: File['id'] }) {
		await this.updateSequence(productDefectId, fileId, MAX_POSITIVE_INTEGER);
		const result = await this.repository.delete({
			productDefect: { id: productDefectId },
			file: typeof fileId === 'string' ? { id: fileId } : fileId,
		});

		return !!result.affected;
	}

	async updateSequence(productDefectId: ProductDefect['id'], fileId: File['id'] | { id: File['id'] }, sequence: number) {
		fileId = typeof fileId === 'string' ? fileId : fileId.id;
		const [records] = await this.list(listAll({ filter: { productDefectId: { eq: productDefectId } } }), this.getQueryBuilder());

		const selectedRecords = records.filter((record) => record.file.id === fileId);
		const originalSequence = selectedRecords[0]?.sequence ?? -1;
		const changedRecords: FileProductDefect[] = [];

		if (originalSequence === sequence || sequence === -1 || originalSequence === -1) return true; // No change

		// Moving file up
		if (originalSequence > sequence) {
			records
				.filter((record) => record.sequence >= sequence && record.sequence < originalSequence)
				.forEach((record) => {
					record.sequence = record.sequence + 1;
					changedRecords.push(record);
				});
		}

		// Moving file down
		if (originalSequence < sequence) {
			records
				.filter((record) => record.sequence <= sequence && record.sequence > originalSequence)
				.forEach((record) => {
					record.sequence = record.sequence - 1;
					changedRecords.push(record);
				});
		}

		selectedRecords.forEach((record) => {
			record.sequence = sequence;
			changedRecords.push(record);
		});

		await this.repository.save(changedRecords);
		return true;
	}
}
