import { type Repository } from 'typeorm';
import { ProductCategoryGrade } from '../entity';
import { BaseController } from '../utils/BaseController';
import { listAll, listOne } from '../utils/list';

export class ProductCategoryGradeController extends BaseController<ProductCategoryGrade> {
	constructor() {
		super(ProductCategoryGrade);
	}

	protected override getQueryBuilder() {
		return super.getQueryBuilder().leftJoinAndSelect('entity.grade', 'grade');
	}

	async handleGrade(data: Parameters<Repository<ProductCategoryGrade>['create']>[0]) {
		if (!data.productCategory?.id || !data.grade?.id) throw new Error('Product category and grade are required');
		const existing = await this.listByCategoryAndGrade(data.productCategory.id, data.grade.id);

		if (existing) {
			if (data.maxCosmeticDefects === 0) {
				await this.delete(existing.id);
				return;
			}

			return await this.update(existing, data);
		}

		return super.create(data);
	}

	async listByCategory(productCategoryId: ProductCategoryGrade['productCategoryId']) {
		return this.list(
			listAll({
				filter: { productCategoryId: { eq: productCategoryId } },
				sort: [['grade.sequence', 'asc']],
			}),
		);
	}

	async listByCategoryAndGrade(productCategoryId: ProductCategoryGrade['productCategoryId'], gradeId: ProductCategoryGrade['gradeId']) {
		const [[match]] = await this.list(listOne({ filter: { productCategoryId: { eq: productCategoryId }, gradeId: { eq: gradeId } } }));
		return match ?? null;
	}
}
