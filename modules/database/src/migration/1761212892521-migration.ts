import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1761212892521 implements MigrationInterface {
    name = 'Migration1761212892521'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productTest" DROP CONSTRAINT "FK_f75ee991be9073444ec68e3efe5"
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelopeAttributeValue" DROP CONSTRAINT "FK_b833891e239c3162c742dedc4e9"
        `);
        await queryRunner.query(`
            ALTER TABLE "productTest"
            ADD CONSTRAINT "FK_f75ee991be9073444ec68e3efe5" FOREIGN KEY ("conditionalAttributeId") REFERENCES "conditionalAttribute"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelopeAttributeValue"
            ADD CONSTRAINT "FK_b833891e239c3162c742dedc4e9" FOREIGN KEY ("productEnvelopeId") REFERENCES "productEnvelope"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productEnvelopeAttributeValue" DROP CONSTRAINT "FK_b833891e239c3162c742dedc4e9"
        `);
        await queryRunner.query(`
            ALTER TABLE "productTest" DROP CONSTRAINT "FK_f75ee991be9073444ec68e3efe5"
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelopeAttributeValue"
            ADD CONSTRAINT "FK_b833891e239c3162c742dedc4e9" FOREIGN KEY ("productEnvelopeId") REFERENCES "productEnvelope"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "productTest"
            ADD CONSTRAINT "FK_f75ee991be9073444ec68e3efe5" FOREIGN KEY ("conditionalAttributeId") REFERENCES "conditionalAttribute"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

}
