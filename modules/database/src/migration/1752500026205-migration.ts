import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1752500026205 implements MigrationInterface {
    name = 'Migration1752500026205'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "productCategoryId" uuid
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_216b74b3000f5a3c135e47ee2b" ON "inventoryItem" ("productCategoryId")
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD CONSTRAINT "FK_216b74b3000f5a3c135e47ee2b0" FOREIGN KEY ("productCategoryId") REFERENCES "productCategory"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP CONSTRAINT "FK_216b74b3000f5a3c135e47ee2b0"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_216b74b3000f5a3c135e47ee2b"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "productCategoryId"
        `);
    }

}
