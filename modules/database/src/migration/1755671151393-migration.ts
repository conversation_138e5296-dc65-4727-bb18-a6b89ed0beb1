import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1755671151393 implements MigrationInterface {
	name = 'Migration1755671151393';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "fileBatch"
            ADD CONSTRAINT "FK_9956ac5374cbbe872fdd203ae2c" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileBatch"
            ADD CONSTRAINT "FK_ed44b23cf3d41e412276ab41006" FOREIGN KEY ("batchId") REFERENCES "batch"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileProduct"
            ADD CONSTRAINT "FK_50b298612e5f1b60c3fd9295df8" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "fileProduct" DROP CONSTRAINT "FK_50b298612e5f1b60c3fd9295df8"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileBatch" DROP CONSTRAINT "FK_ed44b23cf3d41e412276ab41006"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileBatch" DROP CONSTRAINT "FK_9956ac5374cbbe872fdd203ae2c"
        `);
	}
}
