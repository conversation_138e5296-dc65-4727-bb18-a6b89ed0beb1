import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1759993913640 implements MigrationInterface {
    name = 'Migration1759993913640'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "fileCustomerClaim" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "fileId" uuid NOT NULL,
                "customerClaimId" uuid NOT NULL,
                "sequence" integer NOT NULL DEFAULT '0',
                CONSTRAINT "PK_4337e740e569b3d31c635813a0a" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_2a8c757bd7c361ab5e89c7e330" ON "fileCustomerClaim" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_7a683dbae1a1f407dcb4aea91e" ON "fileCustomerClaim" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_7db9cfa56c5f149abf56de122a" ON "fileCustomerClaim" ("fileId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_dcce39ebe7d2c4ba7a9f8b7d7f" ON "fileCustomerClaim" ("customerClaimId")
        `);
        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_5caa3c94710332765da3d5eb9a" ON "fileCustomerClaim" ("fileId", "customerClaimId")
        `);
        await queryRunner.query(`
            CREATE TABLE "customerClaimMessage" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "message" text NOT NULL DEFAULT '',
                "contactId" uuid,
                "userId" uuid,
                "customerClaimId" uuid NOT NULL,
                CONSTRAINT "PK_b42f4c3025e22767283d050a94e" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_c0d597fd9ed77ac791b42d8cac" ON "customerClaimMessage" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_009a47674711c16d96eb4c199a" ON "customerClaimMessage" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_3d1ea12105bebc597cfda9e23a" ON "customerClaimMessage" ("contactId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_aed01340f0b5bb2e189448d566" ON "customerClaimMessage" ("userId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_e95c9775b8a0c4ee1bd5ece60c" ON "customerClaimMessage" ("customerClaimId")
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."CUSTOMER_CLAIM_HANDLING_METHODS" AS ENUM('EXCHANGE', 'REPAIR', 'REFUND')
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD "handlingMethod" "public"."CUSTOMER_CLAIM_HANDLING_METHODS" NOT NULL
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."CUSTOMER_DELIVERY_METHODS" AS ENUM('IN_PERSON', 'SHIPMENT')
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD "customerDeliveryMethod" "public"."CUSTOMER_DELIVERY_METHODS" NOT NULL
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."CMP_DELIVERY_METHODS" AS ENUM('IN_PERSON', 'SHIPMENT')
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD "cmpDeliveryMethod" "public"."CMP_DELIVERY_METHODS"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD "contactId" uuid
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD "addressId" uuid
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD "ecommerceOrderItemId" uuid NOT NULL
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_7aca3d9ff7ee772fd32a30e480" ON "customerClaim" ("handlingMethod")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_e078d2bcf3311d226c5eada82e" ON "customerClaim" ("customerDeliveryMethod")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_68e6f0934845f8adc728f2c399" ON "customerClaim" ("cmpDeliveryMethod")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_418b93fba9d77d73f39639a7ed" ON "customerClaim" ("contactId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_44191882c0631faa9895a62eb2" ON "customerClaim" ("addressId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_3433e3e710d52bad299bc94bed" ON "customerClaim" ("ecommerceOrderItemId")
        `);
        await queryRunner.query(`
            ALTER TABLE "fileCustomerClaim"
            ADD CONSTRAINT "FK_2a8c757bd7c361ab5e89c7e330b" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "fileCustomerClaim"
            ADD CONSTRAINT "FK_7db9cfa56c5f149abf56de122ab" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "fileCustomerClaim"
            ADD CONSTRAINT "FK_dcce39ebe7d2c4ba7a9f8b7d7f7" FOREIGN KEY ("customerClaimId") REFERENCES "customerClaim"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimMessage"
            ADD CONSTRAINT "FK_c0d597fd9ed77ac791b42d8cac8" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimMessage"
            ADD CONSTRAINT "FK_3d1ea12105bebc597cfda9e23a4" FOREIGN KEY ("contactId") REFERENCES "contact"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimMessage"
            ADD CONSTRAINT "FK_aed01340f0b5bb2e189448d5663" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimMessage"
            ADD CONSTRAINT "FK_e95c9775b8a0c4ee1bd5ece60cd" FOREIGN KEY ("customerClaimId") REFERENCES "customerClaim"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD CONSTRAINT "FK_418b93fba9d77d73f39639a7edf" FOREIGN KEY ("contactId") REFERENCES "contact"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD CONSTRAINT "FK_44191882c0631faa9895a62eb24" FOREIGN KEY ("addressId") REFERENCES "address"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD CONSTRAINT "FK_3433e3e710d52bad299bc94bed7" FOREIGN KEY ("ecommerceOrderItemId") REFERENCES "ecommerceOrderItem"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP CONSTRAINT "FK_3433e3e710d52bad299bc94bed7"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP CONSTRAINT "FK_44191882c0631faa9895a62eb24"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP CONSTRAINT "FK_418b93fba9d77d73f39639a7edf"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimMessage" DROP CONSTRAINT "FK_e95c9775b8a0c4ee1bd5ece60cd"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimMessage" DROP CONSTRAINT "FK_aed01340f0b5bb2e189448d5663"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimMessage" DROP CONSTRAINT "FK_3d1ea12105bebc597cfda9e23a4"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimMessage" DROP CONSTRAINT "FK_c0d597fd9ed77ac791b42d8cac8"
        `);
        await queryRunner.query(`
            ALTER TABLE "fileCustomerClaim" DROP CONSTRAINT "FK_dcce39ebe7d2c4ba7a9f8b7d7f7"
        `);
        await queryRunner.query(`
            ALTER TABLE "fileCustomerClaim" DROP CONSTRAINT "FK_7db9cfa56c5f149abf56de122ab"
        `);
        await queryRunner.query(`
            ALTER TABLE "fileCustomerClaim" DROP CONSTRAINT "FK_2a8c757bd7c361ab5e89c7e330b"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_3433e3e710d52bad299bc94bed"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_44191882c0631faa9895a62eb2"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_418b93fba9d77d73f39639a7ed"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_68e6f0934845f8adc728f2c399"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_e078d2bcf3311d226c5eada82e"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_7aca3d9ff7ee772fd32a30e480"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP COLUMN "ecommerceOrderItemId"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP COLUMN "addressId"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP COLUMN "contactId"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP COLUMN "cmpDeliveryMethod"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."CMP_DELIVERY_METHODS"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP COLUMN "customerDeliveryMethod"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."CUSTOMER_DELIVERY_METHODS"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP COLUMN "handlingMethod"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."CUSTOMER_CLAIM_HANDLING_METHODS"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_e95c9775b8a0c4ee1bd5ece60c"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_aed01340f0b5bb2e189448d566"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_3d1ea12105bebc597cfda9e23a"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_009a47674711c16d96eb4c199a"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_c0d597fd9ed77ac791b42d8cac"
        `);
        await queryRunner.query(`
            DROP TABLE "customerClaimMessage"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_5caa3c94710332765da3d5eb9a"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_dcce39ebe7d2c4ba7a9f8b7d7f"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_7db9cfa56c5f149abf56de122a"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_7a683dbae1a1f407dcb4aea91e"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_2a8c757bd7c361ab5e89c7e330"
        `);
        await queryRunner.query(`
            DROP TABLE "fileCustomerClaim"
        `);
    }

}
