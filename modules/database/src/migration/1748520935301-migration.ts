import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1748520935301 implements MigrationInterface {
    name = 'Migration1748520935301'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD "salePrice" numeric(14, 4) NOT NULL DEFAULT '0'
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD "standardPrice" numeric(14, 4) NOT NULL DEFAULT '0'
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD "pricedAt" TIMESTAMP WITH TIME ZONE
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD "pricedById" uuid
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_4c57249793992f8d6d6066ec69" ON "productEnvelope" ("pricedAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_04a43ee465347a3577f80b6793" ON "productEnvelope" ("pricedById")
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD CONSTRAINT "FK_04a43ee465347a3577f80b67934" FOREIGN KEY ("pricedById") REFERENCES "user"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP CONSTRAINT "FK_04a43ee465347a3577f80b67934"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_04a43ee465347a3577f80b6793"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_4c57249793992f8d6d6066ec69"
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP COLUMN "pricedById"
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP COLUMN "pricedAt"
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP COLUMN "standardPrice"
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP COLUMN "salePrice"
        `);
    }

}
