import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1748352542655 implements MigrationInterface {
    name = 'Migration1748352542655'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TYPE "public"."SHOPTET_COUNTRIES" AS ENUM('CZ', 'SK')
        `);
        await queryRunner.query(`
            ALTER TABLE "ecommerceOrder"
            ADD "country" "public"."SHOPTET_COUNTRIES" NOT NULL DEFAULT 'CZ'
        `);
        await queryRunner.query(`
            ALTER TABLE "productTest" DROP CONSTRAINT "FK_ee83ae7ce22733b0c97788c9ada"
        `);
        await queryRunner.query(`
            ALTER TABLE "productTest"
            ALTER COLUMN "productId"
            SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "productTest"
            ADD CONSTRAINT "FK_ee83ae7ce22733b0c97788c9ada" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productTest" DROP CONSTRAINT "FK_ee83ae7ce22733b0c97788c9ada"
        `);
        await queryRunner.query(`
            ALTER TABLE "productTest"
            ALTER COLUMN "productId" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "productTest"
            ADD CONSTRAINT "FK_ee83ae7ce22733b0c97788c9ada" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "ecommerceOrder" DROP COLUMN "country"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."SHOPTET_COUNTRIES"
        `);
    }

}
