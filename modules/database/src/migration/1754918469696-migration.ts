import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1754918469696 implements MigrationInterface {
    name = 'Migration1754918469696'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productEnvelopeAttributeValue" DROP CONSTRAINT "FK_f49549471d70fc9380c35d92c5c"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_207b389edc2145dfe130c5179d"
        `);
        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_207b389edc2145dfe130c5179d" ON "attributeValue" ("value", "attributeId")
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelopeAttributeValue"
            ADD CONSTRAINT "FK_f49549471d70fc9380c35d92c5c" FOREIGN KEY ("attributeValueId") REFERENCES "attributeValue"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productEnvelopeAttributeValue" DROP CONSTRAINT "FK_f49549471d70fc9380c35d92c5c"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_207b389edc2145dfe130c5179d"
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_207b389edc2145dfe130c5179d" ON "attributeValue" ("attributeId", "value")
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelopeAttributeValue"
            ADD CONSTRAINT "FK_f49549471d70fc9380c35d92c5c" FOREIGN KEY ("attributeValueId") REFERENCES "attributeValue"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

}
