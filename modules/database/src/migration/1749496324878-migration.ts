import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1749496324878 implements MigrationInterface {
	name = 'Migration1749496324878';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder"
            ADD "currencyRateId" uuid NOT NULL DEFAULT 'e151af64-b649-40d2-a8ce-4486b5a87459'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_2050e199327255177b62412f49" ON "ecommerceOrder" ("currencyRateId")
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder"
            ADD CONSTRAINT "FK_2050e199327255177b62412f499" FOREIGN KEY ("currencyRateId") REFERENCES "currencyRate"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder" DROP CONSTRAINT "FK_2050e199327255177b62412f499"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_2050e199327255177b62412f49"
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder" DROP COLUMN "currencyRateId"
        `);
	}
}
