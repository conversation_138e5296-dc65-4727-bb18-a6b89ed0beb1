import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1753887307954 implements MigrationInterface {
	name = 'Migration1753887307954';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder"
            ADD "priority" smallint NOT NULL DEFAULT '0'
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder" DROP COLUMN "priority"
        `);
	}
}
