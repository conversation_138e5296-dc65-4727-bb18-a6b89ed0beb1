import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1753359077626 implements MigrationInterface {
    name = 'Migration1753359077626'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productTest"
            ADD "hasTestMismatches" boolean NOT NULL DEFAULT false
        `);
        await queryRunner.query(`
            ALTER TABLE "productTest"
            ADD "hasStockMismatches" boolean NOT NULL DEFAULT false
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productTest" DROP COLUMN "hasStockMismatches"
        `);
        await queryRunner.query(`
            ALTER TABLE "productTest" DROP COLUMN "hasTestMismatches"
        `);
    }

}
