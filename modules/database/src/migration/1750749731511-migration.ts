import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1750749731511 implements MigrationInterface {
	name = 'Migration1750749731511';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TYPE "public"."PRODUCT_STATUS_NAMES"
            RENAME TO "PRODUCT_STATUS_NAMES_old"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_STATUS_NAMES" AS ENUM(
                'AT_SUPPLIER',
                'ON_THE_WAY',
                'EXTERNAL_STOCK',
                'TO_CHECK',
                'TO_CHECK_SN',
                'TO_TEST',
                'TESTED',
                'MISSING',
                'SERVICE',
                'WARRANTY_CLAIM',
                'STOCK',
                'FOR_SALE',
                'RESERVED',
                'SOLD',
                'CUSTOMER_CLAIM',
                'DEAD',
                'AUTOPSY',
                'RETURNED',
                'WAITING_FOR_SHIPMENT',
                'SHIPMENT_DISPATCHED',
                'SHIPMENT_DELIVERED'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status" DROP DEFAULT
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status" TYPE "public"."PRODUCT_STATUS_NAMES" USING "status"::"text"::"public"."PRODUCT_STATUS_NAMES"
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status"
            SET DEFAULT 'AT_SUPPLIER'
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_STATUS_NAMES_old"
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_STATUS_NAMES_old" AS ENUM(
                'AT_SUPPLIER',
                'ON_THE_WAY',
                'EXTERNAL_STOCK',
                'TO_CHECK',
                'TO_CHECK_SN',
                'TO_TEST',
                'TESTED',
                'MISSING',
                'SERVICE',
                'WARRANTY_CLAIM',
                'STOCK',
                'FOR_SALE',
                'RESERVED',
                'SOLD',
                'CUSTOMER_CLAIM',
                'DEAD',
                'AUTOPSY',
                'WAITING_FOR_SHIPMENT',
                'SHIPMENT_DISPATCHED',
                'SHIPMENT_DELIVERED'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status" DROP DEFAULT
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status" TYPE "public"."PRODUCT_STATUS_NAMES_old" USING "status"::"text"::"public"."PRODUCT_STATUS_NAMES_old"
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status"
            SET DEFAULT 'AT_SUPPLIER'
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_STATUS_NAMES"
        `);
		await queryRunner.query(`
            ALTER TYPE "public"."PRODUCT_STATUS_NAMES_old"
            RENAME TO "PRODUCT_STATUS_NAMES"
        `);
	}
}
