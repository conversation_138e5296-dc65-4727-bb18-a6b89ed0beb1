import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1753714539988 implements MigrationInterface {
	name = 'Migration1753714539988';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
			ALTER TABLE "product"
				ADD "pickedAt" TIMESTAMP WITH TIME ZONE
		`);
		await queryRunner.query(`
			ALTER TABLE "product"
				ADD "pickedById" uuid
		`);
		await queryRunner.query(`
			CREATE INDEX "IDX_3042ac53800306e5b6553ac86c" ON "product" ("pickedById")
		`);
		await queryRunner.query(`
			ALTER TABLE "product"
				ADD CONSTRAINT "FK_3042ac53800306e5b6553ac86ca" FOREIGN KEY ("pickedById") REFERENCES "user" ("id") ON DELETE SET NULL ON UPDATE NO ACTION
		`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
			ALTER TABLE "product" DROP CONSTRAINT "FK_3042ac53800306e5b6553ac86ca"
		`);
		await queryRunner.query(`
			DROP INDEX "public"."IDX_3042ac53800306e5b6553ac86c"
		`);
		await queryRunner.query(`
			ALTER TABLE "product" DROP COLUMN "pickedById"
		`);
		await queryRunner.query(`
			ALTER TABLE "product" DROP COLUMN "pickedAt"
		`);
	}
}
