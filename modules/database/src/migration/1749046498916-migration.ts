import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1749046498916 implements MigrationInterface {
    name = 'Migration1749046498916'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TYPE "public"."SERVICE_TASK_PRODUCT_TARGET_STATUSES" AS ENUM('DEAD', 'TO_TEST', 'STOCK', 'SOLD')
        `);
        await queryRunner.query(`
            ALTER TABLE "serviceTaskType"
            ADD "productTargetStatus" "public"."SERVICE_TASK_PRODUCT_TARGET_STATUSES" NOT NULL DEFAULT 'TO_TEST'
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_0dcff7a0355f76a1db87e1c80a" ON "serviceTaskType" ("productTargetStatus")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX "public"."IDX_0dcff7a0355f76a1db87e1c80a"
        `);
        await queryRunner.query(`
            ALTER TABLE "serviceTaskType" DROP COLUMN "productTargetStatus"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."SERVICE_TASK_PRODUCT_TARGET_STATUSES"
        `);
    }

}
