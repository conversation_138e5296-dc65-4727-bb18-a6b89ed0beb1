import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1761112701058 implements MigrationInterface {
    name = 'Migration1761112701058'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "customerClaimCosmeticDefect" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "cosmeticDefectId" uuid NOT NULL,
                "customerClaimId" uuid NOT NULL,
                "cosmeticAreaId" uuid NOT NULL,
                CONSTRAINT "PK_ed16118408f3d647a76f25cdfe2" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_7d7b42745d6bc62c23048fbde7" ON "customerClaimCosmeticDefect" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_32b28d9d232a69d6d8f525e1f5" ON "customerClaimCosmeticDefect" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_6077e13676525c2c0b56cb2088" ON "customerClaimCosmeticDefect" ("cosmeticDefectId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_0908d8434aeb3b57f8b0aa7320" ON "customerClaimCosmeticDefect" ("customerClaimId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_2fc571a11b3a42803f19a2935c" ON "customerClaimCosmeticDefect" ("cosmeticAreaId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_b70db038ce078791f2536bc3ee" ON "customerClaimCosmeticDefect" ("cosmeticDefectId", "customerClaimId")
        `);
        await queryRunner.query(`
            CREATE TABLE "fileCustomerClaimCosmeticDefect" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "fileId" uuid NOT NULL,
                "customerClaimCosmeticDefectId" uuid NOT NULL,
                "sequence" integer NOT NULL DEFAULT '0',
                CONSTRAINT "PK_53bee003b200d24c8de5510e013" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_4ea59c880b09e186a17ab931fd" ON "fileCustomerClaimCosmeticDefect" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_b25535ab1a47e3adba225babd8" ON "fileCustomerClaimCosmeticDefect" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_a295800a420f9257fc736cfd92" ON "fileCustomerClaimCosmeticDefect" ("fileId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_632407b965e9e9094d3b3a0958" ON "fileCustomerClaimCosmeticDefect" ("customerClaimCosmeticDefectId")
        `);
        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_930ae19a0ab8e97813ce7df2af" ON "fileCustomerClaimCosmeticDefect" ("fileId", "customerClaimCosmeticDefectId")
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD "receivedAt" TIMESTAMP WITH TIME ZONE
        `);
        await queryRunner.query(`
            ALTER TYPE "public"."CUSTOMER_CLAIM_STATUS_NAMES"
            RENAME TO "CUSTOMER_CLAIM_STATUS_NAMES_old"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."CUSTOMER_CLAIM_STATUS_NAMES" AS ENUM(
                'NEW',
                'RECEIVED',
                'PROCESSING',
                'WAITING_FOR_SPARE_PART',
                'SENT_TO_EXTERNAL_SERVICE',
                'SENT_BACK',
                'APPROVED',
                'REJECTED',
                'RESOLVED',
                'CANCELLED'
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ALTER COLUMN "status" DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ALTER COLUMN "status" TYPE "public"."CUSTOMER_CLAIM_STATUS_NAMES" USING "status"::"text"::"public"."CUSTOMER_CLAIM_STATUS_NAMES"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ALTER COLUMN "status"
            SET DEFAULT 'NEW'
        `);
        await queryRunner.query(`
            DROP TYPE "public"."CUSTOMER_CLAIM_STATUS_NAMES_old"
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_17468a9c61bfa7e91c3a16ba68" ON "customerClaim" ("receivedAt")
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimCosmeticDefect"
            ADD CONSTRAINT "FK_7d7b42745d6bc62c23048fbde7a" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimCosmeticDefect"
            ADD CONSTRAINT "FK_6077e13676525c2c0b56cb20889" FOREIGN KEY ("cosmeticDefectId") REFERENCES "cosmeticDefect"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimCosmeticDefect"
            ADD CONSTRAINT "FK_0908d8434aeb3b57f8b0aa73200" FOREIGN KEY ("customerClaimId") REFERENCES "customerClaim"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimCosmeticDefect"
            ADD CONSTRAINT "FK_2fc571a11b3a42803f19a2935ce" FOREIGN KEY ("cosmeticAreaId") REFERENCES "cosmeticArea"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "fileCustomerClaimCosmeticDefect"
            ADD CONSTRAINT "FK_4ea59c880b09e186a17ab931fd3" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "fileCustomerClaimCosmeticDefect"
            ADD CONSTRAINT "FK_a295800a420f9257fc736cfd927" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "fileCustomerClaimCosmeticDefect"
            ADD CONSTRAINT "FK_632407b965e9e9094d3b3a09588" FOREIGN KEY ("customerClaimCosmeticDefectId") REFERENCES "customerClaimCosmeticDefect"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "fileCustomerClaimCosmeticDefect" DROP CONSTRAINT "FK_632407b965e9e9094d3b3a09588"
        `);
        await queryRunner.query(`
            ALTER TABLE "fileCustomerClaimCosmeticDefect" DROP CONSTRAINT "FK_a295800a420f9257fc736cfd927"
        `);
        await queryRunner.query(`
            ALTER TABLE "fileCustomerClaimCosmeticDefect" DROP CONSTRAINT "FK_4ea59c880b09e186a17ab931fd3"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimCosmeticDefect" DROP CONSTRAINT "FK_2fc571a11b3a42803f19a2935ce"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimCosmeticDefect" DROP CONSTRAINT "FK_0908d8434aeb3b57f8b0aa73200"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimCosmeticDefect" DROP CONSTRAINT "FK_6077e13676525c2c0b56cb20889"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimCosmeticDefect" DROP CONSTRAINT "FK_7d7b42745d6bc62c23048fbde7a"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_17468a9c61bfa7e91c3a16ba68"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."CUSTOMER_CLAIM_STATUS_NAMES_old" AS ENUM('NEW', 'CLOSED')
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ALTER COLUMN "status" DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ALTER COLUMN "status" TYPE "public"."CUSTOMER_CLAIM_STATUS_NAMES_old" USING "status"::"text"::"public"."CUSTOMER_CLAIM_STATUS_NAMES_old"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ALTER COLUMN "status"
            SET DEFAULT 'NEW'
        `);
        await queryRunner.query(`
            DROP TYPE "public"."CUSTOMER_CLAIM_STATUS_NAMES"
        `);
        await queryRunner.query(`
            ALTER TYPE "public"."CUSTOMER_CLAIM_STATUS_NAMES_old"
            RENAME TO "CUSTOMER_CLAIM_STATUS_NAMES"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP COLUMN "receivedAt"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_930ae19a0ab8e97813ce7df2af"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_632407b965e9e9094d3b3a0958"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_a295800a420f9257fc736cfd92"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_b25535ab1a47e3adba225babd8"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_4ea59c880b09e186a17ab931fd"
        `);
        await queryRunner.query(`
            DROP TABLE "fileCustomerClaimCosmeticDefect"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_b70db038ce078791f2536bc3ee"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_2fc571a11b3a42803f19a2935c"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_0908d8434aeb3b57f8b0aa7320"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_6077e13676525c2c0b56cb2088"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_32b28d9d232a69d6d8f525e1f5"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_7d7b42745d6bc62c23048fbde7"
        `);
        await queryRunner.query(`
            DROP TABLE "customerClaimCosmeticDefect"
        `);
    }

}
