import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1753360329424 implements MigrationInterface {
    name = 'Migration1753360329424'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "inventory"
            ADD "name" character varying NOT NULL DEFAULT ''
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "inventory" DROP COLUMN "name"
        `);
    }

}
