import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1751266364812 implements MigrationInterface {
    name = 'Migration1751266364812'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productTest" DROP CONSTRAINT "FK_ee83ae7ce22733b0c97788c9ada"
        `);
        await queryRunner.query(`
            ALTER TABLE "productTest"
            ADD CONSTRAINT "FK_ee83ae7ce22733b0c97788c9ada" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productTest" DROP CONSTRAINT "FK_ee83ae7ce22733b0c97788c9ada"
        `);
        await queryRunner.query(`
            ALTER TABLE "productTest"
            ADD CONSTRAINT "FK_ee83ae7ce22733b0c97788c9ada" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

}
