import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1749136616553 implements MigrationInterface {
	name = 'Migration1749136616553';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            CREATE TABLE "shoptetBrand" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "shoptetId" uuid NOT NULL,
                "name" character varying NOT NULL,
                CONSTRAINT "PK_c68bd1f31cee981bc2287629271" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_7c918e9eca05578d588cfefb30" ON "shoptetBrand" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d9b25c0d12858a56c52ed0b4b8" ON "shoptetBrand" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_60c05a1d3f8907888ef5c2e6b4" ON "shoptetBrand" ("shoptetId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e2cbcdb06a005234adaa150fb0" ON "shoptetBrand" ("name")
        `);
		await queryRunner.query(`
            ALTER TABLE "shoptetBrand"
            ADD CONSTRAINT "FK_7c918e9eca05578d588cfefb303" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "shoptetBrand" DROP CONSTRAINT "FK_7c918e9eca05578d588cfefb303"
        `);
		await queryRunner.query(`
            DROP TABLE "shoptetBrand"
        `);
	}
}
