import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1748933878992 implements MigrationInterface {
    name = 'Migration1748933878992'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "batch"
            ADD "boughtById" uuid
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_2826e7c2f33de2e2ad3bc99b6d" ON "batch" ("boughtById")
        `);
        await queryRunner.query(`
            ALTER TABLE "batch"
            ADD CONSTRAINT "FK_2826e7c2f33de2e2ad3bc99b6dd" FOREIGN KEY ("boughtById") REFERENCES "user"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "batch" DROP CONSTRAINT "FK_2826e7c2f33de2e2ad3bc99b6dd"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_2826e7c2f33de2e2ad3bc99b6d"
        `);
        await queryRunner.query(`
            ALTER TABLE "batch" DROP COLUMN "boughtById"
        `);
    }

}
