import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1749049323843 implements MigrationInterface {
	name = 'Migration1749049323843';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TYPE "public"."PRODUCT_STATUS_NAMES"
            RENAME TO "PRODUCT_STATUS_NAMES_old"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_STATUS_NAMES" AS ENUM(
                'AT_SUPPLIER',
                'ON_THE_WAY',
                'EXTERNAL_STOCK',
                'TO_CHECK',
                'TO_CHECK_SN',
                'TO_TEST',
                'TESTED',
                'MISSING',
                'SERVICE',
                'WARRANTY_CLAIM',
                'STOCK',
                'FOR_SALE',
                'RESERVED',
                'SOLD',
                'DEAD',
                'AUTOPSY',
                'WAITING_FOR_SHIPMENT',
                'SHIPMENT_DISPATCHED',
                'SHIPMENT_DELIVERED'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status" DROP DEFAULT
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status" TYPE "public"."PRODUCT_STATUS_NAMES" USING "status"::"text"::"public"."PRODUCT_STATUS_NAMES"
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status"
            SET DEFAULT 'AT_SUPPLIER'
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_STATUS_NAMES_old"
        `);
		await queryRunner.query(`
            ALTER TYPE "public"."BATCH_STATUS_NAMES"
            RENAME TO "BATCH_STATUS_NAMES_old"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."BATCH_STATUS_NAMES" AS ENUM(
                'IMPORTING',
                'AT_SUPPLIER',
                'ON_THE_WAY',
                'EXTERNAL_STOCK',
                'TO_CHECK',
                'TO_CHECK_SN',
                'TO_TEST',
                'MARKETING',
                'CLOSED'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ALTER COLUMN "status" DROP DEFAULT
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ALTER COLUMN "status" TYPE "public"."BATCH_STATUS_NAMES" USING "status"::"text"::"public"."BATCH_STATUS_NAMES"
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ALTER COLUMN "status"
            SET DEFAULT 'AT_SUPPLIER'
        `);
		await queryRunner.query(`
            DROP TYPE "public"."BATCH_STATUS_NAMES_old"
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            CREATE TYPE "public"."BATCH_STATUS_NAMES_old" AS ENUM(
                'AT_SUPPLIER',
                'CLOSED',
                'IMPORTING',
                'MARKETING',
                'ON_THE_WAY',
                'TO_CHECK',
                'TO_CHECK_SN',
                'TO_TEST'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ALTER COLUMN "status" DROP DEFAULT
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ALTER COLUMN "status" TYPE "public"."BATCH_STATUS_NAMES_old" USING "status"::"text"::"public"."BATCH_STATUS_NAMES_old"
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ALTER COLUMN "status"
            SET DEFAULT 'AT_SUPPLIER'
        `);
		await queryRunner.query(`
            DROP TYPE "public"."BATCH_STATUS_NAMES"
        `);
		await queryRunner.query(`
            ALTER TYPE "public"."BATCH_STATUS_NAMES_old"
            RENAME TO "BATCH_STATUS_NAMES"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_STATUS_NAMES_old" AS ENUM(
                'AT_SUPPLIER',
                'AUTOPSY',
                'DEAD',
                'FOR_SALE',
                'MISSING',
                'ON_THE_WAY',
                'RESERVED',
                'SERVICE',
                'SHIPMENT_DELIVERED',
                'SHIPMENT_DISPATCHED',
                'SOLD',
                'STOCK',
                'TESTED',
                'TO_CHECK',
                'TO_CHECK_SN',
                'TO_TEST',
                'WAITING_FOR_SHIPMENT',
                'WARRANTY_CLAIM'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status" DROP DEFAULT
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status" TYPE "public"."PRODUCT_STATUS_NAMES_old" USING "status"::"text"::"public"."PRODUCT_STATUS_NAMES_old"
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status"
            SET DEFAULT 'AT_SUPPLIER'
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_STATUS_NAMES"
        `);
		await queryRunner.query(`
            ALTER TYPE "public"."PRODUCT_STATUS_NAMES_old"
            RENAME TO "PRODUCT_STATUS_NAMES"
        `);
	}
}
