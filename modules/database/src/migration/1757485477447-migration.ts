import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1757485477447 implements MigrationInterface {
    name = 'Migration1757485477447'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "grade"
            ADD "sequence" integer NOT NULL DEFAULT '0'
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "grade" DROP COLUMN "sequence"
        `);
    }

}
