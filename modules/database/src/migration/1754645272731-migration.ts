import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1754645272731 implements MigrationInterface {
    name = 'Migration1754645272731'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "preScanWarehousePositionId" uuid
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "preScanWarehousePositionName" character varying
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "postScanWarehousePositionId" uuid
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "postScanWarehousePositionName" character varying
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_c27155cf3b11a192d2e1f0bf22" ON "inventoryItem" ("preScanWarehousePositionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_b2f6e43527fc4782d721f1ba39" ON "inventoryItem" ("postScanWarehousePositionId")
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD CONSTRAINT "FK_c27155cf3b11a192d2e1f0bf22e" FOREIGN KEY ("preScanWarehousePositionId") REFERENCES "warehousePosition"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD CONSTRAINT "FK_b2f6e43527fc4782d721f1ba394" FOREIGN KEY ("postScanWarehousePositionId") REFERENCES "warehousePosition"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP CONSTRAINT "FK_b2f6e43527fc4782d721f1ba394"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP CONSTRAINT "FK_c27155cf3b11a192d2e1f0bf22e"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_b2f6e43527fc4782d721f1ba39"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_c27155cf3b11a192d2e1f0bf22"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "postScanWarehousePositionName"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "postScanWarehousePositionId"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "preScanWarehousePositionName"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "preScanWarehousePositionId"
        `);
    }

}
