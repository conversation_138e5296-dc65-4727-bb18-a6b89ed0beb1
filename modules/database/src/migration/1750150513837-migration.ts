import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1750150513837 implements MigrationInterface {
    name = 'Migration1750150513837'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productPrice"
            ADD "serviceTaskId" uuid
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_9dec6887bb4b7f24249fd6cab1" ON "productPrice" ("serviceTaskId")
        `);
        await queryRunner.query(`
            ALTER TABLE "productPrice"
            ADD CONSTRAINT "FK_9dec6887bb4b7f24249fd6cab1f" FOREIGN KEY ("serviceTaskId") REFERENCES "serviceTask"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productPrice" DROP CONSTRAINT "FK_9dec6887bb4b7f24249fd6cab1f"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_9dec6887bb4b7f24249fd6cab1"
        `);
        await queryRunner.query(`
            ALTER TABLE "productPrice" DROP COLUMN "serviceTaskId"
        `);
    }

}
