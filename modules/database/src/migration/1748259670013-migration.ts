import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1748259670013 implements MigrationInterface {
    name = 'Migration1748259670013'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "warrantyClaim"
            ADD "sourceServiceCaseId" uuid
        `);
        await queryRunner.query(`
            ALTER TABLE "serviceCase"
            ADD "sourceWarrantyClaimId" uuid
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_64a9b26f9b1bb98ab210d73f42" ON "warrantyClaim" ("sourceServiceCaseId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_730e5ca39d7e50a5cfdd3085f0" ON "serviceCase" ("sourceWarrantyClaimId")
        `);
        await queryRunner.query(`
            ALTER TABLE "warrantyClaim"
            ADD CONSTRAINT "FK_64a9b26f9b1bb98ab210d73f421" FOREIGN KEY ("sourceServiceCaseId") REFERENCES "serviceCase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "serviceCase"
            ADD CONSTRAINT "FK_730e5ca39d7e50a5cfdd3085f0c" FOREIGN KEY ("sourceWarrantyClaimId") REFERENCES "warrantyClaim"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "serviceCase" DROP CONSTRAINT "FK_730e5ca39d7e50a5cfdd3085f0c"
        `);
        await queryRunner.query(`
            ALTER TABLE "warrantyClaim" DROP CONSTRAINT "FK_64a9b26f9b1bb98ab210d73f421"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_730e5ca39d7e50a5cfdd3085f0"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_64a9b26f9b1bb98ab210d73f42"
        `);
        await queryRunner.query(`
            ALTER TABLE "serviceCase" DROP COLUMN "sourceWarrantyClaimId"
        `);
        await queryRunner.query(`
            ALTER TABLE "warrantyClaim" DROP COLUMN "sourceServiceCaseId"
        `);
    }

}
