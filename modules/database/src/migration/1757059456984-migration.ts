import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1757059456984 implements MigrationInterface {
    name = 'Migration1757059456984'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TYPE "public"."SERVICE_CASE_TYPE_NAMES" AS ENUM('FRONTEND', 'BACKOFFICE')
        `);
        await queryRunner.query(`
            ALTER TABLE "serviceCase"
            ADD "type" "public"."SERVICE_CASE_TYPE_NAMES" NOT NULL DEFAULT 'BACKOFFICE'
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_43693c6abf2d084050e6305833" ON "serviceCase" ("type")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX "public"."IDX_43693c6abf2d084050e6305833"
        `);
        await queryRunner.query(`
            ALTER TABLE "serviceCase" DROP COLUMN "type"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."SERVICE_CASE_TYPE_NAMES"
        `);
    }

}
