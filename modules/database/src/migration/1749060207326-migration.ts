import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1749060207326 implements MigrationInterface {
    name = 'Migration1749060207326'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productTest"
            ADD "ignoreMismatch" boolean NOT NULL DEFAULT false
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productTest" DROP COLUMN "ignoreMismatch"
        `);
    }

}
