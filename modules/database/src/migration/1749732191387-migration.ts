import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1749732191387 implements MigrationInterface {
    name = 'Migration1749732191387'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "notification" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "notificationCode" uuid NOT NULL,
                "title" character varying NOT NULL,
                "body" text NOT NULL DEFAULT '',
                "priority" smallint NOT NULL DEFAULT '50',
                "href" character varying NOT NULL DEFAULT '/',
                "viewUntil" TIMESTAMP WITH TIME ZONE,
                "viewedAt" TIMESTAMP WITH TIME ZONE,
                "refiredAt" TIMESTAMP WITH TIME ZONE,
                "userId" uuid NOT NULL,
                CONSTRAINT "PK_705b6c7cdf9b2c2ff7ac7872cb7" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_c64341553ec1b00bebcb3699ce" ON "notification" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_d95b732fb7f7d3b03819f09a74" ON "notification" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_1ced25315eb974b73391fb1c81" ON "notification" ("userId")
        `);
        await queryRunner.query(`
            ALTER TABLE "notification"
            ADD CONSTRAINT "FK_c64341553ec1b00bebcb3699cec" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "notification"
            ADD CONSTRAINT "FK_1ced25315eb974b73391fb1c81b" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "notification" DROP CONSTRAINT "FK_1ced25315eb974b73391fb1c81b"
        `);
        await queryRunner.query(`
            ALTER TABLE "notification" DROP CONSTRAINT "FK_c64341553ec1b00bebcb3699cec"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_1ced25315eb974b73391fb1c81"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_d95b732fb7f7d3b03819f09a74"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_c64341553ec1b00bebcb3699ce"
        `);
        await queryRunner.query(`
            DROP TABLE "notification"
        `);
    }

}
