import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1759830922165 implements MigrationInterface {
    name = 'Migration1759830922165'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "role"
            ADD "deletedAt" TIMESTAMP WITH TIME ZONE
        `);
        await queryRunner.query(`
            ALTER TABLE "authentication"
            ADD "deletedAt" TIMESTAMP WITH TIME ZONE
        `);
        await queryRunner.query(`
            ALTER TABLE "user"
            ADD "deletedAt" TIMESTAMP WITH TIME ZONE
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user" DROP COLUMN "deletedAt"
        `);
        await queryRunner.query(`
            ALTER TABLE "authentication" DROP COLUMN "deletedAt"
        `);
        await queryRunner.query(`
            ALTER TABLE "role" DROP COLUMN "deletedAt"
        `);
    }

}
