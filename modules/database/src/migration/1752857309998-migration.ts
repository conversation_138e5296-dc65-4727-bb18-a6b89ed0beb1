import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1752857309998 implements MigrationInterface {
    name = 'Migration1752857309998'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "fileProductCode" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "fileId" uuid NOT NULL,
                "productCodeId" uuid NOT NULL,
                "sequence" integer NOT NULL DEFAULT '0',
                CONSTRAINT "PK_0b661d3baa0378d944c5a3d5a74" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_0a84ee3796395e340ef88dcfd6" ON "fileProductCode" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_feb932017afa3638572fe6b308" ON "fileProductCode" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_188e4e2a84c8a345ed1e704b86" ON "fileProductCode" ("fileId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_e5ce3c1fa6f0b269a625ea98bf" ON "fileProductCode" ("productCodeId")
        `);
        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_682d85aa9510a0990798610a1d" ON "fileProductCode" ("fileId", "productCodeId")
        `);
        await queryRunner.query(`
            ALTER TABLE "fileProductCode"
            ADD CONSTRAINT "FK_0a84ee3796395e340ef88dcfd67" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "fileProductCode"
            ADD CONSTRAINT "FK_188e4e2a84c8a345ed1e704b86e" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "fileProductCode"
            ADD CONSTRAINT "FK_e5ce3c1fa6f0b269a625ea98bf6" FOREIGN KEY ("productCodeId") REFERENCES "productCode"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "fileProductCode" DROP CONSTRAINT "FK_e5ce3c1fa6f0b269a625ea98bf6"
        `);
        await queryRunner.query(`
            ALTER TABLE "fileProductCode" DROP CONSTRAINT "FK_188e4e2a84c8a345ed1e704b86e"
        `);
        await queryRunner.query(`
            ALTER TABLE "fileProductCode" DROP CONSTRAINT "FK_0a84ee3796395e340ef88dcfd67"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_682d85aa9510a0990798610a1d"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_e5ce3c1fa6f0b269a625ea98bf"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_188e4e2a84c8a345ed1e704b86"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_feb932017afa3638572fe6b308"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_0a84ee3796395e340ef88dcfd6"
        `);
        await queryRunner.query(`
            DROP TABLE "fileProductCode"
        `);
    }

}
