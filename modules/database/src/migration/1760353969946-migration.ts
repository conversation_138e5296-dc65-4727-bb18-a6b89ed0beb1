import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1760353969946 implements MigrationInterface {
	name = 'Migration1760353969946';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TYPE "public"."SCOPE_NAMES"
            RENAME TO "SCOPE_NAMES_old"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SCOPE_NAMES" AS ENUM(
                'admin',
                'home',
                'batchRead',
                'batchWrite',
                'batchCheck',
                'batchDelivery',
                'customerClaimRead',
                'customerClaimWrite',
                'envelopeWrite',
                'productTest',
                'productTestLead',
                'testRead',
                'serviceRead',
                'serviceWrite',
                'serviceManage',
                'warrantyClaimRead',
                'warrantyClaimWrite',
                'stock',
                'productRead',
                'productWrite',
                'orderRead',
                'orderWrite',
                'productAdmin',
                'shoptetWrite',
                'warehouseManage',
                'warehouseRead',
                'warehouseWrite'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "scope"
            ALTER COLUMN "name" TYPE "public"."SCOPE_NAMES" USING "name"::"text"::"public"."SCOPE_NAMES"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."SCOPE_NAMES_old"
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            CREATE TYPE "public"."SCOPE_NAMES_old" AS ENUM(
                'admin',
                'home',
                'batchRead',
                'batchWrite',
                'batchCheck',
                'batchDelivery',
                'customerClaimRead',
                'customerClaimWrite',
                'envelopeWrite',
                'productTest',
                'productTestLead',
                'testRead',
                'serviceRead',
                'serviceWrite',
                'serviceManage',
                'warrantyClaimRead',
                'warrantyClaimWrite',
                'stock',
                'productRead',
                'productWrite',
                'orderRead',
                'orderWrite',
                'productAdmin',
                'warehouseManage',
                'warehouseRead',
                'warehouseWrite'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "scope"
            ALTER COLUMN "name" TYPE "public"."SCOPE_NAMES_old" USING "name"::"text"::"public"."SCOPE_NAMES_old"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."SCOPE_NAMES"
        `);
		await queryRunner.query(`
            ALTER TYPE "public"."SCOPE_NAMES_old"
            RENAME TO "SCOPE_NAMES"
        `);
	}
}
