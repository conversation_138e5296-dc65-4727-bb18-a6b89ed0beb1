import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1761837991059 implements MigrationInterface {
    name = 'Migration1761837991059'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "productCategoryGrade" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "maxCosmeticDefects" integer NOT NULL DEFAULT '0',
                "productCategoryId" uuid NOT NULL,
                "gradeId" uuid NOT NULL,
                CONSTRAINT "PK_fc3d0f42899fbdec66d2e94fd8f" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_84b583d4945295a001c9f5faf3" ON "productCategoryGrade" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_320328fbff256b5e442ba3bf1a" ON "productCategoryGrade" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_dca49394f2c0b05a39fc7369f0" ON "productCategoryGrade" ("productCategoryId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_2a7e36fdda92947924f5a22725" ON "productCategoryGrade" ("gradeId")
        `);
        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_cd1a0623a1f72725e4662972e7" ON "productCategoryGrade" ("productCategoryId", "gradeId")
        `);
        await queryRunner.query(`
            ALTER TABLE "grade" DROP COLUMN "maxCosmeticDefects"
        `);
        await queryRunner.query(`
            ALTER TABLE "productCategoryGrade"
            ADD CONSTRAINT "FK_84b583d4945295a001c9f5faf3c" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "productCategoryGrade"
            ADD CONSTRAINT "FK_dca49394f2c0b05a39fc7369f00" FOREIGN KEY ("productCategoryId") REFERENCES "productCategory"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "productCategoryGrade"
            ADD CONSTRAINT "FK_2a7e36fdda92947924f5a227254" FOREIGN KEY ("gradeId") REFERENCES "grade"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productCategoryGrade" DROP CONSTRAINT "FK_2a7e36fdda92947924f5a227254"
        `);
        await queryRunner.query(`
            ALTER TABLE "productCategoryGrade" DROP CONSTRAINT "FK_dca49394f2c0b05a39fc7369f00"
        `);
        await queryRunner.query(`
            ALTER TABLE "productCategoryGrade" DROP CONSTRAINT "FK_84b583d4945295a001c9f5faf3c"
        `);
        await queryRunner.query(`
            ALTER TABLE "grade"
            ADD "maxCosmeticDefects" integer NOT NULL
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_cd1a0623a1f72725e4662972e7"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_2a7e36fdda92947924f5a22725"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_dca49394f2c0b05a39fc7369f0"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_320328fbff256b5e442ba3bf1a"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_84b583d4945295a001c9f5faf3"
        `);
        await queryRunner.query(`
            DROP TABLE "productCategoryGrade"
        `);
    }

}
