import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1758780709105 implements MigrationInterface {
    name = 'Migration1758780709105'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TYPE "public"."NOTIFICATION_TYPE_NAMES"
            RENAME TO "NOTIFICATION_TYPE_NAMES_old"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."NOTIFICATION_TYPE_NAMES" AS ENUM(
                'SHOPTET_EXPORT',
                'ENVELOPE_UPDATE',
                'PRODUCT_PICKUP',
                'MISSING_RECYCLING_FEE'
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "notificationType"
            ALTER COLUMN "name" TYPE "public"."NOTIFICATION_TYPE_NAMES" USING "name"::"text"::"public"."NOTIFICATION_TYPE_NAMES"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."NOTIFICATION_TYPE_NAMES_old"
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TYPE "public"."NOTIFICATION_TYPE_NAMES_old" AS ENUM(
                'SHOPTET_EXPORT',
                'ENVELOPE_UPDATE',
                'PRODUCT_PICKUP'
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "notificationType"
            ALTER COLUMN "name" TYPE "public"."NOTIFICATION_TYPE_NAMES_old" USING "name"::"text"::"public"."NOTIFICATION_TYPE_NAMES_old"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."NOTIFICATION_TYPE_NAMES"
        `);
        await queryRunner.query(`
            ALTER TYPE "public"."NOTIFICATION_TYPE_NAMES_old"
            RENAME TO "NOTIFICATION_TYPE_NAMES"
        `);
    }

}
