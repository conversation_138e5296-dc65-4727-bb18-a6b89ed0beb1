import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1753877971302 implements MigrationInterface {
	name = 'Migration1753877971302';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
			ALTER TABLE "warehouseTask"
				DROP CONSTRAINT "FK_9eab14171403bdb72caf0d93f67"
		`);
		await queryRunner.query(`
			ALTER TABLE "warehouseTask"
				DROP CONSTRAINT "FK_a78dc43054c3058b87cf54e8a2c"
		`);
		await queryRunner.query(`
			DROP INDEX "public"."IDX_9eab14171403bdb72caf0d93f6"
		`);
		await queryRunner.query(`
			DROP INDEX "public"."IDX_a78dc43054c3058b87cf54e8a2"
		`);
		await queryRunner.query(`
			ALTER TABLE "warehouseTask"
				DROP COLUMN "sourceWarehouseId"
		`);
		await queryRunner.query(`
			ALTER TABLE "warehouseTask"
				DROP COLUMN "userId"
		`);
		await queryRunner.query(`
			ALTER TABLE "warehouseTask"
				ADD "ecommerceOrderId" uuid
		`);
		await queryRunner.query(`
			ALTER TABLE "warehouseTask"
				ALTER COLUMN "priority" SET DEFAULT '0'
		`);
		await queryRunner.query(`
			CREATE INDEX "IDX_164fb1ba828933bdb62818e8dc" ON "warehouseTask" ("ecommerceOrderId")
		`);
		await queryRunner.query(`
			ALTER TABLE "warehouseTask"
				ADD CONSTRAINT "FK_164fb1ba828933bdb62818e8dc8" FOREIGN KEY ("ecommerceOrderId") REFERENCES "ecommerceOrder" ("id") ON DELETE CASCADE ON UPDATE NO ACTION
		`);
		await queryRunner.query(`
			ALTER TABLE "productWarehouseTask"
				DROP CONSTRAINT "FK_5a130222de59f6257f07a10f705"
		`);
		await queryRunner.query(`
			ALTER TABLE "productWarehouseTask"
				DROP CONSTRAINT "FK_ed047783973e2b0d8c81af09038"
		`);
		await queryRunner.query(`
			ALTER TABLE "productWarehouseTask"
				ADD CONSTRAINT "FK_5a130222de59f6257f07a10f705" FOREIGN KEY ("productId") REFERENCES "product" ("id") ON DELETE CASCADE ON UPDATE NO ACTION
		`);
		await queryRunner.query(`
			ALTER TABLE "productWarehouseTask"
				ADD CONSTRAINT "FK_ed047783973e2b0d8c81af09038" FOREIGN KEY ("warehouseTaskId") REFERENCES "warehouseTask" ("id") ON DELETE CASCADE ON UPDATE NO ACTION
		`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
			ALTER TABLE "warehouseTask"
				DROP CONSTRAINT "FK_164fb1ba828933bdb62818e8dc8"
		`);
		await queryRunner.query(`
			DROP INDEX "public"."IDX_164fb1ba828933bdb62818e8dc"
		`);
		await queryRunner.query(`
			ALTER TABLE "warehouseTask"
				ALTER COLUMN "priority" SET DEFAULT '50'
		`);
		await queryRunner.query(`
			ALTER TABLE "warehouseTask"
				DROP COLUMN "ecommerceOrderId"
		`);
		await queryRunner.query(`
			ALTER TABLE "warehouseTask"
				ADD "userId" uuid
		`);
		await queryRunner.query(`
			ALTER TABLE "warehouseTask"
				ADD "sourceWarehouseId" uuid NOT NULL
		`);
		await queryRunner.query(`
			CREATE INDEX "IDX_a78dc43054c3058b87cf54e8a2" ON "warehouseTask" ("sourceWarehouseId")
		`);
		await queryRunner.query(`
			CREATE INDEX "IDX_9eab14171403bdb72caf0d93f6" ON "warehouseTask" ("userId")
		`);
		await queryRunner.query(`
			ALTER TABLE "warehouseTask"
				ADD CONSTRAINT "FK_a78dc43054c3058b87cf54e8a2c" FOREIGN KEY ("sourceWarehouseId") REFERENCES "warehouse" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION
		`);
		await queryRunner.query(`
			ALTER TABLE "warehouseTask"
				ADD CONSTRAINT "FK_9eab14171403bdb72caf0d93f67" FOREIGN KEY ("userId") REFERENCES "user" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION
		`);
		await queryRunner.query(`
			ALTER TABLE "productWarehouseTask"
				DROP CONSTRAINT "FK_ed047783973e2b0d8c81af09038"
		`);
		await queryRunner.query(`
			ALTER TABLE "productWarehouseTask"
				DROP CONSTRAINT "FK_5a130222de59f6257f07a10f705"
		`);
		await queryRunner.query(`
			ALTER TABLE "productWarehouseTask"
				ADD CONSTRAINT "FK_ed047783973e2b0d8c81af09038" FOREIGN KEY ("warehouseTaskId") REFERENCES "warehouseTask" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION
		`);
		await queryRunner.query(`
			ALTER TABLE "productWarehouseTask"
				ADD CONSTRAINT "FK_5a130222de59f6257f07a10f705" FOREIGN KEY ("productId") REFERENCES "product" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION
		`);
	}
}
