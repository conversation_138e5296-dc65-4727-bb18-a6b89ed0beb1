import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1757417653227 implements MigrationInterface {
    name = 'Migration1757417653227'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TYPE "public"."ENVELOPE_PRODUCT_TYPES" AS ENUM('NEW', 'REFURBISHED', 'RENEWED')
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD "productType" "public"."ENVELOPE_PRODUCT_TYPES" NOT NULL DEFAULT 'REFURBISHED'
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_TYPES" AS ENUM('NEW', 'REFURBISHED', 'RENEWED')
        `);
        await queryRunner.query(`
            ALTER TABLE "product"
            ADD "type" "public"."PRODUCT_TYPES" NOT NULL DEFAULT 'REFURBISHED'
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "product" DROP COLUMN "type"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_TYPES"
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP COLUMN "productType"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."ENVELOPE_PRODUCT_TYPES"
        `);
    }

}
