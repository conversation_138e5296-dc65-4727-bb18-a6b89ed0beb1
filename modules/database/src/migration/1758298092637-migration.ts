import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1758298092637 implements MigrationInterface {
	name = 'Migration1758298092637';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "warehouseTask" DROP CONSTRAINT "FK_6bb9960c2dbf3cdda9c67bda3ce"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_6bb9960c2dbf3cdda9c67bda3c"
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask"
            DROP COLUMN "destinationWarehouseId"
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask"
            ADD "userId" uuid
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_9eab14171403bdb72caf0d93f6" ON "warehouseTask" ("userId")
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask"
            ADD CONSTRAINT "FK_9eab14171403bdb72caf0d93f67" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "warehouseTask" DROP CONSTRAINT "FK_9eab14171403bdb72caf0d93f67"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_9eab14171403bdb72caf0d93f6"
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask"
            DROP COLUMN "userId"
        `);
		await queryRunner.query(`
			ALTER TABLE "warehouseTask"
			ADD "destinationWarehouseId" uuid NOT NULL
		`);
		await queryRunner.query(`
            CREATE INDEX "IDX_6bb9960c2dbf3cdda9c67bda3c" ON "warehouseTask" ("destinationWarehouseId")
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask"
            ADD CONSTRAINT "FK_6bb9960c2dbf3cdda9c67bda3ce" FOREIGN KEY ("destinationWarehouseId") REFERENCES "warehouse"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
	}
}
