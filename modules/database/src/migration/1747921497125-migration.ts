import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1747921497125 implements MigrationInterface {
	name = 'Migration1747921497125';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "productTest"
            ADD "printedWithEnvelopeId" uuid
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f3c6dae05a5c1e01e7b2be62cb" ON "productTest" ("printedWithEnvelopeId")
        `);
		await queryRunner.query(`
            ALTER TABLE "productTest"
            ADD CONSTRAINT "FK_f3c6dae05a5c1e01e7b2be62cbc" FOREIGN KEY ("printedWithEnvelopeId") REFERENCES "productEnvelope"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "productTest" DROP CONSTRAINT "FK_f3c6dae05a5c1e01e7b2be62cbc"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f3c6dae05a5c1e01e7b2be62cb"
        `);
		await queryRunner.query(`
            ALTER TABLE "productTest" DROP COLUMN "printedWithEnvelopeId"
        `);
	}
}
