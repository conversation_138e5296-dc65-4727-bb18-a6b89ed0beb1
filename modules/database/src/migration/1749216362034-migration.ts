import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1749216362034 implements MigrationInterface {
	name = 'Migration1749216362034';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TYPE "public"."SCOPE_NAMES"
            RENAME TO "SCOPE_NAMES_old"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SCOPE_NAMES" AS ENUM(
                'admin',
                'home',
                'batchRead',
                'batchWrite',
                'batchCheck',
                'batchDelivery',
                'envelopeWrite',
                'productTest',
                'productTestLead',
                'testRead',
                'serviceRead',
                'serviceWrite',
                'serviceManage',
                'warrantyClaimRead',
                'warrantyClaimWrite',
                'stock',
                'productRead',
                'productWrite',
                'orderRead',
                'orderWrite',
                'productAdmin'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "scope"
            ALTER COLUMN "name" TYPE "public"."SCOPE_NAMES" USING "name"::"text"::"public"."SCOPE_NAMES"
        `);
		await queryRunner.query(`
			INSERT INTO "scope" ("name", "description") VALUES ('envelopeWrite', 'Zápis karet produktů')
        `);
		await queryRunner.query(`
            DROP TYPE "public"."SCOPE_NAMES_old"
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
			DELETE FROM "scope" WHERE name = 'envelopeWrite'
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SCOPE_NAMES_old" AS ENUM(
                'admin',
                'batchCheck',
                'batchDelivery',
                'batchRead',
                'batchWrite',
                'home',
                'orderRead',
                'orderWrite',
                'productAdmin',
                'productRead',
                'productTest',
                'productTestLead',
                'productWrite',
                'serviceManage',
                'serviceRead',
                'serviceWrite',
                'stock',
                'testRead',
                'warrantyClaimRead',
                'warrantyClaimWrite'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "scope"
            ALTER COLUMN "name" TYPE "public"."SCOPE_NAMES_old" USING "name"::"text"::"public"."SCOPE_NAMES_old"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."SCOPE_NAMES"
        `);
		await queryRunner.query(`
            ALTER TYPE "public"."SCOPE_NAMES_old"
            RENAME TO "SCOPE_NAMES"
        `);
	}
}
