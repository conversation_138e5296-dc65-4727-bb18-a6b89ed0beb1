import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1755099687268 implements MigrationInterface {
    name = 'Migration1755099687268'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TYPE "public"."ENVELOPE_TYPES" AS ENUM('NEW', 'USED')
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD "type" "public"."ENVELOPE_TYPES" NOT NULL DEFAULT 'USED'
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD "ean" character varying NOT NULL DEFAULT ''
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD "vendorCode" character varying NOT NULL DEFAULT ''
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD "needsRefinement" boolean NOT NULL DEFAULT false
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_9dabf3f17275f8ac695cb6e07c" ON "productEnvelope" ("ean")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_111648b148e82b99b7f93915f5" ON "productEnvelope" ("vendorCode")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX "public"."IDX_111648b148e82b99b7f93915f5"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_9dabf3f17275f8ac695cb6e07c"
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP COLUMN "needsRefinement"
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP COLUMN "vendorCode"
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP COLUMN "ean"
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP COLUMN "type"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."ENVELOPE_TYPES"
        `);
    }

}
