import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1749051110077 implements MigrationInterface {
    name = 'Migration1749051110077'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_DEFECT_SOURCE" AS ENUM('TESTING', 'SERVICE_CASE', 'WARRANTY_CLAIM')
        `);
        await queryRunner.query(`
            ALTER TABLE "productDefect"
            ADD "source" "public"."PRODUCT_DEFECT_SOURCE" NOT NULL DEFAULT 'TESTING'
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_c481814d444e623c21ac87b02a" ON "productDefect" ("source")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX "public"."IDX_c481814d444e623c21ac87b02a"
        `);
        await queryRunner.query(`
            ALTER TABLE "productDefect" DROP COLUMN "source"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_DEFECT_SOURCE"
        `);
    }

}
