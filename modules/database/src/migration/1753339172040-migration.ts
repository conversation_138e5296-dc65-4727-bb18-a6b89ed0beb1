import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1753339172040 implements MigrationInterface {
    name = 'Migration1753339172040'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TYPE "public"."WAREHOUSE_TYPE_NAMES"
            RENAME TO "WAREHOUSE_TYPE_NAMES_old"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."WAREHOUSE_TYPE_NAMES" AS ENUM(
                'STOCK',
                'EXPEDITION',
                'TESTING',
                'DEPOSIT',
                'SERVICE',
                'WARRANTY_CLAIM'
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "warehouse"
            ALTER COLUMN "type" DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TABLE "warehouse"
            ALTER COLUMN "type" TYPE "public"."WAREHOUSE_TYPE_NAMES" USING "type"::"text"::"public"."WAREHOUSE_TYPE_NAMES"
        `);
        await queryRunner.query(`
            ALTER TABLE "warehouse"
            ALTER COLUMN "type"
            SET DEFAULT 'STOCK'
        `);
        await queryRunner.query(`
            DROP TYPE "public"."WAREHOUSE_TYPE_NAMES_old"
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TYPE "public"."WAREHOUSE_TYPE_NAMES_old" AS ENUM(
                'EXPEDITION',
                'SERVICE',
                'STOCK',
                'TESTING',
                'WARRANTY_CLAIM'
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "warehouse"
            ALTER COLUMN "type" DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TABLE "warehouse"
            ALTER COLUMN "type" TYPE "public"."WAREHOUSE_TYPE_NAMES_old" USING "type"::"text"::"public"."WAREHOUSE_TYPE_NAMES_old"
        `);
        await queryRunner.query(`
            ALTER TABLE "warehouse"
            ALTER COLUMN "type"
            SET DEFAULT 'STOCK'
        `);
        await queryRunner.query(`
            DROP TYPE "public"."WAREHOUSE_TYPE_NAMES"
        `);
        await queryRunner.query(`
            ALTER TYPE "public"."WAREHOUSE_TYPE_NAMES_old"
            RENAME TO "WAREHOUSE_TYPE_NAMES"
        `);
    }

}
