import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1753255256331 implements MigrationInterface {
    name = 'Migration1753255256331'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "product"
            ADD "soldAt" TIMESTAMP WITH TIME ZONE
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_5714f31672d07a318e96115d89" ON "product" ("soldAt")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX "public"."IDX_5714f31672d07a318e96115d89"
        `);
        await queryRunner.query(`
            ALTER TABLE "product" DROP COLUMN "soldAt"
        `);
    }

}
