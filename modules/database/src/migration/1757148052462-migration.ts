import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1757148052462 implements MigrationInterface {
    name = 'Migration1757148052462'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "productCategoryCosmeticDefect" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "cosmeticDefectId" uuid NOT NULL,
                "productCategoryId" uuid NOT NULL,
                CONSTRAINT "PK_da9905ce950c75a5f99dff40f9f" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_ea6245ae6bced99cea7b58fd90" ON "productCategoryCosmeticDefect" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_7aef31c3d6edf320e7200b0693" ON "productCategoryCosmeticDefect" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_467e013986eccc0be060ed57a9" ON "productCategoryCosmeticDefect" ("cosmeticDefectId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_cb2a3097e93aaaff7ff744c1fd" ON "productCategoryCosmeticDefect" ("productCategoryId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_78c8e7359204488020fa53383a" ON "productCategoryCosmeticDefect" ("cosmeticDefectId", "productCategoryId")
        `);
        await queryRunner.query(`
            CREATE TABLE "productCosmeticDefect" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "isFix" boolean NOT NULL DEFAULT false,
                "cosmeticDefectId" uuid NOT NULL,
                "productId" uuid NOT NULL,
                "cosmeticAreaId" uuid NOT NULL,
                CONSTRAINT "PK_8d56baafdff0a2fb1314847d761" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_58173a25663d0f8c2d8317e882" ON "productCosmeticDefect" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_a403ac2a5edef8c04b22950ec8" ON "productCosmeticDefect" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_c03226a49b12929c827075bebd" ON "productCosmeticDefect" ("cosmeticDefectId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_9919a7f9f75b235b32ffaa8fe2" ON "productCosmeticDefect" ("productId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_2050b421ca8c4e978f0a5f5f7b" ON "productCosmeticDefect" ("cosmeticAreaId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_30380c69bcd55aab2f8388c460" ON "productCosmeticDefect" ("cosmeticDefectId", "productId")
        `);
        await queryRunner.query(`
            CREATE TABLE "cosmeticArea" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" text NOT NULL,
                "productCategoryId" uuid NOT NULL,
                CONSTRAINT "PK_6b1747c02cc90b361b58694fdd2" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_384edfaea785662509340ac9f3" ON "cosmeticArea" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_837fcb7cf7e90e8593dc913a6d" ON "cosmeticArea" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_e08a64e937d4518322df71bd93" ON "cosmeticArea" ("productCategoryId")
        `);
        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_5a65bb5b6e019c71e720818824" ON "cosmeticArea" ("name", "productCategoryId")
        `);
        await queryRunner.query(`
            CREATE TABLE "cosmeticAreaCosmeticDefect" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "cosmeticDefectId" uuid NOT NULL,
                "cosmeticAreaId" uuid NOT NULL,
                CONSTRAINT "PK_b8fccfeb5e5604a474cfc3b4303" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_4235196b66a9cff3600b1cb99f" ON "cosmeticAreaCosmeticDefect" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_9f5697dbe21c409ca339bf7f15" ON "cosmeticAreaCosmeticDefect" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_769504b18191618f0e54ef370b" ON "cosmeticAreaCosmeticDefect" ("cosmeticDefectId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_04e657a8e1ca6c19471f8b1ae2" ON "cosmeticAreaCosmeticDefect" ("cosmeticAreaId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_822bbf56a23524dba9e93be4de" ON "cosmeticAreaCosmeticDefect" ("cosmeticDefectId", "cosmeticAreaId")
        `);
        await queryRunner.query(`
            CREATE TABLE "cosmeticDefect" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" text NOT NULL,
                "pictureRequired" boolean NOT NULL DEFAULT false,
                "gradeId" uuid NOT NULL,
                CONSTRAINT "PK_a6fa000f8cc8125ef37898d25fc" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_4b94ab2ecba93b57ede209a363" ON "cosmeticDefect" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_d1ab8dbf02c8fd35a1c364df8b" ON "cosmeticDefect" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_ff082a485cc3c799388e1510ad" ON "cosmeticDefect" ("gradeId")
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."PRICE_ROUNDING_STRATEGIES" AS ENUM('HIGH', 'LOW')
        `);
        await queryRunner.query(`
            CREATE TABLE "grade" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" text NOT NULL,
                "maxCosmeticDefects" integer NOT NULL,
                "discountPercentage" real NOT NULL,
                "priceRoundingStrategy" "public"."PRICE_ROUNDING_STRATEGIES" NOT NULL,
                CONSTRAINT "PK_58c2176c3ae96bf57daebdbcb5e" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_016b88634ae887600119ec8699" ON "grade" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_45e35d25a7cc3d6519b58b83ea" ON "grade" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_fc1484b9c802e039fa12a4e7b7" ON "grade" ("priceRoundingStrategy")
        `);
        await queryRunner.query(`
            CREATE TABLE "fileProductCosmeticDefect" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "fileId" uuid NOT NULL,
                "productCosmeticDefectId" uuid NOT NULL,
                "sequence" integer NOT NULL DEFAULT '0',
                CONSTRAINT "PK_aefa5916d65efa02fde50d86fa1" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_6a69493a1cc1ffc099ba8c3d68" ON "fileProductCosmeticDefect" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_ada9be61747de5f4575ca065fc" ON "fileProductCosmeticDefect" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_a4a4222278cfc0ee554c576447" ON "fileProductCosmeticDefect" ("fileId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_4236557345a8e3394d4d3c4700" ON "fileProductCosmeticDefect" ("productCosmeticDefectId")
        `);
        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_6a03775a57b667cbf1f63b4d0a" ON "fileProductCosmeticDefect" ("fileId", "productCosmeticDefectId")
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD "gradeId" uuid
        `);
        await queryRunner.query(`
            ALTER TABLE "product"
            ADD "gradeId" uuid
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_999b21f2e2a18c2370cd44bf45" ON "productEnvelope" ("gradeId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_acf151be5697183974636e755c" ON "product" ("gradeId")
        `);
        await queryRunner.query(`
            ALTER TABLE "productCategoryCosmeticDefect"
            ADD CONSTRAINT "FK_ea6245ae6bced99cea7b58fd90f" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "productCategoryCosmeticDefect"
            ADD CONSTRAINT "FK_467e013986eccc0be060ed57a94" FOREIGN KEY ("cosmeticDefectId") REFERENCES "cosmeticDefect"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "productCategoryCosmeticDefect"
            ADD CONSTRAINT "FK_cb2a3097e93aaaff7ff744c1fd7" FOREIGN KEY ("productCategoryId") REFERENCES "productCategory"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "productCosmeticDefect"
            ADD CONSTRAINT "FK_58173a25663d0f8c2d8317e8820" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "productCosmeticDefect"
            ADD CONSTRAINT "FK_c03226a49b12929c827075bebd6" FOREIGN KEY ("cosmeticDefectId") REFERENCES "cosmeticDefect"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "productCosmeticDefect"
            ADD CONSTRAINT "FK_9919a7f9f75b235b32ffaa8fe2b" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "productCosmeticDefect"
            ADD CONSTRAINT "FK_2050b421ca8c4e978f0a5f5f7bb" FOREIGN KEY ("cosmeticAreaId") REFERENCES "cosmeticArea"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "cosmeticArea"
            ADD CONSTRAINT "FK_384edfaea785662509340ac9f3a" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "cosmeticArea"
            ADD CONSTRAINT "FK_e08a64e937d4518322df71bd93e" FOREIGN KEY ("productCategoryId") REFERENCES "productCategory"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "cosmeticAreaCosmeticDefect"
            ADD CONSTRAINT "FK_4235196b66a9cff3600b1cb99fa" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "cosmeticAreaCosmeticDefect"
            ADD CONSTRAINT "FK_769504b18191618f0e54ef370b5" FOREIGN KEY ("cosmeticDefectId") REFERENCES "cosmeticDefect"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "cosmeticAreaCosmeticDefect"
            ADD CONSTRAINT "FK_04e657a8e1ca6c19471f8b1ae2f" FOREIGN KEY ("cosmeticAreaId") REFERENCES "cosmeticArea"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "cosmeticDefect"
            ADD CONSTRAINT "FK_4b94ab2ecba93b57ede209a363a" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "cosmeticDefect"
            ADD CONSTRAINT "FK_ff082a485cc3c799388e1510ad9" FOREIGN KEY ("gradeId") REFERENCES "grade"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "grade"
            ADD CONSTRAINT "FK_016b88634ae887600119ec86992" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD CONSTRAINT "FK_999b21f2e2a18c2370cd44bf456" FOREIGN KEY ("gradeId") REFERENCES "grade"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "product"
            ADD CONSTRAINT "FK_acf151be5697183974636e755c6" FOREIGN KEY ("gradeId") REFERENCES "grade"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "fileProductCosmeticDefect"
            ADD CONSTRAINT "FK_6a69493a1cc1ffc099ba8c3d683" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "fileProductCosmeticDefect"
            ADD CONSTRAINT "FK_a4a4222278cfc0ee554c5764470" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "fileProductCosmeticDefect"
            ADD CONSTRAINT "FK_4236557345a8e3394d4d3c4700b" FOREIGN KEY ("productCosmeticDefectId") REFERENCES "productCosmeticDefect"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "fileProductCosmeticDefect" DROP CONSTRAINT "FK_4236557345a8e3394d4d3c4700b"
        `);
        await queryRunner.query(`
            ALTER TABLE "fileProductCosmeticDefect" DROP CONSTRAINT "FK_a4a4222278cfc0ee554c5764470"
        `);
        await queryRunner.query(`
            ALTER TABLE "fileProductCosmeticDefect" DROP CONSTRAINT "FK_6a69493a1cc1ffc099ba8c3d683"
        `);
        await queryRunner.query(`
            ALTER TABLE "product" DROP CONSTRAINT "FK_acf151be5697183974636e755c6"
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP CONSTRAINT "FK_999b21f2e2a18c2370cd44bf456"
        `);
        await queryRunner.query(`
            ALTER TABLE "grade" DROP CONSTRAINT "FK_016b88634ae887600119ec86992"
        `);
        await queryRunner.query(`
            ALTER TABLE "cosmeticDefect" DROP CONSTRAINT "FK_ff082a485cc3c799388e1510ad9"
        `);
        await queryRunner.query(`
            ALTER TABLE "cosmeticDefect" DROP CONSTRAINT "FK_4b94ab2ecba93b57ede209a363a"
        `);
        await queryRunner.query(`
            ALTER TABLE "cosmeticAreaCosmeticDefect" DROP CONSTRAINT "FK_04e657a8e1ca6c19471f8b1ae2f"
        `);
        await queryRunner.query(`
            ALTER TABLE "cosmeticAreaCosmeticDefect" DROP CONSTRAINT "FK_769504b18191618f0e54ef370b5"
        `);
        await queryRunner.query(`
            ALTER TABLE "cosmeticAreaCosmeticDefect" DROP CONSTRAINT "FK_4235196b66a9cff3600b1cb99fa"
        `);
        await queryRunner.query(`
            ALTER TABLE "cosmeticArea" DROP CONSTRAINT "FK_e08a64e937d4518322df71bd93e"
        `);
        await queryRunner.query(`
            ALTER TABLE "cosmeticArea" DROP CONSTRAINT "FK_384edfaea785662509340ac9f3a"
        `);
        await queryRunner.query(`
            ALTER TABLE "productCosmeticDefect" DROP CONSTRAINT "FK_2050b421ca8c4e978f0a5f5f7bb"
        `);
        await queryRunner.query(`
            ALTER TABLE "productCosmeticDefect" DROP CONSTRAINT "FK_9919a7f9f75b235b32ffaa8fe2b"
        `);
        await queryRunner.query(`
            ALTER TABLE "productCosmeticDefect" DROP CONSTRAINT "FK_c03226a49b12929c827075bebd6"
        `);
        await queryRunner.query(`
            ALTER TABLE "productCosmeticDefect" DROP CONSTRAINT "FK_58173a25663d0f8c2d8317e8820"
        `);
        await queryRunner.query(`
            ALTER TABLE "productCategoryCosmeticDefect" DROP CONSTRAINT "FK_cb2a3097e93aaaff7ff744c1fd7"
        `);
        await queryRunner.query(`
            ALTER TABLE "productCategoryCosmeticDefect" DROP CONSTRAINT "FK_467e013986eccc0be060ed57a94"
        `);
        await queryRunner.query(`
            ALTER TABLE "productCategoryCosmeticDefect" DROP CONSTRAINT "FK_ea6245ae6bced99cea7b58fd90f"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_acf151be5697183974636e755c"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_999b21f2e2a18c2370cd44bf45"
        `);
        await queryRunner.query(`
            ALTER TABLE "product" DROP COLUMN "gradeId"
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP COLUMN "gradeId"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_6a03775a57b667cbf1f63b4d0a"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_4236557345a8e3394d4d3c4700"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_a4a4222278cfc0ee554c576447"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_ada9be61747de5f4575ca065fc"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_6a69493a1cc1ffc099ba8c3d68"
        `);
        await queryRunner.query(`
            DROP TABLE "fileProductCosmeticDefect"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_fc1484b9c802e039fa12a4e7b7"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_45e35d25a7cc3d6519b58b83ea"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_016b88634ae887600119ec8699"
        `);
        await queryRunner.query(`
            DROP TABLE "grade"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."PRICE_ROUNDING_STRATEGIES"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_ff082a485cc3c799388e1510ad"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_d1ab8dbf02c8fd35a1c364df8b"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_4b94ab2ecba93b57ede209a363"
        `);
        await queryRunner.query(`
            DROP TABLE "cosmeticDefect"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_822bbf56a23524dba9e93be4de"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_04e657a8e1ca6c19471f8b1ae2"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_769504b18191618f0e54ef370b"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_9f5697dbe21c409ca339bf7f15"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_4235196b66a9cff3600b1cb99f"
        `);
        await queryRunner.query(`
            DROP TABLE "cosmeticAreaCosmeticDefect"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_5a65bb5b6e019c71e720818824"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_e08a64e937d4518322df71bd93"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_837fcb7cf7e90e8593dc913a6d"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_384edfaea785662509340ac9f3"
        `);
        await queryRunner.query(`
            DROP TABLE "cosmeticArea"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_30380c69bcd55aab2f8388c460"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_2050b421ca8c4e978f0a5f5f7b"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_9919a7f9f75b235b32ffaa8fe2"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_c03226a49b12929c827075bebd"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_a403ac2a5edef8c04b22950ec8"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_58173a25663d0f8c2d8317e882"
        `);
        await queryRunner.query(`
            DROP TABLE "productCosmeticDefect"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_78c8e7359204488020fa53383a"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_cb2a3097e93aaaff7ff744c1fd"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_467e013986eccc0be060ed57a9"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_7aef31c3d6edf320e7200b0693"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_ea6245ae6bced99cea7b58fd90"
        `);
        await queryRunner.query(`
            DROP TABLE "productCategoryCosmeticDefect"
        `);
    }

}
