import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1747810609356 implements MigrationInterface {
	name = 'Migration1747810609356';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            CREATE TABLE "productCategoryDefectType" (
                "productCategoryId" uuid NOT NULL,
                "defectTypeId" uuid NOT NULL,
                CONSTRAINT "PK_f4858004815430e9fb7eb455e08" PRIMARY KEY ("productCategoryId", "defectTypeId")
            )
        `);
		await queryRunner.query(`
			ALTER TABLE "productCategoryDefectType"
				ADD CONSTRAINT "FK_6b1a20309fb55550fcc7c38329a" FOREIGN KEY ("productCategoryId") REFERENCES "productCategory" ("id") ON DELETE CASCADE ON UPDATE CASCADE
		`);
		await queryRunner.query(`
			ALTER TABLE "productCategoryDefectType"
				ADD CONSTRAINT "FK_aeead1772ae3e8921e68653f699" FOREIGN KEY ("defectTypeId") REFERENCES "defectType" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION
		`);
		await queryRunner.query(`
            CREATE INDEX "IDX_6b1a20309fb55550fcc7c38329" ON "productCategoryDefectType" ("productCategoryId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_aeead1772ae3e8921e68653f69" ON "productCategoryDefectType" ("defectTypeId")
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            DROP INDEX "public"."IDX_aeead1772ae3e8921e68653f69"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_6b1a20309fb55550fcc7c38329"
        `);
		await queryRunner.query(`
			ALTER TABLE "productCategoryDefectType"
				DROP CONSTRAINT "FK_aeead1772ae3e8921e68653f699"
		`);
		await queryRunner.query(`
            ALTER TABLE "productCategoryDefectType" DROP CONSTRAINT "FK_6b1a20309fb55550fcc7c38329a"
        `);
		await queryRunner.query(`
            DROP TABLE "productCategoryDefectType"
        `);
	}
}
