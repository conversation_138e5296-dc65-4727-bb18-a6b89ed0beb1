import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1751315674000 implements MigrationInterface {
    name = 'Migration1751315674000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "fileInventory" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "fileId" uuid NOT NULL,
                "inventoryId" uuid NOT NULL,
                "sequence" integer NOT NULL DEFAULT '0',
                CONSTRAINT "PK_1df0670cde4f343ee7fefc26e00" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_8bad6f415d0f6f4ec75d7f4128" ON "fileInventory" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_f286e6880754388d8cfcc964c0" ON "fileInventory" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_1df2118a9d7e6cf2aa71143d92" ON "fileInventory" ("fileId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_3fa5dac66d64f337c3119fd02d" ON "fileInventory" ("inventoryId")
        `);
        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_29eeb33ce3d64297cad2f1fb7d" ON "fileInventory" ("fileId", "inventoryId")
        `);
        await queryRunner.query(`
            ALTER TABLE "inventory"
            ADD "untestedPrice" numeric(14, 4) NOT NULL DEFAULT '0'
        `);
        await queryRunner.query(`
            ALTER TABLE "inventory"
            ADD "forSalePrice" numeric(14, 4) NOT NULL DEFAULT '0'
        `);
        await queryRunner.query(`
            ALTER TABLE "inventory"
            ADD "servicePrice" numeric(14, 4) NOT NULL DEFAULT '0'
        `);
        await queryRunner.query(`
            ALTER TABLE "inventory"
            ADD "deadPrice" numeric(14, 4) NOT NULL DEFAULT '0'
        `);
        await queryRunner.query(`
            ALTER TABLE "fileInventory"
            ADD CONSTRAINT "FK_8bad6f415d0f6f4ec75d7f41287" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "fileInventory"
            ADD CONSTRAINT "FK_1df2118a9d7e6cf2aa71143d92c" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "fileInventory"
            ADD CONSTRAINT "FK_3fa5dac66d64f337c3119fd02d9" FOREIGN KEY ("inventoryId") REFERENCES "inventory"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "fileInventory" DROP CONSTRAINT "FK_3fa5dac66d64f337c3119fd02d9"
        `);
        await queryRunner.query(`
            ALTER TABLE "fileInventory" DROP CONSTRAINT "FK_1df2118a9d7e6cf2aa71143d92c"
        `);
        await queryRunner.query(`
            ALTER TABLE "fileInventory" DROP CONSTRAINT "FK_8bad6f415d0f6f4ec75d7f41287"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventory" DROP COLUMN "deadPrice"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventory" DROP COLUMN "servicePrice"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventory" DROP COLUMN "forSalePrice"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventory" DROP COLUMN "untestedPrice"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_29eeb33ce3d64297cad2f1fb7d"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_3fa5dac66d64f337c3119fd02d"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_1df2118a9d7e6cf2aa71143d92"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_f286e6880754388d8cfcc964c0"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_8bad6f415d0f6f4ec75d7f4128"
        `);
        await queryRunner.query(`
            DROP TABLE "fileInventory"
        `);
    }

}
