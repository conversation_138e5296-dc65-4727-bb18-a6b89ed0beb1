import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1755519356262 implements MigrationInterface {
    name = 'Migration1755519356262'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "ecommerceOrderItem"
            ADD "note" text
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "ecommerceOrderItem" DROP COLUMN "note"
        `);
    }

}
