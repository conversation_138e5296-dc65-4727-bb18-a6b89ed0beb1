import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1750252077293 implements MigrationInterface {
	name = 'Migration1750252077293';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
			CREATE TABLE "outage"
			(
				"id"          uuid                     NOT NULL DEFAULT uuid_generate_v4(),
				"createdAt"   TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
				"actionId"    uuid,
				"period"      tstzrange                NOT NULL DEFAULT tstzrange(now(), NULL),
				"createdById" uuid,
				"endedAt"     TIMESTAMP WITH TIME ZONE,
				"endedById"   uuid,
				"status"      character varying        NOT NULL,
				CONSTRAINT "PK_4b537b6fa0093fba4fb32df2542" PRIMARY KEY ("id")
			)
		`);
		await queryRunner.query(`
			CREATE INDEX "IDX_4bcb7e1547b8e276adbbb96481" ON "outage" ("actionId")
		`);
		await queryRunner.query(`
			CREATE INDEX "IDX_be2adb06ab7f08512c71bcb7f2" ON "outage" ("id", "createdAt")
		`);
		await queryRunner.query(`
			CREATE INDEX "IDX_e8e9338d903f344c37fdd30ed5" ON "outage" ("createdById")
		`);
		await queryRunner.query(`
			CREATE INDEX "IDX_e6580953fb1ea89ca43901af93" ON "outage" ("endedById")
		`);
		await queryRunner.query(`
			ALTER TABLE "outage"
				ADD CONSTRAINT "FK_4bcb7e1547b8e276adbbb964818" FOREIGN KEY ("actionId") REFERENCES "action" ("id") ON DELETE CASCADE ON UPDATE NO ACTION
		`);
		await queryRunner.query(`
			ALTER TABLE "outage"
				ADD CONSTRAINT "FK_e8e9338d903f344c37fdd30ed53" FOREIGN KEY ("createdById") REFERENCES "user" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION
		`);
		await queryRunner.query(`
			ALTER TABLE "outage"
				ADD CONSTRAINT "FK_e6580953fb1ea89ca43901af938" FOREIGN KEY ("endedById") REFERENCES "user" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION
		`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
			ALTER TABLE "outage"
				DROP CONSTRAINT "FK_e6580953fb1ea89ca43901af938"
		`);
		await queryRunner.query(`
			ALTER TABLE "outage"
				DROP CONSTRAINT "FK_e8e9338d903f344c37fdd30ed53"
		`);
		await queryRunner.query(`
			ALTER TABLE "outage"
				DROP CONSTRAINT "FK_4bcb7e1547b8e276adbbb964818"
		`);
		await queryRunner.query(`
			DROP INDEX "public"."IDX_e6580953fb1ea89ca43901af93"
		`);
		await queryRunner.query(`
			DROP INDEX "public"."IDX_e8e9338d903f344c37fdd30ed5"
		`);
		await queryRunner.query(`
			DROP INDEX "public"."IDX_be2adb06ab7f08512c71bcb7f2"
		`);
		await queryRunner.query(`
			DROP INDEX "public"."IDX_4bcb7e1547b8e276adbbb96481"
		`);
		await queryRunner.query(`
			DROP TABLE "outage"
		`);
	}
}
