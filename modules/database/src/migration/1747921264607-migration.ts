import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1747921264607 implements MigrationInterface {
	name = 'Migration1747921264607';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "productTask"
            ADD "closingNote" character varying NOT NULL DEFAULT ''
        `);
		await queryRunner.query(`
            ALTER TABLE "productAttributeValue"
            ADD "productTaskId" uuid
        `);
		await queryRunner.query(`
            ALTER TABLE "productPrice"
            ADD "productTaskId" uuid
        `);
		await queryRunner.query(`
            ALTER TABLE "productAttributeValue"
            ADD CONSTRAINT "FK_153ac82f3c93c88b084714af159" FOREIGN KEY ("productTaskId") REFERENCES "productTask"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productPrice"
            ADD CONSTRAINT "FK_ac40f54d0390cdd22aae2c218df" FOREIGN KEY ("productTaskId") REFERENCES "productTask"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "productPrice" DROP CONSTRAINT "FK_ac40f54d0390cdd22aae2c218df"
        `);
		await queryRunner.query(`
            ALTER TABLE "productAttributeValue" DROP CONSTRAINT "FK_153ac82f3c93c88b084714af159"
        `);
		await queryRunner.query(`
            ALTER TABLE "productPrice" DROP COLUMN "productTaskId"
        `);
		await queryRunner.query(`
            ALTER TABLE "productAttributeValue" DROP COLUMN "productTaskId"
        `);
		await queryRunner.query(`
            ALTER TABLE "productTask" DROP COLUMN "closingNote"
        `);
	}
}
