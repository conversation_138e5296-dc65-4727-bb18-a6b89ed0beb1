import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1758633585467 implements MigrationInterface {
    name = 'Migration1758633585467'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TYPE "public"."NOTIFICATION_DELIVERY_METHODS_NAMES" AS ENUM('push', 'email')
        `);
        await queryRunner.query(`
            CREATE TABLE "userNotificationType" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "deliveryMethod" "public"."NOTIFICATION_DELIVERY_METHODS_NAMES" NOT NULL,
                "notificationTypeId" uuid NOT NULL,
                "userId" uuid NOT NULL,
                CONSTRAINT "PK_78c9124ed960603cbbb99373a39" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_06d3affec058c7fa6c7976fa48" ON "userNotificationType" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_e590f9ba9e23210a8f8205ab48" ON "userNotificationType" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_f238058ede7302a48601e56743" ON "userNotificationType" ("deliveryMethod")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_c79271001a09377f80b0be5f7b" ON "userNotificationType" ("notificationTypeId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_5eb2f399e6af5f41861f55a7e2" ON "userNotificationType" ("userId")
        `);
        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_34a08f05907f0de589904e548b" ON "userNotificationType" ("notificationTypeId", "userId", "deliveryMethod")
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."NOTIFICATION_TYPE_NAMES" AS ENUM(
                'SHOPTET_EXPORT',
                'ENVELOPE_UPDATE',
                'PRODUCT_PICKUP'
            )
        `);
        await queryRunner.query(`
            CREATE TABLE "notificationType" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" "public"."NOTIFICATION_TYPE_NAMES" NOT NULL,
                "label" character varying NOT NULL,
                CONSTRAINT "UQ_27a5a39904299d760196809bf1b" UNIQUE ("name"),
                CONSTRAINT "PK_7e00166d88e0f5d5d1473b9aa50" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_e6c07908af7af2e788d96dd0d1" ON "notificationType" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_27bc4ecd0b1ebf509c5e184120" ON "notificationType" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_27a5a39904299d760196809bf1" ON "notificationType" ("name")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_0309061df9612140b5d3ae111e" ON "notificationType" ("label")
        `);
        await queryRunner.query(`
            ALTER TABLE "notification"
            ADD "notificationTypeId" uuid NOT NULL
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_f86e31436f538bc4face386f39" ON "notification" ("notificationTypeId")
        `);
        await queryRunner.query(`
            ALTER TABLE "userNotificationType"
            ADD CONSTRAINT "FK_06d3affec058c7fa6c7976fa48e" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "userNotificationType"
            ADD CONSTRAINT "FK_c79271001a09377f80b0be5f7b0" FOREIGN KEY ("notificationTypeId") REFERENCES "notificationType"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "userNotificationType"
            ADD CONSTRAINT "FK_5eb2f399e6af5f41861f55a7e21" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "notificationType"
            ADD CONSTRAINT "FK_e6c07908af7af2e788d96dd0d1d" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "notification"
            ADD CONSTRAINT "FK_f86e31436f538bc4face386f39b" FOREIGN KEY ("notificationTypeId") REFERENCES "notificationType"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "notification" DROP CONSTRAINT "FK_f86e31436f538bc4face386f39b"
        `);
        await queryRunner.query(`
            ALTER TABLE "notificationType" DROP CONSTRAINT "FK_e6c07908af7af2e788d96dd0d1d"
        `);
        await queryRunner.query(`
            ALTER TABLE "userNotificationType" DROP CONSTRAINT "FK_5eb2f399e6af5f41861f55a7e21"
        `);
        await queryRunner.query(`
            ALTER TABLE "userNotificationType" DROP CONSTRAINT "FK_c79271001a09377f80b0be5f7b0"
        `);
        await queryRunner.query(`
            ALTER TABLE "userNotificationType" DROP CONSTRAINT "FK_06d3affec058c7fa6c7976fa48e"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_f86e31436f538bc4face386f39"
        `);
        await queryRunner.query(`
            ALTER TABLE "notification" DROP COLUMN "notificationTypeId"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_0309061df9612140b5d3ae111e"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_27a5a39904299d760196809bf1"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_27bc4ecd0b1ebf509c5e184120"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_e6c07908af7af2e788d96dd0d1"
        `);
        await queryRunner.query(`
            DROP TABLE "notificationType"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."NOTIFICATION_TYPE_NAMES"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_34a08f05907f0de589904e548b"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_5eb2f399e6af5f41861f55a7e2"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_c79271001a09377f80b0be5f7b"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_f238058ede7302a48601e56743"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_e590f9ba9e23210a8f8205ab48"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_06d3affec058c7fa6c7976fa48"
        `);
        await queryRunner.query(`
            DROP TABLE "userNotificationType"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."NOTIFICATION_DELIVERY_METHODS_NAMES"
        `);
    }

}
