import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1746992794274 implements MigrationInterface {
	name = 'Migration1746992794274';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            CREATE TABLE "batchCode" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "code" SERIAL NOT NULL,
                CONSTRAINT "UQ_8a2eb576861c0e91941a4c39f15" UNIQUE ("code"),
                CONSTRAINT "PK_b54756c6f56fb9f3dc2c70d2e76" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_616c3d893b25c48ad8247fd538" ON "batchCode" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_446aaa371fdee04174158ef17f" ON "batchCode" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_8a2eb576861c0e91941a4c39f1" ON "batchCode" ("code")
        `);
		await queryRunner.query(`
            CREATE TABLE "batchDefect" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "note" text NOT NULL DEFAULT '',
                "missing" integer NOT NULL DEFAULT '0',
                "batchId" uuid NOT NULL,
                CONSTRAINT "PK_6b9e31830c32f38616aabee4cb7" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_fb971fb371cab857d51ef80229" ON "batchDefect" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b8ce08291f6c6f2c0cf246f93e" ON "batchDefect" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_9d518d3f8b9db3e8aa91addd80" ON "batchDefect" ("batchId")
        `);
		await queryRunner.query(`
            CREATE TABLE "batchGroupCode" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "code" SERIAL NOT NULL,
                CONSTRAINT "UQ_fab7758250146bf6a5bdbfddb38" UNIQUE ("code"),
                CONSTRAINT "PK_0674c9cbd4560ff00ff0f50321a" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f598a413f6279e42130c5e4316" ON "batchGroupCode" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_73b49cfc290e1d05ea9c3dd0d6" ON "batchGroupCode" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_fab7758250146bf6a5bdbfddb3" ON "batchGroupCode" ("code")
        `);
		await queryRunner.query(`
            CREATE TABLE "batchGroup" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "codeId" uuid NOT NULL,
                CONSTRAINT "PK_6ae4b94b41e399f6b39b987f7d6" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_02e8a3b25a7d6cf87b1f0669f4" ON "batchGroup" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_2bd493c5ffe99d5cd2e1701467" ON "batchGroup" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_0303997ca11597c2f83ce4df31" ON "batchGroup" ("codeId")
        `);
		await queryRunner.query(`
            CREATE TABLE "productEnvelope" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" text NOT NULL DEFAULT '',
                "codeId" uuid NOT NULL,
                "productCategoryId" uuid NOT NULL,
                CONSTRAINT "PK_52bab803f8c52cf8388109a1f10" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_df3c045682423191ac201ff056" ON "productEnvelope" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_7bdb9c305840a30ed950c9f7bc" ON "productEnvelope" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_89fcce02c215fe35a2646a8bba" ON "productEnvelope" ("name")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_1dd9d207d27bcc9b066824ae99" ON "productEnvelope" ("codeId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f9377b0a0562618cdcdf7892ce" ON "productEnvelope" ("productCategoryId")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."ORDER_ITEM_TYPES" AS ENUM(
                'PRODUCT',
                'SHIPPING',
                'PAYMENT',
                'DISCOUNT',
                'SERVICE',
                'GIFT',
                'PRODUCT_SET'
            )
        `);
		await queryRunner.query(`
            CREATE TABLE "ecommerceOrderItem" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "type" "public"."ORDER_ITEM_TYPES" NOT NULL DEFAULT 'PRODUCT',
                "name" character varying NOT NULL DEFAULT '',
                "price" numeric(14, 4) NOT NULL DEFAULT '0',
                "shoptetItemIdentifier" integer NOT NULL DEFAULT '0',
                "productEnvelopeId" uuid,
                "ecommerceOrderId" uuid NOT NULL,
                "productId" uuid,
                CONSTRAINT "PK_3284db438fdc0904db074253e52" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_41c29efb4b30c4dd975bcf866d" ON "ecommerceOrderItem" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_cbd2a77a90cf0b9bf454e4692b" ON "ecommerceOrderItem" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_346eae8d0fee27200c51f0eb3a" ON "ecommerceOrderItem" ("type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_4bbc84191e01f3e760138ee32f" ON "ecommerceOrderItem" ("name")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b445c7040466a8cdc201b59ef2" ON "ecommerceOrderItem" ("shoptetItemIdentifier")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_3ab2501982ea6cd453d516e853" ON "ecommerceOrderItem" ("productEnvelopeId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_c02dd8381d68f069fcc5f651b4" ON "ecommerceOrderItem" ("ecommerceOrderId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_c6d281a26e222c4f7863a82009" ON "ecommerceOrderItem" ("productId")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."HTTP_METHODS_NAMES" AS ENUM('GET', 'POST', 'PUT', 'PATCH', 'DELETE')
        `);
		await queryRunner.query(`
            CREATE TABLE "action" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "txId" character varying NOT NULL DEFAULT ((pg_current_xact_id())),
                "method" "public"."HTTP_METHODS_NAMES",
                "endpoint" character varying NOT NULL,
                "description" text NOT NULL DEFAULT '',
                "authenticationId" uuid,
                CONSTRAINT "PK_2d9db9cf5edfbbae74eb56e3a39" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e71b65767b8c972fdc972c878f" ON "action" ("createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_022328d6ae0993411291b5b974" ON "action" ("txId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_5165a3df690967baa74c183197" ON "action" ("method")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ec10bd0dff5f5e6d7176f0f441" ON "action" ("endpoint")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_bf376bd23a893833bd63e3dd8a" ON "action" ("authenticationId")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SCOPE_NAMES" AS ENUM(
                'admin',
                'home',
                'batchRead',
                'batchWrite',
                'batchCheck',
                'batchDelivery',
                'productTest',
                'productTestLead',
                'testRead',
                'serviceRead',
                'serviceWrite',
                'warrantyClaimRead',
                'warrantyClaimWrite',
                'stock',
                'productRead',
                'productWrite',
                'orderRead',
                'orderWrite',
                'productAdmin'
            )
        `);
		await queryRunner.query(`
            CREATE TABLE "scope" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" "public"."SCOPE_NAMES" NOT NULL,
                "description" character varying NOT NULL DEFAULT '',
                CONSTRAINT "UQ_43d560104ffd3074475049468ce" UNIQUE ("name"),
                CONSTRAINT "PK_d3425631cbb370861a58c3e88c7" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_02c1be68a79d6ebcf0bc2ec03e" ON "scope" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_6e0859e36e5749cb799a71b276" ON "scope" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_43d560104ffd3074475049468c" ON "scope" ("name")
        `);
		await queryRunner.query(`
            CREATE TABLE "role" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" character varying NOT NULL,
                "description" character varying,
                "defaultUrl" character varying NOT NULL DEFAULT '',
                CONSTRAINT "PK_b36bcfe02fc8de3c57a8b2391c2" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_54aaa7ce0f59570c5ba5b68634" ON "role" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_a565c8bb3c45c044d9e2e2a1f9" ON "role" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ae4578dcaed5adff96595e6166" ON "role" ("name")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."AUTH_TYPES" AS ENUM('azure-ad', 'password', 'verificationCode')
        `);
		await queryRunner.query(`
            CREATE TABLE "authentication" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "type" "public"."AUTH_TYPES" NOT NULL,
                "credentials" json NOT NULL,
                "userId" uuid NOT NULL,
                "roleId" uuid NOT NULL,
                CONSTRAINT "PK_684fcb9924c8502d64b129cc8b1" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_be031dd812f7ce15ab2b3bcaff" ON "authentication" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_81daff1b812753e0aa6ffe960b" ON "authentication" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_2ce04c38ee386596885c903d9a" ON "authentication" ("userId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b0a8dd2baaddf18f44779c42d3" ON "authentication" ("roleId")
        `);
		await queryRunner.query(`
            CREATE TABLE "savedFilter" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" character varying NOT NULL,
                "endpoint" character varying NOT NULL,
                "value" json NOT NULL,
                "userId" uuid NOT NULL,
                CONSTRAINT "PK_d5255139c6985d0f11cf36c901d" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_691f0acc1b8d61c19419ac7969" ON "savedFilter" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_9096f5f427ad98ffb0b8991c19" ON "savedFilter" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_8d9427fd7cbe00ed9ecf771558" ON "savedFilter" ("name")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_9900d1d2ea97ce9539dfe535f6" ON "savedFilter" ("endpoint")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f1f29fb451206435201004e2f9" ON "savedFilter" ("userId")
        `);
		await queryRunner.query(`
            CREATE TABLE "shipmentCode" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "code" SERIAL NOT NULL,
                CONSTRAINT "UQ_b58e4286f406f669ee9114eb89c" UNIQUE ("code"),
                CONSTRAINT "PK_b73abdce1380e610edbdf5fcb9e" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_32a67f1b352a94437e165a71a7" ON "shipmentCode" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_215a8307229e4d743b2a2a0588" ON "shipmentCode" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b58e4286f406f669ee9114eb89" ON "shipmentCode" ("code")
        `);
		await queryRunner.query(`
            CREATE TABLE "shipmentProduct" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "shipmentId" uuid NOT NULL,
                "productId" uuid NOT NULL,
                CONSTRAINT "PK_1acfc694ab6cc26ccbfb529b4fb" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_841f4f9b72360cf0175b36d448" ON "shipmentProduct" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_164630d0591f938d6637a92d94" ON "shipmentProduct" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_045eef5ef2ba6937177cb7c077" ON "shipmentProduct" ("shipmentId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_2b8eca828d3cef5c1fe7cc1a82" ON "shipmentProduct" ("productId")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SHIPMENT_STATUS_NAMES" AS ENUM('NEW', 'DISPATCHED', 'DELIVERED')
        `);
		await queryRunner.query(`
            CREATE TABLE "shipment" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "status" "public"."SHIPMENT_STATUS_NAMES" NOT NULL DEFAULT 'NEW',
                "dispatchedAt" TIMESTAMP WITH TIME ZONE,
                "dispatchedById" uuid,
                "deliveredAt" TIMESTAMP WITH TIME ZONE,
                "addressId" uuid NOT NULL,
                "codeId" uuid NOT NULL,
                "contactId" uuid NOT NULL,
                CONSTRAINT "PK_f51f635db95c534ca206bf7a0a4" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_8e067a22d48f3c8cde09115993" ON "shipment" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_6a335e8e0cec3d4b585590a007" ON "shipment" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_75b5f089f72a5671a4f65efbcc" ON "shipment" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f7c7dd9bde72cbf95de3fc708a" ON "shipment" ("dispatchedAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_4feadbe728e249e7063db21453" ON "shipment" ("dispatchedById")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_a39d9ba392f61de135aa3c9bea" ON "shipment" ("deliveredAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_a92bc88b88e244afe28c01b2e3" ON "shipment" ("addressId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_05a6268a9d538679bd5beba5ff" ON "shipment" ("codeId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_7b10b04682400501934ef2e888" ON "shipment" ("contactId")
        `);
		await queryRunner.query(`
            CREATE TABLE "productWarehouseTask" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "productId" uuid NOT NULL,
                "warehouseTaskId" uuid NOT NULL,
                CONSTRAINT "PK_1a451d39a4e5b2fd79c430169f3" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b0f6f83e795787dba8e58ac11c" ON "productWarehouseTask" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d0424a8ea8686b6ce873060399" ON "productWarehouseTask" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_5a130222de59f6257f07a10f70" ON "productWarehouseTask" ("productId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ed047783973e2b0d8c81af0903" ON "productWarehouseTask" ("warehouseTaskId")
        `);
		await queryRunner.query(`
            CREATE TABLE "warehousePosition" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "description" character varying NOT NULL DEFAULT '',
                "sector" character varying,
                "rack" character varying,
                "shelf" character varying,
                "box" character varying,
                "warehouseId" uuid NOT NULL,
                CONSTRAINT "PK_2da830859d874d91169dea0c586" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_440a23c145854c073f551027a4" ON "warehousePosition" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_77de529d19bb639f8005b3a0c9" ON "warehousePosition" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_30e70ba38c62692f96ef5eebb2" ON "warehousePosition" ("sector")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_7c5f6c9b90c250d61717c56c8e" ON "warehousePosition" ("rack")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_a247c2db6f1343c4c610c0b24c" ON "warehousePosition" ("shelf")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_950458a6b7f21964aae97d20fa" ON "warehousePosition" ("box")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_370a4bc80d7524de2e27efc8c7" ON "warehousePosition" ("warehouseId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_65b310c10bcdd39d7deb458cfa" ON "warehousePosition" ("warehouseId", "sector")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_83b24c81bc659c92dd2d41bcad" ON "warehousePosition" ("warehouseId", "sector", "rack")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_c88d1f3fd8ceb318b29a9aba20" ON "warehousePosition" ("warehouseId", "sector", "rack", "shelf")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_c80baa915152bab0da69ce92c7" ON "warehousePosition" ("warehouseId", "sector", "rack", "shelf", "box")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_93d3feca5ac22210cb259bfa51" ON "warehousePosition" ("sector", "rack")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e33fe33c6c5004192525df5583" ON "warehousePosition" ("sector", "rack", "shelf")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e8a92c7116b1aa1d260fa774ac" ON "warehousePosition" ("sector", "rack", "shelf", "box")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."WAREHOUSE_TYPE_NAMES" AS ENUM(
                'STOCK',
                'EXPEDITION',
                'TESTING',
                'SERVICE',
                'WARRANTY_CLAIM'
            )
        `);
		await queryRunner.query(`
            CREATE TABLE "warehouse" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "description" character varying NOT NULL DEFAULT '',
                "name" character varying NOT NULL,
                "shoptetId" integer NOT NULL,
                "type" "public"."WAREHOUSE_TYPE_NAMES" NOT NULL DEFAULT 'STOCK',
                CONSTRAINT "PK_965abf9f99ae8c5983ae74ebde8" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_2281ad941d83f65a88cb0ac9c9" ON "warehouse" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_616aeeb0de4896928462a613d6" ON "warehouse" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d5d5470e55d4238b1239e9f154" ON "warehouse" ("name")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f765e9834b961ab2cb27fc6f99" ON "warehouse" ("shoptetId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d036b2db4cc472e34e013541d8" ON "warehouse" ("type")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."WAREHOUSE_TASK_TYPES" AS ENUM('SHIFT', 'PICKUP', 'STORE', 'INVENTORY')
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."WAREHOUSE_TASK_STATUSES" AS ENUM('OPEN', 'CLOSED')
        `);
		await queryRunner.query(`
            CREATE TABLE "warehouseTask" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "type" "public"."WAREHOUSE_TASK_TYPES" NOT NULL,
                "status" "public"."WAREHOUSE_TASK_STATUSES" NOT NULL DEFAULT 'OPEN',
                "priority" smallint NOT NULL DEFAULT '50',
                "sourceWarehouseId" uuid NOT NULL,
                "destinationWarehouseId" uuid NOT NULL,
                "userId" uuid,
                CONSTRAINT "PK_5e09b711fde525669661051a06c" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d192e157b7dcc827bb72759880" ON "warehouseTask" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d10490c32318c72fde4ada385d" ON "warehouseTask" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_bd58da3c2f99baf7a05c710953" ON "warehouseTask" ("type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b78e494b7c5e466d14c10871a8" ON "warehouseTask" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_a78dc43054c3058b87cf54e8a2" ON "warehouseTask" ("sourceWarehouseId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_6bb9960c2dbf3cdda9c67bda3c" ON "warehouseTask" ("destinationWarehouseId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_9eab14171403bdb72caf0d93f6" ON "warehouseTask" ("userId")
        `);
		await queryRunner.query(`
            CREATE TABLE "user" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "email" character varying,
                "name" character varying NOT NULL,
                "image" character varying,
                CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_519e6553e4e02eba80a0d3989d" ON "user" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_3acde74098c180846b5c5ab4ec" ON "user" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e12875dfb3b1d92d7d7c5377e2" ON "user" ("email")
        `);
		await queryRunner.query(`
            CREATE TABLE "productTest" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "deadlineAt" TIMESTAMP WITH TIME ZONE,
                "testedAt" TIMESTAMP WITH TIME ZONE,
                "testedById" uuid,
                "conditionalAttributeId" uuid,
                "productId" uuid,
                CONSTRAINT "REL_ee83ae7ce22733b0c97788c9ad" UNIQUE ("productId"),
                CONSTRAINT "PK_e0203707ed8e8331fecbd10eb58" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_6567fbcc5e9e64e4108b2448e4" ON "productTest" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ae6aca9a859343ab3dde3b2700" ON "productTest" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d00f3be831f0051ab8c58a7457" ON "productTest" ("deadlineAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_6f3774d9bdd459e658961cc042" ON "productTest" ("testedAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ee83ae7ce22733b0c97788c9ad" ON "productTest" ("productId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_2700f50ad501a3123f80c23fae" ON "productTest" ("testedById")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f75ee991be9073444ec68e3efe" ON "productTest" ("conditionalAttributeId")
        `);
		await queryRunner.query(`
            CREATE TABLE "conditionalAttribute" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "lock" boolean NOT NULL DEFAULT false,
                "attributeValueId" uuid NOT NULL,
                CONSTRAINT "PK_0dfe56346176056cbd2368dc709" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_5e2f956741b8e040af295fb1e4" ON "conditionalAttribute" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_259082c254419ee71277b72891" ON "conditionalAttribute" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_8f02ea9d90016ddf442471a5bb" ON "conditionalAttribute" ("attributeValueId")
        `);
		await queryRunner.query(`
            CREATE TABLE "attributeValueConditionalAttribute" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "attributeValueId" uuid NOT NULL,
                "conditionalAttributeId" uuid NOT NULL,
                CONSTRAINT "PK_10cd3b7c7a546153ed2fd602db6" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b9fc85880b7d9045283f0e703f" ON "attributeValueConditionalAttribute" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_6da1fab004046bf772e60a29a1" ON "attributeValueConditionalAttribute" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b642f3cfafcc199f9938600ef0" ON "attributeValueConditionalAttribute" ("attributeValueId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_46ba4af1bcf8a4f7dbe2aefa5a" ON "attributeValueConditionalAttribute" ("conditionalAttributeId")
        `);
		await queryRunner.query(`
            CREATE TABLE "recyclingFee" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "shoptetId" integer NOT NULL,
                "name" character varying NOT NULL,
                "price" numeric(14, 4) NOT NULL DEFAULT '0',
                "unit" character varying NOT NULL,
                "currencyId" uuid NOT NULL,
                CONSTRAINT "PK_7825f99a92bde8f1b1eb1b34535" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_c1445c0baecf77493db46798aa" ON "recyclingFee" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_5a7da1081796c93d28b12a2575" ON "recyclingFee" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_c83241c869b82f42977126d235" ON "recyclingFee" ("shoptetId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_bb7a71e0ccf3e0f2a4d5d0deca" ON "recyclingFee" ("name")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d660d6e0fe47862abd02aa263a" ON "recyclingFee" ("currencyId")
        `);
		await queryRunner.query(`
            CREATE TABLE "shoptetCategory" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "shoptetId" uuid NOT NULL,
                "name" character varying NOT NULL,
                "linkName" character varying,
                "uri" character varying,
                CONSTRAINT "PK_8e702a62dfacbf9930ed6396135" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_465e3f8671567c587d71a61e4b" ON "shoptetCategory" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_8362d3d810338525e0160644a8" ON "shoptetCategory" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_7793587f7539dad68c1d68f1b2" ON "shoptetCategory" ("shoptetId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_eeae297b4edda040d013323dc3" ON "shoptetCategory" ("name")
        `);
		await queryRunner.query(`
            CREATE TABLE "productCategory" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" character varying NOT NULL,
                "margin" real NOT NULL DEFAULT '0',
                "codePrefix" character varying NOT NULL DEFAULT '',
                "minimumTestPhotos" smallint NOT NULL DEFAULT '1',
                "recyclingFeeId" uuid,
                "shoptetCategoryId" uuid,
                "parentId" uuid,
                CONSTRAINT "PK_1012430e55dad863919f1221a72" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_a7ac5151122c715f8b1f286f73" ON "productCategory" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ca32e610aa6625219fe3547178" ON "productCategory" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_8f2bbb7ead10e637c55e0c86b1" ON "productCategory" ("name")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e2a843ecd0d13aed187a1ae527" ON "productCategory" ("codePrefix")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_471a63c1df20127313ee451e93" ON "productCategory" ("recyclingFeeId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_0c550d237dcfea9fe6913a34ff" ON "productCategory" ("shoptetCategoryId")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_CATEGORY_ATTRIBUTE_TYPE" AS ENUM(
                'default',
                'testing',
                'compare',
                'mandatory',
                'envelope',
                'name',
                'export',
                'list',
                'stock'
            )
        `);
		await queryRunner.query(`
            CREATE TABLE "productCategoryAttribute" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "type" "public"."PRODUCT_CATEGORY_ATTRIBUTE_TYPE" NOT NULL,
                "sequence" integer NOT NULL,
                "attributeId" uuid NOT NULL,
                "productCategoryId" uuid NOT NULL,
                CONSTRAINT "PK_142cddade5e80c232eefd4a0ad6" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_519deb886e0e451a1edb6209c0" ON "productCategoryAttribute" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b1bb75f893729d164ad5f1a4f5" ON "productCategoryAttribute" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_8a85400fbd46b60b0eef60671e" ON "productCategoryAttribute" ("type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f00b50f6dab8c094ccdcfd1274" ON "productCategoryAttribute" ("sequence")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_15b3a7397e68a752de73fcded3" ON "productCategoryAttribute" ("attributeId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_86f9acfe9dea6f7c0a96faa949" ON "productCategoryAttribute" ("productCategoryId")
        `);
		await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_3f0c1f090a9558aad1abb776c2" ON "productCategoryAttribute" ("type", "attributeId", "productCategoryId")
        `);
		await queryRunner.query(`
            CREATE TABLE "productEnvelopeAttributeValue" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "attributeValueId" uuid NOT NULL,
                "productEnvelopeId" uuid NOT NULL,
                CONSTRAINT "PK_5c2f2ba4e05ddf6e6b68cab28d2" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_087294b59b4b45f11a64c22dd5" ON "productEnvelopeAttributeValue" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_a2aa943fcf1690244c59898756" ON "productEnvelopeAttributeValue" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f49549471d70fc9380c35d92c5" ON "productEnvelopeAttributeValue" ("attributeValueId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b833891e239c3162c742dedc4e" ON "productEnvelopeAttributeValue" ("productEnvelopeId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_a2f3eb862794c7cf2fccfa4e43" ON "productEnvelopeAttributeValue" ("attributeValueId", "productEnvelopeId")
        `);
		await queryRunner.query(`
            CREATE TABLE "vendorDefectType" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "defectTypeId" uuid NOT NULL,
                "vendorId" uuid NOT NULL,
                CONSTRAINT "PK_1a6836ee5beb9e42cfdf011ef41" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_a0c81ed8df4cb6ab6a55c691f6" ON "vendorDefectType" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_9c653f4f63b4590b8c9fed8416" ON "vendorDefectType" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ed34c68375c8ea12d442831af3" ON "vendorDefectType" ("defectTypeId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_de4d8709c72f874398bd31ba0c" ON "vendorDefectType" ("vendorId")
        `);
		await queryRunner.query(`
            CREATE TABLE "defectType" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" character varying NOT NULL,
                CONSTRAINT "PK_f6d55a3b44f8ccfdc0b9031f8f0" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_2112482b7cea59d055fb2c57a9" ON "defectType" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f9fd163fd6b24a2677b7e9a2c8" ON "defectType" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_20e994fe6d46d90bf3017494ed" ON "defectType" ("name")
        `);
		await queryRunner.query(`
            CREATE TABLE "warrantyClaimCode" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "code" SERIAL NOT NULL,
                CONSTRAINT "UQ_76654d8dc74fd868e1e3f1a260e" UNIQUE ("code"),
                CONSTRAINT "PK_ddd44c48f96d8c89bf22926c38c" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_a3b2e371af7d019f91efd42754" ON "warrantyClaimCode" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_768205d59e10defe68c5fde4be" ON "warrantyClaimCode" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_76654d8dc74fd868e1e3f1a260" ON "warrantyClaimCode" ("code")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."WARRANTY_CLAIM_STATUS_NAMES" AS ENUM(
                'NEW',
                'WAITING_FOR_VENDOR',
                'SENT_TO_VENDOR',
                'WAITING_FOR_RETURN',
                'CLOSED',
                'REJECTED',
                'REFUNDED',
                'DISCOUNT',
                'RESOLVED_BY_VENDOR',
                'TRADED_PIECE',
                'WAITING_FOR_DISCOUNT',
                'WAITING_FOR_REPAIR'
            )
        `);
		await queryRunner.query(`
            CREATE TABLE "warrantyClaim" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "createdById" uuid,
                "status" "public"."WARRANTY_CLAIM_STATUS_NAMES" NOT NULL DEFAULT 'NEW',
                "note" text NOT NULL DEFAULT '',
                "trackingCode" character varying NOT NULL DEFAULT '',
                "vendorRMAIdentifier" character varying NOT NULL DEFAULT '',
                "codeId" uuid NOT NULL,
                CONSTRAINT "PK_4c2d671bd1940152d728df3637d" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f3b66e9640ebc9a340188b7d70" ON "warrantyClaim" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d6128fa8c966887d489f3a6cb1" ON "warrantyClaim" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_0f658163bd3bbe76d1c0818403" ON "warrantyClaim" ("createdById")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_0a9de6455f156ee46012df4a18" ON "warrantyClaim" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_cde3cec9ae4f04709bb4835623" ON "warrantyClaim" ("codeId")
        `);
		await queryRunner.query(`
            CREATE TABLE "productDefect" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "note" text NOT NULL DEFAULT '',
                "productId" uuid NOT NULL,
                "defectTypeId" uuid NOT NULL,
                "serviceTaskId" uuid,
                "serviceCaseId" uuid,
                "warrantyClaimId" uuid,
                CONSTRAINT "PK_6908ee1778b8c10278a2fc4015d" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_1026a5f82fb6534eea7a7a49ab" ON "productDefect" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_7625336d6bc2aabeee8f6af3ea" ON "productDefect" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_0726ee040109cbafa3bbe7784a" ON "productDefect" ("productId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_410a4f9077a3b113d9906913d0" ON "productDefect" ("defectTypeId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_72ecb7c1727c251c64bf29fa33" ON "productDefect" ("serviceTaskId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_4cecdb1f5e6942dfbea66cda9b" ON "productDefect" ("serviceCaseId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_16846f861b619f1d3315e608a1" ON "productDefect" ("warrantyClaimId")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_TASK_STATUS_TYPES" AS ENUM('NEW', 'CLOSED')
        `);
		await queryRunner.query(`
            CREATE TABLE "productTask" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "note" character varying NOT NULL DEFAULT '',
                "status" "public"."PRODUCT_TASK_STATUS_TYPES" NOT NULL DEFAULT 'NEW',
                "price" numeric(14, 4) NOT NULL DEFAULT '0',
                "productId" uuid NOT NULL,
                "serviceTaskTypeId" uuid NOT NULL,
                CONSTRAINT "PK_748a7d75eee8acce2574047b338" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_630fd38f02c33fe57c3160e7b4" ON "productTask" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_89e81cc1e2fcb0206f2b7d3623" ON "productTask" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_2ae70c935c09dd3d3bbffad48e" ON "productTask" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_1187d6c76d8664b13c0a8b459b" ON "productTask" ("productId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d398a30d5be2a6220983a4f44e" ON "productTask" ("serviceTaskTypeId")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SERVICE_TASK_TYPE_NAMES" AS ENUM('TESTING', 'SERVICE')
        `);
		await queryRunner.query(`
            CREATE TABLE "serviceTaskType" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" character varying NOT NULL,
                "price" numeric(14, 4) NOT NULL DEFAULT '0',
                "type" "public"."SERVICE_TASK_TYPE_NAMES" array NOT NULL DEFAULT '{SERVICE}',
                CONSTRAINT "PK_20bbea0fb5c43e733b01c089565" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_911a25040b79f5ddff739d4e0e" ON "serviceTaskType" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_78da752d3eb715a15009fb6796" ON "serviceTaskType" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_eaff97695cabb878925f446151" ON "serviceTaskType" ("name")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e4be1d81ab5328d61240e59397" ON "serviceTaskType" ("type")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SERVICE_TASK_STATUS_TYPES" AS ENUM('NEW', 'CLOSED')
        `);
		await queryRunner.query(`
            CREATE TABLE "serviceTask" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "note" character varying NOT NULL DEFAULT '',
                "status" "public"."SERVICE_TASK_STATUS_TYPES" NOT NULL DEFAULT 'NEW',
                "price" numeric(14, 4) NOT NULL DEFAULT '0',
                "serviceTaskTypeId" uuid NOT NULL,
                CONSTRAINT "PK_1647757c3234cee1b55f30c9096" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e2c39c3d9860c6836d44d356c7" ON "serviceTask" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_31b0b0cdb765f68d914d3cda61" ON "serviceTask" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e6c48e0b90475e2c27b8543933" ON "serviceTask" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_cd84eaf504186bee6cbf09327c" ON "serviceTask" ("serviceTaskTypeId")
        `);
		await queryRunner.query(`
            CREATE TABLE "shoptetAttribute" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "shoptetId" integer NOT NULL,
                "code" character varying NOT NULL,
                "name" character varying NOT NULL,
                CONSTRAINT "PK_a70803cf65b87b05bf409569061" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_02088f54d81c96564d70224fce" ON "shoptetAttribute" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_cd15bcdf1fb75555fa43267da7" ON "shoptetAttribute" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_88a6275de9c1b72c352d0582be" ON "shoptetAttribute" ("shoptetId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_c1372118f41d1a8307bbe899b3" ON "shoptetAttribute" ("code")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_a68bf2ba56d2f343b2fd118653" ON "shoptetAttribute" ("name")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_ATTRIBUTE_DATATYPE" AS ENUM(
                'boolean',
                'number',
                'string',
                'text',
                'multiselect'
            )
        `);
		await queryRunner.query(`
            CREATE TABLE "attribute" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" character varying NOT NULL,
                "displayName" character varying NOT NULL,
                "note" text NOT NULL DEFAULT '',
                "unit" character varying NOT NULL DEFAULT '',
                "dataType" "public"."PRODUCT_ATTRIBUTE_DATATYPE" NOT NULL DEFAULT 'string',
                "forbiddenNewValues" boolean NOT NULL DEFAULT false,
                "textareaField" boolean NOT NULL DEFAULT false,
                "trusted" boolean NOT NULL DEFAULT false,
                "shoptetAttributeId" uuid,
                CONSTRAINT "PK_b13fb7c5c9e9dff62b60e0de729" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_fe79dbcbfc2a46ff35aba76050" ON "attribute" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_5f6cf10ee3642d9d9a444d73fb" ON "attribute" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_350fb4f7eb87e4c7d35c97a982" ON "attribute" ("name")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d0769b42012663c9e56df3c876" ON "attribute" ("displayName")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_53a371f596b5bb848b02482929" ON "attribute" ("shoptetAttributeId")
        `);
		await queryRunner.query(`
            CREATE TABLE "attributeNameAlternative" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" character varying NOT NULL,
                "note" character varying NOT NULL DEFAULT '',
                "parentId" uuid NOT NULL,
                CONSTRAINT "PK_8c0df4a4c7df1bc303ac8c2e050" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_33fc742d7b5edff5c4e8a65dd1" ON "attributeNameAlternative" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d8e74dc0c0086c65cf79d0a005" ON "attributeNameAlternative" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b205c0cf8a0a166adf857ef648" ON "attributeNameAlternative" ("name")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e0c25dc6a196d051fb78810943" ON "attributeNameAlternative" ("name", "parentId")
        `);
		await queryRunner.query(`
            CREATE TABLE "attributeValue" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "value" jsonb NOT NULL,
                "temporary" boolean NOT NULL DEFAULT false,
                "shoptetId" character varying,
                "attributeId" uuid NOT NULL,
                CONSTRAINT "PK_2d6c05ea0d7f3282295ab912625" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_855bf07c5b4f9a00f3523161bf" ON "attributeValue" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_825d94c1fd0aa70cd3a045a74a" ON "attributeValue" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_4f4349b05760a6d94041cd5e88" ON "attributeValue" ("value")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_5120c24e8f99359551e4387011" ON "attributeValue" ("temporary")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_5f7bc12e31ee2541e76bb9ecc6" ON "attributeValue" ("shoptetId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_34920d38b502e1a4970960ebdd" ON "attributeValue" ("attributeId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_207b389edc2145dfe130c5179d" ON "attributeValue" ("value", "attributeId")
        `);
		await queryRunner.query(`
            CREATE TABLE "attributeValueAlternative" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "value" jsonb NOT NULL,
                "parentId" uuid NOT NULL,
                CONSTRAINT "PK_5d4423b15d996b48d9a15182762" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_bdd5ca2e2f7951a257f91f9de8" ON "attributeValueAlternative" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b5248cba6dbe72badb32092ad2" ON "attributeValueAlternative" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_95661c33dab914746ca0c4468e" ON "attributeValueAlternative" ("value")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_0d80a1af74d132534671602801" ON "attributeValueAlternative" ("value", "parentId")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_ATTRIBUTE_VALUE_TYPE" AS ENUM('import', 'reader', 'resolved', 'service')
        `);
		await queryRunner.query(`
            CREATE TABLE "productAttributeValue" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "type" "public"."PRODUCT_ATTRIBUTE_VALUE_TYPE" NOT NULL,
                "autofill" boolean NOT NULL DEFAULT false,
                "lock" boolean NOT NULL DEFAULT false,
                "attributeValueId" uuid NOT NULL,
                "productId" uuid NOT NULL,
                CONSTRAINT "PK_ff5dbaee6144be22d321be145a7" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_bee9eede0cf860da5f31fba446" ON "productAttributeValue" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_0152b010228155e93798891f0d" ON "productAttributeValue" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d0bc51d992a144a431fe7d4240" ON "productAttributeValue" ("type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_50263a83197641fde835bc65db" ON "productAttributeValue" ("autofill")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_dd8fefce9266128eb7dec80b4d" ON "productAttributeValue" ("lock")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_91b5f4480d6c6939353f4be82d" ON "productAttributeValue" ("attributeValueId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d0262d0e56d9f5125a63384402" ON "productAttributeValue" ("productId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e39e787c48fdc59c63c9f42b9e" ON "productAttributeValue" ("type", "productId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_a29d4249658889a8180fa487c2" ON "productAttributeValue" ("type", "attributeValueId")
        `);
		await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_5fdd5b0fee8ce1af4f2a89c772" ON "productAttributeValue" ("type", "attributeValueId", "productId")
        `);
		await queryRunner.query(`
            CREATE TABLE "productCode" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "code" SERIAL NOT NULL,
                "batchId" uuid,
                CONSTRAINT "UQ_12ffc5b1c8ad013c86be7d6c3f6" UNIQUE ("code"),
                CONSTRAINT "PK_41799af8b5c7d6e97aedb407fff" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_101c0733ba57394c8479656a0c" ON "productCode" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_835bfff37d69a79fdb82129255" ON "productCode" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_12ffc5b1c8ad013c86be7d6c3f" ON "productCode" ("code")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_71998f7a83b18d7361d63f989f" ON "productCode" ("batchId")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."WARRANTY_TYPES" AS ENUM('BUY', 'SELL', 'SERVICE')
        `);
		await queryRunner.query(`
            CREATE TABLE "warranty" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "type" "public"."WARRANTY_TYPES" NOT NULL,
                "expiredAt" TIMESTAMP WITH TIME ZONE NOT NULL,
                "productId" uuid NOT NULL,
                CONSTRAINT "PK_33943757e620d2c5a0f143ad22e" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_63830a7d5015ddc2ff895964bc" ON "warranty" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_35731e9056378d0f168dfb32c0" ON "warranty" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_8b864b2bd249bc1c0a0a5d5f26" ON "warranty" ("type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ff626b061a1b4a0866b3654342" ON "warranty" ("expiredAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f9340c4297884c58c2da3cc35a" ON "warranty" ("productId")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_STATUS_NAMES" AS ENUM(
                'AT_SUPPLIER',
                'ON_THE_WAY',
                'TO_CHECK',
                'TO_CHECK_SN',
                'TO_TEST',
                'TESTED',
                'MISSING',
                'SERVICE',
                'WARRANTY_CLAIM',
                'STOCK',
                'FOR_SALE',
                'RESERVED',
                'SOLD',
                'DEAD',
                'AUTOPSY',
                'WAITING_FOR_SHIPMENT',
                'SHIPMENT_DISPATCHED',
                'SHIPMENT_DELIVERED'
            )
        `);
		await queryRunner.query(`
            CREATE TABLE "product" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "codeId" uuid,
                "sn" character varying NOT NULL DEFAULT '',
                "pastSn" character varying NOT NULL DEFAULT '',
                "status" "public"."PRODUCT_STATUS_NAMES" NOT NULL DEFAULT 'AT_SUPPLIER',
                "productEnvelopeId" uuid,
                "productEnvelopeAssignedAt" TIMESTAMP WITH TIME ZONE,
                "productEnvelopeAssignedById" uuid,
                "batchId" uuid,
                "productCategoryId" uuid,
                "warehousePositionId" uuid,
                CONSTRAINT "PK_bebc9158e480b949565b4dc7a82" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ca058ed10f56f646c6e63c4af5" ON "product" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_3318e5ecf2f04ba0d4c3d21e0e" ON "product" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b916c32dfcf46f001167e6f903" ON "product" ("codeId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_22cc99fb1491212f1206ee39d1" ON "product" ("sn")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_01286e06a0554cbb19375f0178" ON "product" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_483ff5648459d1d4d2270c2dd2" ON "product" ("productEnvelopeId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_6c25e1fc008d926c3504c3c639" ON "product" ("productEnvelopeAssignedById")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_741c8e6d9b2a19dddb96e8d620" ON "product" ("batchId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_618194d24a7ea86a165d7ec628" ON "product" ("productCategoryId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b0bfdff74ca3b7aa3545fefbba" ON "product" ("warehousePositionId")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_PRICE_TYPES" AS ENUM(
                'BUY',
                'DELIVERY',
                'SELL',
                'RECOMMENDED',
                'STANDARD',
                'DISCOUNT',
                'SERVICE'
            )
        `);
		await queryRunner.query(`
            CREATE TABLE "productPrice" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "value" numeric(14, 4) NOT NULL DEFAULT '0',
                "type" "public"."PRODUCT_PRICE_TYPES" NOT NULL,
                "currencyRateId" uuid,
                "productId" uuid NOT NULL,
                CONSTRAINT "PK_37562079a8b1d24df7bfd9bcfa2" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_5b791c5b2667f0d24eb0b0cf94" ON "productPrice" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_1bc7d451790c8bdc516d030679" ON "productPrice" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_97c2d49b66dc70ca1a43ad1009" ON "productPrice" ("type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_0d5c144b05ecf58534900027a0" ON "productPrice" ("currencyRateId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_a7c24be296a22f842f2fb5b0c4" ON "productPrice" ("productId")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SUPPORTED_CURRENCIES" AS ENUM('EUR', 'USD', 'GBP', 'CZK')
        `);
		await queryRunner.query(`
            CREATE TABLE "currency" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" character varying NOT NULL,
                "code" "public"."SUPPORTED_CURRENCIES" NOT NULL,
                CONSTRAINT "UQ_723472e41cae44beb0763f4039c" UNIQUE ("code"),
                CONSTRAINT "PK_3cda65c731a6264f0e444cc9b91" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_cf12ebf939da431c80cc3d88d9" ON "currency" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_fd52399f8d4f56f2ea5215d1ba" ON "currency" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_77f11186dd58a8d87ad5fff024" ON "currency" ("name")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_723472e41cae44beb0763f4039" ON "currency" ("code")
        `);
		await queryRunner.query(`
            CREATE TABLE "currencyRate" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "rate" real NOT NULL,
                "source" character varying NOT NULL,
                "currencyId" uuid NOT NULL,
                CONSTRAINT "PK_86dd7e4855b331b5eb2c7d019f9" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b1b7a8bb508d9ed48e779d1729" ON "currencyRate" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d3442911e9c9cc656d24d2621c" ON "currencyRate" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_887f1c7d857df6c2a956306e83" ON "currencyRate" ("currencyId")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."BATCH_DELIVERY_TYPE_NAMES" AS ENUM('VENDOR', 'OWN')
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."BATCH_STATUS_NAMES" AS ENUM(
                'IMPORTING',
                'AT_SUPPLIER',
                'ON_THE_WAY',
                'TO_CHECK',
                'TO_CHECK_SN',
                'TO_TEST',
                'MARKETING',
                'CLOSED'
            )
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PAYMENT_STATUS_NAMES" AS ENUM('INITIATED', 'PAID', 'UNPAID')
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."BATCH_TYPES" AS ENUM('NEW', 'USED')
        `);
		await queryRunner.query(`
            CREATE TABLE "batch" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "createdById" uuid,
                "checkedAt" TIMESTAMP WITH TIME ZONE,
                "checkedById" uuid,
                "checkedNote" text NOT NULL DEFAULT '',
                "checkedSnAt" TIMESTAMP WITH TIME ZONE,
                "checkedSnById" uuid,
                "checkedSnNote" text NOT NULL DEFAULT '',
                "checkSn" boolean NOT NULL DEFAULT false,
                "deadlineAt" TIMESTAMP WITH TIME ZONE,
                "deliveredAt" TIMESTAMP WITH TIME ZONE,
                "deliveredById" uuid,
                "deliveredNote" text NOT NULL DEFAULT '',
                "deliveryPrice" numeric(14, 4) NOT NULL DEFAULT '0',
                "deliveryType" "public"."BATCH_DELIVERY_TYPE_NAMES" NOT NULL DEFAULT 'OWN',
                "eta" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "name" text NOT NULL DEFAULT '',
                "note" text NOT NULL DEFAULT '',
                "pallets" smallint NOT NULL DEFAULT '0',
                "priority" smallint NOT NULL DEFAULT '50',
                "status" "public"."BATCH_STATUS_NAMES" NOT NULL DEFAULT 'AT_SUPPLIER',
                "paymentStatus" "public"."PAYMENT_STATUS_NAMES" NOT NULL DEFAULT 'UNPAID',
                "testedAt" TIMESTAMP WITH TIME ZONE,
                "testedById" uuid,
                "trackingCode" text NOT NULL DEFAULT '',
                "type" "public"."BATCH_TYPES" NOT NULL DEFAULT 'USED',
                "variableSymbol" text NOT NULL DEFAULT '',
                "warranty" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "codeId" uuid NOT NULL,
                "batchGroupId" uuid NOT NULL,
                "vendorId" uuid,
                "currencyRateId" uuid NOT NULL,
                CONSTRAINT "PK_57da3b830b57bec1fd329dcaf43" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_4f65e1770db73cefd4f74521ba" ON "batch" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_bf0e1ab880d958feaf6f275adb" ON "batch" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f7a1a202027c65b1bab794276a" ON "batch" ("createdById")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f6c147317a665322911d424966" ON "batch" ("checkedAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ec6dc77febfea9330cd988c922" ON "batch" ("checkedById")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_4a34b09ccdf00646a32e8f0109" ON "batch" ("checkedSnAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_2204328354d1b711ec7368cbc5" ON "batch" ("checkedSnById")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_7db1309021918672e7644b4882" ON "batch" ("deadlineAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ca0d3265cb46e137fb02a920d9" ON "batch" ("deliveredAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_bb08f81affbcaa5f81cfce6c7c" ON "batch" ("deliveredById")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_cfea8cb6e387bc8738a456865b" ON "batch" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_be3e6701efbb9b01daa4530f21" ON "batch" ("testedAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_8c26b473113c76ab9aa7fcc088" ON "batch" ("testedById")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d1adc6635d4bdb17fd6e60532c" ON "batch" ("codeId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e28ec0b4b5960fdee5917cb215" ON "batch" ("batchGroupId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_571fa9d810a9f4b1b653a56f01" ON "batch" ("vendorId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_60f72cccb8a36ceee891d0d19d" ON "batch" ("currencyRateId")
        `);
		await queryRunner.query(`
            CREATE TABLE "file" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "mime" character varying NOT NULL,
                "name" character varying NOT NULL,
                "note" text NOT NULL DEFAULT '',
                "preview" text NOT NULL DEFAULT '',
                "photoSessionId" uuid,
                CONSTRAINT "PK_36b46d232307066b3a2c9ea3a1d" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_9791834a3e29ecc4a4c4cc2458" ON "file" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f0f0fde52aebd504b31cbb9839" ON "file" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_df16ff3255e6dfc777b086949b" ON "file" ("name")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."BATCH_FILE_TYPE" AS ENUM('import', 'delivery', 'regular')
        `);
		await queryRunner.query(`
            CREATE TABLE "fileBatch" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "fileId" uuid NOT NULL,
                "batchId" uuid NOT NULL,
                "sequence" integer NOT NULL DEFAULT '0',
                "type" "public"."BATCH_FILE_TYPE" NOT NULL DEFAULT 'regular',
                CONSTRAINT "PK_e39cfcfd6e6cb4f68b800e099f5" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_fa87fcda7a6c576ee77d4c20a6" ON "fileBatch" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_0988e23fe31365673c1e785288" ON "fileBatch" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_9956ac5374cbbe872fdd203ae2" ON "fileBatch" ("fileId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ed44b23cf3d41e412276ab4100" ON "fileBatch" ("batchId")
        `);
		await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_0eef81e5644d20dbb36c687809" ON "fileBatch" ("fileId", "batchId")
        `);
		await queryRunner.query(`
            CREATE TABLE "fileBatchDefect" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "fileId" uuid NOT NULL,
                "batchDefectId" uuid NOT NULL,
                "sequence" integer NOT NULL DEFAULT '0',
                CONSTRAINT "PK_bf30fb6b5a582faacf9234fa978" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_dd089bc241c5db050f715a6894" ON "fileBatchDefect" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b3766f78a3e56635fd74c7df2f" ON "fileBatchDefect" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_02d1f430ba5798509c71605f09" ON "fileBatchDefect" ("fileId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_79c653d66b118788fee2a3a876" ON "fileBatchDefect" ("batchDefectId")
        `);
		await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_9a8b261803bdc14cd338ea224f" ON "fileBatchDefect" ("fileId", "batchDefectId")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_FILE_TYPE" AS ENUM('import', 'reader', 'regular')
        `);
		await queryRunner.query(`
            CREATE TABLE "fileProduct" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "fileId" uuid NOT NULL,
                "productId" uuid NOT NULL,
                "sequence" integer NOT NULL DEFAULT '0',
                "type" "public"."PRODUCT_FILE_TYPE" NOT NULL DEFAULT 'regular',
                CONSTRAINT "PK_00b908aaa27c31a3277b63d38ac" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_86ff2d0d44bbd581f85cae8d46" ON "fileProduct" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_5d5ac6eb0d91c1a877be9c3cc7" ON "fileProduct" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_fc7059771a2d42fa8bee797db5" ON "fileProduct" ("fileId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_50b298612e5f1b60c3fd9295df" ON "fileProduct" ("productId")
        `);
		await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_36d8522edf0a8014e3be1580e4" ON "fileProduct" ("fileId", "productId")
        `);
		await queryRunner.query(`
            CREATE TABLE "fileProductDefect" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "fileId" uuid NOT NULL,
                "productDefectId" uuid NOT NULL,
                "sequence" integer NOT NULL DEFAULT '0',
                CONSTRAINT "PK_18241c6aa2712449b98d6ea1069" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_66174b8e07740d83666c7401f0" ON "fileProductDefect" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_9fa0bb8b51f99c6f1a8cec47c0" ON "fileProductDefect" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_55d354db4573f896138fa630f5" ON "fileProductDefect" ("fileId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_734f16fa31dd2fa097368e83ca" ON "fileProductDefect" ("productDefectId")
        `);
		await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_8baf1e2f109f9509a415427a74" ON "fileProductDefect" ("fileId", "productDefectId")
        `);
		await queryRunner.query(`
            CREATE TABLE "fileServiceCase" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "fileId" uuid NOT NULL,
                "serviceCaseId" uuid NOT NULL,
                "sequence" integer NOT NULL DEFAULT '0',
                CONSTRAINT "PK_c61652733582b030c2266bc4635" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_71da6d2305f2fa5c7008cb9c4c" ON "fileServiceCase" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_48c5d13c2837bdcc619d2d1fb3" ON "fileServiceCase" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_bee814cc3058a95c1bc52992b7" ON "fileServiceCase" ("fileId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_3592b6a8129b7c62adcc7559f6" ON "fileServiceCase" ("serviceCaseId")
        `);
		await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_a63b7bc29dc722ed6fd185ac9f" ON "fileServiceCase" ("fileId", "serviceCaseId")
        `);
		await queryRunner.query(`
            CREATE TABLE "fileVendor" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "fileId" uuid NOT NULL,
                "vendorId" uuid NOT NULL,
                "sequence" integer NOT NULL DEFAULT '0',
                CONSTRAINT "PK_7b3f370c603d51042f901ec6a32" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_850104e91f4965cc55432f4598" ON "fileVendor" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_dc5bee18dfcb2471bd5d14091e" ON "fileVendor" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_0603eb633f19038692853c85f6" ON "fileVendor" ("fileId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_79db0d546f7e6aead109cebcfb" ON "fileVendor" ("vendorId")
        `);
		await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_bbfdaf9b8ad0791ad290b82227" ON "fileVendor" ("fileId", "vendorId")
        `);
		await queryRunner.query(`
            CREATE TABLE "fileWarrantyClaim" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "fileId" uuid NOT NULL,
                "warrantyClaimId" uuid NOT NULL,
                "sequence" integer NOT NULL DEFAULT '0',
                CONSTRAINT "PK_23032c0a18dcde64277a1688243" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_28b4944a40bd4438056c1c18c8" ON "fileWarrantyClaim" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ec69e14c72e132b48c445548d1" ON "fileWarrantyClaim" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_8af67ac221d6b39e55ef79462f" ON "fileWarrantyClaim" ("fileId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_4c387987a0fcb34bcb53c4616b" ON "fileWarrantyClaim" ("warrantyClaimId")
        `);
		await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_ca98b11852c3c677c8b941f880" ON "fileWarrantyClaim" ("fileId", "warrantyClaimId")
        `);
		await queryRunner.query(`
            CREATE TABLE "serviceCaseCode" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "code" SERIAL NOT NULL,
                CONSTRAINT "UQ_cfa6d3cd03f6d90ef290fe01ef7" UNIQUE ("code"),
                CONSTRAINT "PK_ec2935504978bfa9b28164e215c" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_dcf766b7a4bb0040cc6dd7649a" ON "serviceCaseCode" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_6de68e3b39244155b12fa6d6e5" ON "serviceCaseCode" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_cfa6d3cd03f6d90ef290fe01ef" ON "serviceCaseCode" ("code")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SERVICE_CASE_STATUS_NAMES" AS ENUM(
                'NEW',
                'ASSIGNED_TO_INTERNAL_SERVICE',
                'ASSIGNED_TO_EXTERNAL_SERVICE',
                'SENT_TO_SERVICE_CENTER',
                'OFFER_RECEIVED',
                'WAITING_FOR_REPAIR',
                'OFFER_REJECTED',
                'WAITING_FOR_RETURN',
                'WAITING_FOR_PRODUCT',
                'WAITING_FOR_BUYBACK',
                'CLOSED'
            )
        `);
		await queryRunner.query(`
            CREATE TABLE "serviceCase" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "createdById" uuid,
                "status" "public"."SERVICE_CASE_STATUS_NAMES" NOT NULL DEFAULT 'NEW',
                "note" text NOT NULL DEFAULT '',
                "priority" smallint NOT NULL DEFAULT '50',
                "trackingCode" character varying NOT NULL DEFAULT '',
                "serviceCenterId" uuid,
                "codeId" uuid NOT NULL,
                CONSTRAINT "PK_80af02bbdded17ac3806d73a2cf" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_c3a31132a7651477f7a13d1ff8" ON "serviceCase" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_cb7983f7d3d3d6c9a5d00d724f" ON "serviceCase" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_1a244b9dd4ebbe12600a356226" ON "serviceCase" ("createdById")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_770dcccb11dfad0db3c1eda33c" ON "serviceCase" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_2d124c1e9790625a90f96b5c89" ON "serviceCase" ("serviceCenterId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e6aaf77baf8701eff86e71d7a9" ON "serviceCase" ("codeId")
        `);
		await queryRunner.query(`
            CREATE TABLE "serviceCenter" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" character varying NOT NULL,
                "note" text NOT NULL DEFAULT '',
                "addressId" uuid NOT NULL,
                "contactId" uuid NOT NULL,
                CONSTRAINT "PK_138b2bc1bbf721bbdf77907c777" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_4f3f798b21534a5b63c2fa6e92" ON "serviceCenter" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_46631ee702dd09e8d54f9a23ad" ON "serviceCenter" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_3aeca6e65c6b952d96fd8859b3" ON "serviceCenter" ("addressId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e91ba1fb3d37d90fb9cb339d0e" ON "serviceCenter" ("contactId")
        `);
		await queryRunner.query(`
            CREATE TABLE "contact" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" character varying NOT NULL,
                "phone" character varying NOT NULL,
                "email" character varying NOT NULL,
                "website" character varying NOT NULL DEFAULT '',
                CONSTRAINT "PK_2cbbe00f59ab6b3bb5b8d19f989" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_11df95cdada93ad29ba8d0b229" ON "contact" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_bca7555b07d24c10c026a89159" ON "contact" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE TABLE "ecommerceOrder" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "code" character varying NOT NULL DEFAULT '',
                "shoptetUrl" character varying NOT NULL DEFAULT '',
                "placedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                "status" character varying NOT NULL DEFAULT '',
                "customerNote" character varying NOT NULL DEFAULT '',
                "internalNote" character varying NOT NULL DEFAULT '',
                "shippingAddressId" uuid,
                "invoiceAddressId" uuid,
                "contactId" uuid,
                "shipmentId" uuid,
                "serviceCaseId" uuid,
                CONSTRAINT "PK_61806dfaac95ffae3ac8467c1d0" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_dbc3ef7fdcf4628061b6596dd3" ON "ecommerceOrder" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_1591778ad9ad32b683fe9f073d" ON "ecommerceOrder" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_be57fe01dde74f04cc848e9a12" ON "ecommerceOrder" ("code")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_5957ce0613c5e67a7c385fe285" ON "ecommerceOrder" ("placedAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_a13679271aa1759f69db5c864b" ON "ecommerceOrder" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_405f0ad0dc09e5c1d370d1cde0" ON "ecommerceOrder" ("shippingAddressId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_056576d8e550afcf087f54d465" ON "ecommerceOrder" ("invoiceAddressId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ab6f3ac89cf28a74cd4e741e2c" ON "ecommerceOrder" ("contactId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_60892e26101a13df299fc48f95" ON "ecommerceOrder" ("shipmentId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_35021aa6159aa596d157ec4e53" ON "ecommerceOrder" ("serviceCaseId")
        `);
		await queryRunner.query(`
            CREATE TABLE "address" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "street" character varying NOT NULL,
                "city" character varying NOT NULL,
                "postalCode" character varying NOT NULL,
                "country" character varying NOT NULL,
                "addressSpecification" character varying NOT NULL DEFAULT '',
                CONSTRAINT "PK_d92de1f82754668b5f5f5dd4fd5" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_c948a852777a12d6da82177774" ON "address" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e9b3a8f3565ba85961f2de3032" ON "address" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."BATCH_STATUS_TRANSITION_TIMESTAMPS" AS ENUM(
                'createdAt',
                'deliveredAt',
                'checkedAt',
                'checkedSnAt',
                'testedAt'
            )
        `);
		await queryRunner.query(`
            CREATE TABLE "vendor" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" character varying NOT NULL DEFAULT '',
                "email" character varying NOT NULL DEFAULT '',
                "phone" character varying NOT NULL DEFAULT '',
                "note" text NOT NULL DEFAULT '',
                "eta" integer NOT NULL DEFAULT '14',
                "warranty" integer NOT NULL DEFAULT '14',
                "warrantyBaseline" "public"."BATCH_STATUS_TRANSITION_TIMESTAMPS" NOT NULL DEFAULT 'deliveredAt',
                "addressId" uuid,
                "contactId" uuid,
                CONSTRAINT "PK_931a23f6231a57604f5a0e32780" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_674aa06f94b490103c9c511056" ON "vendor" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_9d9e875b701769efea460169b6" ON "vendor" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f61018bdc439c6d1a941261b67" ON "vendor" ("name")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_aba8090534d8b8b8845784086c" ON "vendor" ("email")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_7c84c55b891e7576bb2bb4c70b" ON "vendor" ("addressId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_507e4f6f306f0c489fc9a3dfdb" ON "vendor" ("contactId")
        `);
		await queryRunner.query(`
            CREATE TABLE "account" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "iban" character varying NOT NULL,
                "swift" character varying NOT NULL,
                "vendorId" uuid,
                CONSTRAINT "PK_54115ee388cdb6d86bb4bf5b2ea" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f58ea9d22bc49f073b24871e5e" ON "account" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_49326795c180fd8c71fc929126" ON "account" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f7be4981150b23fa62fe552c0c" ON "account" ("vendorId")
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRINTER_LOCATIONS" AS ENUM('DELIVERY', 'SERVICE', 'SHIPMENT', 'TESTING')
        `);
		await queryRunner.query(`
            CREATE TABLE "printer" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" character varying NOT NULL,
                "ip" character varying NOT NULL,
                "type" "public"."PRINTER_LOCATIONS" array NOT NULL DEFAULT '{DELIVERY}',
                "crt" character varying,
                "key" character varying,
                CONSTRAINT "PK_a07d4f7686a51f38ae237def52b" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_4c9eedbc520f5438a503703726" ON "printer" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f0beb691907f95f8e791f5e36d" ON "printer" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_12e6612123eba6db99643eb871" ON "printer" ("type")
        `);
		await queryRunner.query(`
            CREATE TABLE "productEnvelopeCode" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "code" SERIAL NOT NULL,
                CONSTRAINT "UQ_d88753aee827c0bcae033810c60" UNIQUE ("code"),
                CONSTRAINT "PK_c34bca5357e08282986ad3c2975" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_2522b85f17d56f84bd5f9965b3" ON "productEnvelopeCode" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_dbbe71eb09a34902923133f57b" ON "productEnvelopeCode" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d88753aee827c0bcae033810c6" ON "productEnvelopeCode" ("code")
        `);
		await queryRunner.query(`
            CREATE TABLE "shoptetOrderSync" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "finishedAt" TIMESTAMP WITH TIME ZONE,
                "amount" integer NOT NULL DEFAULT '0',
                CONSTRAINT "PK_8c1543a8191f677c347a194e38b" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_9b584b2c074e37658005887670" ON "shoptetOrderSync" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_06b9ad79e52ecd9f57f1913d02" ON "shoptetOrderSync" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e5f4a48a825b8e8d8799dc2103" ON "shoptetOrderSync" ("finishedAt")
        `);
		await queryRunner.query(`
            CREATE TABLE "wholesalePricing" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "name" character varying NOT NULL,
                "shoptetId" integer,
                "coefficient" double precision NOT NULL,
                CONSTRAINT "PK_5729d9a4a857c747aa6d1ce365f" PRIMARY KEY ("id")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_c8dbd34233e7db74d63d33a5a5" ON "wholesalePricing" ("actionId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_11aee25a12cb0b07f6750cf3fe" ON "wholesalePricing" ("id", "createdAt")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_16f362bb3d4641cf1a8becdcf6" ON "wholesalePricing" ("name")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_da21fc6efddeed0efcd0cdc639" ON "wholesalePricing" ("shoptetId")
        `);
		await queryRunner.query(`
            CREATE TABLE "roleScope" (
                "roleId" uuid NOT NULL,
                "scopeId" uuid NOT NULL,
                CONSTRAINT "PK_8beef2391028682ab694921ef8e" PRIMARY KEY ("roleId", "scopeId")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_025c678968c1ee13a58c22f646" ON "roleScope" ("roleId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_6268a4bea48e57b7dbce43715f" ON "roleScope" ("scopeId")
        `);
		await queryRunner.query(`
            CREATE TABLE "attributeValueServiceTask" (
                "attributeValueId" uuid NOT NULL,
                "serviceTaskId" uuid NOT NULL,
                CONSTRAINT "PK_dee8dff1710c5d887d02c7699a8" PRIMARY KEY ("attributeValueId", "serviceTaskId")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_70850d5af8a741aa616550dba9" ON "attributeValueServiceTask" ("attributeValueId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_cdac7e4c33f3c1aae7994b8ef4" ON "attributeValueServiceTask" ("serviceTaskId")
        `);
		await queryRunner.query(`
            CREATE TABLE "productCategory_closure" (
                "id_ancestor" uuid NOT NULL,
                "id_descendant" uuid NOT NULL,
                CONSTRAINT "PK_34020b3b9cff29a80479610d82c" PRIMARY KEY ("id_ancestor", "id_descendant")
            )
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_fd4a95d52bcf38fefa9f5b5717" ON "productCategory_closure" ("id_ancestor")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_91b51eae21eeb18cebf5e56d9f" ON "productCategory_closure" ("id_descendant")
        `);
		await queryRunner.query(`
            ALTER TABLE "batchCode"
            ADD CONSTRAINT "FK_616c3d893b25c48ad8247fd538a" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "batchDefect"
            ADD CONSTRAINT "FK_fb971fb371cab857d51ef802296" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "batchDefect"
            ADD CONSTRAINT "FK_9d518d3f8b9db3e8aa91addd804" FOREIGN KEY ("batchId") REFERENCES "batch"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "batchGroupCode"
            ADD CONSTRAINT "FK_f598a413f6279e42130c5e43164" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "batchGroup"
            ADD CONSTRAINT "FK_02e8a3b25a7d6cf87b1f0669f47" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "batchGroup"
            ADD CONSTRAINT "FK_0303997ca11597c2f83ce4df310" FOREIGN KEY ("codeId") REFERENCES "batchGroupCode"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD CONSTRAINT "FK_df3c045682423191ac201ff0569" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD CONSTRAINT "FK_1dd9d207d27bcc9b066824ae99b" FOREIGN KEY ("codeId") REFERENCES "productEnvelopeCode"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD CONSTRAINT "FK_f9377b0a0562618cdcdf7892ce3" FOREIGN KEY ("productCategoryId") REFERENCES "productCategory"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrderItem"
            ADD CONSTRAINT "FK_41c29efb4b30c4dd975bcf866d2" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrderItem"
            ADD CONSTRAINT "FK_3ab2501982ea6cd453d516e853f" FOREIGN KEY ("productEnvelopeId") REFERENCES "productEnvelope"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrderItem"
            ADD CONSTRAINT "FK_c02dd8381d68f069fcc5f651b4e" FOREIGN KEY ("ecommerceOrderId") REFERENCES "ecommerceOrder"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrderItem"
            ADD CONSTRAINT "FK_c6d281a26e222c4f7863a820091" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "action"
            ADD CONSTRAINT "FK_bf376bd23a893833bd63e3dd8a3" FOREIGN KEY ("authenticationId") REFERENCES "authentication"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "scope"
            ADD CONSTRAINT "FK_02c1be68a79d6ebcf0bc2ec03e8" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "role"
            ADD CONSTRAINT "FK_54aaa7ce0f59570c5ba5b686342" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "authentication"
            ADD CONSTRAINT "FK_be031dd812f7ce15ab2b3bcaffb" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "authentication"
            ADD CONSTRAINT "FK_2ce04c38ee386596885c903d9ad" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "authentication"
            ADD CONSTRAINT "FK_b0a8dd2baaddf18f44779c42d37" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "savedFilter"
            ADD CONSTRAINT "FK_691f0acc1b8d61c19419ac79697" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "savedFilter"
            ADD CONSTRAINT "FK_f1f29fb451206435201004e2f9f" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "shipmentCode"
            ADD CONSTRAINT "FK_32a67f1b352a94437e165a71a7b" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "shipmentProduct"
            ADD CONSTRAINT "FK_841f4f9b72360cf0175b36d448f" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "shipmentProduct"
            ADD CONSTRAINT "FK_045eef5ef2ba6937177cb7c077c" FOREIGN KEY ("shipmentId") REFERENCES "shipment"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "shipmentProduct"
            ADD CONSTRAINT "FK_2b8eca828d3cef5c1fe7cc1a82a" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "shipment"
            ADD CONSTRAINT "FK_8e067a22d48f3c8cde09115993b" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "shipment"
            ADD CONSTRAINT "FK_4feadbe728e249e7063db214539" FOREIGN KEY ("dispatchedById") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "shipment"
            ADD CONSTRAINT "FK_a92bc88b88e244afe28c01b2e33" FOREIGN KEY ("addressId") REFERENCES "address"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "shipment"
            ADD CONSTRAINT "FK_05a6268a9d538679bd5beba5ffe" FOREIGN KEY ("codeId") REFERENCES "shipmentCode"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "shipment"
            ADD CONSTRAINT "FK_7b10b04682400501934ef2e8887" FOREIGN KEY ("contactId") REFERENCES "contact"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productWarehouseTask"
            ADD CONSTRAINT "FK_b0f6f83e795787dba8e58ac11c1" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productWarehouseTask"
            ADD CONSTRAINT "FK_5a130222de59f6257f07a10f705" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productWarehouseTask"
            ADD CONSTRAINT "FK_ed047783973e2b0d8c81af09038" FOREIGN KEY ("warehouseTaskId") REFERENCES "warehouseTask"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "warehousePosition"
            ADD CONSTRAINT "FK_440a23c145854c073f551027a4c" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "warehousePosition"
            ADD CONSTRAINT "FK_370a4bc80d7524de2e27efc8c70" FOREIGN KEY ("warehouseId") REFERENCES "warehouse"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouse"
            ADD CONSTRAINT "FK_2281ad941d83f65a88cb0ac9c99" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask"
            ADD CONSTRAINT "FK_d192e157b7dcc827bb727598806" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask"
            ADD CONSTRAINT "FK_a78dc43054c3058b87cf54e8a2c" FOREIGN KEY ("sourceWarehouseId") REFERENCES "warehouse"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask"
            ADD CONSTRAINT "FK_6bb9960c2dbf3cdda9c67bda3ce" FOREIGN KEY ("destinationWarehouseId") REFERENCES "warehouse"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask"
            ADD CONSTRAINT "FK_9eab14171403bdb72caf0d93f67" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "user"
            ADD CONSTRAINT "FK_519e6553e4e02eba80a0d3989d4" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productTest"
            ADD CONSTRAINT "FK_6567fbcc5e9e64e4108b2448e42" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productTest"
            ADD CONSTRAINT "FK_ee83ae7ce22733b0c97788c9ada" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productTest"
            ADD CONSTRAINT "FK_2700f50ad501a3123f80c23faeb" FOREIGN KEY ("testedById") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productTest"
            ADD CONSTRAINT "FK_f75ee991be9073444ec68e3efe5" FOREIGN KEY ("conditionalAttributeId") REFERENCES "conditionalAttribute"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "conditionalAttribute"
            ADD CONSTRAINT "FK_5e2f956741b8e040af295fb1e4c" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "conditionalAttribute"
            ADD CONSTRAINT "FK_8f02ea9d90016ddf442471a5bb8" FOREIGN KEY ("attributeValueId") REFERENCES "attributeValue"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValueConditionalAttribute"
            ADD CONSTRAINT "FK_b9fc85880b7d9045283f0e703f1" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValueConditionalAttribute"
            ADD CONSTRAINT "FK_b642f3cfafcc199f9938600ef0c" FOREIGN KEY ("attributeValueId") REFERENCES "attributeValue"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValueConditionalAttribute"
            ADD CONSTRAINT "FK_46ba4af1bcf8a4f7dbe2aefa5a8" FOREIGN KEY ("conditionalAttributeId") REFERENCES "conditionalAttribute"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "recyclingFee"
            ADD CONSTRAINT "FK_c1445c0baecf77493db46798aab" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "recyclingFee"
            ADD CONSTRAINT "FK_d660d6e0fe47862abd02aa263a1" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "shoptetCategory"
            ADD CONSTRAINT "FK_465e3f8671567c587d71a61e4ba" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategory"
            ADD CONSTRAINT "FK_a7ac5151122c715f8b1f286f737" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategory"
            ADD CONSTRAINT "FK_b34eb5c6311a840c8f151dd198f" FOREIGN KEY ("parentId") REFERENCES "productCategory"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategory"
            ADD CONSTRAINT "FK_471a63c1df20127313ee451e931" FOREIGN KEY ("recyclingFeeId") REFERENCES "recyclingFee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategory"
            ADD CONSTRAINT "FK_0c550d237dcfea9fe6913a34ffd" FOREIGN KEY ("shoptetCategoryId") REFERENCES "shoptetCategory"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategoryAttribute"
            ADD CONSTRAINT "FK_519deb886e0e451a1edb6209c06" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategoryAttribute"
            ADD CONSTRAINT "FK_15b3a7397e68a752de73fcded31" FOREIGN KEY ("attributeId") REFERENCES "attribute"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategoryAttribute"
            ADD CONSTRAINT "FK_86f9acfe9dea6f7c0a96faa949b" FOREIGN KEY ("productCategoryId") REFERENCES "productCategory"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelopeAttributeValue"
            ADD CONSTRAINT "FK_087294b59b4b45f11a64c22dd52" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelopeAttributeValue"
            ADD CONSTRAINT "FK_f49549471d70fc9380c35d92c5c" FOREIGN KEY ("attributeValueId") REFERENCES "attributeValue"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelopeAttributeValue"
            ADD CONSTRAINT "FK_b833891e239c3162c742dedc4e9" FOREIGN KEY ("productEnvelopeId") REFERENCES "productEnvelope"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "vendorDefectType"
            ADD CONSTRAINT "FK_a0c81ed8df4cb6ab6a55c691f6e" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "vendorDefectType"
            ADD CONSTRAINT "FK_ed34c68375c8ea12d442831af37" FOREIGN KEY ("defectTypeId") REFERENCES "defectType"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "vendorDefectType"
            ADD CONSTRAINT "FK_de4d8709c72f874398bd31ba0c8" FOREIGN KEY ("vendorId") REFERENCES "vendor"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "defectType"
            ADD CONSTRAINT "FK_2112482b7cea59d055fb2c57a9d" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "warrantyClaimCode"
            ADD CONSTRAINT "FK_a3b2e371af7d019f91efd427544" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "warrantyClaim"
            ADD CONSTRAINT "FK_f3b66e9640ebc9a340188b7d70e" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "warrantyClaim"
            ADD CONSTRAINT "FK_0f658163bd3bbe76d1c0818403f" FOREIGN KEY ("createdById") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "warrantyClaim"
            ADD CONSTRAINT "FK_cde3cec9ae4f04709bb48356232" FOREIGN KEY ("codeId") REFERENCES "warrantyClaimCode"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productDefect"
            ADD CONSTRAINT "FK_1026a5f82fb6534eea7a7a49abb" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productDefect"
            ADD CONSTRAINT "FK_0726ee040109cbafa3bbe7784a3" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productDefect"
            ADD CONSTRAINT "FK_410a4f9077a3b113d9906913d02" FOREIGN KEY ("defectTypeId") REFERENCES "defectType"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productDefect"
            ADD CONSTRAINT "FK_72ecb7c1727c251c64bf29fa33a" FOREIGN KEY ("serviceTaskId") REFERENCES "serviceTask"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productDefect"
            ADD CONSTRAINT "FK_4cecdb1f5e6942dfbea66cda9ba" FOREIGN KEY ("serviceCaseId") REFERENCES "serviceCase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productDefect"
            ADD CONSTRAINT "FK_16846f861b619f1d3315e608a19" FOREIGN KEY ("warrantyClaimId") REFERENCES "warrantyClaim"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productTask"
            ADD CONSTRAINT "FK_630fd38f02c33fe57c3160e7b4e" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productTask"
            ADD CONSTRAINT "FK_1187d6c76d8664b13c0a8b459ba" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productTask"
            ADD CONSTRAINT "FK_d398a30d5be2a6220983a4f44e1" FOREIGN KEY ("serviceTaskTypeId") REFERENCES "serviceTaskType"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceTaskType"
            ADD CONSTRAINT "FK_911a25040b79f5ddff739d4e0e2" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceTask"
            ADD CONSTRAINT "FK_e2c39c3d9860c6836d44d356c74" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceTask"
            ADD CONSTRAINT "FK_cd84eaf504186bee6cbf09327cd" FOREIGN KEY ("serviceTaskTypeId") REFERENCES "serviceTaskType"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "shoptetAttribute"
            ADD CONSTRAINT "FK_02088f54d81c96564d70224fce4" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "attribute"
            ADD CONSTRAINT "FK_fe79dbcbfc2a46ff35aba76050f" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "attribute"
            ADD CONSTRAINT "FK_53a371f596b5bb848b024829294" FOREIGN KEY ("shoptetAttributeId") REFERENCES "shoptetAttribute"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeNameAlternative"
            ADD CONSTRAINT "FK_33fc742d7b5edff5c4e8a65dd16" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeNameAlternative"
            ADD CONSTRAINT "FK_fb3ca43e5ca9af5d8fb638fa2c7" FOREIGN KEY ("parentId") REFERENCES "attribute"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValue"
            ADD CONSTRAINT "FK_855bf07c5b4f9a00f3523161bf8" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValue"
            ADD CONSTRAINT "FK_34920d38b502e1a4970960ebdde" FOREIGN KEY ("attributeId") REFERENCES "attribute"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValueAlternative"
            ADD CONSTRAINT "FK_bdd5ca2e2f7951a257f91f9de87" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValueAlternative"
            ADD CONSTRAINT "FK_2f8494fe2bde9354f29d1cbc6f1" FOREIGN KEY ("parentId") REFERENCES "attributeValue"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productAttributeValue"
            ADD CONSTRAINT "FK_bee9eede0cf860da5f31fba4466" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productAttributeValue"
            ADD CONSTRAINT "FK_91b5f4480d6c6939353f4be82db" FOREIGN KEY ("attributeValueId") REFERENCES "attributeValue"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productAttributeValue"
            ADD CONSTRAINT "FK_d0262d0e56d9f5125a633844026" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productCode"
            ADD CONSTRAINT "FK_101c0733ba57394c8479656a0c4" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productCode"
            ADD CONSTRAINT "FK_71998f7a83b18d7361d63f989f7" FOREIGN KEY ("batchId") REFERENCES "batch"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "warranty"
            ADD CONSTRAINT "FK_63830a7d5015ddc2ff895964bcf" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "warranty"
            ADD CONSTRAINT "FK_f9340c4297884c58c2da3cc35a8" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ADD CONSTRAINT "FK_ca058ed10f56f646c6e63c4af55" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ADD CONSTRAINT "FK_b916c32dfcf46f001167e6f9038" FOREIGN KEY ("codeId") REFERENCES "productCode"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ADD CONSTRAINT "FK_483ff5648459d1d4d2270c2dd2d" FOREIGN KEY ("productEnvelopeId") REFERENCES "productEnvelope"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ADD CONSTRAINT "FK_6c25e1fc008d926c3504c3c6397" FOREIGN KEY ("productEnvelopeAssignedById") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ADD CONSTRAINT "FK_741c8e6d9b2a19dddb96e8d6204" FOREIGN KEY ("batchId") REFERENCES "batch"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ADD CONSTRAINT "FK_618194d24a7ea86a165d7ec628e" FOREIGN KEY ("productCategoryId") REFERENCES "productCategory"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ADD CONSTRAINT "FK_b0bfdff74ca3b7aa3545fefbbae" FOREIGN KEY ("warehousePositionId") REFERENCES "warehousePosition"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productPrice"
            ADD CONSTRAINT "FK_5b791c5b2667f0d24eb0b0cf944" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productPrice"
            ADD CONSTRAINT "FK_0d5c144b05ecf58534900027a03" FOREIGN KEY ("currencyRateId") REFERENCES "currencyRate"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productPrice"
            ADD CONSTRAINT "FK_a7c24be296a22f842f2fb5b0c48" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "currency"
            ADD CONSTRAINT "FK_cf12ebf939da431c80cc3d88d9e" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "currencyRate"
            ADD CONSTRAINT "FK_b1b7a8bb508d9ed48e779d1729a" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "currencyRate"
            ADD CONSTRAINT "FK_887f1c7d857df6c2a956306e83d" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ADD CONSTRAINT "FK_4f65e1770db73cefd4f74521bad" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ADD CONSTRAINT "FK_f7a1a202027c65b1bab794276af" FOREIGN KEY ("createdById") REFERENCES "user"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ADD CONSTRAINT "FK_ec6dc77febfea9330cd988c9226" FOREIGN KEY ("checkedById") REFERENCES "user"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ADD CONSTRAINT "FK_2204328354d1b711ec7368cbc5a" FOREIGN KEY ("checkedSnById") REFERENCES "user"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ADD CONSTRAINT "FK_bb08f81affbcaa5f81cfce6c7c1" FOREIGN KEY ("deliveredById") REFERENCES "user"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ADD CONSTRAINT "FK_8c26b473113c76ab9aa7fcc0882" FOREIGN KEY ("testedById") REFERENCES "user"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ADD CONSTRAINT "FK_d1adc6635d4bdb17fd6e60532c1" FOREIGN KEY ("codeId") REFERENCES "batchCode"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ADD CONSTRAINT "FK_e28ec0b4b5960fdee5917cb2150" FOREIGN KEY ("batchGroupId") REFERENCES "batchGroup"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ADD CONSTRAINT "FK_571fa9d810a9f4b1b653a56f018" FOREIGN KEY ("vendorId") REFERENCES "vendor"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ADD CONSTRAINT "FK_60f72cccb8a36ceee891d0d19d8" FOREIGN KEY ("currencyRateId") REFERENCES "currencyRate"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "file"
            ADD CONSTRAINT "FK_9791834a3e29ecc4a4c4cc2458f" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileBatch"
            ADD CONSTRAINT "FK_fa87fcda7a6c576ee77d4c20a61" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileBatchDefect"
            ADD CONSTRAINT "FK_dd089bc241c5db050f715a6894b" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileBatchDefect"
            ADD CONSTRAINT "FK_02d1f430ba5798509c71605f092" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileBatchDefect"
            ADD CONSTRAINT "FK_79c653d66b118788fee2a3a876a" FOREIGN KEY ("batchDefectId") REFERENCES "batchDefect"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileProduct"
            ADD CONSTRAINT "FK_86ff2d0d44bbd581f85cae8d460" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileProduct"
            ADD CONSTRAINT "FK_fc7059771a2d42fa8bee797db57" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileProductDefect"
            ADD CONSTRAINT "FK_66174b8e07740d83666c7401f08" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileProductDefect"
            ADD CONSTRAINT "FK_55d354db4573f896138fa630f56" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileProductDefect"
            ADD CONSTRAINT "FK_734f16fa31dd2fa097368e83ca9" FOREIGN KEY ("productDefectId") REFERENCES "productDefect"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileServiceCase"
            ADD CONSTRAINT "FK_71da6d2305f2fa5c7008cb9c4cb" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileServiceCase"
            ADD CONSTRAINT "FK_bee814cc3058a95c1bc52992b77" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileServiceCase"
            ADD CONSTRAINT "FK_3592b6a8129b7c62adcc7559f66" FOREIGN KEY ("serviceCaseId") REFERENCES "serviceCase"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileVendor"
            ADD CONSTRAINT "FK_850104e91f4965cc55432f45984" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileVendor"
            ADD CONSTRAINT "FK_0603eb633f19038692853c85f67" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileVendor"
            ADD CONSTRAINT "FK_79db0d546f7e6aead109cebcfb5" FOREIGN KEY ("vendorId") REFERENCES "vendor"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileWarrantyClaim"
            ADD CONSTRAINT "FK_28b4944a40bd4438056c1c18c8d" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileWarrantyClaim"
            ADD CONSTRAINT "FK_8af67ac221d6b39e55ef79462fd" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "fileWarrantyClaim"
            ADD CONSTRAINT "FK_4c387987a0fcb34bcb53c4616b6" FOREIGN KEY ("warrantyClaimId") REFERENCES "warrantyClaim"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCaseCode"
            ADD CONSTRAINT "FK_dcf766b7a4bb0040cc6dd7649a6" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCase"
            ADD CONSTRAINT "FK_c3a31132a7651477f7a13d1ff84" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCase"
            ADD CONSTRAINT "FK_1a244b9dd4ebbe12600a356226b" FOREIGN KEY ("createdById") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCase"
            ADD CONSTRAINT "FK_2d124c1e9790625a90f96b5c893" FOREIGN KEY ("serviceCenterId") REFERENCES "serviceCenter"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCase"
            ADD CONSTRAINT "FK_e6aaf77baf8701eff86e71d7a94" FOREIGN KEY ("codeId") REFERENCES "serviceCaseCode"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCenter"
            ADD CONSTRAINT "FK_4f3f798b21534a5b63c2fa6e920" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCenter"
            ADD CONSTRAINT "FK_3aeca6e65c6b952d96fd8859b3d" FOREIGN KEY ("addressId") REFERENCES "address"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCenter"
            ADD CONSTRAINT "FK_e91ba1fb3d37d90fb9cb339d0e8" FOREIGN KEY ("contactId") REFERENCES "contact"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "contact"
            ADD CONSTRAINT "FK_11df95cdada93ad29ba8d0b2298" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder"
            ADD CONSTRAINT "FK_dbc3ef7fdcf4628061b6596dd34" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder"
            ADD CONSTRAINT "FK_405f0ad0dc09e5c1d370d1cde0e" FOREIGN KEY ("shippingAddressId") REFERENCES "address"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder"
            ADD CONSTRAINT "FK_056576d8e550afcf087f54d4659" FOREIGN KEY ("invoiceAddressId") REFERENCES "address"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder"
            ADD CONSTRAINT "FK_ab6f3ac89cf28a74cd4e741e2c6" FOREIGN KEY ("contactId") REFERENCES "contact"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder"
            ADD CONSTRAINT "FK_60892e26101a13df299fc48f95c" FOREIGN KEY ("shipmentId") REFERENCES "shipment"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder"
            ADD CONSTRAINT "FK_35021aa6159aa596d157ec4e53e" FOREIGN KEY ("serviceCaseId") REFERENCES "serviceCase"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "address"
            ADD CONSTRAINT "FK_c948a852777a12d6da82177774c" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "vendor"
            ADD CONSTRAINT "FK_674aa06f94b490103c9c511056b" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "vendor"
            ADD CONSTRAINT "FK_7c84c55b891e7576bb2bb4c70b6" FOREIGN KEY ("addressId") REFERENCES "address"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "vendor"
            ADD CONSTRAINT "FK_507e4f6f306f0c489fc9a3dfdb6" FOREIGN KEY ("contactId") REFERENCES "contact"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "account"
            ADD CONSTRAINT "FK_f58ea9d22bc49f073b24871e5e4" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "account"
            ADD CONSTRAINT "FK_f7be4981150b23fa62fe552c0c2" FOREIGN KEY ("vendorId") REFERENCES "vendor"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "printer"
            ADD CONSTRAINT "FK_4c9eedbc520f5438a5037037266" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelopeCode"
            ADD CONSTRAINT "FK_2522b85f17d56f84bd5f9965b33" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "shoptetOrderSync"
            ADD CONSTRAINT "FK_9b584b2c074e37658005887670b" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "wholesalePricing"
            ADD CONSTRAINT "FK_c8dbd34233e7db74d63d33a5a59" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "roleScope"
            ADD CONSTRAINT "FK_025c678968c1ee13a58c22f6469" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE CASCADE ON UPDATE CASCADE
        `);
		await queryRunner.query(`
            ALTER TABLE "roleScope"
            ADD CONSTRAINT "FK_6268a4bea48e57b7dbce43715f4" FOREIGN KEY ("scopeId") REFERENCES "scope"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValueServiceTask"
            ADD CONSTRAINT "FK_70850d5af8a741aa616550dba90" FOREIGN KEY ("attributeValueId") REFERENCES "attributeValue"("id") ON DELETE CASCADE ON UPDATE CASCADE
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValueServiceTask"
            ADD CONSTRAINT "FK_cdac7e4c33f3c1aae7994b8ef48" FOREIGN KEY ("serviceTaskId") REFERENCES "serviceTask"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategory_closure"
            ADD CONSTRAINT "FK_fd4a95d52bcf38fefa9f5b5717f" FOREIGN KEY ("id_ancestor") REFERENCES "productCategory"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategory_closure"
            ADD CONSTRAINT "FK_91b51eae21eeb18cebf5e56d9f5" FOREIGN KEY ("id_descendant") REFERENCES "productCategory"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "productCategory_closure" DROP CONSTRAINT "FK_91b51eae21eeb18cebf5e56d9f5"
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategory_closure" DROP CONSTRAINT "FK_fd4a95d52bcf38fefa9f5b5717f"
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValueServiceTask" DROP CONSTRAINT "FK_cdac7e4c33f3c1aae7994b8ef48"
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValueServiceTask" DROP CONSTRAINT "FK_70850d5af8a741aa616550dba90"
        `);
		await queryRunner.query(`
            ALTER TABLE "roleScope" DROP CONSTRAINT "FK_6268a4bea48e57b7dbce43715f4"
        `);
		await queryRunner.query(`
            ALTER TABLE "roleScope" DROP CONSTRAINT "FK_025c678968c1ee13a58c22f6469"
        `);
		await queryRunner.query(`
            ALTER TABLE "wholesalePricing" DROP CONSTRAINT "FK_c8dbd34233e7db74d63d33a5a59"
        `);
		await queryRunner.query(`
            ALTER TABLE "shoptetOrderSync" DROP CONSTRAINT "FK_9b584b2c074e37658005887670b"
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelopeCode" DROP CONSTRAINT "FK_2522b85f17d56f84bd5f9965b33"
        `);
		await queryRunner.query(`
            ALTER TABLE "printer" DROP CONSTRAINT "FK_4c9eedbc520f5438a5037037266"
        `);
		await queryRunner.query(`
            ALTER TABLE "account" DROP CONSTRAINT "FK_f7be4981150b23fa62fe552c0c2"
        `);
		await queryRunner.query(`
            ALTER TABLE "account" DROP CONSTRAINT "FK_f58ea9d22bc49f073b24871e5e4"
        `);
		await queryRunner.query(`
            ALTER TABLE "vendor" DROP CONSTRAINT "FK_507e4f6f306f0c489fc9a3dfdb6"
        `);
		await queryRunner.query(`
            ALTER TABLE "vendor" DROP CONSTRAINT "FK_7c84c55b891e7576bb2bb4c70b6"
        `);
		await queryRunner.query(`
            ALTER TABLE "vendor" DROP CONSTRAINT "FK_674aa06f94b490103c9c511056b"
        `);
		await queryRunner.query(`
            ALTER TABLE "address" DROP CONSTRAINT "FK_c948a852777a12d6da82177774c"
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder" DROP CONSTRAINT "FK_35021aa6159aa596d157ec4e53e"
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder" DROP CONSTRAINT "FK_60892e26101a13df299fc48f95c"
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder" DROP CONSTRAINT "FK_ab6f3ac89cf28a74cd4e741e2c6"
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder" DROP CONSTRAINT "FK_056576d8e550afcf087f54d4659"
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder" DROP CONSTRAINT "FK_405f0ad0dc09e5c1d370d1cde0e"
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder" DROP CONSTRAINT "FK_dbc3ef7fdcf4628061b6596dd34"
        `);
		await queryRunner.query(`
            ALTER TABLE "contact" DROP CONSTRAINT "FK_11df95cdada93ad29ba8d0b2298"
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCenter" DROP CONSTRAINT "FK_e91ba1fb3d37d90fb9cb339d0e8"
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCenter" DROP CONSTRAINT "FK_3aeca6e65c6b952d96fd8859b3d"
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCenter" DROP CONSTRAINT "FK_4f3f798b21534a5b63c2fa6e920"
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCase" DROP CONSTRAINT "FK_e6aaf77baf8701eff86e71d7a94"
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCase" DROP CONSTRAINT "FK_2d124c1e9790625a90f96b5c893"
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCase" DROP CONSTRAINT "FK_1a244b9dd4ebbe12600a356226b"
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCase" DROP CONSTRAINT "FK_c3a31132a7651477f7a13d1ff84"
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCaseCode" DROP CONSTRAINT "FK_dcf766b7a4bb0040cc6dd7649a6"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileWarrantyClaim" DROP CONSTRAINT "FK_4c387987a0fcb34bcb53c4616b6"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileWarrantyClaim" DROP CONSTRAINT "FK_8af67ac221d6b39e55ef79462fd"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileWarrantyClaim" DROP CONSTRAINT "FK_28b4944a40bd4438056c1c18c8d"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileVendor" DROP CONSTRAINT "FK_79db0d546f7e6aead109cebcfb5"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileVendor" DROP CONSTRAINT "FK_0603eb633f19038692853c85f67"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileVendor" DROP CONSTRAINT "FK_850104e91f4965cc55432f45984"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileServiceCase" DROP CONSTRAINT "FK_3592b6a8129b7c62adcc7559f66"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileServiceCase" DROP CONSTRAINT "FK_bee814cc3058a95c1bc52992b77"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileServiceCase" DROP CONSTRAINT "FK_71da6d2305f2fa5c7008cb9c4cb"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileProductDefect" DROP CONSTRAINT "FK_734f16fa31dd2fa097368e83ca9"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileProductDefect" DROP CONSTRAINT "FK_55d354db4573f896138fa630f56"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileProductDefect" DROP CONSTRAINT "FK_66174b8e07740d83666c7401f08"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileProduct" DROP CONSTRAINT "FK_fc7059771a2d42fa8bee797db57"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileProduct" DROP CONSTRAINT "FK_86ff2d0d44bbd581f85cae8d460"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileBatchDefect" DROP CONSTRAINT "FK_79c653d66b118788fee2a3a876a"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileBatchDefect" DROP CONSTRAINT "FK_02d1f430ba5798509c71605f092"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileBatchDefect" DROP CONSTRAINT "FK_dd089bc241c5db050f715a6894b"
        `);
		await queryRunner.query(`
            ALTER TABLE "fileBatch" DROP CONSTRAINT "FK_fa87fcda7a6c576ee77d4c20a61"
        `);
		await queryRunner.query(`
            ALTER TABLE "file" DROP CONSTRAINT "FK_9791834a3e29ecc4a4c4cc2458f"
        `);
		await queryRunner.query(`
            ALTER TABLE "batch" DROP CONSTRAINT "FK_60f72cccb8a36ceee891d0d19d8"
        `);
		await queryRunner.query(`
            ALTER TABLE "batch" DROP CONSTRAINT "FK_571fa9d810a9f4b1b653a56f018"
        `);
		await queryRunner.query(`
            ALTER TABLE "batch" DROP CONSTRAINT "FK_e28ec0b4b5960fdee5917cb2150"
        `);
		await queryRunner.query(`
            ALTER TABLE "batch" DROP CONSTRAINT "FK_d1adc6635d4bdb17fd6e60532c1"
        `);
		await queryRunner.query(`
            ALTER TABLE "batch" DROP CONSTRAINT "FK_8c26b473113c76ab9aa7fcc0882"
        `);
		await queryRunner.query(`
            ALTER TABLE "batch" DROP CONSTRAINT "FK_bb08f81affbcaa5f81cfce6c7c1"
        `);
		await queryRunner.query(`
            ALTER TABLE "batch" DROP CONSTRAINT "FK_2204328354d1b711ec7368cbc5a"
        `);
		await queryRunner.query(`
            ALTER TABLE "batch" DROP CONSTRAINT "FK_ec6dc77febfea9330cd988c9226"
        `);
		await queryRunner.query(`
            ALTER TABLE "batch" DROP CONSTRAINT "FK_f7a1a202027c65b1bab794276af"
        `);
		await queryRunner.query(`
            ALTER TABLE "batch" DROP CONSTRAINT "FK_4f65e1770db73cefd4f74521bad"
        `);
		await queryRunner.query(`
            ALTER TABLE "currencyRate" DROP CONSTRAINT "FK_887f1c7d857df6c2a956306e83d"
        `);
		await queryRunner.query(`
            ALTER TABLE "currencyRate" DROP CONSTRAINT "FK_b1b7a8bb508d9ed48e779d1729a"
        `);
		await queryRunner.query(`
            ALTER TABLE "currency" DROP CONSTRAINT "FK_cf12ebf939da431c80cc3d88d9e"
        `);
		await queryRunner.query(`
            ALTER TABLE "productPrice" DROP CONSTRAINT "FK_a7c24be296a22f842f2fb5b0c48"
        `);
		await queryRunner.query(`
            ALTER TABLE "productPrice" DROP CONSTRAINT "FK_0d5c144b05ecf58534900027a03"
        `);
		await queryRunner.query(`
            ALTER TABLE "productPrice" DROP CONSTRAINT "FK_5b791c5b2667f0d24eb0b0cf944"
        `);
		await queryRunner.query(`
            ALTER TABLE "product" DROP CONSTRAINT "FK_b0bfdff74ca3b7aa3545fefbbae"
        `);
		await queryRunner.query(`
            ALTER TABLE "product" DROP CONSTRAINT "FK_618194d24a7ea86a165d7ec628e"
        `);
		await queryRunner.query(`
            ALTER TABLE "product" DROP CONSTRAINT "FK_741c8e6d9b2a19dddb96e8d6204"
        `);
		await queryRunner.query(`
            ALTER TABLE "product" DROP CONSTRAINT "FK_6c25e1fc008d926c3504c3c6397"
        `);
		await queryRunner.query(`
            ALTER TABLE "product" DROP CONSTRAINT "FK_483ff5648459d1d4d2270c2dd2d"
        `);
		await queryRunner.query(`
            ALTER TABLE "product" DROP CONSTRAINT "FK_b916c32dfcf46f001167e6f9038"
        `);
		await queryRunner.query(`
            ALTER TABLE "product" DROP CONSTRAINT "FK_ca058ed10f56f646c6e63c4af55"
        `);
		await queryRunner.query(`
            ALTER TABLE "warranty" DROP CONSTRAINT "FK_f9340c4297884c58c2da3cc35a8"
        `);
		await queryRunner.query(`
            ALTER TABLE "warranty" DROP CONSTRAINT "FK_63830a7d5015ddc2ff895964bcf"
        `);
		await queryRunner.query(`
            ALTER TABLE "productCode" DROP CONSTRAINT "FK_71998f7a83b18d7361d63f989f7"
        `);
		await queryRunner.query(`
            ALTER TABLE "productCode" DROP CONSTRAINT "FK_101c0733ba57394c8479656a0c4"
        `);
		await queryRunner.query(`
            ALTER TABLE "productAttributeValue" DROP CONSTRAINT "FK_d0262d0e56d9f5125a633844026"
        `);
		await queryRunner.query(`
            ALTER TABLE "productAttributeValue" DROP CONSTRAINT "FK_91b5f4480d6c6939353f4be82db"
        `);
		await queryRunner.query(`
            ALTER TABLE "productAttributeValue" DROP CONSTRAINT "FK_bee9eede0cf860da5f31fba4466"
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValueAlternative" DROP CONSTRAINT "FK_2f8494fe2bde9354f29d1cbc6f1"
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValueAlternative" DROP CONSTRAINT "FK_bdd5ca2e2f7951a257f91f9de87"
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValue" DROP CONSTRAINT "FK_34920d38b502e1a4970960ebdde"
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValue" DROP CONSTRAINT "FK_855bf07c5b4f9a00f3523161bf8"
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeNameAlternative" DROP CONSTRAINT "FK_fb3ca43e5ca9af5d8fb638fa2c7"
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeNameAlternative" DROP CONSTRAINT "FK_33fc742d7b5edff5c4e8a65dd16"
        `);
		await queryRunner.query(`
            ALTER TABLE "attribute" DROP CONSTRAINT "FK_53a371f596b5bb848b024829294"
        `);
		await queryRunner.query(`
            ALTER TABLE "attribute" DROP CONSTRAINT "FK_fe79dbcbfc2a46ff35aba76050f"
        `);
		await queryRunner.query(`
            ALTER TABLE "shoptetAttribute" DROP CONSTRAINT "FK_02088f54d81c96564d70224fce4"
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceTask" DROP CONSTRAINT "FK_cd84eaf504186bee6cbf09327cd"
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceTask" DROP CONSTRAINT "FK_e2c39c3d9860c6836d44d356c74"
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceTaskType" DROP CONSTRAINT "FK_911a25040b79f5ddff739d4e0e2"
        `);
		await queryRunner.query(`
            ALTER TABLE "productTask" DROP CONSTRAINT "FK_d398a30d5be2a6220983a4f44e1"
        `);
		await queryRunner.query(`
            ALTER TABLE "productTask" DROP CONSTRAINT "FK_1187d6c76d8664b13c0a8b459ba"
        `);
		await queryRunner.query(`
            ALTER TABLE "productTask" DROP CONSTRAINT "FK_630fd38f02c33fe57c3160e7b4e"
        `);
		await queryRunner.query(`
            ALTER TABLE "productDefect" DROP CONSTRAINT "FK_16846f861b619f1d3315e608a19"
        `);
		await queryRunner.query(`
            ALTER TABLE "productDefect" DROP CONSTRAINT "FK_4cecdb1f5e6942dfbea66cda9ba"
        `);
		await queryRunner.query(`
            ALTER TABLE "productDefect" DROP CONSTRAINT "FK_72ecb7c1727c251c64bf29fa33a"
        `);
		await queryRunner.query(`
            ALTER TABLE "productDefect" DROP CONSTRAINT "FK_410a4f9077a3b113d9906913d02"
        `);
		await queryRunner.query(`
            ALTER TABLE "productDefect" DROP CONSTRAINT "FK_0726ee040109cbafa3bbe7784a3"
        `);
		await queryRunner.query(`
            ALTER TABLE "productDefect" DROP CONSTRAINT "FK_1026a5f82fb6534eea7a7a49abb"
        `);
		await queryRunner.query(`
            ALTER TABLE "warrantyClaim" DROP CONSTRAINT "FK_cde3cec9ae4f04709bb48356232"
        `);
		await queryRunner.query(`
            ALTER TABLE "warrantyClaim" DROP CONSTRAINT "FK_0f658163bd3bbe76d1c0818403f"
        `);
		await queryRunner.query(`
            ALTER TABLE "warrantyClaim" DROP CONSTRAINT "FK_f3b66e9640ebc9a340188b7d70e"
        `);
		await queryRunner.query(`
            ALTER TABLE "warrantyClaimCode" DROP CONSTRAINT "FK_a3b2e371af7d019f91efd427544"
        `);
		await queryRunner.query(`
            ALTER TABLE "defectType" DROP CONSTRAINT "FK_2112482b7cea59d055fb2c57a9d"
        `);
		await queryRunner.query(`
            ALTER TABLE "vendorDefectType" DROP CONSTRAINT "FK_de4d8709c72f874398bd31ba0c8"
        `);
		await queryRunner.query(`
            ALTER TABLE "vendorDefectType" DROP CONSTRAINT "FK_ed34c68375c8ea12d442831af37"
        `);
		await queryRunner.query(`
            ALTER TABLE "vendorDefectType" DROP CONSTRAINT "FK_a0c81ed8df4cb6ab6a55c691f6e"
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelopeAttributeValue" DROP CONSTRAINT "FK_b833891e239c3162c742dedc4e9"
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelopeAttributeValue" DROP CONSTRAINT "FK_f49549471d70fc9380c35d92c5c"
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelopeAttributeValue" DROP CONSTRAINT "FK_087294b59b4b45f11a64c22dd52"
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategoryAttribute" DROP CONSTRAINT "FK_86f9acfe9dea6f7c0a96faa949b"
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategoryAttribute" DROP CONSTRAINT "FK_15b3a7397e68a752de73fcded31"
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategoryAttribute" DROP CONSTRAINT "FK_519deb886e0e451a1edb6209c06"
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategory" DROP CONSTRAINT "FK_0c550d237dcfea9fe6913a34ffd"
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategory" DROP CONSTRAINT "FK_471a63c1df20127313ee451e931"
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategory" DROP CONSTRAINT "FK_b34eb5c6311a840c8f151dd198f"
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategory" DROP CONSTRAINT "FK_a7ac5151122c715f8b1f286f737"
        `);
		await queryRunner.query(`
            ALTER TABLE "shoptetCategory" DROP CONSTRAINT "FK_465e3f8671567c587d71a61e4ba"
        `);
		await queryRunner.query(`
            ALTER TABLE "recyclingFee" DROP CONSTRAINT "FK_d660d6e0fe47862abd02aa263a1"
        `);
		await queryRunner.query(`
            ALTER TABLE "recyclingFee" DROP CONSTRAINT "FK_c1445c0baecf77493db46798aab"
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValueConditionalAttribute" DROP CONSTRAINT "FK_46ba4af1bcf8a4f7dbe2aefa5a8"
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValueConditionalAttribute" DROP CONSTRAINT "FK_b642f3cfafcc199f9938600ef0c"
        `);
		await queryRunner.query(`
            ALTER TABLE "attributeValueConditionalAttribute" DROP CONSTRAINT "FK_b9fc85880b7d9045283f0e703f1"
        `);
		await queryRunner.query(`
            ALTER TABLE "conditionalAttribute" DROP CONSTRAINT "FK_8f02ea9d90016ddf442471a5bb8"
        `);
		await queryRunner.query(`
            ALTER TABLE "conditionalAttribute" DROP CONSTRAINT "FK_5e2f956741b8e040af295fb1e4c"
        `);
		await queryRunner.query(`
            ALTER TABLE "productTest" DROP CONSTRAINT "FK_f75ee991be9073444ec68e3efe5"
        `);
		await queryRunner.query(`
            ALTER TABLE "productTest" DROP CONSTRAINT "FK_2700f50ad501a3123f80c23faeb"
        `);
		await queryRunner.query(`
            ALTER TABLE "productTest" DROP CONSTRAINT "FK_ee83ae7ce22733b0c97788c9ada"
        `);
		await queryRunner.query(`
            ALTER TABLE "productTest" DROP CONSTRAINT "FK_6567fbcc5e9e64e4108b2448e42"
        `);
		await queryRunner.query(`
            ALTER TABLE "user" DROP CONSTRAINT "FK_519e6553e4e02eba80a0d3989d4"
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask" DROP CONSTRAINT "FK_9eab14171403bdb72caf0d93f67"
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask" DROP CONSTRAINT "FK_6bb9960c2dbf3cdda9c67bda3ce"
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask" DROP CONSTRAINT "FK_a78dc43054c3058b87cf54e8a2c"
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask" DROP CONSTRAINT "FK_d192e157b7dcc827bb727598806"
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouse" DROP CONSTRAINT "FK_2281ad941d83f65a88cb0ac9c99"
        `);
		await queryRunner.query(`
            ALTER TABLE "warehousePosition" DROP CONSTRAINT "FK_370a4bc80d7524de2e27efc8c70"
        `);
		await queryRunner.query(`
            ALTER TABLE "warehousePosition" DROP CONSTRAINT "FK_440a23c145854c073f551027a4c"
        `);
		await queryRunner.query(`
            ALTER TABLE "productWarehouseTask" DROP CONSTRAINT "FK_ed047783973e2b0d8c81af09038"
        `);
		await queryRunner.query(`
            ALTER TABLE "productWarehouseTask" DROP CONSTRAINT "FK_5a130222de59f6257f07a10f705"
        `);
		await queryRunner.query(`
            ALTER TABLE "productWarehouseTask" DROP CONSTRAINT "FK_b0f6f83e795787dba8e58ac11c1"
        `);
		await queryRunner.query(`
            ALTER TABLE "shipment" DROP CONSTRAINT "FK_7b10b04682400501934ef2e8887"
        `);
		await queryRunner.query(`
            ALTER TABLE "shipment" DROP CONSTRAINT "FK_05a6268a9d538679bd5beba5ffe"
        `);
		await queryRunner.query(`
            ALTER TABLE "shipment" DROP CONSTRAINT "FK_a92bc88b88e244afe28c01b2e33"
        `);
		await queryRunner.query(`
            ALTER TABLE "shipment" DROP CONSTRAINT "FK_4feadbe728e249e7063db214539"
        `);
		await queryRunner.query(`
            ALTER TABLE "shipment" DROP CONSTRAINT "FK_8e067a22d48f3c8cde09115993b"
        `);
		await queryRunner.query(`
            ALTER TABLE "shipmentProduct" DROP CONSTRAINT "FK_2b8eca828d3cef5c1fe7cc1a82a"
        `);
		await queryRunner.query(`
            ALTER TABLE "shipmentProduct" DROP CONSTRAINT "FK_045eef5ef2ba6937177cb7c077c"
        `);
		await queryRunner.query(`
            ALTER TABLE "shipmentProduct" DROP CONSTRAINT "FK_841f4f9b72360cf0175b36d448f"
        `);
		await queryRunner.query(`
            ALTER TABLE "shipmentCode" DROP CONSTRAINT "FK_32a67f1b352a94437e165a71a7b"
        `);
		await queryRunner.query(`
            ALTER TABLE "savedFilter" DROP CONSTRAINT "FK_f1f29fb451206435201004e2f9f"
        `);
		await queryRunner.query(`
            ALTER TABLE "savedFilter" DROP CONSTRAINT "FK_691f0acc1b8d61c19419ac79697"
        `);
		await queryRunner.query(`
            ALTER TABLE "authentication" DROP CONSTRAINT "FK_b0a8dd2baaddf18f44779c42d37"
        `);
		await queryRunner.query(`
            ALTER TABLE "authentication" DROP CONSTRAINT "FK_2ce04c38ee386596885c903d9ad"
        `);
		await queryRunner.query(`
            ALTER TABLE "authentication" DROP CONSTRAINT "FK_be031dd812f7ce15ab2b3bcaffb"
        `);
		await queryRunner.query(`
            ALTER TABLE "role" DROP CONSTRAINT "FK_54aaa7ce0f59570c5ba5b686342"
        `);
		await queryRunner.query(`
            ALTER TABLE "scope" DROP CONSTRAINT "FK_02c1be68a79d6ebcf0bc2ec03e8"
        `);
		await queryRunner.query(`
            ALTER TABLE "action" DROP CONSTRAINT "FK_bf376bd23a893833bd63e3dd8a3"
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrderItem" DROP CONSTRAINT "FK_c6d281a26e222c4f7863a820091"
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrderItem" DROP CONSTRAINT "FK_c02dd8381d68f069fcc5f651b4e"
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrderItem" DROP CONSTRAINT "FK_3ab2501982ea6cd453d516e853f"
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrderItem" DROP CONSTRAINT "FK_41c29efb4b30c4dd975bcf866d2"
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP CONSTRAINT "FK_f9377b0a0562618cdcdf7892ce3"
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP CONSTRAINT "FK_1dd9d207d27bcc9b066824ae99b"
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP CONSTRAINT "FK_df3c045682423191ac201ff0569"
        `);
		await queryRunner.query(`
            ALTER TABLE "batchGroup" DROP CONSTRAINT "FK_0303997ca11597c2f83ce4df310"
        `);
		await queryRunner.query(`
            ALTER TABLE "batchGroup" DROP CONSTRAINT "FK_02e8a3b25a7d6cf87b1f0669f47"
        `);
		await queryRunner.query(`
            ALTER TABLE "batchGroupCode" DROP CONSTRAINT "FK_f598a413f6279e42130c5e43164"
        `);
		await queryRunner.query(`
            ALTER TABLE "batchDefect" DROP CONSTRAINT "FK_9d518d3f8b9db3e8aa91addd804"
        `);
		await queryRunner.query(`
            ALTER TABLE "batchDefect" DROP CONSTRAINT "FK_fb971fb371cab857d51ef802296"
        `);
		await queryRunner.query(`
            ALTER TABLE "batchCode" DROP CONSTRAINT "FK_616c3d893b25c48ad8247fd538a"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_91b51eae21eeb18cebf5e56d9f"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_fd4a95d52bcf38fefa9f5b5717"
        `);
		await queryRunner.query(`
            DROP TABLE "productCategory_closure"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_cdac7e4c33f3c1aae7994b8ef4"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_70850d5af8a741aa616550dba9"
        `);
		await queryRunner.query(`
            DROP TABLE "attributeValueServiceTask"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_6268a4bea48e57b7dbce43715f"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_025c678968c1ee13a58c22f646"
        `);
		await queryRunner.query(`
            DROP TABLE "roleScope"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_da21fc6efddeed0efcd0cdc639"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_16f362bb3d4641cf1a8becdcf6"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_11aee25a12cb0b07f6750cf3fe"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_c8dbd34233e7db74d63d33a5a5"
        `);
		await queryRunner.query(`
            DROP TABLE "wholesalePricing"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e5f4a48a825b8e8d8799dc2103"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_06b9ad79e52ecd9f57f1913d02"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_9b584b2c074e37658005887670"
        `);
		await queryRunner.query(`
            DROP TABLE "shoptetOrderSync"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d88753aee827c0bcae033810c6"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_dbbe71eb09a34902923133f57b"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_2522b85f17d56f84bd5f9965b3"
        `);
		await queryRunner.query(`
            DROP TABLE "productEnvelopeCode"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_12e6612123eba6db99643eb871"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f0beb691907f95f8e791f5e36d"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_4c9eedbc520f5438a503703726"
        `);
		await queryRunner.query(`
            DROP TABLE "printer"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRINTER_LOCATIONS"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f7be4981150b23fa62fe552c0c"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_49326795c180fd8c71fc929126"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f58ea9d22bc49f073b24871e5e"
        `);
		await queryRunner.query(`
            DROP TABLE "account"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_507e4f6f306f0c489fc9a3dfdb"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_7c84c55b891e7576bb2bb4c70b"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_aba8090534d8b8b8845784086c"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f61018bdc439c6d1a941261b67"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_9d9e875b701769efea460169b6"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_674aa06f94b490103c9c511056"
        `);
		await queryRunner.query(`
            DROP TABLE "vendor"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."BATCH_STATUS_TRANSITION_TIMESTAMPS"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e9b3a8f3565ba85961f2de3032"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_c948a852777a12d6da82177774"
        `);
		await queryRunner.query(`
            DROP TABLE "address"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_35021aa6159aa596d157ec4e53"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_60892e26101a13df299fc48f95"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ab6f3ac89cf28a74cd4e741e2c"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_056576d8e550afcf087f54d465"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_405f0ad0dc09e5c1d370d1cde0"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a13679271aa1759f69db5c864b"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_5957ce0613c5e67a7c385fe285"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_be57fe01dde74f04cc848e9a12"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_1591778ad9ad32b683fe9f073d"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_dbc3ef7fdcf4628061b6596dd3"
        `);
		await queryRunner.query(`
            DROP TABLE "ecommerceOrder"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_bca7555b07d24c10c026a89159"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_11df95cdada93ad29ba8d0b229"
        `);
		await queryRunner.query(`
            DROP TABLE "contact"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e91ba1fb3d37d90fb9cb339d0e"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_3aeca6e65c6b952d96fd8859b3"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_46631ee702dd09e8d54f9a23ad"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_4f3f798b21534a5b63c2fa6e92"
        `);
		await queryRunner.query(`
            DROP TABLE "serviceCenter"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e6aaf77baf8701eff86e71d7a9"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_2d124c1e9790625a90f96b5c89"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_770dcccb11dfad0db3c1eda33c"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_1a244b9dd4ebbe12600a356226"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_cb7983f7d3d3d6c9a5d00d724f"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_c3a31132a7651477f7a13d1ff8"
        `);
		await queryRunner.query(`
            DROP TABLE "serviceCase"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."SERVICE_CASE_STATUS_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_cfa6d3cd03f6d90ef290fe01ef"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_6de68e3b39244155b12fa6d6e5"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_dcf766b7a4bb0040cc6dd7649a"
        `);
		await queryRunner.query(`
            DROP TABLE "serviceCaseCode"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ca98b11852c3c677c8b941f880"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_4c387987a0fcb34bcb53c4616b"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_8af67ac221d6b39e55ef79462f"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ec69e14c72e132b48c445548d1"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_28b4944a40bd4438056c1c18c8"
        `);
		await queryRunner.query(`
            DROP TABLE "fileWarrantyClaim"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_bbfdaf9b8ad0791ad290b82227"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_79db0d546f7e6aead109cebcfb"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_0603eb633f19038692853c85f6"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_dc5bee18dfcb2471bd5d14091e"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_850104e91f4965cc55432f4598"
        `);
		await queryRunner.query(`
            DROP TABLE "fileVendor"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a63b7bc29dc722ed6fd185ac9f"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_3592b6a8129b7c62adcc7559f6"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_bee814cc3058a95c1bc52992b7"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_48c5d13c2837bdcc619d2d1fb3"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_71da6d2305f2fa5c7008cb9c4c"
        `);
		await queryRunner.query(`
            DROP TABLE "fileServiceCase"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_8baf1e2f109f9509a415427a74"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_734f16fa31dd2fa097368e83ca"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_55d354db4573f896138fa630f5"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_9fa0bb8b51f99c6f1a8cec47c0"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_66174b8e07740d83666c7401f0"
        `);
		await queryRunner.query(`
            DROP TABLE "fileProductDefect"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_36d8522edf0a8014e3be1580e4"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_50b298612e5f1b60c3fd9295df"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_fc7059771a2d42fa8bee797db5"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_5d5ac6eb0d91c1a877be9c3cc7"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_86ff2d0d44bbd581f85cae8d46"
        `);
		await queryRunner.query(`
            DROP TABLE "fileProduct"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_FILE_TYPE"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_9a8b261803bdc14cd338ea224f"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_79c653d66b118788fee2a3a876"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_02d1f430ba5798509c71605f09"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b3766f78a3e56635fd74c7df2f"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_dd089bc241c5db050f715a6894"
        `);
		await queryRunner.query(`
            DROP TABLE "fileBatchDefect"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_0eef81e5644d20dbb36c687809"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ed44b23cf3d41e412276ab4100"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_9956ac5374cbbe872fdd203ae2"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_0988e23fe31365673c1e785288"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_fa87fcda7a6c576ee77d4c20a6"
        `);
		await queryRunner.query(`
            DROP TABLE "fileBatch"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."BATCH_FILE_TYPE"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_df16ff3255e6dfc777b086949b"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f0f0fde52aebd504b31cbb9839"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_9791834a3e29ecc4a4c4cc2458"
        `);
		await queryRunner.query(`
            DROP TABLE "file"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_60f72cccb8a36ceee891d0d19d"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_571fa9d810a9f4b1b653a56f01"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e28ec0b4b5960fdee5917cb215"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d1adc6635d4bdb17fd6e60532c"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_8c26b473113c76ab9aa7fcc088"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_be3e6701efbb9b01daa4530f21"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_cfea8cb6e387bc8738a456865b"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_bb08f81affbcaa5f81cfce6c7c"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ca0d3265cb46e137fb02a920d9"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_7db1309021918672e7644b4882"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_2204328354d1b711ec7368cbc5"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_4a34b09ccdf00646a32e8f0109"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ec6dc77febfea9330cd988c922"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f6c147317a665322911d424966"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f7a1a202027c65b1bab794276a"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_bf0e1ab880d958feaf6f275adb"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_4f65e1770db73cefd4f74521ba"
        `);
		await queryRunner.query(`
            DROP TABLE "batch"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."BATCH_TYPES"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PAYMENT_STATUS_NAMES"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."BATCH_STATUS_NAMES"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."BATCH_DELIVERY_TYPE_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_887f1c7d857df6c2a956306e83"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d3442911e9c9cc656d24d2621c"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b1b7a8bb508d9ed48e779d1729"
        `);
		await queryRunner.query(`
            DROP TABLE "currencyRate"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_723472e41cae44beb0763f4039"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_77f11186dd58a8d87ad5fff024"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_fd52399f8d4f56f2ea5215d1ba"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_cf12ebf939da431c80cc3d88d9"
        `);
		await queryRunner.query(`
            DROP TABLE "currency"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."SUPPORTED_CURRENCIES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a7c24be296a22f842f2fb5b0c4"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_0d5c144b05ecf58534900027a0"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_97c2d49b66dc70ca1a43ad1009"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_1bc7d451790c8bdc516d030679"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_5b791c5b2667f0d24eb0b0cf94"
        `);
		await queryRunner.query(`
            DROP TABLE "productPrice"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_PRICE_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b0bfdff74ca3b7aa3545fefbba"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_618194d24a7ea86a165d7ec628"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_741c8e6d9b2a19dddb96e8d620"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_6c25e1fc008d926c3504c3c639"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_483ff5648459d1d4d2270c2dd2"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_01286e06a0554cbb19375f0178"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_22cc99fb1491212f1206ee39d1"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b916c32dfcf46f001167e6f903"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_3318e5ecf2f04ba0d4c3d21e0e"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ca058ed10f56f646c6e63c4af5"
        `);
		await queryRunner.query(`
            DROP TABLE "product"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_STATUS_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f9340c4297884c58c2da3cc35a"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ff626b061a1b4a0866b3654342"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_8b864b2bd249bc1c0a0a5d5f26"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_35731e9056378d0f168dfb32c0"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_63830a7d5015ddc2ff895964bc"
        `);
		await queryRunner.query(`
            DROP TABLE "warranty"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."WARRANTY_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_71998f7a83b18d7361d63f989f"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_12ffc5b1c8ad013c86be7d6c3f"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_835bfff37d69a79fdb82129255"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_101c0733ba57394c8479656a0c"
        `);
		await queryRunner.query(`
            DROP TABLE "productCode"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_5fdd5b0fee8ce1af4f2a89c772"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a29d4249658889a8180fa487c2"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e39e787c48fdc59c63c9f42b9e"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d0262d0e56d9f5125a63384402"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_91b5f4480d6c6939353f4be82d"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_dd8fefce9266128eb7dec80b4d"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_50263a83197641fde835bc65db"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d0bc51d992a144a431fe7d4240"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_0152b010228155e93798891f0d"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_bee9eede0cf860da5f31fba446"
        `);
		await queryRunner.query(`
            DROP TABLE "productAttributeValue"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_ATTRIBUTE_VALUE_TYPE"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_0d80a1af74d132534671602801"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_95661c33dab914746ca0c4468e"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b5248cba6dbe72badb32092ad2"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_bdd5ca2e2f7951a257f91f9de8"
        `);
		await queryRunner.query(`
            DROP TABLE "attributeValueAlternative"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_207b389edc2145dfe130c5179d"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_34920d38b502e1a4970960ebdd"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_5f7bc12e31ee2541e76bb9ecc6"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_5120c24e8f99359551e4387011"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_4f4349b05760a6d94041cd5e88"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_825d94c1fd0aa70cd3a045a74a"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_855bf07c5b4f9a00f3523161bf"
        `);
		await queryRunner.query(`
            DROP TABLE "attributeValue"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e0c25dc6a196d051fb78810943"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b205c0cf8a0a166adf857ef648"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d8e74dc0c0086c65cf79d0a005"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_33fc742d7b5edff5c4e8a65dd1"
        `);
		await queryRunner.query(`
            DROP TABLE "attributeNameAlternative"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_53a371f596b5bb848b02482929"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d0769b42012663c9e56df3c876"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_350fb4f7eb87e4c7d35c97a982"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_5f6cf10ee3642d9d9a444d73fb"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_fe79dbcbfc2a46ff35aba76050"
        `);
		await queryRunner.query(`
            DROP TABLE "attribute"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_ATTRIBUTE_DATATYPE"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a68bf2ba56d2f343b2fd118653"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_c1372118f41d1a8307bbe899b3"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_88a6275de9c1b72c352d0582be"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_cd15bcdf1fb75555fa43267da7"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_02088f54d81c96564d70224fce"
        `);
		await queryRunner.query(`
            DROP TABLE "shoptetAttribute"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_cd84eaf504186bee6cbf09327c"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e6c48e0b90475e2c27b8543933"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_31b0b0cdb765f68d914d3cda61"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e2c39c3d9860c6836d44d356c7"
        `);
		await queryRunner.query(`
            DROP TABLE "serviceTask"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."SERVICE_TASK_STATUS_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e4be1d81ab5328d61240e59397"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_eaff97695cabb878925f446151"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_78da752d3eb715a15009fb6796"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_911a25040b79f5ddff739d4e0e"
        `);
		await queryRunner.query(`
            DROP TABLE "serviceTaskType"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."SERVICE_TASK_TYPE_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d398a30d5be2a6220983a4f44e"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_1187d6c76d8664b13c0a8b459b"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_2ae70c935c09dd3d3bbffad48e"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_89e81cc1e2fcb0206f2b7d3623"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_630fd38f02c33fe57c3160e7b4"
        `);
		await queryRunner.query(`
            DROP TABLE "productTask"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_TASK_STATUS_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_16846f861b619f1d3315e608a1"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_4cecdb1f5e6942dfbea66cda9b"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_72ecb7c1727c251c64bf29fa33"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_410a4f9077a3b113d9906913d0"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_0726ee040109cbafa3bbe7784a"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_7625336d6bc2aabeee8f6af3ea"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_1026a5f82fb6534eea7a7a49ab"
        `);
		await queryRunner.query(`
            DROP TABLE "productDefect"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_cde3cec9ae4f04709bb4835623"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_0a9de6455f156ee46012df4a18"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_0f658163bd3bbe76d1c0818403"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d6128fa8c966887d489f3a6cb1"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f3b66e9640ebc9a340188b7d70"
        `);
		await queryRunner.query(`
            DROP TABLE "warrantyClaim"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."WARRANTY_CLAIM_STATUS_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_76654d8dc74fd868e1e3f1a260"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_768205d59e10defe68c5fde4be"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a3b2e371af7d019f91efd42754"
        `);
		await queryRunner.query(`
            DROP TABLE "warrantyClaimCode"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_20e994fe6d46d90bf3017494ed"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f9fd163fd6b24a2677b7e9a2c8"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_2112482b7cea59d055fb2c57a9"
        `);
		await queryRunner.query(`
            DROP TABLE "defectType"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_de4d8709c72f874398bd31ba0c"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ed34c68375c8ea12d442831af3"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_9c653f4f63b4590b8c9fed8416"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a0c81ed8df4cb6ab6a55c691f6"
        `);
		await queryRunner.query(`
            DROP TABLE "vendorDefectType"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a2f3eb862794c7cf2fccfa4e43"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b833891e239c3162c742dedc4e"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f49549471d70fc9380c35d92c5"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a2aa943fcf1690244c59898756"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_087294b59b4b45f11a64c22dd5"
        `);
		await queryRunner.query(`
            DROP TABLE "productEnvelopeAttributeValue"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_3f0c1f090a9558aad1abb776c2"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_86f9acfe9dea6f7c0a96faa949"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_15b3a7397e68a752de73fcded3"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f00b50f6dab8c094ccdcfd1274"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_8a85400fbd46b60b0eef60671e"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b1bb75f893729d164ad5f1a4f5"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_519deb886e0e451a1edb6209c0"
        `);
		await queryRunner.query(`
            DROP TABLE "productCategoryAttribute"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_CATEGORY_ATTRIBUTE_TYPE"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_0c550d237dcfea9fe6913a34ff"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_471a63c1df20127313ee451e93"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e2a843ecd0d13aed187a1ae527"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_8f2bbb7ead10e637c55e0c86b1"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ca32e610aa6625219fe3547178"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a7ac5151122c715f8b1f286f73"
        `);
		await queryRunner.query(`
            DROP TABLE "productCategory"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_eeae297b4edda040d013323dc3"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_7793587f7539dad68c1d68f1b2"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_8362d3d810338525e0160644a8"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_465e3f8671567c587d71a61e4b"
        `);
		await queryRunner.query(`
            DROP TABLE "shoptetCategory"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d660d6e0fe47862abd02aa263a"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_bb7a71e0ccf3e0f2a4d5d0deca"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_c83241c869b82f42977126d235"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_5a7da1081796c93d28b12a2575"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_c1445c0baecf77493db46798aa"
        `);
		await queryRunner.query(`
            DROP TABLE "recyclingFee"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_46ba4af1bcf8a4f7dbe2aefa5a"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b642f3cfafcc199f9938600ef0"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_6da1fab004046bf772e60a29a1"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b9fc85880b7d9045283f0e703f"
        `);
		await queryRunner.query(`
            DROP TABLE "attributeValueConditionalAttribute"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_8f02ea9d90016ddf442471a5bb"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_259082c254419ee71277b72891"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_5e2f956741b8e040af295fb1e4"
        `);
		await queryRunner.query(`
            DROP TABLE "conditionalAttribute"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f75ee991be9073444ec68e3efe"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_2700f50ad501a3123f80c23fae"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ee83ae7ce22733b0c97788c9ad"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_6f3774d9bdd459e658961cc042"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d00f3be831f0051ab8c58a7457"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ae6aca9a859343ab3dde3b2700"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_6567fbcc5e9e64e4108b2448e4"
        `);
		await queryRunner.query(`
            DROP TABLE "productTest"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e12875dfb3b1d92d7d7c5377e2"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_3acde74098c180846b5c5ab4ec"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_519e6553e4e02eba80a0d3989d"
        `);
		await queryRunner.query(`
            DROP TABLE "user"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_9eab14171403bdb72caf0d93f6"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_6bb9960c2dbf3cdda9c67bda3c"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a78dc43054c3058b87cf54e8a2"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b78e494b7c5e466d14c10871a8"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_bd58da3c2f99baf7a05c710953"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d10490c32318c72fde4ada385d"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d192e157b7dcc827bb72759880"
        `);
		await queryRunner.query(`
            DROP TABLE "warehouseTask"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."WAREHOUSE_TASK_STATUSES"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."WAREHOUSE_TASK_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d036b2db4cc472e34e013541d8"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f765e9834b961ab2cb27fc6f99"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d5d5470e55d4238b1239e9f154"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_616aeeb0de4896928462a613d6"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_2281ad941d83f65a88cb0ac9c9"
        `);
		await queryRunner.query(`
            DROP TABLE "warehouse"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."WAREHOUSE_TYPE_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e8a92c7116b1aa1d260fa774ac"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e33fe33c6c5004192525df5583"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_93d3feca5ac22210cb259bfa51"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_c80baa915152bab0da69ce92c7"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_c88d1f3fd8ceb318b29a9aba20"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_83b24c81bc659c92dd2d41bcad"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_65b310c10bcdd39d7deb458cfa"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_370a4bc80d7524de2e27efc8c7"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_950458a6b7f21964aae97d20fa"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a247c2db6f1343c4c610c0b24c"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_7c5f6c9b90c250d61717c56c8e"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_30e70ba38c62692f96ef5eebb2"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_77de529d19bb639f8005b3a0c9"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_440a23c145854c073f551027a4"
        `);
		await queryRunner.query(`
            DROP TABLE "warehousePosition"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ed047783973e2b0d8c81af0903"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_5a130222de59f6257f07a10f70"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d0424a8ea8686b6ce873060399"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b0f6f83e795787dba8e58ac11c"
        `);
		await queryRunner.query(`
            DROP TABLE "productWarehouseTask"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_7b10b04682400501934ef2e888"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_05a6268a9d538679bd5beba5ff"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a92bc88b88e244afe28c01b2e3"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a39d9ba392f61de135aa3c9bea"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_4feadbe728e249e7063db21453"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f7c7dd9bde72cbf95de3fc708a"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_75b5f089f72a5671a4f65efbcc"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_6a335e8e0cec3d4b585590a007"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_8e067a22d48f3c8cde09115993"
        `);
		await queryRunner.query(`
            DROP TABLE "shipment"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."SHIPMENT_STATUS_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_2b8eca828d3cef5c1fe7cc1a82"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_045eef5ef2ba6937177cb7c077"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_164630d0591f938d6637a92d94"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_841f4f9b72360cf0175b36d448"
        `);
		await queryRunner.query(`
            DROP TABLE "shipmentProduct"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b58e4286f406f669ee9114eb89"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_215a8307229e4d743b2a2a0588"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_32a67f1b352a94437e165a71a7"
        `);
		await queryRunner.query(`
            DROP TABLE "shipmentCode"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f1f29fb451206435201004e2f9"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_9900d1d2ea97ce9539dfe535f6"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_8d9427fd7cbe00ed9ecf771558"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_9096f5f427ad98ffb0b8991c19"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_691f0acc1b8d61c19419ac7969"
        `);
		await queryRunner.query(`
            DROP TABLE "savedFilter"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b0a8dd2baaddf18f44779c42d3"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_2ce04c38ee386596885c903d9a"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_81daff1b812753e0aa6ffe960b"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_be031dd812f7ce15ab2b3bcaff"
        `);
		await queryRunner.query(`
            DROP TABLE "authentication"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."AUTH_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ae4578dcaed5adff96595e6166"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a565c8bb3c45c044d9e2e2a1f9"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_54aaa7ce0f59570c5ba5b68634"
        `);
		await queryRunner.query(`
            DROP TABLE "role"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_43d560104ffd3074475049468c"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_6e0859e36e5749cb799a71b276"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_02c1be68a79d6ebcf0bc2ec03e"
        `);
		await queryRunner.query(`
            DROP TABLE "scope"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."SCOPE_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_bf376bd23a893833bd63e3dd8a"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ec10bd0dff5f5e6d7176f0f441"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_5165a3df690967baa74c183197"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_022328d6ae0993411291b5b974"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e71b65767b8c972fdc972c878f"
        `);
		await queryRunner.query(`
            DROP TABLE "action"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."HTTP_METHODS_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_c6d281a26e222c4f7863a82009"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_c02dd8381d68f069fcc5f651b4"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_3ab2501982ea6cd453d516e853"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b445c7040466a8cdc201b59ef2"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_4bbc84191e01f3e760138ee32f"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_346eae8d0fee27200c51f0eb3a"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_cbd2a77a90cf0b9bf454e4692b"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_41c29efb4b30c4dd975bcf866d"
        `);
		await queryRunner.query(`
            DROP TABLE "ecommerceOrderItem"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."ORDER_ITEM_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f9377b0a0562618cdcdf7892ce"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_1dd9d207d27bcc9b066824ae99"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_89fcce02c215fe35a2646a8bba"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_7bdb9c305840a30ed950c9f7bc"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_df3c045682423191ac201ff056"
        `);
		await queryRunner.query(`
            DROP TABLE "productEnvelope"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_0303997ca11597c2f83ce4df31"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_2bd493c5ffe99d5cd2e1701467"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_02e8a3b25a7d6cf87b1f0669f4"
        `);
		await queryRunner.query(`
            DROP TABLE "batchGroup"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_fab7758250146bf6a5bdbfddb3"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_73b49cfc290e1d05ea9c3dd0d6"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f598a413f6279e42130c5e4316"
        `);
		await queryRunner.query(`
            DROP TABLE "batchGroupCode"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_9d518d3f8b9db3e8aa91addd80"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b8ce08291f6c6f2c0cf246f93e"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_fb971fb371cab857d51ef80229"
        `);
		await queryRunner.query(`
            DROP TABLE "batchDefect"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_8a2eb576861c0e91941a4c39f1"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_446aaa371fdee04174158ef17f"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_616c3d893b25c48ad8247fd538"
        `);
		await queryRunner.query(`
            DROP TABLE "batchCode"
        `);
	}
}
