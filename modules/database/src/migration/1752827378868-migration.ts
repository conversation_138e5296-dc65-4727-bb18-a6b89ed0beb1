import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1752827378868 implements MigrationInterface {
    name = 'Migration1752827378868'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "warehousePositionCode" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "code" SERIAL NOT NULL,
                CONSTRAINT "UQ_af7e13e8a2e72d62552022a7b34" UNIQUE ("code"),
                CONSTRAINT "PK_0bc8caa3026547a8890209969d9" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_e189fe1a4a77afc787daa8ce46" ON "warehousePositionCode" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_c3566bd17d6df1474834c4a430" ON "warehousePositionCode" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_af7e13e8a2e72d62552022a7b3" ON "warehousePositionCode" ("code")
        `);
        await queryRunner.query(`
            ALTER TABLE "warehousePosition"
            ADD "codeId" uuid NOT NULL
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_51a4dbbab110426fd30ece47cc" ON "warehousePosition" ("codeId")
        `);
        await queryRunner.query(`
            ALTER TABLE "warehousePositionCode"
            ADD CONSTRAINT "FK_e189fe1a4a77afc787daa8ce46b" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "warehousePosition"
            ADD CONSTRAINT "FK_51a4dbbab110426fd30ece47cc3" FOREIGN KEY ("codeId") REFERENCES "warehousePositionCode"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "warehousePosition" DROP CONSTRAINT "FK_51a4dbbab110426fd30ece47cc3"
        `);
        await queryRunner.query(`
            ALTER TABLE "warehousePositionCode" DROP CONSTRAINT "FK_e189fe1a4a77afc787daa8ce46b"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_51a4dbbab110426fd30ece47cc"
        `);
        await queryRunner.query(`
            ALTER TABLE "warehousePosition" DROP COLUMN "codeId"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_af7e13e8a2e72d62552022a7b3"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_c3566bd17d6df1474834c4a430"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_e189fe1a4a77afc787daa8ce46"
        `);
        await queryRunner.query(`
            DROP TABLE "warehousePositionCode"
        `);
    }

}
