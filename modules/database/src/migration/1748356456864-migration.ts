import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1748356456864 implements MigrationInterface {
    name = 'Migration1748356456864'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productAttributeValue"
            ADD "isFix" boolean NOT NULL DEFAULT false
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_b406b93a8cf4b4ccecfb0bf47c" ON "productAttributeValue" ("isFix")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX "public"."IDX_b406b93a8cf4b4ccecfb0bf47c"
        `);
        await queryRunner.query(`
            ALTER TABLE "productAttributeValue" DROP COLUMN "isFix"
        `);
    }

}
