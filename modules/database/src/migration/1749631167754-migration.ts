import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1749631167754 implements MigrationInterface {
    name = 'Migration1749631167754'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "note" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "content" text NOT NULL DEFAULT '',
                "createdById" uuid NOT NULL,
                "warrantyClaimId" uuid,
                "serviceCaseId" uuid,
                CONSTRAINT "PK_96d0c172a4fba276b1bbed43058" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_cc325f65e21a11a4a97cc5f9d1" ON "note" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_d86fa7be62f96f85f4a9eff6c0" ON "note" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_1100c955b41aeaca61ddd9308d" ON "note" ("createdById")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_e26bc72405216e5fa8c3444d22" ON "note" ("warrantyClaimId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_c13f7ac881b12ccb934e8db14c" ON "note" ("serviceCaseId")
        `);
        await queryRunner.query(`
            ALTER TABLE "warrantyClaim" DROP COLUMN "note"
        `);
        await queryRunner.query(`
            ALTER TABLE "serviceCase" DROP COLUMN "note"
        `);
        await queryRunner.query(`
            ALTER TABLE "note"
            ADD CONSTRAINT "FK_cc325f65e21a11a4a97cc5f9d13" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "note"
            ADD CONSTRAINT "FK_1100c955b41aeaca61ddd9308d4" FOREIGN KEY ("createdById") REFERENCES "user"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "note"
            ADD CONSTRAINT "FK_e26bc72405216e5fa8c3444d220" FOREIGN KEY ("warrantyClaimId") REFERENCES "warrantyClaim"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "note"
            ADD CONSTRAINT "FK_c13f7ac881b12ccb934e8db14c4" FOREIGN KEY ("serviceCaseId") REFERENCES "serviceCase"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "note" DROP CONSTRAINT "FK_c13f7ac881b12ccb934e8db14c4"
        `);
        await queryRunner.query(`
            ALTER TABLE "note" DROP CONSTRAINT "FK_e26bc72405216e5fa8c3444d220"
        `);
        await queryRunner.query(`
            ALTER TABLE "note" DROP CONSTRAINT "FK_1100c955b41aeaca61ddd9308d4"
        `);
        await queryRunner.query(`
            ALTER TABLE "note" DROP CONSTRAINT "FK_cc325f65e21a11a4a97cc5f9d13"
        `);
        await queryRunner.query(`
            ALTER TABLE "serviceCase"
            ADD "note" text NOT NULL DEFAULT ''
        `);
        await queryRunner.query(`
            ALTER TABLE "warrantyClaim"
            ADD "note" text NOT NULL DEFAULT ''
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_c13f7ac881b12ccb934e8db14c"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_e26bc72405216e5fa8c3444d22"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_1100c955b41aeaca61ddd9308d"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_d86fa7be62f96f85f4a9eff6c0"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_cc325f65e21a11a4a97cc5f9d1"
        `);
        await queryRunner.query(`
            DROP TABLE "note"
        `);
    }

}
