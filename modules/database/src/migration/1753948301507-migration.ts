import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1753948301507 implements MigrationInterface {
	name = 'Migration1753948301507';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "warehouseTask"
            ADD "productId" uuid NOT NULL
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_47a4d404b82b6fb66d2def85b3" ON "warehouseTask" ("productId")
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask"
            ADD CONSTRAINT "FK_47a4d404b82b6fb66d2def85b3c" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "warehouseTask" DROP CONSTRAINT "FK_47a4d404b82b6fb66d2def85b3c"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_47a4d404b82b6fb66d2def85b3"
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask" DROP COLUMN "productId"
        `);
	}
}
