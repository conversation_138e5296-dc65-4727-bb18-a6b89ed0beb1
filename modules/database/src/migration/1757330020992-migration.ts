import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1757330020992 implements MigrationInterface {
    name = 'Migration1757330020992'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "batch"
            ALTER COLUMN "checkSn"
            SET DEFAULT true
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "batch"
            ALTER COLUMN "checkSn"
            SET DEFAULT false
        `);
    }

}
