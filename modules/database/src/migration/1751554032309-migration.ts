import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1751554032309 implements MigrationInterface {
	name = 'Migration1751554032309';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "batchId" uuid
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "ecommerceOrderCode" character varying
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "serviceCaseCode" integer
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "customerClaimCode" integer
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "warrantyClaimCode" integer
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ccc990ca931231bd4def52f965" ON "inventoryItem" ("batchId")
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD CONSTRAINT "FK_ccc990ca931231bd4def52f9651" FOREIGN KEY ("batchId") REFERENCES "batch"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP CONSTRAINT "FK_ccc990ca931231bd4def52f9651"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ccc990ca931231bd4def52f965"
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "warrantyClaimCode"
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "customerClaimCode"
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "serviceCaseCode"
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "ecommerceOrderCode"
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "batchId"
        `);
	}
}
