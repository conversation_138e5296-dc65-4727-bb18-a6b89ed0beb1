import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1754317740608 implements MigrationInterface {
	name = 'Migration1754317740608';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "shoptetBrand"
            ADD "code" character varying NOT NULL
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_59eeaa85a6200fc814d5c31fef" ON "shoptetBrand" ("code")
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            DROP INDEX "public"."IDX_59eeaa85a6200fc814d5c31fef"
        `);
		await queryRunner.query(`
            ALTER TABLE "shoptetBrand" DROP COLUMN "code"
        `);
	}
}
