import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1750696704107 implements MigrationInterface {
    name = 'Migration1750696704107'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "customerClaimCode" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "code" SERIAL NOT NULL,
                CONSTRAINT "UQ_0bef37be50b4d25777e64d88058" UNIQUE ("code"),
                CONSTRAINT "PK_8c7106042baeca1cf85d5763785" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_612a53c50d4800a9d375c8d7b6" ON "customerClaimCode" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_d25224d08d35b0c26530cdbf7d" ON "customerClaimCode" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_0bef37be50b4d25777e64d8805" ON "customerClaimCode" ("code")
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."CUSTOMER_CLAIM_STATUS_NAMES" AS ENUM('NEW', 'CLOSED')
        `);
        await queryRunner.query(`
            CREATE TABLE "customerClaim" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "createdById" uuid,
                "status" "public"."CUSTOMER_CLAIM_STATUS_NAMES" NOT NULL DEFAULT 'NEW',
                "codeId" uuid NOT NULL,
                CONSTRAINT "PK_c8749ba60f90fd0cce9b0925389" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_e07174bbd6cf2c50f813e5a06c" ON "customerClaim" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_4f7b205021948c950c6885bfed" ON "customerClaim" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_aeaf78b79ac1d88310d8a5adb2" ON "customerClaim" ("createdById")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_d4df559e273cd276f34b614194" ON "customerClaim" ("status")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_1a0f10091f700f3538b1910325" ON "customerClaim" ("codeId")
        `);
        await queryRunner.query(`
            ALTER TABLE "productDefect"
            ADD "customerClaimId" uuid
        `);
        await queryRunner.query(`
            ALTER TABLE "note"
            ADD "customerClaimId" uuid
        `);
        await queryRunner.query(`
            ALTER TYPE "public"."SCOPE_NAMES"
            RENAME TO "SCOPE_NAMES_old"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."SCOPE_NAMES" AS ENUM(
                'admin',
                'home',
                'batchRead',
                'batchWrite',
                'batchCheck',
                'batchDelivery',
                'customerClaimRead',
                'customerClaimWrite',
                'envelopeWrite',
                'productTest',
                'productTestLead',
                'testRead',
                'serviceRead',
                'serviceWrite',
                'serviceManage',
                'warrantyClaimRead',
                'warrantyClaimWrite',
                'stock',
                'productRead',
                'productWrite',
                'orderRead',
                'orderWrite',
                'productAdmin'
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "scope"
            ALTER COLUMN "name" TYPE "public"."SCOPE_NAMES" USING "name"::"text"::"public"."SCOPE_NAMES"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."SCOPE_NAMES_old"
        `);
        await queryRunner.query(`
            ALTER TYPE "public"."PRODUCT_DEFECT_SOURCE"
            RENAME TO "PRODUCT_DEFECT_SOURCE_old"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_DEFECT_SOURCE" AS ENUM(
                'TESTING',
                'SERVICE_CASE',
                'WARRANTY_CLAIM',
                'CUSTOMER_CLAIM'
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "productDefect"
            ALTER COLUMN "source" DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TABLE "productDefect"
            ALTER COLUMN "source" TYPE "public"."PRODUCT_DEFECT_SOURCE" USING "source"::"text"::"public"."PRODUCT_DEFECT_SOURCE"
        `);
        await queryRunner.query(`
            ALTER TABLE "productDefect"
            ALTER COLUMN "source"
            SET DEFAULT 'TESTING'
        `);
        await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_DEFECT_SOURCE_old"
        `);
        await queryRunner.query(`
            ALTER TYPE "public"."PRODUCT_STATUS_NAMES"
            RENAME TO "PRODUCT_STATUS_NAMES_old"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_STATUS_NAMES" AS ENUM(
                'AT_SUPPLIER',
                'ON_THE_WAY',
                'EXTERNAL_STOCK',
                'TO_CHECK',
                'TO_CHECK_SN',
                'TO_TEST',
                'TESTED',
                'MISSING',
                'SERVICE',
                'WARRANTY_CLAIM',
                'STOCK',
                'FOR_SALE',
                'RESERVED',
                'SOLD',
                'CUSTOMER_CLAIM',
                'DEAD',
                'AUTOPSY',
                'WAITING_FOR_SHIPMENT',
                'SHIPMENT_DISPATCHED',
                'SHIPMENT_DELIVERED'
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status" DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status" TYPE "public"."PRODUCT_STATUS_NAMES" USING "status"::"text"::"public"."PRODUCT_STATUS_NAMES"
        `);
        await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status"
            SET DEFAULT 'AT_SUPPLIER'
        `);
        await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_STATUS_NAMES_old"
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_f9ab7760cba6787e49c9780721" ON "productDefect" ("customerClaimId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_ec5245afcc315aa027df6cfec7" ON "note" ("customerClaimId")
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimCode"
            ADD CONSTRAINT "FK_612a53c50d4800a9d375c8d7b6a" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "productDefect"
            ADD CONSTRAINT "FK_f9ab7760cba6787e49c9780721e" FOREIGN KEY ("customerClaimId") REFERENCES "customerClaim"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "note"
            ADD CONSTRAINT "FK_ec5245afcc315aa027df6cfec79" FOREIGN KEY ("customerClaimId") REFERENCES "customerClaim"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD CONSTRAINT "FK_e07174bbd6cf2c50f813e5a06c9" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD CONSTRAINT "FK_aeaf78b79ac1d88310d8a5adb20" FOREIGN KEY ("createdById") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD CONSTRAINT "FK_1a0f10091f700f3538b1910325f" FOREIGN KEY ("codeId") REFERENCES "customerClaimCode"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP CONSTRAINT "FK_1a0f10091f700f3538b1910325f"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP CONSTRAINT "FK_aeaf78b79ac1d88310d8a5adb20"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP CONSTRAINT "FK_e07174bbd6cf2c50f813e5a06c9"
        `);
        await queryRunner.query(`
            ALTER TABLE "note" DROP CONSTRAINT "FK_ec5245afcc315aa027df6cfec79"
        `);
        await queryRunner.query(`
            ALTER TABLE "productDefect" DROP CONSTRAINT "FK_f9ab7760cba6787e49c9780721e"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaimCode" DROP CONSTRAINT "FK_612a53c50d4800a9d375c8d7b6a"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_ec5245afcc315aa027df6cfec7"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_f9ab7760cba6787e49c9780721"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_STATUS_NAMES_old" AS ENUM(
                'AT_SUPPLIER',
                'ON_THE_WAY',
                'EXTERNAL_STOCK',
                'TO_CHECK',
                'TO_CHECK_SN',
                'TO_TEST',
                'TESTED',
                'MISSING',
                'SERVICE',
                'WARRANTY_CLAIM',
                'STOCK',
                'FOR_SALE',
                'RESERVED',
                'SOLD',
                'DEAD',
                'AUTOPSY',
                'WAITING_FOR_SHIPMENT',
                'SHIPMENT_DISPATCHED',
                'SHIPMENT_DELIVERED'
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status" DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status" TYPE "public"."PRODUCT_STATUS_NAMES_old" USING "status"::"text"::"public"."PRODUCT_STATUS_NAMES_old"
        `);
        await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status"
            SET DEFAULT 'AT_SUPPLIER'
        `);
        await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_STATUS_NAMES"
        `);
        await queryRunner.query(`
            ALTER TYPE "public"."PRODUCT_STATUS_NAMES_old"
            RENAME TO "PRODUCT_STATUS_NAMES"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_DEFECT_SOURCE_old" AS ENUM('TESTING', 'SERVICE_CASE', 'WARRANTY_CLAIM')
        `);
        await queryRunner.query(`
            ALTER TABLE "productDefect"
            ALTER COLUMN "source" DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TABLE "productDefect"
            ALTER COLUMN "source" TYPE "public"."PRODUCT_DEFECT_SOURCE_old" USING "source"::"text"::"public"."PRODUCT_DEFECT_SOURCE_old"
        `);
        await queryRunner.query(`
            ALTER TABLE "productDefect"
            ALTER COLUMN "source"
            SET DEFAULT 'TESTING'
        `);
        await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_DEFECT_SOURCE"
        `);
        await queryRunner.query(`
            ALTER TYPE "public"."PRODUCT_DEFECT_SOURCE_old"
            RENAME TO "PRODUCT_DEFECT_SOURCE"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."SCOPE_NAMES_old" AS ENUM(
                'admin',
                'home',
                'batchRead',
                'batchWrite',
                'batchCheck',
                'batchDelivery',
                'envelopeWrite',
                'productTest',
                'productTestLead',
                'testRead',
                'serviceRead',
                'serviceWrite',
                'serviceManage',
                'warrantyClaimRead',
                'warrantyClaimWrite',
                'stock',
                'productRead',
                'productWrite',
                'orderRead',
                'orderWrite',
                'productAdmin'
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "scope"
            ALTER COLUMN "name" TYPE "public"."SCOPE_NAMES_old" USING "name"::"text"::"public"."SCOPE_NAMES_old"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."SCOPE_NAMES"
        `);
        await queryRunner.query(`
            ALTER TYPE "public"."SCOPE_NAMES_old"
            RENAME TO "SCOPE_NAMES"
        `);
        await queryRunner.query(`
            ALTER TABLE "note" DROP COLUMN "customerClaimId"
        `);
        await queryRunner.query(`
            ALTER TABLE "productDefect" DROP COLUMN "customerClaimId"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_1a0f10091f700f3538b1910325"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_d4df559e273cd276f34b614194"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_aeaf78b79ac1d88310d8a5adb2"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_4f7b205021948c950c6885bfed"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_e07174bbd6cf2c50f813e5a06c"
        `);
        await queryRunner.query(`
            DROP TABLE "customerClaim"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."CUSTOMER_CLAIM_STATUS_NAMES"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_0bef37be50b4d25777e64d8805"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_d25224d08d35b0c26530cdbf7d"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_612a53c50d4800a9d375c8d7b6"
        `);
        await queryRunner.query(`
            DROP TABLE "customerClaimCode"
        `);
    }

}
