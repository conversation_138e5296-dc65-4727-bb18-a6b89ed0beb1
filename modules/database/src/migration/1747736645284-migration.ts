import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1747736645284 implements MigrationInterface {
	name = 'Migration1747736645284';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            DROP INDEX "public"."IDX_3f0c1f090a9558aad1abb776c2"
        `);
		await queryRunner.query(`
            ALTER TYPE "public"."PRODUCT_CATEGORY_ATTRIBUTE_TYPE"
            RENAME TO "PRODUCT_CATEGORY_ATTRIBUTE_TYPE_old"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_CATEGORY_ATTRIBUTE_TYPE" AS ENUM(
                'default',
                'testing',
                'compare',
                'mandatory',
                'envelope',
                'name',
                'export',
                'list',
                'config',
                'stock',
                'report'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategoryAttribute"
            ALTER COLUMN "type" TYPE "public"."PRODUCT_CATEGORY_ATTRIBUTE_TYPE" USING "type"::"text"::"public"."PRODUCT_CATEGORY_ATTRIBUTE_TYPE"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_CATEGORY_ATTRIBUTE_TYPE_old"
        `);
		await queryRunner.query(`
			CREATE UNIQUE INDEX "IDX_3f0c1f090a9558aad1abb776c2" ON "productCategoryAttribute" ("type", "attributeId", "productCategoryId")
		`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            DROP INDEX "public"."IDX_3f0c1f090a9558aad1abb776c2"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_CATEGORY_ATTRIBUTE_TYPE_old" AS ENUM(
                'compare',
                'default',
                'envelope',
                'export',
                'list',
                'mandatory',
                'name',
                'report',
                'stock',
                'testing'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategoryAttribute"
            ALTER COLUMN "type" TYPE "public"."PRODUCT_CATEGORY_ATTRIBUTE_TYPE_old" USING "type"::"text"::"public"."PRODUCT_CATEGORY_ATTRIBUTE_TYPE_old"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_CATEGORY_ATTRIBUTE_TYPE"
        `);
		await queryRunner.query(`
            ALTER TYPE "public"."PRODUCT_CATEGORY_ATTRIBUTE_TYPE_old"
            RENAME TO "PRODUCT_CATEGORY_ATTRIBUTE_TYPE"
        `);
		await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_3f0c1f090a9558aad1abb776c2" ON "productCategoryAttribute" ("attributeId", "productCategoryId", "type")
        `);
	}
}
