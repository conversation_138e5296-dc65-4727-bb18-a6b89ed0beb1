import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1749047021831 implements MigrationInterface {
	name = 'Migration1749047021831';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TYPE "public"."PRODUCT_PRICE_TYPES"
            RENAME TO "PRODUCT_PRICE_TYPES_old"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_PRICE_TYPES" AS ENUM(
                'BUY',
                'DELIVERY',
                'SELL',
                'RECOMMENDED',
                'STANDARD',
                'DISCOUNT',
                'SERVICE',
                'CUSTOMER_CLAIM'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "productPrice"
            ALTER COLUMN "type" TYPE "public"."PRODUCT_PRICE_TYPES" USING "type"::"text"::"public"."PRODUCT_PRICE_TYPES"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_PRICE_TYPES_old"
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_PRICE_TYPES_old" AS ENUM(
                'BUY',
                'DELIVERY',
                'DISCOUNT',
                'RECOMMENDED',
                'SELL',
                'SERVICE',
                'STANDARD'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "productPrice"
            ALTER COLUMN "type" TYPE "public"."PRODUCT_PRICE_TYPES_old" USING "type"::"text"::"public"."PRODUCT_PRICE_TYPES_old"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_PRICE_TYPES"
        `);
		await queryRunner.query(`
            ALTER TYPE "public"."PRODUCT_PRICE_TYPES_old"
            RENAME TO "PRODUCT_PRICE_TYPES"
        `);
	}
}
