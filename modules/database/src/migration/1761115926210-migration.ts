import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1761115926210 implements MigrationInterface {
	name = 'Migration1761115926210';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            DROP INDEX "public"."IDX_5165a3df690967baa74c183197"
        `);
		await queryRunner.query(`
            ALTER TABLE "action"
			ALTER COLUMN "method" TYPE varchar USING "method"::varchar;
        `);
		await queryRunner.query(`
            ALTER TABLE "action"
            ALTER COLUMN "method"
            SET NOT NULL
        `);
		await queryRunner.query(`
            DROP TYPE "public"."HTTP_METHODS_NAMES"
        `);
		await queryRunner.query(`
            ALTER TABLE "action"
            ALTER COLUMN "authenticationId"
            SET NOT NULL
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_43d560104ffd3074475049468c"
        `);
		await queryRunner.query(`
            ALTER TABLE "scope" DROP CONSTRAINT "UQ_43d560104ffd3074475049468ce"
        `);
		await queryRunner.query(`
            ALTER TABLE "scope" ALTER COLUMN "name" TYPE varchar USING "name"::varchar;
        `);
		await queryRunner.query(`
            DROP TYPE "public"."SCOPE_NAMES"
        `);
		await queryRunner.query(`
            ALTER TABLE "scope"
            ADD CONSTRAINT "UQ_43d560104ffd3074475049468ce" UNIQUE ("name")
        `);
		await queryRunner.query(`
            ALTER TABLE "authentication" ALTER COLUMN "type" TYPE varchar USING "type"::varchar;
        `);
		await queryRunner.query(`
            DROP TYPE "public"."AUTH_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_0a9de6455f156ee46012df4a18"
        `);
		await queryRunner.query(`
			ALTER TABLE "warrantyClaim" ALTER COLUMN "status" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "warrantyClaim" ALTER COLUMN "status" TYPE varchar USING "status"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "warrantyClaim" ALTER COLUMN "status" SET DEFAULT 'NEW';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."WARRANTY_CLAIM_STATUS_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_770dcccb11dfad0db3c1eda33c"
        `);
		await queryRunner.query(`
			ALTER TABLE "serviceCase" ALTER COLUMN "status" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "serviceCase" ALTER COLUMN "status" TYPE varchar USING "status"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "serviceCase" ALTER COLUMN "status" SET DEFAULT 'NEW';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."SERVICE_CASE_STATUS_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_43693c6abf2d084050e6305833"
        `);
		await queryRunner.query(`
			ALTER TABLE "serviceCase" ALTER COLUMN "type" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "serviceCase" ALTER COLUMN "type" TYPE varchar USING "type"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "serviceCase" ALTER COLUMN "type" SET DEFAULT 'BACKOFFICE';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."SERVICE_CASE_TYPE_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e4be1d81ab5328d61240e59397"
        `);
		await queryRunner.query(`
			ALTER TABLE "serviceTaskType" ALTER COLUMN "type" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "serviceTaskType" ALTER COLUMN "type" TYPE varchar[] USING "type"::varchar[];
        `);
		await queryRunner.query(`
			ALTER TABLE "serviceTaskType" ALTER COLUMN "type" SET DEFAULT '{SERVICE}';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."SERVICE_TASK_TYPE_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_385fd1ff82716a4f643bcb5b77"
        `);
		await queryRunner.query(`
			ALTER TABLE "serviceTaskType" ALTER COLUMN "afterServiceProductTargetStatus" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "serviceTaskType" ALTER COLUMN "afterServiceProductTargetStatus" TYPE varchar USING "afterServiceProductTargetStatus"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "serviceTaskType" ALTER COLUMN "afterServiceProductTargetStatus" SET DEFAULT 'TO_TEST';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."AFTER_SERVICE_PRODUCT_TARGET_STATUSES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_c3e46d7386c69de1b015b43922"
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceTaskType" ALTER COLUMN "afterWarrantyClaimProductTargetStatus" TYPE varchar USING "afterWarrantyClaimProductTargetStatus"::varchar;
        `);
		await queryRunner.query(`
            DROP TYPE "public"."AFTER_WARRANTY_CLAIM_PRODUCT_TARGET_STATUSES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_2ae70c935c09dd3d3bbffad48e"
        `);
		await queryRunner.query(`
			ALTER TABLE "productTask" ALTER COLUMN "status" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "productTask" ALTER COLUMN "status" TYPE varchar USING "status"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "productTask" ALTER COLUMN "status" SET DEFAULT 'NEW';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_TASK_STATUS_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e39e787c48fdc59c63c9f42b9e"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a29d4249658889a8180fa487c2"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_5fdd5b0fee8ce1af4f2a89c772"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d0bc51d992a144a431fe7d4240"
        `);
		await queryRunner.query(`
            ALTER TABLE "productAttributeValue" ALTER COLUMN "type" TYPE varchar USING "type"::varchar;
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_ATTRIBUTE_VALUE_TYPE"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_8a85400fbd46b60b0eef60671e"
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategoryAttribute" ALTER COLUMN "type" TYPE varchar USING "type"::varchar;
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_CATEGORY_ATTRIBUTE_TYPE"
        `);
		await queryRunner.query(`
			ALTER TABLE "attribute" ALTER COLUMN "dataType" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "attribute" ALTER COLUMN "dataType" TYPE varchar USING "dataType"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "attribute" ALTER COLUMN "dataType" SET DEFAULT 'string';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_ATTRIBUTE_DATATYPE"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e6c48e0b90475e2c27b8543933"
        `);
		await queryRunner.query(`
			ALTER TABLE "serviceTask" ALTER COLUMN "status" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "serviceTask" ALTER COLUMN "status" TYPE varchar USING "status"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "serviceTask" ALTER COLUMN "status" SET DEFAULT 'NEW';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."SERVICE_TASK_STATUS_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_c481814d444e623c21ac87b02a"
        `);
		await queryRunner.query(`
			ALTER TABLE "productDefect" ALTER COLUMN "source" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "productDefect" ALTER COLUMN "source" TYPE varchar USING "source"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "productDefect" ALTER COLUMN "source" SET DEFAULT 'TESTING';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_DEFECT_SOURCE"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_fc1484b9c802e039fa12a4e7b7"
        `);
		await queryRunner.query(`
			ALTER TABLE "grade"
				ALTER COLUMN "priceRoundingStrategy" DROP DEFAULT
		`);
		await queryRunner.query(`
            ALTER TABLE "grade" ALTER COLUMN "priceRoundingStrategy" TYPE varchar USING "priceRoundingStrategy"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "grade" ALTER COLUMN "priceRoundingStrategy" SET DEFAULT 'HIGH'
		`);
		await queryRunner.query(`
            DROP TYPE "public"."PRICE_ROUNDING_STRATEGIES"
        `);
		await queryRunner.query(`
			ALTER TABLE "productEnvelope" ALTER COLUMN "type" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "productEnvelope" ALTER COLUMN "type" TYPE varchar USING "type"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "productEnvelope" ALTER COLUMN "type" SET DEFAULT 'USED';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."ENVELOPE_TYPES"
        `);
		await queryRunner.query(`
			ALTER TABLE "productEnvelope" ALTER COLUMN "productType" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "productEnvelope" ALTER COLUMN "productType" TYPE varchar USING "productType"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "productEnvelope" ALTER COLUMN "productType" SET DEFAULT 'REFURBISHED';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."ENVELOPE_PRODUCT_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_346eae8d0fee27200c51f0eb3a"
        `);
		await queryRunner.query(`
			ALTER TABLE "ecommerceOrderItem" ALTER COLUMN "type" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrderItem" ALTER COLUMN "type" TYPE varchar USING "type"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "ecommerceOrderItem" ALTER COLUMN "type" SET DEFAULT 'PRODUCT';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."ORDER_ITEM_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_75b5f089f72a5671a4f65efbcc"
        `);
		await queryRunner.query(`
			ALTER TABLE "shipment" ALTER COLUMN "status" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "shipment" ALTER COLUMN "status" TYPE varchar USING "status"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "shipment" ALTER COLUMN "status" SET DEFAULT 'NEW';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."SHIPMENT_STATUS_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d036b2db4cc472e34e013541d8"
        `);
		await queryRunner.query(`
			ALTER TABLE "warehouse" ALTER COLUMN "type" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "warehouse" ALTER COLUMN "type" TYPE varchar USING "type"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "warehouse" ALTER COLUMN "type" SET DEFAULT 'STOCK';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."WAREHOUSE_TYPE_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_bd58da3c2f99baf7a05c710953"
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask" ALTER COLUMN "type" TYPE varchar USING "type"::varchar;
        `);
		await queryRunner.query(`
            DROP TYPE "public"."WAREHOUSE_TASK_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b78e494b7c5e466d14c10871a8"
        `);
		await queryRunner.query(`
			ALTER TABLE "warehouseTask" ALTER COLUMN "status" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask" ALTER COLUMN "status" TYPE varchar USING "status"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "warehouseTask" ALTER COLUMN "status" SET DEFAULT 'OPEN';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."WAREHOUSE_TASK_STATUSES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_8b864b2bd249bc1c0a0a5d5f26"
        `);
		await queryRunner.query(`
            ALTER TABLE "warranty" ALTER COLUMN "type" TYPE varchar USING "type"::varchar;
        `);
		await queryRunner.query(`
            DROP TYPE "public"."WARRANTY_TYPES"
        `);
		await queryRunner.query(`
			ALTER TABLE "product" ALTER COLUMN "type" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "product" ALTER COLUMN "type" TYPE varchar USING "type"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "product" ALTER COLUMN "type" SET DEFAULT 'REFURBISHED';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_01286e06a0554cbb19375f0178"
        `);
		await queryRunner.query(`
			ALTER TABLE "product" ALTER COLUMN "status" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "product" ALTER COLUMN "status" TYPE varchar USING "status"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "product" ALTER COLUMN "status" SET DEFAULT 'AT_SUPPLIER';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_STATUS_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_97c2d49b66dc70ca1a43ad1009"
        `);
		await queryRunner.query(`
            ALTER TABLE "productPrice" ALTER COLUMN "type" TYPE varchar USING "type"::varchar;
        `);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_PRICE_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_723472e41cae44beb0763f4039"
        `);
		await queryRunner.query(`
            ALTER TABLE "currency" DROP CONSTRAINT "UQ_723472e41cae44beb0763f4039c"
        `);
		await queryRunner.query(`
            ALTER TABLE "currency" ALTER COLUMN "code" TYPE varchar USING "code"::varchar;
        `);
		await queryRunner.query(`
            DROP TYPE "public"."SUPPORTED_CURRENCIES"
        `);
		await queryRunner.query(`
            ALTER TABLE "currency"
            ADD CONSTRAINT "UQ_723472e41cae44beb0763f4039c" UNIQUE ("code")
        `);
		await queryRunner.query(`
			ALTER TABLE "ecommerceOrder" ALTER COLUMN "country" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder" ALTER COLUMN "country" TYPE varchar USING "country"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "ecommerceOrder" ALTER COLUMN "country" SET DEFAULT 'CZ';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."SHOPTET_COUNTRIES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_7b06ddff30231646a28f3fa118"
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem" ALTER COLUMN "status" TYPE varchar USING "status"::varchar;
        `);
		await queryRunner.query(`
            DROP TYPE "public"."INVENTORY_ITEM_PRODUCT_STATUS_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_16f66394da6d3aeab5c17471aa"
        `);
		await queryRunner.query(`
			ALTER TABLE "inventoryItem" ALTER COLUMN "inventoryStatus" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem" ALTER COLUMN "inventoryStatus" TYPE varchar USING "inventoryStatus"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "inventoryItem" ALTER COLUMN "inventoryStatus" SET DEFAULT 'PENDING';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."INVENTORY_ITEM_STATUS_NAMES"
        `);
		await queryRunner.query(`
			ALTER TABLE "inventoryItem" ALTER COLUMN "productEnvelopeType" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem" ALTER COLUMN "productEnvelopeType" TYPE varchar USING "productEnvelopeType"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "inventoryItem" ALTER COLUMN "productEnvelopeType" SET DEFAULT 'USED';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."INVENTORY_ITEM_ENVELOPE_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ef43024ae995c2a217d3882d25"
        `);
		await queryRunner.query(`
			ALTER TABLE "inventory" ALTER COLUMN "status" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "inventory" ALTER COLUMN "status" TYPE varchar USING "status"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "inventory" ALTER COLUMN "status" SET DEFAULT 'OPEN';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."INVENTORY_STATUS_NAMES"
        `);
		await queryRunner.query(`
			ALTER TABLE "fileBatch" ALTER COLUMN "type" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "fileBatch" ALTER COLUMN "type" TYPE varchar USING "type"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "fileBatch" ALTER COLUMN "type" SET DEFAULT 'regular';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."BATCH_FILE_TYPE"
        `);
		await queryRunner.query(`
			ALTER TABLE "fileProduct" ALTER COLUMN "type" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "fileProduct" ALTER COLUMN "type" TYPE varchar USING "type"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "fileProduct" ALTER COLUMN "type" SET DEFAULT 'regular';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_FILE_TYPE"
        `);
		await queryRunner.query(`
			ALTER TABLE "batch" ALTER COLUMN "deliveryType" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "batch" ALTER COLUMN "deliveryType" TYPE varchar USING "deliveryType"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "batch" ALTER COLUMN "deliveryType" SET DEFAULT 'OWN';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."BATCH_DELIVERY_TYPE_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_cfea8cb6e387bc8738a456865b"
        `);
		await queryRunner.query(`
			ALTER TABLE "batch" ALTER COLUMN "status" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "batch" ALTER COLUMN "status" TYPE varchar USING "status"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "batch" ALTER COLUMN "status" SET DEFAULT 'AT_SUPPLIER';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."BATCH_STATUS_NAMES"
        `);
		await queryRunner.query(`
			ALTER TABLE "batch" ALTER COLUMN "paymentStatus" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "batch" ALTER COLUMN "paymentStatus" TYPE varchar USING "paymentStatus"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "batch" ALTER COLUMN "paymentStatus" SET DEFAULT 'UNPAID';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."PAYMENT_STATUS_NAMES"
        `);
		await queryRunner.query(`
			ALTER TABLE "batch" ALTER COLUMN "type" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "batch" ALTER COLUMN "type" TYPE varchar USING "type"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "batch" ALTER COLUMN "type" SET DEFAULT 'USED';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."BATCH_TYPES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_34a08f05907f0de589904e548b"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f238058ede7302a48601e56743"
        `);
		await queryRunner.query(`
            ALTER TABLE "userNotificationType" ALTER COLUMN "deliveryMethod" TYPE varchar USING "deliveryMethod"::varchar;
        `);
		await queryRunner.query(`
            DROP TYPE "public"."NOTIFICATION_DELIVERY_METHODS_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_27a5a39904299d760196809bf1"
        `);
		await queryRunner.query(`
            ALTER TABLE "notificationType" DROP CONSTRAINT "UQ_27a5a39904299d760196809bf1b"
        `);
		await queryRunner.query(`
            ALTER TABLE "notificationType" ALTER COLUMN "name" TYPE varchar USING "name"::varchar;
        `);
		await queryRunner.query(`
            DROP TYPE "public"."NOTIFICATION_TYPE_NAMES"
        `);
		await queryRunner.query(`
            ALTER TABLE "notificationType"
            ADD CONSTRAINT "UQ_27a5a39904299d760196809bf1b" UNIQUE ("name")
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d4df559e273cd276f34b614194"
        `);
		await queryRunner.query(`
			ALTER TABLE "customerClaim" ALTER COLUMN "status" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "customerClaim" ALTER COLUMN "status" TYPE varchar USING "status"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "customerClaim" ALTER COLUMN "status" SET DEFAULT 'NEW';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."CUSTOMER_CLAIM_STATUS_NAMES"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_7aca3d9ff7ee772fd32a30e480"
        `);
		await queryRunner.query(`
            ALTER TABLE "customerClaim" ALTER COLUMN "handlingMethod" TYPE varchar USING "handlingMethod"::varchar;
        `);
		await queryRunner.query(`
            DROP TYPE "public"."CUSTOMER_CLAIM_HANDLING_METHODS"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e078d2bcf3311d226c5eada82e"
        `);
		await queryRunner.query(`
            ALTER TABLE "customerClaim" ALTER COLUMN "customerDeliveryMethod" TYPE varchar USING "customerDeliveryMethod"::varchar;
        `);
		await queryRunner.query(`
            DROP TYPE "public"."CUSTOMER_DELIVERY_METHODS"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_68e6f0934845f8adc728f2c399"
        `);
		await queryRunner.query(`
            ALTER TABLE "customerClaim" ALTER COLUMN "cmpDeliveryMethod" TYPE varchar USING "cmpDeliveryMethod"::varchar;
        `);
		await queryRunner.query(`
            DROP TYPE "public"."CMP_DELIVERY_METHODS"
        `);
		await queryRunner.query(`
			ALTER TABLE "vendor" ALTER COLUMN "warrantyBaseline" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "vendor" ALTER COLUMN "warrantyBaseline" TYPE varchar USING "warrantyBaseline"::varchar;
        `);
		await queryRunner.query(`
			ALTER TABLE "vendor" ALTER COLUMN "warrantyBaseline" SET DEFAULT 'deliveredAt';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."BATCH_STATUS_TRANSITION_TIMESTAMPS"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_12e6612123eba6db99643eb871"
        `);
		await queryRunner.query(`
			ALTER TABLE "printer" ALTER COLUMN "type" DROP DEFAULT;
		`);
		await queryRunner.query(`
            ALTER TABLE "printer" ALTER COLUMN "type" TYPE varchar[] USING "type"::varchar[];
        `);
		await queryRunner.query(`
			ALTER TABLE "printer" ALTER COLUMN "type" SET DEFAULT '{DELIVERY}';
		`);
		await queryRunner.query(`
            DROP TYPE "public"."PRINTER_LOCATIONS"
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_5165a3df690967baa74c183197" ON "action" ("method")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_43d560104ffd3074475049468c" ON "scope" ("name")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_0a9de6455f156ee46012df4a18" ON "warrantyClaim" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_770dcccb11dfad0db3c1eda33c" ON "serviceCase" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_43693c6abf2d084050e6305833" ON "serviceCase" ("type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e4be1d81ab5328d61240e59397" ON "serviceTaskType" ("type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_385fd1ff82716a4f643bcb5b77" ON "serviceTaskType" ("afterServiceProductTargetStatus")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_c3e46d7386c69de1b015b43922" ON "serviceTaskType" ("afterWarrantyClaimProductTargetStatus")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_2ae70c935c09dd3d3bbffad48e" ON "productTask" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d0bc51d992a144a431fe7d4240" ON "productAttributeValue" ("type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e39e787c48fdc59c63c9f42b9e" ON "productAttributeValue" ("type", "productId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_a29d4249658889a8180fa487c2" ON "productAttributeValue" ("type", "attributeValueId")
        `);
		await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_5fdd5b0fee8ce1af4f2a89c772" ON "productAttributeValue" ("type", "attributeValueId", "productId")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_8a85400fbd46b60b0eef60671e" ON "productCategoryAttribute" ("type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e6c48e0b90475e2c27b8543933" ON "serviceTask" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_c481814d444e623c21ac87b02a" ON "productDefect" ("source")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_fc1484b9c802e039fa12a4e7b7" ON "grade" ("priceRoundingStrategy")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_346eae8d0fee27200c51f0eb3a" ON "ecommerceOrderItem" ("type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_75b5f089f72a5671a4f65efbcc" ON "shipment" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d036b2db4cc472e34e013541d8" ON "warehouse" ("type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_bd58da3c2f99baf7a05c710953" ON "warehouseTask" ("type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b78e494b7c5e466d14c10871a8" ON "warehouseTask" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_8b864b2bd249bc1c0a0a5d5f26" ON "warranty" ("type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_01286e06a0554cbb19375f0178" ON "product" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_97c2d49b66dc70ca1a43ad1009" ON "productPrice" ("type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_723472e41cae44beb0763f4039" ON "currency" ("code")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_7b06ddff30231646a28f3fa118" ON "inventoryItem" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_16f66394da6d3aeab5c17471aa" ON "inventoryItem" ("inventoryStatus")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ef43024ae995c2a217d3882d25" ON "inventory" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_cfea8cb6e387bc8738a456865b" ON "batch" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f238058ede7302a48601e56743" ON "userNotificationType" ("deliveryMethod")
        `);
		await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_34a08f05907f0de589904e548b" ON "userNotificationType" ("notificationTypeId", "userId", "deliveryMethod")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_27a5a39904299d760196809bf1" ON "notificationType" ("name")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d4df559e273cd276f34b614194" ON "customerClaim" ("status")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_7aca3d9ff7ee772fd32a30e480" ON "customerClaim" ("handlingMethod")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e078d2bcf3311d226c5eada82e" ON "customerClaim" ("customerDeliveryMethod")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_68e6f0934845f8adc728f2c399" ON "customerClaim" ("cmpDeliveryMethod")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_12e6612123eba6db99643eb871" ON "printer" ("type")
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TABLE "action" DROP CONSTRAINT "FK_bf376bd23a893833bd63e3dd8a3"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_12e6612123eba6db99643eb871"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_68e6f0934845f8adc728f2c399"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e078d2bcf3311d226c5eada82e"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_7aca3d9ff7ee772fd32a30e480"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d4df559e273cd276f34b614194"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_27a5a39904299d760196809bf1"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_34a08f05907f0de589904e548b"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_f238058ede7302a48601e56743"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_cfea8cb6e387bc8738a456865b"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_ef43024ae995c2a217d3882d25"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_16f66394da6d3aeab5c17471aa"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_7b06ddff30231646a28f3fa118"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_723472e41cae44beb0763f4039"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_97c2d49b66dc70ca1a43ad1009"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_01286e06a0554cbb19375f0178"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_8b864b2bd249bc1c0a0a5d5f26"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_b78e494b7c5e466d14c10871a8"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_bd58da3c2f99baf7a05c710953"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d036b2db4cc472e34e013541d8"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_75b5f089f72a5671a4f65efbcc"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_346eae8d0fee27200c51f0eb3a"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_fc1484b9c802e039fa12a4e7b7"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_c481814d444e623c21ac87b02a"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e6c48e0b90475e2c27b8543933"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_3f0c1f090a9558aad1abb776c2"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_8a85400fbd46b60b0eef60671e"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_5fdd5b0fee8ce1af4f2a89c772"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_a29d4249658889a8180fa487c2"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e39e787c48fdc59c63c9f42b9e"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_d0bc51d992a144a431fe7d4240"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_2ae70c935c09dd3d3bbffad48e"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_c3e46d7386c69de1b015b43922"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_385fd1ff82716a4f643bcb5b77"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_e4be1d81ab5328d61240e59397"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_43693c6abf2d084050e6305833"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_770dcccb11dfad0db3c1eda33c"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_0a9de6455f156ee46012df4a18"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_43d560104ffd3074475049468c"
        `);
		await queryRunner.query(`
            DROP INDEX "public"."IDX_5165a3df690967baa74c183197"
        `);
		await queryRunner.query(`
            ALTER TABLE "printer" DROP COLUMN "type"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRINTER_LOCATIONS" AS ENUM('DELIVERY', 'SERVICE', 'SHIPMENT', 'TESTING')
        `);
		await queryRunner.query(`
            ALTER TABLE "printer"
            ADD "type" "public"."PRINTER_LOCATIONS" array NOT NULL DEFAULT '{DELIVERY}'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_12e6612123eba6db99643eb871" ON "printer" ("type")
        `);
		await queryRunner.query(`
            ALTER TABLE "vendor" DROP COLUMN "warrantyBaseline"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."BATCH_STATUS_TRANSITION_TIMESTAMPS" AS ENUM(
                'createdAt',
                'deliveredAt',
                'checkedAt',
                'checkedSnAt',
                'testedAt'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "vendor"
            ADD "warrantyBaseline" "public"."BATCH_STATUS_TRANSITION_TIMESTAMPS" NOT NULL DEFAULT 'deliveredAt'
        `);
		await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP COLUMN "cmpDeliveryMethod"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."CMP_DELIVERY_METHODS" AS ENUM('IN_PERSON', 'SHIPMENT')
        `);
		await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD "cmpDeliveryMethod" "public"."CMP_DELIVERY_METHODS"
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_68e6f0934845f8adc728f2c399" ON "customerClaim" ("cmpDeliveryMethod")
        `);
		await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP COLUMN "customerDeliveryMethod"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."CUSTOMER_DELIVERY_METHODS" AS ENUM('IN_PERSON', 'SHIPMENT')
        `);
		await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD "customerDeliveryMethod" "public"."CUSTOMER_DELIVERY_METHODS" NOT NULL
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e078d2bcf3311d226c5eada82e" ON "customerClaim" ("customerDeliveryMethod")
        `);
		await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP COLUMN "handlingMethod"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."CUSTOMER_CLAIM_HANDLING_METHODS" AS ENUM('EXCHANGE', 'REPAIR', 'REFUND')
        `);
		await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD "handlingMethod" "public"."CUSTOMER_CLAIM_HANDLING_METHODS" NOT NULL
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_7aca3d9ff7ee772fd32a30e480" ON "customerClaim" ("handlingMethod")
        `);
		await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP COLUMN "status"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."CUSTOMER_CLAIM_STATUS_NAMES" AS ENUM('NEW', 'CLOSED')
        `);
		await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD "status" "public"."CUSTOMER_CLAIM_STATUS_NAMES" NOT NULL DEFAULT 'NEW'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d4df559e273cd276f34b614194" ON "customerClaim" ("status")
        `);
		await queryRunner.query(`
            ALTER TABLE "notificationType" DROP CONSTRAINT "UQ_27a5a39904299d760196809bf1b"
        `);
		await queryRunner.query(`
            ALTER TABLE "notificationType" DROP COLUMN "name"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."NOTIFICATION_TYPE_NAMES" AS ENUM(
                'SHOPTET_EXPORT',
                'ENVELOPE_UPDATE',
                'PRODUCT_PICKUP',
                'MISSING_RECYCLING_FEE'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "notificationType"
            ADD "name" "public"."NOTIFICATION_TYPE_NAMES" NOT NULL
        `);
		await queryRunner.query(`
            ALTER TABLE "notificationType"
            ADD CONSTRAINT "UQ_27a5a39904299d760196809bf1b" UNIQUE ("name")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_27a5a39904299d760196809bf1" ON "notificationType" ("name")
        `);
		await queryRunner.query(`
            ALTER TABLE "userNotificationType" DROP COLUMN "deliveryMethod"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."NOTIFICATION_DELIVERY_METHODS_NAMES" AS ENUM('push', 'email')
        `);
		await queryRunner.query(`
            ALTER TABLE "userNotificationType"
            ADD "deliveryMethod" "public"."NOTIFICATION_DELIVERY_METHODS_NAMES" NOT NULL
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_f238058ede7302a48601e56743" ON "userNotificationType" ("deliveryMethod")
        `);
		await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_34a08f05907f0de589904e548b" ON "userNotificationType" ("deliveryMethod", "notificationTypeId", "userId")
        `);
		await queryRunner.query(`
            ALTER TABLE "batch" DROP COLUMN "type"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."BATCH_TYPES" AS ENUM('NEW', 'USED')
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ADD "type" "public"."BATCH_TYPES" NOT NULL DEFAULT 'USED'
        `);
		await queryRunner.query(`
            ALTER TABLE "batch" DROP COLUMN "paymentStatus"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PAYMENT_STATUS_NAMES" AS ENUM('INITIATED', 'PAID', 'UNPAID')
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ADD "paymentStatus" "public"."PAYMENT_STATUS_NAMES" NOT NULL DEFAULT 'UNPAID'
        `);
		await queryRunner.query(`
            ALTER TABLE "batch" DROP COLUMN "status"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."BATCH_STATUS_NAMES" AS ENUM(
                'IMPORTING',
                'AT_SUPPLIER',
                'ON_THE_WAY',
                'EXTERNAL_STOCK',
                'TO_CHECK',
                'TO_CHECK_SN',
                'TO_TEST',
                'MARKETING',
                'CLOSED'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ADD "status" "public"."BATCH_STATUS_NAMES" NOT NULL DEFAULT 'AT_SUPPLIER'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_cfea8cb6e387bc8738a456865b" ON "batch" ("status")
        `);
		await queryRunner.query(`
            ALTER TABLE "batch" DROP COLUMN "deliveryType"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."BATCH_DELIVERY_TYPE_NAMES" AS ENUM('VENDOR', 'OWN')
        `);
		await queryRunner.query(`
            ALTER TABLE "batch"
            ADD "deliveryType" "public"."BATCH_DELIVERY_TYPE_NAMES" NOT NULL DEFAULT 'OWN'
        `);
		await queryRunner.query(`
            ALTER TABLE "fileProduct" DROP COLUMN "type"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_FILE_TYPE" AS ENUM('import', 'reader', 'regular')
        `);
		await queryRunner.query(`
            ALTER TABLE "fileProduct"
            ADD "type" "public"."PRODUCT_FILE_TYPE" NOT NULL DEFAULT 'regular'
        `);
		await queryRunner.query(`
            ALTER TABLE "fileBatch" DROP COLUMN "type"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."BATCH_FILE_TYPE" AS ENUM('import', 'delivery', 'regular')
        `);
		await queryRunner.query(`
            ALTER TABLE "fileBatch"
            ADD "type" "public"."BATCH_FILE_TYPE" NOT NULL DEFAULT 'regular'
        `);
		await queryRunner.query(`
            ALTER TABLE "inventory" DROP COLUMN "status"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."INVENTORY_STATUS_NAMES" AS ENUM('OPEN', 'CLOSED')
        `);
		await queryRunner.query(`
            ALTER TABLE "inventory"
            ADD "status" "public"."INVENTORY_STATUS_NAMES" NOT NULL DEFAULT 'OPEN'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_ef43024ae995c2a217d3882d25" ON "inventory" ("status")
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "productEnvelopeType"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."INVENTORY_ITEM_ENVELOPE_TYPES" AS ENUM('NEW', 'USED', 'AMOUNT')
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "productEnvelopeType" "public"."INVENTORY_ITEM_ENVELOPE_TYPES" NOT NULL DEFAULT 'USED'
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "inventoryStatus"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."INVENTORY_ITEM_STATUS_NAMES" AS ENUM('PENDING', 'OK', 'ERROR', 'NEW_ADDITION')
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "inventoryStatus" "public"."INVENTORY_ITEM_STATUS_NAMES" NOT NULL DEFAULT 'PENDING'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_16f66394da6d3aeab5c17471aa" ON "inventoryItem" ("inventoryStatus")
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "status"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."INVENTORY_ITEM_PRODUCT_STATUS_NAMES" AS ENUM(
                'AT_SUPPLIER',
                'ON_THE_WAY',
                'EXTERNAL_STOCK',
                'TO_CHECK',
                'TO_CHECK_SN',
                'TO_TEST',
                'TESTED',
                'MISSING',
                'SERVICE',
                'WARRANTY_CLAIM',
                'STOCK',
                'FOR_SALE',
                'RESERVED',
                'SOLD',
                'CUSTOMER_CLAIM',
                'DEAD',
                'AUTOPSY',
                'RETURNED',
                'LOST_FROM_INVENTORY',
                'WAITING_FOR_SHIPMENT',
                'SHIPMENT_DISPATCHED',
                'SHIPMENT_DELIVERED'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "status" "public"."INVENTORY_ITEM_PRODUCT_STATUS_NAMES" NOT NULL
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_7b06ddff30231646a28f3fa118" ON "inventoryItem" ("status")
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder" DROP COLUMN "country"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SHOPTET_COUNTRIES" AS ENUM('CZ', 'SK')
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrder"
            ADD "country" "public"."SHOPTET_COUNTRIES" NOT NULL DEFAULT 'CZ'
        `);
		await queryRunner.query(`
            ALTER TABLE "currency" DROP CONSTRAINT "UQ_723472e41cae44beb0763f4039c"
        `);
		await queryRunner.query(`
            ALTER TABLE "currency" DROP COLUMN "code"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SUPPORTED_CURRENCIES" AS ENUM('EUR', 'USD', 'GBP', 'CZK')
        `);
		await queryRunner.query(`
            ALTER TABLE "currency"
            ADD "code" "public"."SUPPORTED_CURRENCIES" NOT NULL
        `);
		await queryRunner.query(`
            ALTER TABLE "currency"
            ADD CONSTRAINT "UQ_723472e41cae44beb0763f4039c" UNIQUE ("code")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_723472e41cae44beb0763f4039" ON "currency" ("code")
        `);
		await queryRunner.query(`
            ALTER TABLE "productPrice" DROP COLUMN "type"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_PRICE_TYPES" AS ENUM(
                'BUY',
                'DELIVERY',
                'SELL',
                'RECOMMENDED',
                'STANDARD',
                'DISCOUNT',
                'SERVICE',
                'CUSTOMER_CLAIM'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "productPrice"
            ADD "type" "public"."PRODUCT_PRICE_TYPES" NOT NULL
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_97c2d49b66dc70ca1a43ad1009" ON "productPrice" ("type")
        `);
		await queryRunner.query(`
            ALTER TABLE "product" DROP COLUMN "status"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_STATUS_NAMES" AS ENUM(
                'AT_SUPPLIER',
                'ON_THE_WAY',
                'EXTERNAL_STOCK',
                'TO_CHECK',
                'TO_CHECK_SN',
                'TO_TEST',
                'TESTED',
                'MISSING',
                'SERVICE',
                'WARRANTY_CLAIM',
                'STOCK',
                'FOR_SALE',
                'RESERVED',
                'SOLD',
                'CUSTOMER_CLAIM',
                'DEAD',
                'AUTOPSY',
                'RETURNED',
                'LOST_FROM_INVENTORY',
                'WAITING_FOR_SHIPMENT',
                'SHIPMENT_DISPATCHED',
                'SHIPMENT_DELIVERED'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ADD "status" "public"."PRODUCT_STATUS_NAMES" NOT NULL DEFAULT 'AT_SUPPLIER'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_01286e06a0554cbb19375f0178" ON "product" ("status")
        `);
		await queryRunner.query(`
            ALTER TABLE "product" DROP COLUMN "type"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_TYPES" AS ENUM('NEW', 'REFURBISHED', 'RENEWED')
        `);
		await queryRunner.query(`
            ALTER TABLE "product"
            ADD "type" "public"."PRODUCT_TYPES" NOT NULL DEFAULT 'REFURBISHED'
        `);
		await queryRunner.query(`
            ALTER TABLE "warranty" DROP COLUMN "type"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."WARRANTY_TYPES" AS ENUM('BUY', 'SELL', 'SERVICE')
        `);
		await queryRunner.query(`
            ALTER TABLE "warranty"
            ADD "type" "public"."WARRANTY_TYPES" NOT NULL
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_8b864b2bd249bc1c0a0a5d5f26" ON "warranty" ("type")
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask" DROP COLUMN "status"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."WAREHOUSE_TASK_STATUSES" AS ENUM('OPEN', 'CLOSED')
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask"
            ADD "status" "public"."WAREHOUSE_TASK_STATUSES" NOT NULL DEFAULT 'OPEN'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_b78e494b7c5e466d14c10871a8" ON "warehouseTask" ("status")
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask" DROP COLUMN "type"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."WAREHOUSE_TASK_TYPES" AS ENUM('SHIFT', 'PICKUP', 'STORE', 'INVENTORY')
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouseTask"
            ADD "type" "public"."WAREHOUSE_TASK_TYPES" NOT NULL
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_bd58da3c2f99baf7a05c710953" ON "warehouseTask" ("type")
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouse" DROP COLUMN "type"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."WAREHOUSE_TYPE_NAMES" AS ENUM(
                'STOCK',
                'EXPEDITION',
                'TESTING',
                'DEPOSIT',
                'SERVICE',
                'WARRANTY_CLAIM'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "warehouse"
            ADD "type" "public"."WAREHOUSE_TYPE_NAMES" NOT NULL DEFAULT 'STOCK'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d036b2db4cc472e34e013541d8" ON "warehouse" ("type")
        `);
		await queryRunner.query(`
            ALTER TABLE "shipment" DROP COLUMN "status"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SHIPMENT_STATUS_NAMES" AS ENUM('NEW', 'DISPATCHED', 'DELIVERED')
        `);
		await queryRunner.query(`
            ALTER TABLE "shipment"
            ADD "status" "public"."SHIPMENT_STATUS_NAMES" NOT NULL DEFAULT 'NEW'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_75b5f089f72a5671a4f65efbcc" ON "shipment" ("status")
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrderItem" DROP COLUMN "type"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."ORDER_ITEM_TYPES" AS ENUM(
                'PRODUCT',
                'SHIPPING',
                'PAYMENT',
                'DISCOUNT',
                'SERVICE',
                'GIFT',
                'PRODUCT_SET'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "ecommerceOrderItem"
            ADD "type" "public"."ORDER_ITEM_TYPES" NOT NULL DEFAULT 'PRODUCT'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_346eae8d0fee27200c51f0eb3a" ON "ecommerceOrderItem" ("type")
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP COLUMN "productType"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."ENVELOPE_PRODUCT_TYPES" AS ENUM('NEW', 'REFURBISHED', 'RENEWED')
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD "productType" "public"."ENVELOPE_PRODUCT_TYPES" NOT NULL DEFAULT 'REFURBISHED'
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelope" DROP COLUMN "type"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."ENVELOPE_TYPES" AS ENUM('NEW', 'USED', 'AMOUNT')
        `);
		await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ADD "type" "public"."ENVELOPE_TYPES" NOT NULL DEFAULT 'USED'
        `);
		await queryRunner.query(`
            ALTER TABLE "grade" DROP COLUMN "priceRoundingStrategy"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRICE_ROUNDING_STRATEGIES" AS ENUM('HIGH', 'LOW')
        `);
		await queryRunner.query(`
            ALTER TABLE "grade"
            ADD "priceRoundingStrategy" "public"."PRICE_ROUNDING_STRATEGIES" NOT NULL
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_fc1484b9c802e039fa12a4e7b7" ON "grade" ("priceRoundingStrategy")
        `);
		await queryRunner.query(`
            ALTER TABLE "productDefect" DROP COLUMN "source"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_DEFECT_SOURCE" AS ENUM(
                'TESTING',
                'SERVICE_CASE',
                'WARRANTY_CLAIM',
                'CUSTOMER_CLAIM'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "productDefect"
            ADD "source" "public"."PRODUCT_DEFECT_SOURCE" NOT NULL DEFAULT 'TESTING'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_c481814d444e623c21ac87b02a" ON "productDefect" ("source")
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceTask" DROP COLUMN "status"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SERVICE_TASK_STATUS_TYPES" AS ENUM('NEW', 'CLOSED')
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceTask"
            ADD "status" "public"."SERVICE_TASK_STATUS_TYPES" NOT NULL DEFAULT 'NEW'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e6c48e0b90475e2c27b8543933" ON "serviceTask" ("status")
        `);
		await queryRunner.query(`
            ALTER TABLE "attribute" DROP COLUMN "dataType"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_ATTRIBUTE_DATATYPE" AS ENUM(
                'boolean',
                'number',
                'string',
                'text',
                'multiselect'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "attribute"
            ADD "dataType" "public"."PRODUCT_ATTRIBUTE_DATATYPE" NOT NULL DEFAULT 'string'
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategoryAttribute" DROP COLUMN "type"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_CATEGORY_ATTRIBUTE_TYPE" AS ENUM(
                'default',
                'testing',
                'compare',
                'mandatory',
                'envelope',
                'name',
                'export',
                'list',
                'config',
                'stock',
                'report'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "productCategoryAttribute"
            ADD "type" "public"."PRODUCT_CATEGORY_ATTRIBUTE_TYPE" NOT NULL
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_8a85400fbd46b60b0eef60671e" ON "productCategoryAttribute" ("type")
        `);
		await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_3f0c1f090a9558aad1abb776c2" ON "productCategoryAttribute" ("attributeId", "productCategoryId", "type")
        `);
		await queryRunner.query(`
            ALTER TABLE "productAttributeValue" DROP COLUMN "type"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_ATTRIBUTE_VALUE_TYPE" AS ENUM('import', 'reader', 'resolved', 'service')
        `);
		await queryRunner.query(`
            ALTER TABLE "productAttributeValue"
            ADD "type" "public"."PRODUCT_ATTRIBUTE_VALUE_TYPE" NOT NULL
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_d0bc51d992a144a431fe7d4240" ON "productAttributeValue" ("type")
        `);
		await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_5fdd5b0fee8ce1af4f2a89c772" ON "productAttributeValue" ("attributeValueId", "productId", "type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_a29d4249658889a8180fa487c2" ON "productAttributeValue" ("attributeValueId", "type")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e39e787c48fdc59c63c9f42b9e" ON "productAttributeValue" ("productId", "type")
        `);
		await queryRunner.query(`
            ALTER TABLE "productTask" DROP COLUMN "status"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_TASK_STATUS_TYPES" AS ENUM('NEW', 'CLOSED')
        `);
		await queryRunner.query(`
            ALTER TABLE "productTask"
            ADD "status" "public"."PRODUCT_TASK_STATUS_TYPES" NOT NULL DEFAULT 'NEW'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_2ae70c935c09dd3d3bbffad48e" ON "productTask" ("status")
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceTaskType" DROP COLUMN "afterWarrantyClaimProductTargetStatus"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."AFTER_WARRANTY_CLAIM_PRODUCT_TARGET_STATUSES" AS ENUM('AUTOPSY', 'TO_TEST', 'STOCK')
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceTaskType"
            ADD "afterWarrantyClaimProductTargetStatus" "public"."AFTER_WARRANTY_CLAIM_PRODUCT_TARGET_STATUSES"
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_c3e46d7386c69de1b015b43922" ON "serviceTaskType" ("afterWarrantyClaimProductTargetStatus")
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceTaskType" DROP COLUMN "afterServiceProductTargetStatus"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."AFTER_SERVICE_PRODUCT_TARGET_STATUSES" AS ENUM('DEAD', 'AUTOPSY', 'TO_TEST', 'STOCK', 'SOLD')
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceTaskType"
            ADD "afterServiceProductTargetStatus" "public"."AFTER_SERVICE_PRODUCT_TARGET_STATUSES" NOT NULL DEFAULT 'TO_TEST'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_385fd1ff82716a4f643bcb5b77" ON "serviceTaskType" ("afterServiceProductTargetStatus")
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceTaskType" DROP COLUMN "type"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SERVICE_TASK_TYPE_NAMES" AS ENUM('TESTING', 'SERVICE')
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceTaskType"
            ADD "type" "public"."SERVICE_TASK_TYPE_NAMES" array NOT NULL DEFAULT '{SERVICE}'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_e4be1d81ab5328d61240e59397" ON "serviceTaskType" ("type")
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCase" DROP COLUMN "type"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SERVICE_CASE_TYPE_NAMES" AS ENUM('FRONTEND', 'BACKOFFICE')
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCase"
            ADD "type" "public"."SERVICE_CASE_TYPE_NAMES" NOT NULL DEFAULT 'BACKOFFICE'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_43693c6abf2d084050e6305833" ON "serviceCase" ("type")
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCase" DROP COLUMN "status"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SERVICE_CASE_STATUS_NAMES" AS ENUM(
                'NEW',
                'ASSIGNED_TO_INTERNAL_SERVICE',
                'ASSIGNED_TO_EXTERNAL_SERVICE',
                'SENT_TO_SERVICE_CENTER',
                'OFFER_RECEIVED',
                'WAITING_FOR_REPAIR',
                'OFFER_REJECTED',
                'WAITING_FOR_RETURN',
                'WAITING_FOR_PRODUCT',
                'WAITING_FOR_BUYBACK',
                'CLOSED'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "serviceCase"
            ADD "status" "public"."SERVICE_CASE_STATUS_NAMES" NOT NULL DEFAULT 'NEW'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_770dcccb11dfad0db3c1eda33c" ON "serviceCase" ("status")
        `);
		await queryRunner.query(`
            ALTER TABLE "warrantyClaim" DROP COLUMN "status"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."WARRANTY_CLAIM_STATUS_NAMES" AS ENUM(
                'NEW',
                'WAITING_FOR_VENDOR',
                'SENT_TO_VENDOR',
                'WAITING_FOR_RETURN',
                'CLOSED',
                'REJECTED',
                'REFUNDED',
                'DISCOUNT',
                'RESOLVED_BY_VENDOR',
                'TRADED_PIECE',
                'WAITING_FOR_DISCOUNT',
                'WAITING_FOR_REPAIR'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "warrantyClaim"
            ADD "status" "public"."WARRANTY_CLAIM_STATUS_NAMES" NOT NULL DEFAULT 'NEW'
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_0a9de6455f156ee46012df4a18" ON "warrantyClaim" ("status")
        `);
		await queryRunner.query(`
            ALTER TABLE "authentication" DROP COLUMN "type"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."AUTH_TYPES" AS ENUM('azure-ad', 'password', 'verificationCode')
        `);
		await queryRunner.query(`
            ALTER TABLE "authentication"
            ADD "type" "public"."AUTH_TYPES" NOT NULL
        `);
		await queryRunner.query(`
            ALTER TABLE "scope" DROP CONSTRAINT "UQ_43d560104ffd3074475049468ce"
        `);
		await queryRunner.query(`
            ALTER TABLE "scope" DROP COLUMN "name"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SCOPE_NAMES" AS ENUM(
                'admin',
                'home',
                'batchRead',
                'batchWrite',
                'batchCheck',
                'batchDelivery',
                'customerClaimRead',
                'customerClaimWrite',
                'envelopeWrite',
                'productTest',
                'productTestLead',
                'testRead',
                'serviceRead',
                'serviceWrite',
                'serviceManage',
                'warrantyClaimRead',
                'warrantyClaimWrite',
                'stock',
                'productRead',
                'productWrite',
                'orderRead',
                'orderWrite',
                'productAdmin',
                'warehouseManage',
                'warehouseRead',
                'warehouseWrite'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "scope"
            ADD "name" "public"."SCOPE_NAMES" NOT NULL
        `);
		await queryRunner.query(`
            ALTER TABLE "scope"
            ADD CONSTRAINT "UQ_43d560104ffd3074475049468ce" UNIQUE ("name")
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_43d560104ffd3074475049468c" ON "scope" ("name")
        `);
		await queryRunner.query(`
            ALTER TABLE "action"
            ALTER COLUMN "authenticationId" DROP NOT NULL
        `);
		await queryRunner.query(`
            ALTER TABLE "action" DROP COLUMN "method"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."HTTP_METHODS_NAMES" AS ENUM('GET', 'POST', 'PUT', 'PATCH', 'DELETE')
        `);
		await queryRunner.query(`
            ALTER TABLE "action"
            ADD "method" "public"."HTTP_METHODS_NAMES"
        `);
		await queryRunner.query(`
            CREATE INDEX "IDX_5165a3df690967baa74c183197" ON "action" ("method")
        `);
		await queryRunner.query(`
            ALTER TABLE "action"
            ADD CONSTRAINT "FK_bf376bd23a893833bd63e3dd8a3" FOREIGN KEY ("authenticationId") REFERENCES "authentication"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
	}
}
