import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1760501448003 implements MigrationInterface {
    name = 'Migration1760501448003'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "ecommerceOrderItemId" uuid
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP CONSTRAINT "FK_418b93fba9d77d73f39639a7edf"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP CONSTRAINT "FK_44191882c0631faa9895a62eb24"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ALTER COLUMN "cmpDeliveryMethod"
            SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ALTER COLUMN "contactId"
            SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ALTER COLUMN "addressId"
            SET NOT NULL
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_559341b0192fb9dc4c2510572a" ON "inventoryItem" ("ecommerceOrderItemId")
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD CONSTRAINT "FK_559341b0192fb9dc4c2510572a2" FOREIGN KEY ("ecommerceOrderItemId") REFERENCES "ecommerceOrderItem"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD CONSTRAINT "FK_418b93fba9d77d73f39639a7edf" FOREIGN KEY ("contactId") REFERENCES "contact"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD CONSTRAINT "FK_44191882c0631faa9895a62eb24" FOREIGN KEY ("addressId") REFERENCES "address"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP CONSTRAINT "FK_44191882c0631faa9895a62eb24"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim" DROP CONSTRAINT "FK_418b93fba9d77d73f39639a7edf"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP CONSTRAINT "FK_559341b0192fb9dc4c2510572a2"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_559341b0192fb9dc4c2510572a"
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ALTER COLUMN "addressId" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ALTER COLUMN "contactId" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ALTER COLUMN "cmpDeliveryMethod" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD CONSTRAINT "FK_44191882c0631faa9895a62eb24" FOREIGN KEY ("addressId") REFERENCES "address"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "customerClaim"
            ADD CONSTRAINT "FK_418b93fba9d77d73f39639a7edf" FOREIGN KEY ("contactId") REFERENCES "contact"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "ecommerceOrderItemId"
        `);
    }

}
