import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1751305070371 implements MigrationInterface {
    name = 'Migration1751305070371'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "inventoryCode" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "code" SERIAL NOT NULL,
                CONSTRAINT "UQ_ec141ecd7d64ed75b097e334c88" UNIQUE ("code"),
                CONSTRAINT "PK_a0392a8065daa71acf67d356b2d" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_1a56a28b60e1c3efd105f1ad2b" ON "inventoryCode" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_a6739e4ec5bcae5e2d296a86c7" ON "inventoryCode" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_ec141ecd7d64ed75b097e334c8" ON "inventoryCode" ("code")
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."INVENTORY_STATUS_NAMES" AS ENUM('OPEN', 'CLOSED')
        `);
        await queryRunner.query(`
            CREATE TABLE "inventory" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "createdById" uuid,
                "finishedAt" TIMESTAMP WITH TIME ZONE,
                "codeId" uuid NOT NULL,
                "finishedById" uuid,
                "status" "public"."INVENTORY_STATUS_NAMES" NOT NULL DEFAULT 'OPEN',
                CONSTRAINT "PK_82aa5da437c5bbfb80703b08309" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_ddfc33ed23fa98af2424b98ab2" ON "inventory" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_8e274d20e053eeff00f8423dcf" ON "inventory" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_a35b2af7fa30e71e4229e3d4e2" ON "inventory" ("createdById")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_4c4f8b4033c384d0889519de3a" ON "inventory" ("finishedAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_bec6a233c732ac855ad715cb49" ON "inventory" ("codeId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_5f544029aef4db29f7df0067a2" ON "inventory" ("finishedById")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_ef43024ae995c2a217d3882d25" ON "inventory" ("status")
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."INVENTORY_ITEM_PRODUCT_STATUS_NAMES" AS ENUM(
                'AT_SUPPLIER',
                'ON_THE_WAY',
                'EXTERNAL_STOCK',
                'TO_CHECK',
                'TO_CHECK_SN',
                'TO_TEST',
                'TESTED',
                'MISSING',
                'SERVICE',
                'WARRANTY_CLAIM',
                'STOCK',
                'FOR_SALE',
                'RESERVED',
                'SOLD',
                'CUSTOMER_CLAIM',
                'DEAD',
                'AUTOPSY',
                'RETURNED',
                'LOST_FROM_INVENTORY',
                'WAITING_FOR_SHIPMENT',
                'SHIPMENT_DISPATCHED',
                'SHIPMENT_DELIVERED'
            )
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."INVENTORY_ITEM_STATUS_NAMES" AS ENUM('PENDING', 'OK', 'ERROR', 'NEW_ADDITION')
        `);
        await queryRunner.query(`
            CREATE TABLE "inventoryItem" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "actionId" uuid,
                "period" tstzrange NOT NULL DEFAULT tstzrange(now(), NULL),
                "scannedAt" TIMESTAMP WITH TIME ZONE,
                "scannedById" uuid,
                "status" "public"."INVENTORY_ITEM_PRODUCT_STATUS_NAMES" NOT NULL,
                "inventoryStatus" "public"."INVENTORY_ITEM_STATUS_NAMES" NOT NULL DEFAULT 'PENDING',
                "code" character varying NOT NULL,
                "sn" character varying,
                "buyPrice" numeric(14, 4) NOT NULL DEFAULT '0',
                "productId" uuid NOT NULL,
                "productEnvelopeId" uuid,
                "productEnvelopeCode" character varying,
                "productEnvelopeName" character varying,
                "ecommerceOrderId" uuid,
                "serviceCaseId" uuid,
                "customerClaimId" uuid,
                "warrantyClaimId" uuid,
                "inventoryId" uuid NOT NULL,
                CONSTRAINT "PK_056fb0e15949f7aae29524a35eb" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_261efc6c0e5cf0b3a15d58d792" ON "inventoryItem" ("actionId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_071f9a06ef0f5aeee28637954c" ON "inventoryItem" ("id", "createdAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_ba35f8ccc8a4c608c6eee9d4b4" ON "inventoryItem" ("scannedAt")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_ef9a06c941502c98f1fcb9145d" ON "inventoryItem" ("scannedById")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_7b06ddff30231646a28f3fa118" ON "inventoryItem" ("status")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_16f66394da6d3aeab5c17471aa" ON "inventoryItem" ("inventoryStatus")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_f0106cfcdaf013dcbd2c5fbb3e" ON "inventoryItem" ("productId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_ad641a2026c266763a04510ed4" ON "inventoryItem" ("productEnvelopeId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_ae304bd2a6e14e143f1bb70b2a" ON "inventoryItem" ("ecommerceOrderId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_c6eeac86c03e034935ea4fbead" ON "inventoryItem" ("serviceCaseId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_fa8f6c5e784f90c7216e539251" ON "inventoryItem" ("customerClaimId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_448ad44be5e45f8fc44cd112d5" ON "inventoryItem" ("warrantyClaimId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_9e57008712d58d77f04577946b" ON "inventoryItem" ("inventoryId")
        `);
        await queryRunner.query(`
            ALTER TYPE "public"."PRODUCT_STATUS_NAMES"
            RENAME TO "PRODUCT_STATUS_NAMES_old"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_STATUS_NAMES" AS ENUM(
                'AT_SUPPLIER',
                'ON_THE_WAY',
                'EXTERNAL_STOCK',
                'TO_CHECK',
                'TO_CHECK_SN',
                'TO_TEST',
                'TESTED',
                'MISSING',
                'SERVICE',
                'WARRANTY_CLAIM',
                'STOCK',
                'FOR_SALE',
                'RESERVED',
                'SOLD',
                'CUSTOMER_CLAIM',
                'DEAD',
                'AUTOPSY',
                'RETURNED',
                'LOST_FROM_INVENTORY',
                'WAITING_FOR_SHIPMENT',
                'SHIPMENT_DISPATCHED',
                'SHIPMENT_DELIVERED'
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status" DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status" TYPE "public"."PRODUCT_STATUS_NAMES" USING "status"::"text"::"public"."PRODUCT_STATUS_NAMES"
        `);
        await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status"
            SET DEFAULT 'AT_SUPPLIER'
        `);
        await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_STATUS_NAMES_old"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryCode"
            ADD CONSTRAINT "FK_1a56a28b60e1c3efd105f1ad2b1" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "inventory"
            ADD CONSTRAINT "FK_ddfc33ed23fa98af2424b98ab2b" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "inventory"
            ADD CONSTRAINT "FK_a35b2af7fa30e71e4229e3d4e22" FOREIGN KEY ("createdById") REFERENCES "user"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "inventory"
            ADD CONSTRAINT "FK_bec6a233c732ac855ad715cb49e" FOREIGN KEY ("codeId") REFERENCES "inventoryCode"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "inventory"
            ADD CONSTRAINT "FK_5f544029aef4db29f7df0067a29" FOREIGN KEY ("finishedById") REFERENCES "user"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD CONSTRAINT "FK_261efc6c0e5cf0b3a15d58d7922" FOREIGN KEY ("actionId") REFERENCES "action"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD CONSTRAINT "FK_ef9a06c941502c98f1fcb9145d2" FOREIGN KEY ("scannedById") REFERENCES "user"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD CONSTRAINT "FK_f0106cfcdaf013dcbd2c5fbb3e4" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD CONSTRAINT "FK_ad641a2026c266763a04510ed46" FOREIGN KEY ("productEnvelopeId") REFERENCES "productEnvelope"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD CONSTRAINT "FK_ae304bd2a6e14e143f1bb70b2a8" FOREIGN KEY ("ecommerceOrderId") REFERENCES "ecommerceOrder"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD CONSTRAINT "FK_c6eeac86c03e034935ea4fbead4" FOREIGN KEY ("serviceCaseId") REFERENCES "serviceCase"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD CONSTRAINT "FK_fa8f6c5e784f90c7216e5392515" FOREIGN KEY ("customerClaimId") REFERENCES "customerClaim"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD CONSTRAINT "FK_448ad44be5e45f8fc44cd112d51" FOREIGN KEY ("warrantyClaimId") REFERENCES "warrantyClaim"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD CONSTRAINT "FK_9e57008712d58d77f04577946b7" FOREIGN KEY ("inventoryId") REFERENCES "inventory"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP CONSTRAINT "FK_9e57008712d58d77f04577946b7"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP CONSTRAINT "FK_448ad44be5e45f8fc44cd112d51"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP CONSTRAINT "FK_fa8f6c5e784f90c7216e5392515"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP CONSTRAINT "FK_c6eeac86c03e034935ea4fbead4"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP CONSTRAINT "FK_ae304bd2a6e14e143f1bb70b2a8"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP CONSTRAINT "FK_ad641a2026c266763a04510ed46"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP CONSTRAINT "FK_f0106cfcdaf013dcbd2c5fbb3e4"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP CONSTRAINT "FK_ef9a06c941502c98f1fcb9145d2"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP CONSTRAINT "FK_261efc6c0e5cf0b3a15d58d7922"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventory" DROP CONSTRAINT "FK_5f544029aef4db29f7df0067a29"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventory" DROP CONSTRAINT "FK_bec6a233c732ac855ad715cb49e"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventory" DROP CONSTRAINT "FK_a35b2af7fa30e71e4229e3d4e22"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventory" DROP CONSTRAINT "FK_ddfc33ed23fa98af2424b98ab2b"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryCode" DROP CONSTRAINT "FK_1a56a28b60e1c3efd105f1ad2b1"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."PRODUCT_STATUS_NAMES_old" AS ENUM(
                'AT_SUPPLIER',
                'ON_THE_WAY',
                'EXTERNAL_STOCK',
                'TO_CHECK',
                'TO_CHECK_SN',
                'TO_TEST',
                'TESTED',
                'MISSING',
                'SERVICE',
                'WARRANTY_CLAIM',
                'STOCK',
                'FOR_SALE',
                'RESERVED',
                'SOLD',
                'CUSTOMER_CLAIM',
                'DEAD',
                'AUTOPSY',
                'RETURNED',
                'WAITING_FOR_SHIPMENT',
                'SHIPMENT_DISPATCHED',
                'SHIPMENT_DELIVERED'
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status" DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status" TYPE "public"."PRODUCT_STATUS_NAMES_old" USING "status"::"text"::"public"."PRODUCT_STATUS_NAMES_old"
        `);
        await queryRunner.query(`
            ALTER TABLE "product"
            ALTER COLUMN "status"
            SET DEFAULT 'AT_SUPPLIER'
        `);
        await queryRunner.query(`
            DROP TYPE "public"."PRODUCT_STATUS_NAMES"
        `);
        await queryRunner.query(`
            ALTER TYPE "public"."PRODUCT_STATUS_NAMES_old"
            RENAME TO "PRODUCT_STATUS_NAMES"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_9e57008712d58d77f04577946b"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_448ad44be5e45f8fc44cd112d5"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_fa8f6c5e784f90c7216e539251"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_c6eeac86c03e034935ea4fbead"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_ae304bd2a6e14e143f1bb70b2a"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_ad641a2026c266763a04510ed4"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_f0106cfcdaf013dcbd2c5fbb3e"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_16f66394da6d3aeab5c17471aa"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_7b06ddff30231646a28f3fa118"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_ef9a06c941502c98f1fcb9145d"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_ba35f8ccc8a4c608c6eee9d4b4"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_071f9a06ef0f5aeee28637954c"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_261efc6c0e5cf0b3a15d58d792"
        `);
        await queryRunner.query(`
            DROP TABLE "inventoryItem"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."INVENTORY_ITEM_STATUS_NAMES"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."INVENTORY_ITEM_PRODUCT_STATUS_NAMES"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_ef43024ae995c2a217d3882d25"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_5f544029aef4db29f7df0067a2"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_bec6a233c732ac855ad715cb49"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_4c4f8b4033c384d0889519de3a"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_a35b2af7fa30e71e4229e3d4e2"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_8e274d20e053eeff00f8423dcf"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_ddfc33ed23fa98af2424b98ab2"
        `);
        await queryRunner.query(`
            DROP TABLE "inventory"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."INVENTORY_STATUS_NAMES"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_ec141ecd7d64ed75b097e334c8"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_a6739e4ec5bcae5e2d296a86c7"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_1a56a28b60e1c3efd105f1ad2b"
        `);
        await queryRunner.query(`
            DROP TABLE "inventoryCode"
        `);
    }

}
