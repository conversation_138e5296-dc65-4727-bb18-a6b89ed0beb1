import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1750422982413 implements MigrationInterface {
    name = 'Migration1750422982413'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "batch"
            ADD "reportSentAt" TIMESTAMP WITH TIME ZONE
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_0461d03a5a9d95bad2f29959de" ON "batch" ("reportSentAt")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX "public"."IDX_0461d03a5a9d95bad2f29959de"
        `);
        await queryRunner.query(`
            ALTER TABLE "batch" DROP COLUMN "reportSentAt"
        `);
    }

}
