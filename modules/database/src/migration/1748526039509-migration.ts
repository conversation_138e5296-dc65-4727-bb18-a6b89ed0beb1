import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1748526039509 implements MigrationInterface {
	name = 'Migration1748526039509';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            ALTER TYPE "public"."SCOPE_NAMES"
            RENAME TO "SCOPE_NAMES_old"
        `);
		await queryRunner.query(`
            CREATE TYPE "public"."SCOPE_NAMES" AS ENUM(
                'admin',
                'home',
                'batchRead',
                'batchWrite',
                'batchCheck',
                'batchDelivery',
                'productTest',
                'productTestLead',
                'testRead',
                'serviceRead',
                'serviceWrite',
                'serviceManage',
                'warrantyClaimRead',
                'warrantyClaimWrite',
                'stock',
                'productRead',
                'productWrite',
                'orderRead',
                'orderWrite',
                'productAdmin'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "scope"
            ALTER COLUMN "name" TYPE "public"."SCOPE_NAMES" USING "name"::"text"::"public"."SCOPE_NAMES"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."SCOPE_NAMES_old"
        `);
		await queryRunner.query(`
			INSERT INTO "scope" ("name", "description") VALUES ('serviceManage', 'Management servisu')
		`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
			DELETE FROM "scope" WHERE name = 'serviceManage';
		`);
		await queryRunner.query(`
            CREATE TYPE "public"."SCOPE_NAMES_old" AS ENUM(
                'admin',
                'batchCheck',
                'batchDelivery',
                'batchRead',
                'batchWrite',
                'home',
                'orderRead',
                'orderWrite',
                'productAdmin',
                'productRead',
                'productTest',
                'productTestLead',
                'productWrite',
                'serviceRead',
                'serviceWrite',
                'stock',
                'testRead',
                'warrantyClaimRead',
                'warrantyClaimWrite'
            )
        `);
		await queryRunner.query(`
            ALTER TABLE "scope"
            ALTER COLUMN "name" TYPE "public"."SCOPE_NAMES_old" USING "name"::"text"::"public"."SCOPE_NAMES_old"
        `);
		await queryRunner.query(`
            DROP TYPE "public"."SCOPE_NAMES"
        `);
		await queryRunner.query(`
            ALTER TYPE "public"."SCOPE_NAMES_old"
            RENAME TO "SCOPE_NAMES"
        `);
	}
}
