import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Migration1753719488916 implements MigrationInterface {
	name = 'Migration1753719488916';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
			ALTER TABLE "notification"
				ALTER COLUMN "notificationCode" TYPE varchar USING "notificationCode"::varchar;
		`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
			ALTER TABLE "notification"
				ALTER COLUMN "notificationCode" TYPE uuid USING "notificationCode"::uuid;
		`);
	}
}
