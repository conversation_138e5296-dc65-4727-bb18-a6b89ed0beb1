import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1750661277813 implements MigrationInterface {
    name = 'Migration1750661277813'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX "public"."IDX_0dcff7a0355f76a1db87e1c80a"
        `);
        await queryRunner.query(`
            ALTER TABLE "serviceTaskType" DROP COLUMN "productTargetStatus"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."SERVICE_TASK_PRODUCT_TARGET_STATUSES"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."AFTER_SERVICE_PRODUCT_TARGET_STATUSES" AS ENUM('DEAD', 'AUTOPSY', 'TO_TEST', 'STOCK', 'SOLD')
        `);
        await queryRunner.query(`
            ALTER TABLE "serviceTaskType"
            ADD "afterServiceProductTargetStatus" "public"."AFTER_SERVICE_PRODUCT_TARGET_STATUSES" NOT NULL DEFAULT 'TO_TEST'
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."AFTER_WARRANTY_CLAIM_PRODUCT_TARGET_STATUSES" AS ENUM('AUTOPSY', 'TO_TEST', 'STOCK')
        `);
        await queryRunner.query(`
            ALTER TABLE "serviceTaskType"
            ADD "afterWarrantyClaimProductTargetStatus" "public"."AFTER_WARRANTY_CLAIM_PRODUCT_TARGET_STATUSES"
        `);
        await queryRunner.query(`
            ALTER TABLE "productDefect"
            ADD "vendorTaskId" uuid
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_385fd1ff82716a4f643bcb5b77" ON "serviceTaskType" ("afterServiceProductTargetStatus")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_c3e46d7386c69de1b015b43922" ON "serviceTaskType" ("afterWarrantyClaimProductTargetStatus")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_90aea708a48b5d847da1b59d46" ON "productDefect" ("vendorTaskId")
        `);
        await queryRunner.query(`
            ALTER TABLE "productDefect"
            ADD CONSTRAINT "FK_90aea708a48b5d847da1b59d460" FOREIGN KEY ("vendorTaskId") REFERENCES "serviceTask"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "productDefect" DROP CONSTRAINT "FK_90aea708a48b5d847da1b59d460"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_90aea708a48b5d847da1b59d46"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_c3e46d7386c69de1b015b43922"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_385fd1ff82716a4f643bcb5b77"
        `);
        await queryRunner.query(`
            ALTER TABLE "productDefect" DROP COLUMN "vendorTaskId"
        `);
        await queryRunner.query(`
            ALTER TABLE "serviceTaskType" DROP COLUMN "afterWarrantyClaimProductTargetStatus"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."AFTER_WARRANTY_CLAIM_PRODUCT_TARGET_STATUSES"
        `);
        await queryRunner.query(`
            ALTER TABLE "serviceTaskType" DROP COLUMN "afterServiceProductTargetStatus"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."AFTER_SERVICE_PRODUCT_TARGET_STATUSES"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."SERVICE_TASK_PRODUCT_TARGET_STATUSES" AS ENUM('DEAD', 'TO_TEST', 'STOCK', 'SOLD')
        `);
        await queryRunner.query(`
            ALTER TABLE "serviceTaskType"
            ADD "productTargetStatus" "public"."SERVICE_TASK_PRODUCT_TARGET_STATUSES" NOT NULL DEFAULT 'TO_TEST'
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_0dcff7a0355f76a1db87e1c80a" ON "serviceTaskType" ("productTargetStatus")
        `);
    }

}
