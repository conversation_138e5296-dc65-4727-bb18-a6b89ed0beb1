import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1759919081139 implements MigrationInterface {
    name = 'Migration1759919081139'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TYPE "public"."INVENTORY_ITEM_ENVELOPE_TYPES" AS ENUM('NEW', 'USED', 'AMOUNT')
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "productEnvelopeType" "public"."INVENTORY_ITEM_ENVELOPE_TYPES" NOT NULL DEFAULT 'USED'
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "envelopeProductsExpectedCount" integer
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "envelopeProductsRealCount" integer
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem"
            ADD "envelopeProductsCountWasConfirmed" boolean
        `);
        await queryRunner.query(`
            ALTER TYPE "public"."ENVELOPE_TYPES"
            RENAME TO "ENVELOPE_TYPES_old"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."ENVELOPE_TYPES" AS ENUM('NEW', 'USED', 'AMOUNT')
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ALTER COLUMN "type" DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ALTER COLUMN "type" TYPE "public"."ENVELOPE_TYPES" USING "type"::"text"::"public"."ENVELOPE_TYPES"
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ALTER COLUMN "type"
            SET DEFAULT 'USED'
        `);
        await queryRunner.query(`
            DROP TYPE "public"."ENVELOPE_TYPES_old"
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TYPE "public"."ENVELOPE_TYPES_old" AS ENUM('NEW', 'USED')
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ALTER COLUMN "type" DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ALTER COLUMN "type" TYPE "public"."ENVELOPE_TYPES_old" USING "type"::"text"::"public"."ENVELOPE_TYPES_old"
        `);
        await queryRunner.query(`
            ALTER TABLE "productEnvelope"
            ALTER COLUMN "type"
            SET DEFAULT 'USED'
        `);
        await queryRunner.query(`
            DROP TYPE "public"."ENVELOPE_TYPES"
        `);
        await queryRunner.query(`
            ALTER TYPE "public"."ENVELOPE_TYPES_old"
            RENAME TO "ENVELOPE_TYPES"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "envelopeProductsCountWasConfirmed"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "envelopeProductsRealCount"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "envelopeProductsExpectedCount"
        `);
        await queryRunner.query(`
            ALTER TABLE "inventoryItem" DROP COLUMN "productEnvelopeType"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."INVENTORY_ITEM_ENVELOPE_TYPES"
        `);
    }

}
