import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { CosmeticAreaCosmeticDefect } from './CosmeticAreaCosmeticDefect';
import { ProductCategory } from './ProductCategory';
import { ProductCosmeticDefect } from './ProductCosmeticDefect';

@Entity({ name: 'cosmeticArea' })
@Index(['name', 'productCategory'], { unique: true })
export class CosmeticArea extends CommonEntity {
	@Column({ type: 'text' })
	name: string;

	@ManyToOne(() => ProductCategory, (productCategory) => productCategory.cosmeticAreas, { onDelete: 'CASCADE' })
	@Index()
	productCategory: ProductCategory;
	@Column({ type: 'uuid' })
	productCategoryId: string;

	@OneToMany(() => CosmeticAreaCosmeticDefect, (cosmeticAreaCosmeticDefect) => cosmeticAreaCosmeticDefect.cosmeticArea)
	cosmeticAreaCosmeticDefects: CosmeticAreaCosmeticDefect[];

	@OneToMany(() => ProductCosmeticDefect, (productCosmeticDefect) => productCosmeticDefect.cosmeticArea)
	productCosmeticDefects: ProductCosmeticDefect[];
}
