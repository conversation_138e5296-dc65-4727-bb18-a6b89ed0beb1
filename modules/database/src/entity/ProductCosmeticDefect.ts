import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { CosmeticArea } from './CosmeticArea';
import { CosmeticDefect } from './CosmeticDefect';
import { FileProductCosmeticDefect } from './File';
import { Product } from './Product';

@Entity({ name: 'productCosmeticDefect' })
@Index(['cosmeticDefect', 'product'])
export class ProductCosmeticDefect extends CommonEntity {
	@Column({ type: 'boolean', default: false })
	isFix: boolean;

	@ManyToOne(() => CosmeticDefect, (cosmeticDefect) => cosmeticDefect.productCosmeticDefects, { onDelete: 'CASCADE' })
	@Index()
	cosmeticDefect: CosmeticDefect;
	@Column({ type: 'uuid' })
	cosmeticDefectId: string;

	@ManyToOne(() => Product, (product) => product.productCosmeticDefects, { onDelete: 'CASCADE' })
	@Index()
	product: Product;
	@Column({ type: 'uuid' })
	productId: string;

	@ManyToOne(() => CosmeticArea, (cosmeticArea) => cosmeticArea.productCosmeticDefects, { onDelete: 'CASCADE' })
	@Index()
	cosmeticArea: CosmeticArea;
	@Column({ type: 'uuid' })
	cosmeticAreaId: string;

	@OneToMany(() => FileProductCosmeticDefect, (fileProductCosmeticDefect) => fileProductCosmeticDefect.productCosmeticDefect)
	files: FileProductCosmeticDefect[];
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'productCosmeticDefectHistory', synchronize: false })
export class ProductCosmeticDefectHistory extends ProductCosmeticDefect {}
