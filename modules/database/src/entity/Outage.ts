import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { User } from './User';

@Entity({ name: 'outage' })
export class Outage extends CommonEntity {
	@ManyToOne(() => User, (user) => user.createdOutages)
	@Index()
	createdBy: User | null;
	@Column({ type: 'uuid', nullable: true })
	createdById: string | null;

	@Column({ type: 'timestamptz', nullable: true })
	endedAt: Date | null;

	@ManyToOne(() => User, (user) => user.endedOutages)
	@Index()
	endedBy: User | null;
	@Column({ type: 'uuid', nullable: true })
	endedById: string | null;

	@Column({ type: 'varchar' })
	status: 'ok' | 'outage';
}
