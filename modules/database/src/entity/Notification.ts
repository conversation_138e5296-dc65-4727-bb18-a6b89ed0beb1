import { PRIORITY_VALUES } from '@pocitarna-nx-2023/config';
import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { NotificationType } from './NotificationType';
import { User } from './User';

@Entity({ name: 'notification' })
export class Notification extends CommonEntity {
	@Column({ type: 'varchar' })
	notificationCode: string;

	@Column({ type: 'varchar' })
	title: string;

	@Column({ type: 'text', default: '' })
	body: string;

	@Column({ type: 'int2', default: PRIORITY_VALUES.MEDIUM })
	priority: number;

	@Column({ type: 'varchar', default: '/' })
	href: string;

	@Column({ type: 'timestamptz', nullable: true })
	viewUntil: Date;

	@Column({ type: 'timestamptz', nullable: true })
	viewedAt: Date | null;

	@Column({ type: 'timestamptz', nullable: true })
	refiredAt: Date | null;

	@ManyToOne(() => User, (user) => user.notifications)
	@Index()
	user: User;
	@Column({ type: 'uuid' })
	userId: string;

	@ManyToOne(() => NotificationType, (notificationType) => notificationType.notifications)
	@Index()
	notificationType: NotificationType;
	@Column({ type: 'uuid' })
	notificationTypeId: string;
}
