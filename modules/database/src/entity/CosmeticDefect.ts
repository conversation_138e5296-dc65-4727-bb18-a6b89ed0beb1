import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { CosmeticAreaCosmeticDefect } from './CosmeticAreaCosmeticDefect';
import { CustomerClaimCosmeticDefect } from './CustomerClaimCosmeticDefect';
import { Grade } from './Grade';
import { ProductCategoryCosmeticDefect } from './ProductCategoryCosmeticDefect';
import { ProductCosmeticDefect } from './ProductCosmeticDefect';

@Entity({ name: 'cosmeticDefect' })
export class CosmeticDefect extends CommonEntity {
	@Column({ type: 'text' })
	name: string;

	@Column({ type: 'boolean', default: false })
	pictureRequired: boolean;

	@OneToMany(() => ProductCategoryCosmeticDefect, (productCategoryCosmeticDefect) => productCategoryCosmeticDefect.cosmeticDefect)
	productCategoryCosmeticDefects: ProductCategoryCosmeticDefect[];

	@OneToMany(() => CosmeticAreaCosmeticDefect, (cosmeticAreaCosmeticDefect) => cosmeticAreaCosmeticDefect.cosmeticDefect)
	cosmeticAreaCosmeticDefects: CosmeticAreaCosmeticDefect[];

	@OneToMany(() => ProductCosmeticDefect, (productCosmeticDefect) => productCosmeticDefect.cosmeticDefect)
	productCosmeticDefects: ProductCosmeticDefect[];

	@OneToMany(() => CustomerClaimCosmeticDefect, (customerClaimCosmeticDefect) => customerClaimCosmeticDefect.cosmeticDefect)
	customerClaimCosmeticDefects: CustomerClaimCosmeticDefect[];

	@ManyToOne(() => Grade, (grade) => grade.cosmeticDefects, { onDelete: 'SET NULL' })
	@Index()
	grade: Grade;
	@Column({ type: 'uuid' })
	gradeId: string;
}
