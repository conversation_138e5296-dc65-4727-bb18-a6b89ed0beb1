import { type PriceRoundingStrategy } from '@pocitarna-nx-2023/config';
import { Column, Entity, Index, OneToMany } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { CosmeticDefect } from './CosmeticDefect';
import { Product } from './Product';
import { ProductCategoryGrade } from './ProductCategoryGrade';
import { ProductEnvelope } from './ProductEnvelope';

@Entity({ name: 'grade' })
export class Grade extends CommonEntity {
	@Column({ type: 'text' })
	name: string;

	@Column({ type: 'float4' })
	discountPercentage: number;

	@Column({ type: 'int', default: 0 })
	sequence: number;

	@Column({ type: 'varchar', default: 'HIGH' })
	@Index()
	priceRoundingStrategy: PriceRoundingStrategy;

	@OneToMany(() => Product, (product) => product.grade)
	products: Product[];

	@OneToMany(() => ProductEnvelope, (productEnvelope) => productEnvelope.grade)
	productEnvelopes: ProductEnvelope[];

	@OneToMany(() => CosmeticDefect, (cosmeticDefect) => cosmeticDefect.grade)
	cosmeticDefects: CosmeticDefect[];

	@OneToMany(() => ProductCategoryGrade, (categoryGrading) => categoryGrading.grade)
	productCategories: ProductCategoryGrade[];
}
