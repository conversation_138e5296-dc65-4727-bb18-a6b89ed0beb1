import { Column, Entity, Generated, Index, OneToMany } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { WarehousePosition } from './WarehousePosition';

@Entity({ name: 'warehousePositionCode' })
export class WarehousePositionCode extends CommonEntity {
	@Column({ type: 'int', unique: true })
	@Generated('increment')
	@Index()
	code: number;

	@OneToMany(() => WarehousePosition, (warehousePosition) => warehousePosition.code)
	warehousePosition: WarehousePosition;
}
