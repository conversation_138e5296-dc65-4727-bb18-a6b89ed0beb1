import { type EnvelopeType, type InventoryItemStatus, type ProductStatus } from '@pocitarna-nx-2023/config';
import { type Decimal } from 'decimal.js';
import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { DecimalTransformer } from '../utils/DecimalTransformer';
import { Batch } from './Batch';
import { CustomerClaim } from './CustomerClaim';
import { EcommerceOrder } from './EcommerceOrder';
import { EcommerceOrderItem } from './EcommerceOrderItem';
import { Inventory } from './Inventory';
import { Product } from './Product';
import { ProductCategory } from './ProductCategory';
import { ProductEnvelope } from './ProductEnvelope';
import { ServiceCase } from './ServiceCase';
import { User } from './User';
import { WarehousePosition } from './WarehousePosition';
import { WarrantyClaim } from './WarrantyClaim';

@Entity({ name: 'inventoryItem' })
export class InventoryItem extends CommonEntity {
	@Column({ type: 'timestamptz', default: null })
	@Index()
	scannedAt: Date | null;

	@ManyToOne(() => User, (user) => user.scannedInventoryItems, { onDelete: 'SET NULL' })
	@Index()
	scannedBy: User | null;
	@Column({ type: 'uuid', nullable: true })
	scannedById: string | null;

	@Column({ type: 'varchar' })
	@Index()
	status: ProductStatus;

	@Column({ type: 'varchar', default: 'PENDING' })
	@Index()
	inventoryStatus: InventoryItemStatus;

	@Column({ type: 'varchar' })
	code: string;

	@Column({ type: 'varchar', nullable: true, default: null })
	sn: string | null;

	@Column({ type: 'decimal', precision: 14, scale: 4, default: 0.0, transformer: new DecimalTransformer() })
	buyPrice: Decimal;

	@ManyToOne(() => Product, (product) => product.inventoryItems, { onDelete: 'CASCADE' })
	@Index()
	product: Product;
	@Column({ type: 'uuid' })
	productId: string;

	@ManyToOne(() => ProductCategory, (productCategory) => productCategory.inventoryItems, { onDelete: 'CASCADE' })
	@Index()
	productCategory: ProductCategory | null;
	@Column({ type: 'uuid', nullable: true })
	productCategoryId: string | null;

	@ManyToOne(() => Batch, (batch) => batch.inventoryItems, { onDelete: 'CASCADE' })
	@Index()
	batch: Batch | null;
	@Column({ type: 'uuid', nullable: true })
	batchId: string | null;

	@ManyToOne(() => ProductEnvelope, (productEnvelope) => productEnvelope.inventoryItems, { onDelete: 'CASCADE' })
	@Index()
	productEnvelope: ProductEnvelope | null;
	@Column({ type: 'uuid', nullable: true })
	productEnvelopeId: string | null;

	@Column({ type: 'varchar', nullable: true })
	productEnvelopeCode: string | null;

	@Column({ type: 'varchar', nullable: true })
	productEnvelopeName: string | null;

	@Column({ type: 'varchar', default: 'USED' })
	productEnvelopeType: EnvelopeType;

	@Column({ type: 'int', nullable: true })
	envelopeProductsExpectedCount: number | null;

	@Column({ type: 'int', nullable: true, default: null })
	envelopeProductsRealCount: number | null;

	@Column({ type: 'boolean', nullable: true, default: null })
	envelopeProductsCountWasConfirmed: boolean | null;

	@ManyToOne(() => EcommerceOrder, (order) => order.inventoryItems, { onDelete: 'CASCADE' })
	@Index()
	ecommerceOrder: EcommerceOrder | null;
	@Column({ type: 'uuid', nullable: true })
	ecommerceOrderId: string | null;

	@ManyToOne(() => EcommerceOrderItem, (orderItem) => orderItem.inventoryItems, { onDelete: 'CASCADE' })
	@Index()
	ecommerceOrderItem: EcommerceOrderItem | null;
	@Column({ type: 'uuid', nullable: true })
	ecommerceOrderItemId: string | null;

	@Column({ type: 'varchar', nullable: true })
	ecommerceOrderCode: string | null;

	@ManyToOne(() => ServiceCase, (serviceCase) => serviceCase.inventoryItems, { onDelete: 'CASCADE' })
	@Index()
	serviceCase: ServiceCase | null;
	@Column({ type: 'uuid', nullable: true })
	serviceCaseId: string | null;

	@Column({ type: 'int', nullable: true })
	serviceCaseCode: number | null;

	@ManyToOne(() => CustomerClaim, (customerClaim) => customerClaim.inventoryItems, { onDelete: 'CASCADE' })
	@Index()
	customerClaim: CustomerClaim | null;
	@Column({ type: 'uuid', nullable: true })
	customerClaimId: string | null;

	@Column({ type: 'int', nullable: true })
	customerClaimCode: number | null;

	@ManyToOne(() => WarrantyClaim, (warrantyClaim) => warrantyClaim.inventoryItems, { onDelete: 'CASCADE' })
	@Index()
	warrantyClaim: WarrantyClaim | null;
	@Column({ type: 'uuid', nullable: true })
	warrantyClaimId: string | null;

	@Column({ type: 'int', nullable: true })
	warrantyClaimCode: number | null;

	@ManyToOne(() => WarehousePosition, (warehousePosition) => warehousePosition.preScanInventoryItems, { onDelete: 'SET NULL' })
	@Index()
	preScanWarehousePosition: WarehousePosition | null;
	@Column({ type: 'uuid', nullable: true })
	preScanWarehousePositionId: string | null;
	@Column({ type: 'varchar', nullable: true })
	preScanWarehousePositionName: string | null;

	@ManyToOne(() => WarehousePosition, (warehousePosition) => warehousePosition.postScanInventoryItems, { onDelete: 'SET NULL' })
	@Index()
	postScanWarehousePosition: WarehousePosition | null;
	@Column({ type: 'uuid', nullable: true })
	postScanWarehousePositionId: string | null;
	@Column({ type: 'varchar', nullable: true })
	postScanWarehousePositionName: string | null;

	@ManyToOne(() => Inventory, (inventory) => inventory.inventoryItems, { onDelete: 'CASCADE' })
	@Index()
	inventory: Inventory;
	@Column({ type: 'uuid' })
	inventoryId: string;
}
