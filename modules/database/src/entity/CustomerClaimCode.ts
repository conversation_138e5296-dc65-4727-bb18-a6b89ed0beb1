import { Column, Entity, Generated, Index, OneToMany } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { CustomerClaim } from './CustomerClaim';

@Entity({ name: 'customerClaimCode' })
export class CustomerClaimCode extends CommonEntity {
	@Column({ type: 'int', unique: true })
	@Generated('increment')
	@Index()
	code: number;

	@OneToMany(() => CustomerClaim, (customerClaim) => customerClaim.code)
	customerClaim: CustomerClaim;
}
