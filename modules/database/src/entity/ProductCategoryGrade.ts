import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { Grade } from './Grade';
import { ProductCategory } from './ProductCategory';

@Entity({ name: 'productCategoryGrade' })
@Index(['productCategory', 'grade'], { unique: true })
export class ProductCategoryGrade extends CommonEntity {
	@Column({ type: 'int', default: 0 })
	maxCosmeticDefects: number;

	@ManyToOne(() => ProductCategory, (productCategory) => productCategory.grades, { onDelete: 'CASCADE' })
	@Index()
	productCategory: ProductCategory;
	@Column({ type: 'uuid' })
	productCategoryId: string;

	@ManyToOne(() => Grade, (grade) => grade.productCategories, { onDelete: 'CASCADE' })
	@Index()
	grade: Grade;
	@Column({ type: 'uuid' })
	gradeId: string;
}
