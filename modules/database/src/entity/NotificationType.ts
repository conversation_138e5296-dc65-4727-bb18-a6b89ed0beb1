import { type NotificationType as NotificationTypeName } from '@pocitarna-nx-2023/config';
import { Column, Entity, Index, OneToMany } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { Notification } from './Notification';
import { UserNotificationType } from './UserNotificationType';

@Entity({ name: 'notificationType' })
export class NotificationType extends CommonEntity {
	@Column({ type: 'varchar', unique: true })
	@Index()
	name: NotificationTypeName;

	@Column({ type: 'varchar' })
	@Index()
	label: string;

	@OneToMany(() => Notification, (notification) => notification.notificationType)
	notifications: Notification[];

	@OneToMany(() => UserNotificationType, (userNotificationType) => userNotificationType.notificationType)
	users: UserNotificationType[];
}
