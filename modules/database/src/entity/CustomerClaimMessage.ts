import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { Contact } from './Contact';
import { CustomerClaim } from './CustomerClaim';
import { User } from './User';

@Entity({ name: 'customerClaimMessage' })
export class CustomerClaimMessage extends CommonEntity {
	@Column({ type: 'text', default: '' })
	message: string;

	@ManyToOne(() => Contact, (contact) => contact.customerClaimMessages, { onDelete: 'SET NULL', nullable: true })
	@Index()
	contact: Contact | null;
	@Column({ type: 'uuid', nullable: true })
	contactId: string | null;

	@ManyToOne(() => User, (user) => user.customerClaimMessages, { onDelete: 'SET NULL', nullable: true })
	@Index()
	user: User | null;
	@Column({ type: 'uuid', nullable: true })
	userId: string | null;

	@ManyToOne(() => CustomerClaim, (customerClaim) => customerClaim.messages, { onDelete: 'CASCADE' })
	@Index()
	customerClaim: CustomerClaim;
	@Column({ type: 'uuid' })
	customerClaimId: string;
}
