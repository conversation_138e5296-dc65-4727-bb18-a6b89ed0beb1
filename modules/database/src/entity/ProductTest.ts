import { Column, Entity, Index, JoinColumn, ManyToOne, OneToOne } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { ConditionalAttribute } from './ConditionalAttribute';
import { Product } from './Product';
import { ProductEnvelope } from './ProductEnvelope';
import { User } from './User';

@Entity({ name: 'productTest' })
export class ProductTest extends CommonEntity {
	@Column({ type: 'timestamptz', default: null })
	@Index()
	deadlineAt: Date | null;

	@Column({ type: 'timestamptz', default: null })
	@Index()
	testedAt: Date | null;

	@Column({ type: 'boolean', default: false })
	ignoreMismatch: boolean;

	@Column({ type: 'boolean', default: false })
	hasTestMismatches: boolean;

	@Column({ type: 'boolean', default: false })
	hasStockMismatches: boolean;

	@ManyToOne(() => ProductEnvelope, (productEnvelope) => productEnvelope.printedTests, { onDelete: 'SET NULL' })
	@Index()
	printedWithEnvelope: ProductEnvelope | null;
	@Column({ type: 'uuid', nullable: true })
	printedWithEnvelopeId: string | null;

	@OneToOne(() => Product, (product) => product.productTest, { onDelete: 'CASCADE' })
	@JoinColumn()
	@Index()
	product: Product;
	@Column({ type: 'uuid' })
	productId: string;

	@ManyToOne(() => User, (user) => user.productTests)
	@Index()
	testedBy: User | null;
	@Column({ type: 'uuid', nullable: true })
	testedById: string | null;

	@ManyToOne(() => ConditionalAttribute, (conditionalAttribute) => conditionalAttribute.productTests, { onDelete: 'SET NULL' })
	@Index()
	conditionalAttribute: ConditionalAttribute | null;
	@Column({ type: 'uuid', nullable: true })
	conditionalAttributeId: string | null;
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'productTestHistory', synchronize: false })
export class ProductTestHistory extends ProductTest {}
