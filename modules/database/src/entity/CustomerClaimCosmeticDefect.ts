import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { CosmeticArea } from './CosmeticArea';
import { CosmeticDefect } from './CosmeticDefect';
import { CustomerClaim } from './CustomerClaim';
import { FileCustomerClaimCosmeticDefect } from './File';

@Entity({ name: 'customerClaimCosmeticDefect' })
@Index(['cosmeticDefect', 'customerClaim'])
export class CustomerClaimCosmeticDefect extends CommonEntity {
	@ManyToOne(() => CosmeticDefect, (cosmeticDefect) => cosmeticDefect.customerClaimCosmeticDefects, { onDelete: 'CASCADE' })
	@Index()
	cosmeticDefect: CosmeticDefect;
	@Column({ type: 'uuid' })
	cosmeticDefectId: string;

	@ManyToOne(() => CustomerClaim, (customerClaim) => customerClaim.customerClaimCosmeticDefects, { onDelete: 'CASCADE' })
	@Index()
	customerClaim: CustomerClaim;
	@Column({ type: 'uuid' })
	customerClaimId: string;

	@ManyToOne(() => CosmeticArea, (cosmeticArea) => cosmeticArea.productCosmeticDefects, { onDelete: 'CASCADE' })
	@Index()
	cosmeticArea: CosmeticArea;
	@Column({ type: 'uuid' })
	cosmeticAreaId: string;

	@OneToMany(
		() => FileCustomerClaimCosmeticDefect,
		(fileCustomerClaimCosmeticDefect) => fileCustomerClaimCosmeticDefect.customerClaimCosmeticDefect,
	)
	files: FileCustomerClaimCosmeticDefect[];
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'customerClaimCosmeticDefectHistory', synchronize: false })
export class CustomerClaimCosmeticDefectHistory extends CustomerClaimCosmeticDefect {}
