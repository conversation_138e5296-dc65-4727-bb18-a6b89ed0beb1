import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { CosmeticArea } from './CosmeticArea';
import { CosmeticDefect } from './CosmeticDefect';

@Entity({ name: 'cosmeticAreaCosmeticDefect' })
@Index(['cosmeticDefect', 'cosmeticArea'])
export class CosmeticAreaCosmeticDefect extends CommonEntity {
	@ManyToOne(() => CosmeticDefect, (cosmeticDefect) => cosmeticDefect.cosmeticAreaCosmeticDefects, { onDelete: 'CASCADE' })
	@Index()
	cosmeticDefect: CosmeticDefect;
	@Column({ type: 'uuid' })
	cosmeticDefectId: string;

	@ManyToOne(() => CosmeticArea, (cosmeticArea) => cosmeticArea.cosmeticAreaCosmeticDefects, { onDelete: 'CASCADE' })
	@Index()
	cosmeticArea: CosmeticArea;
	@Column({ type: 'uuid' })
	cosmeticAreaId: string;
}
