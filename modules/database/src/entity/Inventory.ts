import { type InventoryStatus } from '@pocitarna-nx-2023/config';
import type Decimal from 'decimal.js';
import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { DecimalTransformer } from '../utils/DecimalTransformer';
import { FileInventory } from './File';
import { InventoryCode } from './InventoryCode';
import { InventoryItem } from './InventoryItem';
import { User } from './User';

@Entity({ name: 'inventory' })
export class Inventory extends CommonEntity {
	@ManyToOne(() => User, (user) => user.createdInventories, { onDelete: 'SET NULL' })
	@Index()
	createdBy: User | null;
	@Column({ type: 'uuid', nullable: true })
	createdById: string | null;

	@Column({ type: 'varchar', default: '' })
	name: string;

	@Column({ type: 'timestamptz', default: null })
	@Index()
	finishedAt: Date | null;

	@Column({ type: 'varchar', default: 'OPEN' })
	@Index()
	status: InventoryStatus;

	@Column({ type: 'decimal', precision: 14, scale: 4, default: 0.0, transformer: new DecimalTransformer() })
	untestedPrice: Decimal;

	@Column({ type: 'decimal', precision: 14, scale: 4, default: 0.0, transformer: new DecimalTransformer() })
	forSalePrice: Decimal;

	@Column({ type: 'decimal', precision: 14, scale: 4, default: 0.0, transformer: new DecimalTransformer() })
	servicePrice: Decimal;

	@Column({ type: 'decimal', precision: 14, scale: 4, default: 0.0, transformer: new DecimalTransformer() })
	deadPrice: Decimal;

	@ManyToOne(() => InventoryCode, (code) => code.inventory, { onDelete: 'CASCADE' })
	@Index()
	code: InventoryCode;
	@Column({ type: 'uuid' })
	codeId: string;

	@ManyToOne(() => User, (user) => user.finishedInventories, { onDelete: 'SET NULL' })
	@Index()
	finishedBy: User | null;
	@Column({ type: 'uuid', nullable: true })
	finishedById: string | null;

	@OneToMany(() => InventoryItem, (inventoryItem) => inventoryItem.inventory)
	inventoryItems: InventoryItem[];

	@OneToMany(() => FileInventory, (fileInventory) => fileInventory.inventory)
	files: FileInventory[];
}
