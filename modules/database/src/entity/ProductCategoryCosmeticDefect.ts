import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { CosmeticDefect } from './CosmeticDefect';
import { ProductCategory } from './ProductCategory';

@Entity({ name: 'productCategoryCosmeticDefect' })
@Index(['cosmeticDefect', 'productCategory'])
export class ProductCategoryCosmeticDefect extends CommonEntity {
	@ManyToOne(() => CosmeticDefect, (cosmeticDefect) => cosmeticDefect.productCategoryCosmeticDefects, { onDelete: 'CASCADE' })
	@Index()
	cosmeticDefect: CosmeticDefect;
	@Column({ type: 'uuid' })
	cosmeticDefectId: string;

	@ManyToOne(() => ProductCategory, (productCategory) => productCategory.productCategoryCosmeticDefects, { onDelete: 'CASCADE' })
	@Index()
	productCategory: ProductCategory;
	@Column({ type: 'uuid' })
	productCategoryId: string;
}
