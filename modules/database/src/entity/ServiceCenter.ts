import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { Address } from './Address';
import { Contact } from './Contact';
import { ServiceCase } from './ServiceCase';

@Entity({ name: 'serviceCenter' })
export class ServiceCenter extends CommonEntity {
	@Column({ type: 'varchar' })
	name: string;

	@Column({ type: 'text', default: '' })
	note: string;

	@ManyToOne(() => Address, (address) => address.serviceCenter, { cascade: true, onDelete: 'SET NULL' })
	@Index()
	address: Address;
	@Column({ type: 'uuid' })
	addressId: string;

	@ManyToOne(() => Contact, (contact) => contact.serviceCenter, { cascade: true, onDelete: 'SET NULL' })
	@Index()
	contact: Contact;
	@Column({ type: 'uuid' })
	contactId: string;

	@OneToMany(() => ServiceCase, (serviceCase) => serviceCase.serviceCenter)
	serviceCases: ServiceCase[];
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'serviceCenterHistory', synchronize: false })
export class ServiceCenterHistory extends ServiceCenter {}
