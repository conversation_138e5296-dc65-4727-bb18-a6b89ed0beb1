import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { AttributeValue, ProductEnvelope } from '.';

@Entity({ name: 'productEnvelopeAttributeValue' })
@Index(['attributeValue', 'productEnvelope'])
export class ProductEnvelopeAttributeValue extends CommonEntity {
	@ManyToOne(() => AttributeValue, (attributeValue) => attributeValue.productEnvelopes, { onDelete: 'CASCADE' })
	@Index()
	attributeValue: AttributeValue;
	@Column({ type: 'uuid' })
	attributeValueId: string;

	@ManyToOne(() => ProductEnvelope, (productEnvelope) => productEnvelope.attributeValues, { onDelete: 'CASCADE' })
	@Index()
	productEnvelope: ProductEnvelope;
	@Column({ type: 'uuid' })
	productEnvelopeId: string;
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'productEnvelopeAttributeValueHistory', synchronize: false })
export class ProductEnvelopeAttributeValueHistory extends ProductEnvelopeAttributeValue {}
