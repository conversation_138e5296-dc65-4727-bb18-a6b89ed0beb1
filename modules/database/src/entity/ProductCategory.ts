import { Column, Entity, Index, JoinTable, ManyToMany, ManyToOne, OneToMany, Tree, TreeChildren, TreeParent } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { CosmeticArea } from './CosmeticArea';
import { DefectType } from './DefectType';
import { InventoryItem } from './InventoryItem';
import { Product } from './Product';
import { ProductCategoryAttribute } from './ProductCategoryAttribute';
import { ProductCategoryCosmeticDefect } from './ProductCategoryCosmeticDefect';
import { ProductCategoryGrade } from './ProductCategoryGrade';
import { ProductEnvelope } from './ProductEnvelope';
import { RecyclingFee } from './RecyclingFee';
import { ShoptetCategory } from './ShoptetCategory';

@Entity({ name: 'productCategory' })
@Tree('closure-table')
export class ProductCategory extends CommonEntity {
	@Column({ type: 'varchar' })
	@Index()
	name: string;

	@Column({ type: 'float4', default: 0.0 })
	margin: number;

	@Column({ type: 'varchar', default: '' })
	@Index()
	codePrefix: string;

	@Column({ type: 'int2', default: 1 })
	minimumTestPhotos: number;

	@TreeChildren()
	children: ProductCategory[];

	@TreeParent()
	parent: ProductCategory;

	@OneToMany(() => ProductEnvelope, (productEnvelope) => productEnvelope.productCategory)
	productEnvelopes: ProductEnvelope[];

	@OneToMany(() => InventoryItem, (inventoryItem) => inventoryItem.productCategory)
	inventoryItems: InventoryItem[];

	@OneToMany(() => ProductCategoryAttribute, (productCategoryAttribute) => productCategoryAttribute.productCategory)
	categoryAttributes: ProductCategoryAttribute[];

	@OneToMany(() => Product, (product) => product.productCategory)
	products: Product[];

	@OneToMany(() => CosmeticArea, (cosmeticArea) => cosmeticArea.productCategory)
	cosmeticAreas: CosmeticArea[];

	@OneToMany(() => ProductCategoryGrade, (categoryGrading) => categoryGrading.productCategory)
	grades: ProductCategoryGrade[];

	@ManyToOne(() => RecyclingFee, (recyclingFee) => recyclingFee.productCategories, { eager: true })
	@Index()
	recyclingFee: RecyclingFee | null;
	@Column({ type: 'uuid', nullable: true })
	recyclingFeeId: string | null;

	@ManyToOne(() => ShoptetCategory, (shoptetCategory) => shoptetCategory.productCategories, { eager: true })
	@Index()
	shoptetCategory: ShoptetCategory | null;
	@Column({ type: 'uuid', nullable: true })
	shoptetCategoryId: string | null;

	@ManyToMany(() => DefectType, (defectType) => defectType.productCategories)
	@JoinTable()
	defectTypes: DefectType[];

	@OneToMany(() => ProductCategoryCosmeticDefect, (productCategoryCosmeticDefect) => productCategoryCosmeticDefect.productCategory)
	productCategoryCosmeticDefects: ProductCategoryCosmeticDefect[];
}
