import { Column, Entity, Generated, Index, OneToMany } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { Inventory } from './Inventory';

@Entity({ name: 'inventoryCode' })
export class InventoryCode extends CommonEntity {
	@Column({ type: 'int', unique: true })
	@Generated('increment')
	@Index()
	code: number;

	@OneToMany(() => Inventory, (inventory) => inventory.code)
	inventory: Inventory;
}
