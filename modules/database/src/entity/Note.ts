import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { CustomerClaim } from './CustomerClaim';
import { ServiceCase } from './ServiceCase';
import { User } from './User';
import { WarrantyClaim } from './WarrantyClaim';

@Entity({ name: 'note' })
export class Note extends CommonEntity {
	@Column({ type: 'text', default: '' })
	content: string;

	@ManyToOne(() => User, (user) => user.createdNotes, { onDelete: 'SET NULL' })
	@Index()
	createdBy: User | null;
	@Column({ type: 'uuid' })
	createdById: string | null;

	@ManyToOne(() => WarrantyClaim, (warrantyClaim) => warrantyClaim.notes, { nullable: true, onDelete: 'SET NULL' })
	@Index()
	warrantyClaim: WarrantyClaim | null;
	@Column({ type: 'uuid', nullable: true })
	warrantyClaimId: string | null;

	@ManyToOne(() => ServiceCase, (serviceCase) => serviceCase.notes, { nullable: true, onDelete: 'SET NULL' })
	@Index()
	serviceCase: ServiceCase | null;
	@Column({ type: 'uuid', nullable: true })
	serviceCaseId: string | null;

	@ManyToOne(() => CustomerClaim, (customerClaim) => customerClaim.notes, { nullable: true, onDelete: 'CASCADE' })
	@Index()
	customerClaim: CustomerClaim | null;
	@Column({ type: 'uuid', nullable: true })
	customerClaimId: string | null;
}
