import { triggerEmail } from '@pocitarna-nx-2023/aws';
import { EMAIL_STOCK_REPORT_RECIPIENTS, EMPTY_VALUE, MATEJ_EMAIL, PETR_EMAIL, ProductTypeMessage } from '@pocitarna-nx-2023/config';
import {
	filterToMatchHeader,
	filterUndefined,
	formatAttributeValue,
	formatBatchCode,
	formatEnvelopeCode,
	formatPrice,
	formatProductCode,
	formatSheetForEmail,
	getProductStatusesAbove,
	stripCode,
	uniques,
	uniquesBy,
	withVat,
} from '@pocitarna-nx-2023/utils';
import { Decimal } from 'decimal.js';
import * as XLSX from 'xlsx';
import { AttributeController, AttributeValueController, BatchController, FileController, ProductController } from '../controller';
import { type AttributeValue, type Batch, type InventoryItem, type Product, type ProductCategory } from '../entity';
import { listAll } from './list';

// TODO: Change namse after deleting attribute values
const GRADE_COLUMN_NAME = 'Stav_NEW';
const PRODUCT_TYPE_COLUMN_NAME = 'Typ produktu_NEW';
const PRODUCT_GRADE_DESCRIPTION_COLUMN_NAME = 'Stav - poznámka_NEW';
const GRADE_COLUMNS = [GRADE_COLUMN_NAME, PRODUCT_TYPE_COLUMN_NAME, PRODUCT_GRADE_DESCRIPTION_COLUMN_NAME];

export const generateStockReport = async (productIds: Product['id'][]) => {
	const [products] = await new ProductController().listWithCosmeticDefects(listAll({ filter: { id: { eq: productIds } } }));
	const envelopes = uniquesBy(filterUndefined(products.map(({ productEnvelope }) => productEnvelope)), 'id');
	const batchIds = filterUndefined(uniques(products.map((product) => product.batchId)));
	const [batches] = await new BatchController().list(listAll({ filter: { id: { eq: batchIds } } }));
	const categories = uniquesBy(filterUndefined(products.map(({ productCategory }) => productCategory)), 'id');
	const totalBuyPrice = products
		.map(({ productPrice }) => productPrice.find((price) => price.type === 'BUY'))
		.reduce((acc, price) => acc.plus(price?.value ?? 0), new Decimal(0));
	const [attributeValues] = await new AttributeValueController().listByProduct(
		productIds,
		listAll({ filter: { 'productAttributeValue.type': { eq: 'resolved' } } }),
	);

	const workbook = XLSX.utils.book_new();
	let emailBody = '';
	for (const category of categories) {
		const productsInCategory = products.filter((product) => product.productCategoryId === category.id);
		const categoryEnvelopeIds = uniques(filterUndefined(productsInCategory.map(({ productEnvelopeId }) => productEnvelopeId)));
		const categoryBuyPrice = productsInCategory
			.map(({ productPrice }) => productPrice.find((price) => price.type === 'BUY'))
			.reduce((acc, price) => acc.plus(price?.value ?? 0), new Decimal(0));
		emailBody += `<h3>${category.name}</h3>`;
		emailBody += `<p>Produktů v kategorii: ${categoryEnvelopeIds.length}, Počet kusů: ${productsInCategory.length}, Nákupní cena celkem: ${formatPrice(categoryBuyPrice.toNumber())} bez DPH</p>`;
		const [notSortedAttributes] = await new AttributeController().listByCategory(
			category.id,
			listAll({ filter: { 'categoryAttributes.type': { eq: ['export', 'report'] } } }),
		);
		// FIXME - should have been sorted already
		const attributes = notSortedAttributes.toSorted((a, b) => {
			const aSequence = a.categoryAttributes[0].sequence;
			const bSequence = b.categoryAttributes[0].sequence;
			return aSequence - bSequence;
		});
		const attributeNames = attributes.map((attribute) => attribute.displayName);
		const reportAttributeNames = attributes
			.filter((attribute) => attribute.categoryAttributes.some((categoryAttribute) => categoryAttribute.type === 'report'))
			.map((attribute) => attribute.displayName);

		const data = productsInCategory
			.map((product) => {
				const { gradeData } = handleGradeRelatedData(product);
				const batch = batches.find((batch) => batch.id === product.batchId);
				if (!batch) throw new Error('Could not find batch');

				const envelope = product.productEnvelope;
				if (!envelope) throw new Error('Could not find envelope');

				const productAttributeValues = attributeValues.filter((attributeValue) =>
					attributeValue.products.map((productAttributeValue) => productAttributeValue.product.id).includes(product.id),
				);

				const buyPrice = product.productPrice.find((price) => price.productId === product.id && price.type === 'BUY');

				return {
					Várka: formatBatchCode(batch.code),
					'Karta produktu': formatEnvelopeCode(category.codePrefix)(envelope.code),
					PCN: formatProductCode(product.code),
					'Prodejní cena': formatPrice(withVat(envelope.salePrice)),
					'Nákupní cena': formatPrice(withVat(buyPrice?.value ? buyPrice.value : 0)),
					SN: product.sn
						.split('|')
						.filter((sn) => sn !== '')
						.join(', '),
					...productAttributeValues.reduce<Record<string, string>>((acc, attributeValue) => {
						acc[attributeValue.attribute.displayName] = formatAttributeValue(attributeValue);
						return acc;
					}, {}),
					...gradeData,
				};
			})
			.toSorted((a, b) => {
				const aBatchCode = stripCode(a['Várka']);
				const bBatchCode = stripCode(b['Várka']);
				if (aBatchCode < bBatchCode) return -1;
				if (aBatchCode > bBatchCode) return 1;

				const aEnvelopeCode = stripCode(a['Karta produktu']);
				const bEnvelopeCode = stripCode(b['Karta produktu']);
				if (aEnvelopeCode < bEnvelopeCode) return -1;
				if (aEnvelopeCode > bEnvelopeCode) return 1;

				const aProductCode = stripCode(a['PCN']);
				const bProductCode = stripCode(b['PCN']);
				if (aProductCode < bProductCode) return -1;
				if (aProductCode > bProductCode) return 1;

				return a['SN'].localeCompare(b['SN']);
			});

		const worksheet = XLSX.utils.json_to_sheet(
			filterToMatchHeader(data, [
				'Várka',
				'Karta produktu',
				'PCN',
				'Prodejní cena',
				'Nákupní cena',
				'SN',
				...attributeNames,
				...GRADE_COLUMNS,
			]),
		);
		const emailSheet = XLSX.utils.json_to_sheet(
			filterToMatchHeader(data, ['Várka', 'Karta produktu', 'PCN', 'Prodejní cena', 'SN', ...reportAttributeNames, ...GRADE_COLUMNS]),
		);
		emailBody += formatSheetForEmail(emailSheet);

		XLSX.utils.book_append_sheet(workbook, worksheet, category.name);
	}

	const now = new Date();
	const filename = `naskladneni-${now.toISOString()}.xlsx`;
	const rawFile = XLSX.writeXLSX(workbook, {
		type: 'buffer',
		bookType: 'xlsx',
		compression: true,
		cellStyles: true,
	}) as Buffer;
	const file = await new FileController().saveFile(rawFile, filename);
	const fileUrl = await new FileController().getFileDownloadLink(file.id);
	await triggerEmail({
		recipients: EMAIL_STOCK_REPORT_RECIPIENTS,
		subject: `Produkty na web`,
		message: `<p>Produktů celkem: ${envelopes.length}, Počet kusů: ${products.length}, Nákupní cena celkem: ${formatPrice(totalBuyPrice.toNumber())} bez DPH</p>${emailBody}`,
		attachments: [{ filename, path: fileUrl }],
	});
};

export const generateBatchReport = async (batch: Batch) => {
	const [productsInBatch] = await new ProductController().listWithCosmeticDefects(listAll({ filter: { batchId: { eq: batch.id } } }));

	const stockedProducts = productsInBatch.filter((p) => p.status === 'STOCK' || getProductStatusesAbove('STOCK').includes(p.status));
	const warrantyClaimProducts = productsInBatch.filter((p) => p.status === 'WARRANTY_CLAIM');
	const serviceProducts = productsInBatch.filter((p) => p.status === 'SERVICE');
	const missingProducts = productsInBatch.filter((p) => p.status === 'MISSING');

	const categories = uniquesBy(filterUndefined(stockedProducts.map(({ productCategory }) => productCategory)), 'id');

	const statistics = {
		totalProducts: productsInBatch.length,
		stockedCount: stockedProducts.length,
		warrantyClaimCount: warrantyClaimProducts.length,
		serviceCount: serviceProducts.length,
		missingCount: missingProducts.length,
	};

	const stateBreakdown = await getProductStateBreakdown(productsInBatch);

	const emailBody = await generateBatchReportEmailBody(
		batch,
		statistics,
		stateBreakdown,
		{
			stockedProducts,
			warrantyClaimProducts,
			serviceProducts,
			missingProducts,
		},
		categories,
	);

	const workbook = XLSX.utils.book_new();

	for (const category of categories) {
		const productsInCategory = stockedProducts.filter((product) => product.productCategoryId === category.id);
		if (productsInCategory.length === 0) continue;

		const worksheet = await generateProductWorksheet(productsInCategory, category);
		XLSX.utils.book_append_sheet(workbook, worksheet, category.name);
	}

	const now = new Date();
	const filename = `batch-report-${batch.code.code}-${now.toISOString()}.xlsx`;
	const rawFile = XLSX.writeXLSX(workbook, {
		type: 'buffer',
		bookType: 'xlsx',
		compression: true,
		cellStyles: true,
	}) as Buffer;

	const file = await new FileController().saveFile(rawFile, filename);
	const fileUrl = await new FileController().getFileDownloadLink(file.id);

	const recipient = batch.boughtBy?.email ?? batch.createdBy?.email;
	const ccRecipients = [MATEJ_EMAIL, PETR_EMAIL];

	await triggerEmail({
		recipients: [recipient ?? MATEJ_EMAIL],
		cc: recipient ? ccRecipients : [PETR_EMAIL],
		subject: `Report várky ${formatBatchCode(batch.code)}`,
		message: emailBody,
		attachments: [{ filename, path: fileUrl }],
	});

	await new BatchController().update(batch.id, { reportSentAt: new Date() });
};

const getProductStateBreakdown = async (products: Product[]) => {
	const breakdown: Record<string, number> = {};

	return products
		.filter((product) => product.grade != null)
		.reduce((acc, product) => {
			const gradeName = product.grade?.name as string;
			if (!acc[gradeName]) acc[gradeName] = 0;
			acc[gradeName]++;
			return acc;
		}, breakdown);
};

const generateBatchReportEmailBody = async (
	batch: Batch,
	statistics: { totalProducts: number; stockedCount: number; warrantyClaimCount: number; serviceCount: number; missingCount: number },
	stateBreakdown: Record<string, number>,
	productGroups: { stockedProducts: Product[]; warrantyClaimProducts: Product[]; serviceProducts: Product[]; missingProducts: Product[] },
	categories: ProductCategory[],
) => {
	let body = `<h1>Report várky ${formatBatchCode(batch.code)}</h1>`;

	// Statistics
	body += `<h2>Statistika</h2>`;
	body += `<p>Celkový počet produktů: ${statistics.totalProducts}</p>`;
	body += `<p>Naskladněno: ${statistics.stockedCount}</p>`;
	body += `<p>V reklamaci: ${statistics.warrantyClaimCount}</p>`;
	body += `<p>V servisu: ${statistics.serviceCount}</p>`;
	body += `<p>Chybějící: ${statistics.missingCount}</p>`;

	// State breakdown
	body += `<h2>Rozdělení podle stavu</h2>`;
	body += `<ul>`;
	for (const [state, count] of Object.entries(stateBreakdown)) {
		body += `<li>${count}x ${state}</li>`;
	}
	body += `</ul>`;

	// Add product tables for each category and status
	if (productGroups.stockedProducts.length > 0) {
		body += await generateProductTablesForStatus(productGroups.stockedProducts, categories, 'Naskladněné produkty');
	}

	if (productGroups.warrantyClaimProducts.length > 0) {
		body += await generateProductTablesForStatus(productGroups.warrantyClaimProducts, categories, 'Produkty v reklamaci');
	}

	if (productGroups.serviceProducts.length > 0) {
		body += await generateProductTablesForStatus(productGroups.serviceProducts, categories, 'Produkty v servisu');
	}

	if (productGroups.missingProducts.length > 0) {
		body += await generateProductTablesForStatus(productGroups.missingProducts, categories, 'Chybějící produkty');
	}

	return body;
};

const generateProductTablesForStatus = async (products: Product[], categories: ProductCategory[], title: string) => {
	let html = `<h2>${title}</h2>`;

	for (const category of categories) {
		const productsInCategory = products.filter((product) => product.productCategoryId === category.id);
		if (productsInCategory.length === 0) continue;

		html += `<h3>${category.name}</h3>`;

		// Get attributes for this category
		const [attributes] = await new AttributeController().listByCategory(
			category.id,
			listAll({ filter: { 'categoryAttributes.type': { eq: 'report' } } }),
		);
		const attributeNames = attributes.map((attribute) => attribute.displayName);

		const productIds = productsInCategory.map((product) => product.id);
		const [allProductsAttributeValues] = await new AttributeValueController().listByProduct(productIds, listAll());

		const attributeValuesByProductId = allProductsAttributeValues.reduce<Record<string, AttributeValue[]>>((acc, attributeValue) => {
			attributeValue.products.forEach((productAttributeValue) => {
				const productId = productAttributeValue.product.id;
				if (!acc[productId]) {
					acc[productId] = [];
				}
				acc[productId].push(attributeValue);
			});
			return acc;
		}, {});

		const data = productsInCategory.map((product) => {
			const { gradeData } = handleGradeRelatedData(product);
			const attributeValues = attributeValuesByProductId[product.id] || [];

			const envelope = product.productEnvelope;
			if (!envelope) throw new Error('Could not find envelope');

			const buyPrice = product.productPrice.find((price) => price.productId === product.id && price.type === 'BUY');

			return {
				PCN: formatProductCode(product.code),
				'Prodejní cena': formatPrice(withVat(envelope.salePrice)),
				'Nákupní cena': formatPrice(withVat(buyPrice?.value ? buyPrice.value : 0)),
				SN: product.sn
					.split('|')
					.filter((sn) => sn !== '')
					.join(', '),
				...attributeValues.reduce<Record<string, string>>((acc, attributeValue) => {
					acc[attributeValue.attribute.displayName] = formatAttributeValue(attributeValue);
					return acc;
				}, {}),
				...gradeData,
			};
		});

		// Create HTML table
		const worksheet = XLSX.utils.json_to_sheet(
			filterToMatchHeader(data, ['PCN', 'Prodejní cena', 'Nákupní cena', 'SN', ...attributeNames, ...GRADE_COLUMNS]),
		);

		html += formatSheetForEmail(worksheet);
	}

	return html;
};

const generateProductWorksheet = async (products: Product[], category: ProductCategory) => {
	const [attributes] = await new AttributeController().listByCategory(
		category.id,
		listAll({ filter: { 'categoryAttributes.type': { eq: ['export', 'report'] } } }),
	);

	const sortedAttributes = attributes.toSorted((a, b) => {
		const aSequence = a.categoryAttributes[0].sequence;
		const bSequence = b.categoryAttributes[0].sequence;
		return aSequence - bSequence;
	});

	const attributeNames = sortedAttributes.map((attribute) => attribute.displayName);

	// Get attribute values for all products
	const productIds = products.map((product) => product.id);
	const [attributeValues] = await new AttributeValueController().listByProduct(
		productIds,
		listAll({ filter: { 'productAttributeValue.type': { eq: 'resolved' } } }),
	);

	// Generate data for the worksheet
	const data = products.map((product) => {
		const { gradeData } = handleGradeRelatedData(product);
		const envelope = product.productEnvelope;
		if (!envelope) throw new Error('Could not find envelope');

		const buyPrice = product.productPrice.find((price) => price.productId === product.id && price.type === 'BUY');

		const productAttributeValues = attributeValues.filter((attributeValue) =>
			attributeValue.products.map((productAttributeValue) => productAttributeValue.product.id).includes(product.id),
		);

		return {
			PCN: formatProductCode(product.code),
			'Prodejní cena': formatPrice(withVat(envelope.salePrice)),
			'Nákupní cena': formatPrice(withVat(buyPrice?.value ? buyPrice.value : 0)),
			SN: product.sn
				.split('|')
				.filter((sn) => sn !== '')
				.join(', '),
			...productAttributeValues.reduce<Record<string, string>>((acc, attributeValue) => {
				acc[attributeValue.attribute.displayName] = formatAttributeValue(attributeValue);
				return acc;
			}, {}),
			...gradeData,
		};
	});

	// Create worksheet
	return XLSX.utils.json_to_sheet(
		filterToMatchHeader(data, ['PCN', 'Prodejní cena', 'Nákupní cena', 'SN', ...attributeNames, ...GRADE_COLUMNS]),
	);
};

export const generateInventoryReport = async (inventoryItems: InventoryItem[]) => {
	const productIds = uniques(filterUndefined(inventoryItems.map((item) => item.productId)));
	const batchIds = uniques(filterUndefined(inventoryItems.map((item) => item.batchId)));
	const [batches] = await new BatchController().list(listAll({ filter: { id: { eq: batchIds } } }));
	const [attributeValues] = await new AttributeValueController().listByProduct(
		productIds,
		listAll({
			filter: { 'productAttributeValue.type': { eq: 'resolved' }, 'attribute.displayName': { eq: ['Model', 'Značka (výrobce)'] } },
		}),
	);

	const data = inventoryItems.map((inventoryItem) => {
		const batch = batches.find((batch) => batch.id === inventoryItem.batchId);
		const productAttributeValues = attributeValues.filter((attributeValue) =>
			attributeValue.products.map((productAttributeValue) => productAttributeValue.product.id).includes(inventoryItem.productId),
		);

		return {
			Várka: batch ? formatBatchCode(batch.code) : EMPTY_VALUE,
			PCN: inventoryItem.code,
			'Nákupní cena bez DPH': inventoryItem.buyPrice.toNumber(),
			'Nákupní cena s DPH': withVat(inventoryItem.buyPrice),
			...productAttributeValues.reduce<Record<string, string>>((acc, attributeValue) => {
				acc[attributeValue.attribute.displayName] = formatAttributeValue(attributeValue);
				return acc;
			}, {}),
		};
	});

	const workbook = XLSX.utils.book_new();
	const worksheet = XLSX.utils.json_to_sheet(
		filterToMatchHeader(data, ['Várka', 'PCN', 'Značka (výrobce)', 'Model', 'Nákupní cena bez DPH', 'Nákupní cena s DPH']),
	);
	XLSX.utils.book_append_sheet(workbook, worksheet);
	return XLSX.writeXLSX(workbook, {
		type: 'buffer',
		bookType: 'xlsx',
		compression: true,
		cellStyles: true,
	}) as Buffer;
};

const handleGradeRelatedData = (product: Product) => {
	const gradeData = {
		[GRADE_COLUMN_NAME]: product.grade?.name ?? EMPTY_VALUE,
		[PRODUCT_TYPE_COLUMN_NAME]: ProductTypeMessage[product.type],
		[PRODUCT_GRADE_DESCRIPTION_COLUMN_NAME]: product.productCosmeticDefects
			.map((defect) => defect.cosmeticDefect.name)
			.filter((name) => name !== '')
			.join(', '),
	};

	return { gradeData };
};
