import { ENTITY_MANAGER_LABEL, SYSTEM_USER_AUTH_ID } from '@pocitarna-nx-2023/config';
import type { <PERSON><PERSON> } from 'aws-lambda';
import * as httpContext from 'express-http-context';
import { ActionController } from '../controller';
import { Database } from '../database';

export const withAction =
	<T, U>(endpoint: string, handler: Handler<T, U>): Handler<T, U> =>
	// @ts-expect-error Invalid return type
	async (...args: Parameters<Handler<T, U>>): Promise<U> | void =>
		Database.transaction(async (entityManager) => {
			httpContext.asyncLocalStorage.enterWith(new Map());
			httpContext.set(ENTITY_MANAGER_LABEL, entityManager);
			await new ActionController().create({ endpoint, method: 'POST', authentication: { id: SYSTEM_USER_AUTH_ID } });

			return handler(...args);
		});
