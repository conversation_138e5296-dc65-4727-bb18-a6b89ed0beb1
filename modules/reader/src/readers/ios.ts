import { AttributeController } from '@pocitarna-nx-2023/database';
import { filterUndefined, normalizeAttributeValue } from '@pocitarna-nx-2023/utils';
import { path } from 'rambdax';
import { BaseReader } from '../utils/BaseReader';

type ResultType = {
	battery:
		| {
				IORegistry: {
					LPEMData: {
						MaxCapacity: number;
					};
					DesignCapacity: number;
					CycleCount: number;
				};
		  }
		| Record<string, never>;
	legacyBattery:
		| {
				IORegistry: {
					DesignCapacity: number;
					CycleCount: number;
					AppleRawMaxCapacity: number;
				};
		  }
		| Record<string, never>;
	device: {
		SerialNumber: string;
		InternationalMobileEquipmentIdentity: string;
		InternationalMobileEquipmentIdentity2?: string;
		ModelNumber: string;
		RegionInfo: string;
		HardwareModel: string;
		HardwarePlatform: string;
		ProductVersion: string;
	};
	disk: {
		TotalDiskCapacity: number;
	};
	regulatoryModelNumber: string;
};

export class iOS extends BaseReader<ResultType> {
	public override fileName = 'ios.json';
	public override mimeType = 'application/json' as const;

	protected override async parse(data: Uint8Array) {
		const result = JSON.parse(new TextDecoder().decode(data)) as ResultType;
		const names = await new AttributeController().getNames();

		const attributeValues: { attributeId: string; attributeValueId: string }[] = [];
		for (const { name, attributeId } of names) {
			if (name !== 'regulatoryModelNumber' && !name.includes('.')) continue;

			const value = path(name, result);
			if (value === undefined || value === null) continue;

			// @ts-expect-error - TypeScript doesn't know that value is not {}
			const normalizedValue = normalizeAttributeValue(value);

			if (typeof normalizedValue === 'number' && name.includes('TotalDiskCapacity')) {
				// Assuming the value is in bytes (metric)
				const attributeValue = await new AttributeController().getOrCreateValue({ attributeId }, normalizedValue / 10 ** 9);
				attributeValues.push(...attributeValue);
				continue;
			}

			if (typeof normalizedValue === 'number' && name.includes('MaxCapacity') && name.includes('battery')) {
				const attributeValue = await new AttributeController().getOrCreateValue(
					{ attributeId },
					Math.min(
						Math.round((result.battery.IORegistry.LPEMData.MaxCapacity / result.battery.IORegistry.DesignCapacity) * 100),
						100,
					),
				);
				attributeValues.push(...attributeValue);
				continue;
			}

			if (typeof normalizedValue === 'number' && name.includes('MaxCapacity') && name.includes('legacyBattery')) {
				const attributeValue = await new AttributeController().getOrCreateValue(
					{ attributeId },
					Math.min(
						Math.round(
							(result.legacyBattery.IORegistry.AppleRawMaxCapacity / result.legacyBattery.IORegistry.DesignCapacity) * 100,
						),
						100,
					),
				);
				attributeValues.push(...attributeValue);
				continue;
			}

			if (name.includes('OperatingSystem')) {
				const attributeValue = await new AttributeController().getOrCreateValue({ attributeId }, `iOS`);
				attributeValues.push(...attributeValue);
				continue;
			}

			const attributeValue = await new AttributeController().getOrCreateValue({ attributeId }, normalizedValue);
			attributeValues.push(...attributeValue);
		}

		return { attributeValues, raw: result };
	}

	protected override async hardCodedParse(raw: ResultType) {
		const sn = filterUndefined([
			raw.device.SerialNumber,
			raw.device.InternationalMobileEquipmentIdentity,
			raw.device.InternationalMobileEquipmentIdentity2,
		]).join('|');
		return { sn: sn.toUpperCase() };
	}
}
