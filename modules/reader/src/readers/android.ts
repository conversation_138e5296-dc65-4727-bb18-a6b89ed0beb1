import { EMPTY_VALUE } from '@pocitarna-nx-2023/config';
import { AttributeController } from '@pocitarna-nx-2023/database';
import { capitalize, convert, filterUndefined, formatNumber, uniques } from '@pocitarna-nx-2023/utils';
import { BaseReader } from '../utils/BaseReader';

type ResultType = {
	battery: string;
	devicestoragemonitor: string;
	diskstats: string;
	display: string;
	getprop: string;
	imei: string;
	meminfo: string;
};

export class Android extends BaseReader<ResultType> {
	public override fileName = 'android.json';
	public override mimeType = 'application/json' as const;

	protected override async parse(data: Uint8Array) {
		const result = JSON.parse(new TextDecoder().decode(data)) as ResultType;
		const names = await new AttributeController().getNames();
		const values = {
			manufacturer: this.parseManufacturer(result.getprop),
			model: this.parseModel(result.getprop),
			pn: this.parsePN(result.getprop),
			modelNumber: EMPTY_VALUE,
			region: EMPTY_VALUE,
			system: 'Android',
			ram: this.parseRam(result.meminfo),
			cpu: this.parseCpu(result.getprop),
			capacity: this.parseCapacity(result.devicestoragemonitor, result.diskstats),
			version: this.parseVersion(result.getprop),
			resolution: this.parseResolution(result.display),
			size: this.parseSize(result.display),
			dualSim: this.parseDualSim(result.getprop),
			esim: this.parseEsim(result.getprop),
			bluetooth: this.parseBluetooth(result.getprop),
			batteryHealth: this.parseBatteryHealth(result.battery),
			batteryCycles: this.parseBatteryCycles(result.battery),
			...this.parseModem(result.getprop),
		};

		const attributeValues: { attributeId: string; attributeValueId: string }[] = [];
		for (const { name, attributeId } of names) {
			const value = values[name.slice(8) as keyof typeof values]; // slicing `android.` prefix
			if (value === undefined) continue;

			const attributeValue = await new AttributeController().getOrCreateValue({ attributeId }, value);
			attributeValues.push(...attributeValue);
		}

		return { attributeValues, raw: result };
	}

	protected override async hardCodedParse(raw: ResultType) {
		const sn = filterUndefined([this.parseSn(raw.getprop), ...this.parseImei(raw.imei)]).join('|');
		return { sn: sn.toUpperCase() };
	}

	parseManufacturer(data: string) {
		const match = data.match(/\[ro\.product\.manufacturer]: \[(.*?)]/);
		if (match && match.length > 0) {
			return capitalize(match[1]);
		}

		return EMPTY_VALUE;
	}

	parseModel(data: string) {
		const match = data.match(/\[ro\.product\.model]: \[(.*?)]/);
		if (match && match.length > 0) {
			return match[1];
		}

		return EMPTY_VALUE;
	}

	parsePN(data: string) {
		const match = data.match(/\[ril\.product_code]: \[(.*?)]/);
		if (match && match.length > 0) {
			return match[1];
		}

		return this.parseModel(data);
	}

	parseRam(data: string) {
		const match = data.match(/Total RAM: ([\d,]+)K/);
		if (match && match.length > 0) {
			const kB = parseInt(match[1].replace(/,/g, ''));
			return Math.ceil(convert(kB, 'kB', 'GB').amount);
		}

		return EMPTY_VALUE;
	}

	parseCpu(data: string) {
		const manufacturerMatches = data.match(/\[ro\.soc\.manufacturer]: \[(.*?)]/);
		const modelMatches = data.match(/\[ro\.soc\.model]: \[(.*?)]/);
		if (manufacturerMatches && manufacturerMatches.length > 0 && modelMatches && modelMatches.length > 0) {
			return `${manufacturerMatches[1]} ${modelMatches[1]}`;
		} else if (modelMatches && modelMatches.length > 0) {
			return modelMatches[1];
		}

		return EMPTY_VALUE;
	}

	parseCapacity(data: string, fallback: string) {
		const match = data.match(/mRomTotalBytes : (\d+)/);
		if (match && match.length > 0) {
			const bytes = parseInt(match[1]);
			return convert(bytes, 'B', 'GiB').amount;
		}

		const regex = /\/ (\d+)K total/g;
		let m;
		const result = [];
		while ((m = regex.exec(fallback)) !== null) {
			if (m.index === regex.lastIndex) {
				regex.lastIndex++;
			}

			result.push(m[1]);
		}
		const kB = uniques(result.map((match) => parseInt(match))).reduce((acc, value) => acc + value, 0);
		if (kB === 0) return EMPTY_VALUE;

		const GB = convert(kB, 'kB', 'GB').amount;
		return 2 ** Math.ceil(Math.log2(GB)); // Rounding to the nearest higher power of two
	}

	parseVersion(data: string) {
		const match = data.match(/\[ro\.system\.build\.version\.release]: \[(.*?)]/);
		if (match && match.length > 0) {
			return parseInt(match[1]);
		}

		return EMPTY_VALUE;
	}

	parseDisplay(data: string) {
		const regex =
			/DisplayMode{.*?width=(?<width>[\d.]+), height=(?<height>[\d.]+), xDpi=(?<xDpi>[\d.]+), yDpi=(?<yDpi>[\d.]+), refreshRate=(?<refreshDate>[\d.]+).*?}/g;
		let m;
		const result = [];
		while ((m = regex.exec(data)) !== null) {
			if (m.index === regex.lastIndex) {
				regex.lastIndex++;
			}

			result.push(m.groups as { width: string; height: string; xDpi: string; yDpi: string; refreshRate: string });
		}

		return result
			.map(({ width, height, xDpi, yDpi, refreshRate }) => ({
				width: Number(width),
				height: Number(height),
				xDpi: Number(xDpi),
				yDpi: Number(yDpi),
				refreshRate: Number(refreshRate),
			}))
			.toSorted((a, b) => {
				const aSize = a.width * a.height;
				const bSize = b.width * b.height;
				return bSize - aSize;
			});
	}

	parseResolution(data: string) {
		const display = this.parseDisplay(data).at(0);
		if (display) {
			return `${display.width} x ${display.height}`;
		}

		return EMPTY_VALUE;
	}

	parseSize(data: string) {
		const display = this.parseDisplay(data).at(0);
		if (display) {
			const size = Math.ceil(Math.sqrt((display.width / display.xDpi) ** 2 + (display.height / display.yDpi) ** 2) * 10) / 10;
			return formatNumber(size, 0, size >= 5 ? 1 : 0);
		}

		return EMPTY_VALUE;
	}

	parseSn(data: string) {
		const matches = data.match(/\[ro\.serialno]: \[(.*?)]/);
		if (matches && matches.length > 0) {
			return matches[1];
		}

		return '';
	}

	parseImei(data: string) {
		const regex = /\D(\d{15})\D/g;
		let m;
		const result = [];
		while ((m = regex.exec(data)) !== null) {
			if (m.index === regex.lastIndex) {
				regex.lastIndex++;
			}

			result.push(m[1]);
		}

		return uniques(result);
	}

	parseDualSim(data: string) {
		const match = data.match(/\[gsm\.sim\.state]: \[(.*?)]/);
		if (match && match.length > 0) {
			return match[1].split(',').length > 1;
		}

		return false;
	}

	parseBluetooth(data: string) {
		return data.includes('bluetooth');
	}

	parseBatteryHealth(data: string) {
		const match1 = data.match(/mSavedBatteryAsoc: (\d+)/);
		if (match1 && match1.length > 0) {
			return Number(match1[1]);
		}

		const match2 = data.match(/health: (\d+)/);
		if (match2 && match2.length > 0) {
			return 100 - Number(match2[1]);
		}

		return EMPTY_VALUE;
	}

	parseBatteryCycles(data: string) {
		const match1 = data.match(/mSavedBatteryUsage: (\d+)/);
		if (match1 && match1.length > 0) {
			return Math.floor(Number(match1[1]) / 100);
		}

		const match2 = data.match(/battery usage number: (\d+)/);
		if (match2 && match2.length > 0) {
			return Number(match2[1]);
		}

		return EMPTY_VALUE;
	}

	// https://github.com/aosp-mirror/platform_frameworks_base/blob/master/telephony/java/com/android/internal/telephony/RILConstants.java
	parseModem(data: string) {
		const match = data.match(/\[ro\.telephony\.default_network]: \[(.*?)]/);
		if (match && match.length > 0) {
			const value = Math.max(...match[1].split(',').map(Number));
			return {
				'3g': [0, 2, 3, 4, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33].includes(
					value,
				),
				'4g': [8, 9, 10, 11, 12, 15, 17, 19, 20, 22, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33].includes(value),
				'5g': [23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33].includes(value),
			};
		}

		return { '3g': false, '4g': false, '5g': false };
	}

	parseEsim(data: string) {
		return data.includes('.esim');
	}
}
