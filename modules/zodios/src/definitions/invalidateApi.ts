import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors } from '../entities';
import { apiResponse } from '../utils/wrapper';

export const invalidateApi = makeApi([
	{
		method: 'post',
		path: '/',
		alias: 'postInvalidate',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'aliases', type: 'Body', schema: z.array(z.string()) }],
		errors,
	},
	{
		method: 'post',
		path: '/resend-unread-notfications',
		alias: 'resendUnreadNotifications',
		response: apiResponse(z.boolean()),
		errors,
	},
]);
