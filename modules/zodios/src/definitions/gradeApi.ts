import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, grade, gradeCreate, gradeUpdate, listProps } from '../entities';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';

export const gradeApi = makeApi([
	{
		method: 'post',
		path: '/',
		alias: 'createGrade',
		response: apiResponse(grade),
		parameters: [{ name: 'grade', type: 'Body', schema: gradeCreate }],
		errors,
	},
	{
		method: 'get',
		path: '/',
		alias: 'getGrades',
		response: apiPagedResponse(grade),
		parameters: listProps,
		errors,
	},

	{
		method: 'get',
		path: '/ranked',
		alias: 'getRankedGrades',
		response: apiResponse(z.array(grade)),
		errors,
	},
	{
		method: 'get',
		path: '/:gradeId',
		alias: 'getGrade',
		response: apiResponse(grade),
		parameters: [{ name: 'gradeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/:gradeId',
		alias: 'updateGrade',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'gradeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'grade', type: 'Body', schema: gradeUpdate },
		],
		errors,
	},
	{
		method: 'patch',
		path: '/sequence',
		alias: 'updateGradeSequence',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'body', type: 'Body', schema: z.object({ gradeId: z.string().uuid(), sequence: z.number().min(0) }) }],
		errors,
	},
	{
		method: 'delete',
		path: '/:gradeId',
		alias: 'deleteGrade',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'gradeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
