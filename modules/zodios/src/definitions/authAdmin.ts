import { AUTH_TYPES, SCOPE_NAMES } from '@pocitarna-nx-2023/config';
import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { authentication, errors, listProps, role, scope, user, userCreate, userUpdate } from '../entities';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';

export const authAdmin = makeApi([
	{
		method: 'get',
		path: '/role',
		alias: 'getRoles',
		response: apiPagedResponse(role.and(z.object({ scopes: z.array(scope) }))),
		parameters: listProps,
		errors,
	},
	{
		method: 'post',
		path: '/role',
		alias: 'createRole',
		response: apiResponse(role),
		parameters: [
			{
				name: 'createRole',
				type: 'Body',
				schema: role.omit({ id: true }).and(z.object({ scopes: z.array(z.string().uuid()) })),
			},
		],
		errors,
	},
	{
		method: 'delete',
		path: '/role/:roleId',
		alias: 'deleteRole',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'roleId',
				type: 'Path',
				schema: z.string().uuid(),
			},
		],
		errors,
	},
	{
		method: 'put',
		path: '/role/:roleId',
		alias: 'updateRole',
		response: apiResponse(role.nullable()),
		parameters: [
			{
				name: 'roleId',
				type: 'Path',
				schema: z.string().uuid(),
			},
			{
				name: 'updateRole',
				type: 'Body',
				schema: role.omit({ id: true }).and(z.object({ scopes: z.array(z.string().uuid()) })),
			},
		],
	},
	{
		method: 'get',
		path: '/scope',
		alias: 'getScopes',
		response: apiPagedResponse(scope),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/user',
		alias: 'getUsers',
		response: apiPagedResponse(
			user.and(
				z.object({
					authentications: z.array(
						authentication.and(z.object({ role: role.and(z.object({ scopes: z.array(scope) })).nullable() })),
					),
				}),
			),
		),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/scope/:scopeName/user',
		alias: 'getUsersByScope',
		response: apiPagedResponse(user),
		parameters: [
			...listProps,
			{
				name: 'scopeName',
				type: 'Path',
				schema: z.enum(SCOPE_NAMES),
			},
		],
		errors,
	},
	{
		method: 'post',
		path: '/user',
		alias: 'createUserAdmin',
		response: apiResponse(user),
		parameters: [
			{
				name: 'createUser',
				type: 'Body',
				schema: userCreate,
			},
		],
		errors,
	},
	{
		method: 'put',
		path: '/user/:userId',
		alias: 'updateUserAdmin',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'userId',
				type: 'Path',
				schema: z.string().uuid(),
			},
			{
				name: 'updateUser',
				type: 'Body',
				schema: userUpdate,
			},
		],
		errors,
	},
	{
		method: 'delete',
		path: '/user/:userId',
		alias: 'deleteUserAdmin',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'userId',
				type: 'Path',
				schema: z.string().uuid(),
			},
		],
		errors,
	},
	{
		method: 'post',
		path: '/user/:userId/authentication',
		alias: 'createUserAuthentication',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'userId',
				type: 'Path',
				schema: z.string().uuid(),
			},
			{
				name: 'createUserAuthentication',
				type: 'Body',
				schema: z.object({ type: z.enum(AUTH_TYPES), role: z.string().uuid() }),
			},
		],
		errors,
	},
	{
		method: 'delete',
		path: '/user/:userId/authentication/:authenticationId',
		alias: 'deleteUserAuthentication',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'userId',
				type: 'Path',
				schema: z.string().uuid(),
			},
			{
				name: 'authenticationId',
				type: 'Path',
				schema: z.string().uuid(),
			},
		],
		errors,
	},
	{
		method: 'put',
		path: '/user/:userId/authentication/:authenticationId',
		alias: 'updateUserAuthentication',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'userId',
				type: 'Path',
				schema: z.string().uuid(),
			},
			{
				name: 'authenticationId',
				type: 'Path',
				schema: z.string().uuid(),
			},
			{
				name: 'updateUserAuthentication',
				type: 'Body',
				schema: z.object({ type: z.enum(AUTH_TYPES), role: z.string().uuid() }),
			},
		],
	},
]);
