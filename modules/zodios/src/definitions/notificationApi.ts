import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, listProps, notification, notificationUpdate } from '../entities';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';

export const notificationApi = makeApi([
	{
		method: 'get',
		path: '/',
		alias: 'getNotifications',
		response: apiPagedResponse(notification),
		parameters: listProps,
		errors,
	},
	{
		method: 'patch',
		path: '/:notificationId',
		alias: 'updateNotification',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'notificationId', type: 'Path', schema: z.string().uuid() },
			{ name: 'body', type: 'Body', schema: notificationUpdate },
		],
		errors,
	},
]);
