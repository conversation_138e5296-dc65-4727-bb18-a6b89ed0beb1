import { NOTIFICATION_DELIVERY_METHODS_NAMES } from '@pocitarna-nx-2023/config';
import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, listProps, userNotificationType } from '../entities';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';

export const userNotificationTypeApi = makeApi([
	{
		method: 'post',
		path: '/:userId/:notificationTypeId',
		alias: 'createUserNotificationType',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'userId', type: 'Path', schema: z.string().uuid() },
			{ name: 'notificationTypeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'body', type: 'Body', schema: z.object({ deliveryMethod: z.enum(NOTIFICATION_DELIVERY_METHODS_NAMES) }) },
		],
		errors,
	},
	{
		method: 'get',
		path: '/',
		alias: 'getUserNotificationTypes',
		response: apiPagedResponse(userNotificationType),
		parameters: listProps,
		errors,
	},
	{
		method: 'delete',
		path: '/:userId/:notificationTypeId',
		alias: 'deleteUserNotificationType',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'userId', type: 'Path', schema: z.string().uuid() },
			{ name: 'notificationTypeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'body', type: 'Body', schema: z.object({ deliveryMethod: z.enum(NOTIFICATION_DELIVERY_METHODS_NAMES) }) },
		],
		errors,
	},
]);
