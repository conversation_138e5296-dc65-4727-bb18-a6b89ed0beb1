import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { cosmeticDefect, cosmeticDefectCreate, errors, listProps } from '../entities';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';

export const cosmeticDefectApi = makeApi([
	{
		method: 'post',
		path: '/',
		alias: 'createCosmeticDefect',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'grade', type: 'Body', schema: cosmeticDefectCreate }],
		errors,
	},
	{
		method: 'get',
		path: '/',
		alias: 'getCosmeticDefects',
		response: apiPagedResponse(cosmeticDefect),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/:cosmeticDefectId',
		alias: 'getCosmeticDefect',
		response: apiResponse(cosmeticDefect),
		parameters: [{ name: 'cosmeticDefectId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/:cosmeticDefectId',
		alias: 'updateCosmeticDefect',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'cosmeticDefectId', type: 'Path', schema: z.string().uuid() },
			{ name: 'grade', type: 'Body', schema: cosmeticDefectCreate.partial() },
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:cosmeticDefectId',
		alias: 'deleteCosmeticDefect',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'cosmeticDefectId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
