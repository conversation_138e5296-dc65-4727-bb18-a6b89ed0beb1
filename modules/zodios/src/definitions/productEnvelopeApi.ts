import { makeApi } from '@zodios/core';
import { z } from 'zod';
import {
	attributeValue,
	attributeValueUpdate,
	errors,
	listProps,
	primitive,
	product,
	productEnvelope,
	productEnvelopeCreate,
	productEnvelopeUpdate,
	productPrice,
	zDate,
} from '../entities';
import { bulk } from '../entities/bulk';
import { code } from '../entities/code';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';
import { zodDecimal } from '../utils/zodDecimal';

export const productEnvelopeApi = makeApi([
	{
		method: 'post',
		path: '/',
		alias: 'createProductEnvelope',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'productEnvelope', type: 'Body', schema: productEnvelopeCreate }],
		errors,
	},
	{
		method: 'get',
		path: '/',
		alias: 'getAllProductEnvelopes',
		response: apiPagedResponse(productEnvelope),
		parameters: listProps,
		errors,
	},
	{
		method: 'post',
		path: '/:productEnvelopeId/shoptet',
		alias: 'triggerShoptetExport',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'productEnvelopeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/existing-match',
		alias: 'getProductEnvelopeByAttributeValues',
		parameters: [
			...listProps,
			{
				name: 'attributeValues',
				type: 'Query',
				schema: z.array(
					z.object({
						attributeId: z.string().uuid(),
						attributeValueId: z.string().optional(),
						value: primitive.optional(),
					}),
				),
			},
		],
		response: apiResponse(productEnvelope.nullable()),
		errors,
	},
	{
		method: 'get',
		path: '/ready-for-stock',
		alias: 'getProductEnvelopesReadyForStock',
		response: apiResponse(z.array(productEnvelope.extend({ products: product.array() }))),
		errors,
	},
	{
		method: 'get',
		path: '/:productEnvelopeId',
		alias: 'getProductEnvelope',
		response: apiResponse(productEnvelope),
		parameters: [{ name: 'productEnvelopeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/code/:productEnvelopeCode',
		alias: 'getProductEnvelopeByCode',
		response: apiResponse(productEnvelope),
		parameters: [
			{
				name: 'productEnvelopeCode',
				type: 'Path',
				required: true,
				schema: z.string(),
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/code/:productEnvelopeCodeId',
		alias: 'getProductEnvelopeCode',
		response: apiResponse(code),
		parameters: [{ name: 'productEnvelopeCodeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:productEnvelopeId/products',
		alias: 'getProductEnvelopeProducts',
		response: apiPagedResponse(product),
		parameters: [...listProps, { name: 'productEnvelopeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:productEnvelopeId/products/count',
		alias: 'getProductEnvelopeProductsCount',
		response: apiResponse(z.number()),
		parameters: [...listProps, { name: 'productEnvelopeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:productEnvelopeId/prices',
		alias: 'getProductEnvelopePrices',
		response: apiResponse(z.array(productPrice)),
		parameters: [{ name: 'productEnvelopeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/bulk/sale-price-by-margin',
		alias: 'bulkUpdateProductEnvelopeSalePriceByMargin',
		response: apiResponse(z.boolean()),
		parameters: bulk(z.object({ margin: z.number() })),
		errors,
	},
	{
		method: 'patch',
		path: '/:productEnvelopeId',
		alias: 'updateProductEnvelope',
		response: apiResponse(productEnvelope),
		parameters: [
			{ name: 'productEnvelopeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'productEnvelope', type: 'Body', schema: productEnvelopeUpdate },
		],
		errors,
	},
	{
		method: 'get',
		path: '/:productEnvelopeId/attribute-value',
		alias: 'getProductEnvelopeAttributeValues',
		response: apiPagedResponse(attributeValue),
		parameters: [...listProps, { name: 'productEnvelopeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'put',
		path: '/:productEnvelopeId/attribute-value',
		alias: 'updateProductEnvelopeAttributeValues',
		response: apiResponse(productEnvelope),
		parameters: [
			{ name: 'productEnvelopeId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: z.object({
					attributeValues: z.array(attributeValueUpdate),
				}),
			},
		],
		errors,
	},
	{
		method: 'post',
		path: '/stock',
		alias: 'stockProductEnvelopes',
		response: apiResponse(z.object({ products: z.array(z.string().uuid()), productEnvelopes: z.array(z.string().uuid()) })),
		parameters: [
			{
				name: 'body',
				type: 'Body',
				schema: z.array(
					z.object({
						envelopeId: z.string().uuid(),
						salePrice: zodDecimal(),
						standardPrice: zodDecimal().optional(),
					}),
				),
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/code/:productEnvelopeCode/products',
		alias: 'getProductEnvelopeCodeProducts',
		response: apiResponse(
			z.array(
				z.object({
					code: z.string(),
					pcn: z.string(),
					sn: z.string(),
					warehousePosition: z.string().nullable(),
					pickedAt: zDate.nullable(),
					pickedBy: z.string().nullable(),
					ecommerceOrder: z.string().nullable(),
				}),
			),
		),
		parameters: [
			...listProps,
			{
				name: 'productEnvelopeCode',
				type: 'Path',
				schema: z.string(),
			},
		],
		errors,
	},
]);
