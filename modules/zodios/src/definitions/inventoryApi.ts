import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, inventory, inventoryItem, inventoryItemScan, inventoryItemUpdateBody, inventoryUpdate, listProps } from '../entities';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';

export const inventoryApi = makeApi([
	{
		method: 'post',
		path: '/',
		alias: 'createInventory',
		response: apiResponse(inventory),
		parameters: [],
		errors,
	},
	{
		method: 'get',
		path: '/',
		alias: 'getInventories',
		response: apiPagedResponse(inventory),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/:inventoryId',
		alias: 'getInventory',
		response: apiResponse(inventory),
		parameters: [{ name: 'inventoryId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'post',
		path: '/:inventoryId/scan',
		alias: 'scanInventoryItem',
		response: apiResponse(z.array(inventoryItemScan)),
		parameters: [
			{ name: 'inventoryId', type: 'Path', schema: z.string().uuid() },
			{ name: 'warehousePositionId', type: 'Query', schema: z.string().uuid().optional() },
			{ name: 'body', type: 'Body', schema: z.array(z.string()) },
		],
		errors,
	},
	{
		method: 'get',
		path: '/:inventoryId/items',
		alias: 'getInventoryItems',
		response: apiPagedResponse(inventoryItem),
		parameters: [...listProps, { name: 'inventoryId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/:inventoryId',
		alias: 'updateInventory',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'inventoryId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: inventoryUpdate.partial(),
			},
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:inventoryId/:inventoryItemId',
		alias: 'handleInventoryItemAction',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'inventoryId', type: 'Path', schema: z.string().uuid() },
			{ name: 'inventoryItemId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: inventoryItemUpdateBody,
			},
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:inventoryId/files',
		alias: 'addFilesToInventory',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'inventoryId', type: 'Path', schema: z.string().uuid() },
			{ name: 'files', type: 'Body', schema: z.array(z.string()) },
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:inventoryId/files/:fileId',
		alias: 'deleteInventoryFile',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'inventoryId', type: 'Path', schema: z.string().uuid() },
			{ name: 'fileId', type: 'Path', schema: z.string().uuid() },
		],
		errors,
	},
	{
		method: 'get',
		path: '/:inventoryId/excel',
		alias: 'exportInventoryExcel',
		response: z.any(),
		parameters: [
			{ name: 'inventoryId', type: 'Path', schema: z.string().uuid() },
			{ name: 'variant', type: 'Query', schema: z.enum(['untestedItems', 'forSaleItems', 'serviceItems', 'deadItems']) },
		],
		errors,
	},
]);
