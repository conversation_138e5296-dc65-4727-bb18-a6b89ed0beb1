import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, noteCreation } from '../entities';
import { apiResponse } from '../utils/wrapper';

export const noteApi = makeApi([
	{
		method: 'post',
		path: '/',
		alias: 'createNote',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'body',
				type: 'Body',
				schema: noteCreation,
			},
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:noteId',
		alias: 'deleteNote',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'noteId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
