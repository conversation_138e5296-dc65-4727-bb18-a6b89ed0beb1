import { makeApi } from '@zodios/core';
import { customerClaimCosmeticDefect, errors, listProps } from '../entities';
import { apiPagedResponse } from '../utils/wrapper';

export const customerClaimCosmeticDefectApi = makeApi([
	{
		method: 'get',
		path: '/',
		alias: 'getCustomerClaimCosmeticDefects',
		response: apiPagedResponse(customerClaimCosmeticDefect),
		parameters: listProps,
		errors,
	},
]);
