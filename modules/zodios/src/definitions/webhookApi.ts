import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors } from '../entities';
import { apiResponse } from '../utils/wrapper';

export const webhookApi = makeApi([
	{
		method: 'post',
		path: '/shoptet-job-finished',
		alias: 'shoptetJobFinished',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'event',
				type: 'Body',
				schema: z.object({
					eshopId: z.number(),
					event: z.literal('job:finished'),
					eventCreated: z.string(), // Cannot coerce to date, because it then fails checksum
					eventInstance: z.string(),
				}),
			},
		],
		errors,
	},
]);
