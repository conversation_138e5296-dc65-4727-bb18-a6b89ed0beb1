import { makeApi } from '@zodios/core';
import { z } from 'zod';
import {
	batch,
	batchCreation,
	batchDefect,
	batchSnDuplicates,
	batchStatusTransitionBodySchema,
	batchUpdate,
	errors,
	finishSmallTestBody,
	listProps,
	product,
	productCode,
	productTest,
	productTestUpdate,
	serviceCase,
	smallTestBody,
	warrantyClaim,
} from '../entities';
import { bulk } from '../entities/bulk';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';
import { zodDecimal } from '../utils/zodDecimal';

export const batchApi = makeApi([
	{
		method: 'post',
		path: '/',
		alias: 'createBatch',
		response: apiResponse(batch),
		parameters: [{ name: 'batch', type: 'Body', schema: batchCreation }],
		errors,
	},
	{
		method: 'post',
		path: '/import',
		alias: 'importBatch',
		response: apiResponse(z.string().uuid()),
		parameters: [
			{
				name: 'batch',
				type: 'Body',
				schema: batchUpdate.pick({ files: true }).required(),
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/',
		alias: 'getBatches',
		response: apiPagedResponse(batch),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/:batchId',
		alias: 'getBatch',
		response: apiResponse(batch),
		parameters: [{ name: 'batchId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/code/:batchCode',
		alias: 'getBatchByCode',
		response: apiResponse(batch),
		parameters: [
			{
				name: 'batchCode',
				type: 'Path',
				required: true,
				schema: z.string(),
			},
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:batchId/files',
		alias: 'addFilesToBatch',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'batchId', type: 'Path', schema: z.string().uuid() },
			{ name: 'files', type: 'Body', schema: z.array(z.string()) },
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:batchId/small-test',
		alias: 'pairSmallTestProduct',
		response: apiResponse(z.string().uuid()),
		parameters: [
			{ name: 'batchId', type: 'Path', schema: z.string().uuid() },

			{
				name: 'smallTestBody',
				type: 'Body',
				schema: smallTestBody,
			},
		],
		errors,
	},
	{
		method: 'post',
		path: '/:batchId/small-test',
		alias: 'finishSmallTest',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'batchId', type: 'Path', schema: z.string().uuid() },

			{
				name: 'finishSmallTestBody',
				type: 'Body',
				schema: finishSmallTestBody,
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/:batchId/defect',
		alias: 'getBatchDefects',
		response: apiPagedResponse(batchDefect),
		parameters: [{ name: 'batchId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},

	{
		method: 'post',
		path: '/:batchId/products',
		alias: 'createBatchProducts',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'batchId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: z.object({
					productsCount: z.number().min(1),
					totalPrice: zodDecimal(),
					productCategoryId: z.union([z.string().uuid(), z.literal('')]),
				}),
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/:batchId/products',
		alias: 'getBatchProducts',
		response: apiPagedResponse(product),
		parameters: [{ name: 'batchId', type: 'Path', schema: z.string().uuid() }, ...listProps],
		errors,
	},
	{
		method: 'get',
		path: '/:batchId/sn-duplicates',
		alias: 'getBatchSnDuplicates',
		response: apiResponse(batchSnDuplicates),
		parameters: [{ name: 'batchId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:batchId/product-tests',
		alias: 'getBatchProductTests',
		response: apiResponse(z.array(productTest)),
		parameters: [{ name: 'batchId', type: 'Path', schema: z.string().uuid() }, ...listProps],
		errors,
	},
	{
		method: 'get',
		path: '/:batchId/price',
		alias: 'getBatchPrice',
		response: apiResponse(zodDecimal()),
		parameters: [{ name: 'batchId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:batchId/products/count',
		alias: 'getBatchProductsCount',
		response: apiResponse(z.object({ notTested: z.number(), notVerified: z.number(), tested: z.number(), total: z.number() })),
		parameters: [{ name: 'batchId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'delete',
		path: '/:batchId/files/:fileId',
		alias: 'deleteBatchFile',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'batchId', type: 'Path', schema: z.string().uuid() },
			{ name: 'fileId', type: 'Path', schema: z.string().uuid() },
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:batchId',
		alias: 'updateBatch',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'batchId', type: 'Path', schema: z.string().uuid() },
			{ name: 'body', type: 'Body', schema: batchUpdate },
		],
		errors,
	},
	{
		method: 'post',
		path: '/import/product',
		alias: 'importBatchProducts',
		response: apiResponse(z.array(z.string())),
		parameters: [
			{
				name: 'body',
				type: 'Body',
				schema: z.object({
					files: z.object({ id: z.string().uuid() }).array(),
					batchId: z.string().uuid(),
				}),
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/:batchId/products/code',
		alias: 'getBatchProductCodes',
		response: apiPagedResponse(productCode),
		parameters: [
			...listProps,
			{ name: 'batchId', type: 'Path', schema: z.string().uuid() },
			{ name: 'unassigned', type: 'Query', schema: z.boolean().default(false) },
		],
		errors,
	},
	{
		method: 'post',
		path: '/:batchId/products/code',
		alias: 'assignProductCodes',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'batchId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/bulk',
		alias: 'bulkUpdateBatch',
		response: apiResponse(z.boolean()),
		parameters: bulk(batchUpdate.omit({ files: true })),
		errors,
	},
	{
		method: 'patch',
		path: '/bulk/product-test',
		alias: 'bulkUpdateBatchProductTests',
		response: apiResponse(z.boolean()),
		parameters: [...bulk(productTestUpdate), ...listProps],
		errors,
	},
	{
		method: 'post',
		path: '/:batchId/delivery',
		alias: 'setBatchDeliveryDate',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'batchId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: batchStatusTransitionBodySchema,
			},
		],
		errors,
	},
	{
		method: 'post',
		path: '/:batchId/check',
		alias: 'setBatchCheckDate',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'batchId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: batchStatusTransitionBodySchema,
			},
		],
		errors,
	},
	{
		method: 'post',
		path: '/:batchId/check-sn',
		alias: 'setBatchCheckSnDate',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'batchId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: batchStatusTransitionBodySchema,
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/:batchId/service-case',
		alias: 'getBatchServiceCases',
		response: apiResponse(z.array(serviceCase)),
		parameters: [{ name: 'batchId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:batchId/warranty-claim',
		alias: 'getBatchWarrantyClaims',
		response: apiResponse(z.array(warrantyClaim)),
		parameters: [{ name: 'batchId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'delete',
		path: '/:batchId',
		alias: 'deleteBatch',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'batchId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
