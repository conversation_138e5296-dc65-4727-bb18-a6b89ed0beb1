import { makeApi } from '@zodios/core';
import { z } from 'zod';
import {
	attribute,
	attributeAlternativeName,
	attributeParameters,
	attributeValue,
	attributeValueAlternative,
	attributeValueAlternativeParameters,
	attributeValueParameters,
	errors,
	fullAttribute,
	listProps,
	product,
} from '../entities';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';

export const attributeApi = makeApi([
	{
		method: 'get',
		path: '/',
		alias: 'getAttributes',
		response: apiPagedResponse(attribute),
		parameters: listProps,
		errors,
	},
	{
		method: 'post',
		path: '/',
		alias: 'createAttribute',
		parameters: [{ name: 'attribute', type: 'Body', schema: attributeParameters }],
		response: apiResponse(z.object({ id: z.string().uuid() })),
		errors,
	},
	{
		method: 'get',
		path: '/:attributeId',
		alias: 'getAttribute',
		response: apiResponse(attribute),
		parameters: [{ name: 'attributeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:attributeId/full',
		alias: 'getFullAttribute',
		response: apiResponse(fullAttribute),
		parameters: [{ name: 'attributeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/:attributeId',
		alias: 'updateAttribute',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'attributeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attribute', type: 'Body', schema: attributeParameters },
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:attributeId',
		alias: 'deleteAttribute',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'attributeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'post',
		path: '/:attributeId/name',
		alias: 'createAttributeAlternativeName',
		response: apiResponse(attributeAlternativeName),
		parameters: [
			{ name: 'attributeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeAlternativeName', type: 'Body', schema: z.object({ name: z.string(), note: z.string().optional() }) },
		],
		errors,
	},
	{
		method: 'put',
		path: '/:attributeId/name/:attributeAlternativeNameId',
		alias: 'updateAttributeAlternativeName',
		response: apiResponse(attributeAlternativeName),
		parameters: [
			{ name: 'attributeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeAlternativeNameId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeAlternativeName', type: 'Body', schema: z.object({ name: z.string(), note: z.string().optional() }) },
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:attributeId/name/:attributeAlternativeNameId',
		alias: 'deleteAttributeAlternativeName',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'attributeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeAlternativeNameId', type: 'Path', schema: z.string().uuid() },
		],
		errors,
	},

	{
		method: 'get',
		path: '/:attributeId/value',
		alias: 'getAttributeValues',
		response: apiPagedResponse(attributeValue),
		parameters: [...listProps, { name: 'attributeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'post',
		path: '/:attributeId/value',
		alias: 'createAttributeValue',
		response: apiResponse(attributeValue),
		parameters: [
			{ name: 'attributeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeValue', type: 'Body', schema: attributeValueParameters },
		],
		errors,
	},
	{
		method: 'get',
		path: '/:attributeId/value/:attributeValueId',
		alias: 'getAttributeValue',
		response: apiResponse(attributeValue),
		parameters: [
			{ name: 'attributeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeValueId', type: 'Path', schema: z.string().uuid() },
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:attributeId/value/:attributeValueId',
		alias: 'updateAttributeValue',
		response: apiResponse(attributeValue),
		parameters: [
			{ name: 'attributeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeValueId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeValue', type: 'Body', schema: attributeValueParameters.extend({ attributeValueId: z.string().uuid() }) },
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:attributeId/value/:attributeValueId',
		alias: 'deleteAttributeValue',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'attributeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeValueId', type: 'Path', schema: z.string().uuid() },
		],
		errors,
	},
	{
		method: 'get',
		path: '/:attributeId/value/:attributeValueId/products',
		alias: 'getAttributeValueProducts',
		response: apiResponse(z.array(product)),
		parameters: [
			{ name: 'attributeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeValueId', type: 'Path', schema: z.string().uuid() },
		],
		errors,
	},
	{
		method: 'post',
		path: '/:attributeId/value/:attributeValueId/alternative',
		alias: 'createAttributeValueAlternative',
		response: apiResponse(attributeValueAlternative),
		parameters: [
			{ name: 'attributeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeValueId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeValueAlternative', type: 'Body', schema: attributeValueAlternativeParameters },
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:attributeId/value/:attributeValueId/alternative/:attributeValueAlternativeId',
		alias: 'updateAttributeValueAlternative',
		response: apiResponse(attributeValueAlternative),
		parameters: [
			{ name: 'attributeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeValueId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeValueAlternativeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeValueAlternative', type: 'Body', schema: attributeValueAlternativeParameters },
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:attributeId/value/:attributeValueId/alternative/:attributeValueAlternativeId',
		alias: 'deleteAttributeValueAlternative',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'attributeId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeValueId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeValueAlternativeId', type: 'Path', schema: z.string().uuid() },
		],
		errors,
	},
]);
