import { makeApi } from '@zodios/core';
import { errors, outage } from '../entities';
import { apiResponse } from '../utils/wrapper';

export const outageApi = makeApi([
	{
		method: 'get',
		path: '/',
		alias: 'getOutageStatus',
		response: apiResponse(outage).nullable(),
		errors,
	},
	{
		method: 'post',
		path: '/',
		alias: 'toggleOutageStatus',
		response: apiResponse(outage),
		errors,
	},
]);
