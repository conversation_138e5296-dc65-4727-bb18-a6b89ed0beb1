import { makeApi } from '@zodios/core';
import { z } from 'zod';
import {
	agingStockAnalytics,
	closedBatchAnalytics,
	errors,
	productAnalytics,
	testingAnalytics,
	testingFixesAnalytics,
	vendorAnalytics,
} from '../entities';
import { apiResponse } from '../utils/wrapper';

export const analyticsApi = makeApi([
	{
		method: 'get',
		path: '/testing',
		alias: 'getTestingAnalytics',
		response: apiResponse(testingAnalytics),
		parameters: [
			{ name: 'startDate', type: 'Query', schema: z.coerce.date() },
			{ name: 'endDate', type: 'Query', schema: z.coerce.date() },
		],
		errors,
	},
	{
		method: 'get',
		path: '/testing/fixes',
		alias: 'getTestingFixesAnalytics',
		response: apiResponse(testingFixesAnalytics),
		parameters: [
			{ name: 'startDate', type: 'Query', schema: z.coerce.date() },
			{ name: 'endDate', type: 'Query', schema: z.coerce.date() },
			{ name: 'testerId', type: 'Query', schema: z.string().uuid().optional() },
		],
		errors,
	},
	{
		method: 'get',
		path: '/vendor',
		alias: 'getVendorAnalytics',
		response: apiResponse(vendorAnalytics),
		parameters: [
			{ name: 'startDate', type: 'Query', schema: z.coerce.date() },
			{ name: 'endDate', type: 'Query', schema: z.coerce.date() },
		],
		errors,
	},
	{
		method: 'get',
		path: '/product',
		alias: 'getProductAnalytics',
		response: apiResponse(productAnalytics),
		parameters: [
			{ name: 'startDate', type: 'Query', schema: z.coerce.date() },
			{ name: 'endDate', type: 'Query', schema: z.coerce.date() },
		],
		errors,
	},
	{
		method: 'get',
		path: '/aging-stock',
		alias: 'getAgingStockAnalytics',
		response: apiResponse(agingStockAnalytics),
		parameters: [],
		errors,
	},
	{
		method: 'get',
		path: '/batch/:batchId',
		alias: 'getClosedBatchAnalytics',
		response: apiResponse(closedBatchAnalytics),
		parameters: [{ name: 'batchId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
