import { makeApi } from '@zodios/core';
import { z } from 'zod';
import {
	categoryAttribute,
	errors,
	listProps,
	productCategory,
	productCategoryAttribute,
	productCategoryAttributeCreation,
	productCategoryAttributeDeletion,
	productCategoryAttributeUpdateSequence,
	productCategoryCreate,
	productCategoryGrade,
	productCategoryUpdate,
} from '../entities';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';

export const productCategoryApi = makeApi([
	{
		method: 'get',
		path: '/',
		alias: 'getProductCategories',
		response: apiResponse(z.array(productCategory)),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/list',
		alias: 'getProductCategoriesList',
		response: apiPagedResponse(productCategory),
		parameters: listProps,
		errors,
	},
	{
		method: 'post',
		path: '/',
		alias: 'createProductCategory',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'crateProductCategoryBody',
				type: 'Body',
				required: true,
				schema: productCategoryCreate,
			},
		],
		errors,
	},
	{
		method: 'put',
		path: '/:productCategoryId',
		alias: 'updateProductCategory',
		response: apiResponse(productCategory),
		parameters: [
			{
				name: 'productCategoryId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
			{
				name: 'updateProductCategoryBody',
				type: 'Body',
				required: true,
				schema: productCategoryUpdate,
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/:productCategoryId',
		alias: 'getProductCategory',
		response: apiResponse(productCategory),
		parameters: [
			{
				name: 'productCategoryId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:productCategoryId',
		alias: 'deleteProductCategory',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'productCategoryId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/:productCategoryId/ancestors',
		alias: 'getProductCategoryAncestors',
		response: apiResponse(productCategory),
		parameters: [
			{
				name: 'productCategoryId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/:productCategoryId/descendants',
		alias: 'getProductCategoryDescendants',
		response: apiResponse(productCategory),
		parameters: [
			{
				name: 'productCategoryId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/:productCategoryId/descendants-array',
		alias: 'getProductCategoryDescendantsArray',
		response: apiResponse(z.array(z.string().uuid())),
		parameters: [
			{
				name: 'productCategoryId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/:productCategoryId/siblings',
		alias: 'getProductCategorySiblings',
		response: apiResponse(productCategory),
		parameters: [
			{
				name: 'productCategoryId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:productCategoryId',
		alias: 'moveProductCategory',
		response: apiResponse(productCategory),
		parameters: [
			{
				name: 'productCategoryId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
			{
				name: 'moveProductCategoryBody',
				type: 'Body',
				required: true,
				schema: z.object({
					parentId: z.string().uuid(),
				}),
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/:productCategoryId/attribute',
		alias: 'getProductCategoryAttributes',
		response: apiPagedResponse(categoryAttribute),
		parameters: [
			...listProps,
			{
				name: 'productCategoryId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
		],
		errors,
	},
	{
		method: 'post',
		path: '/:productCategoryId/attribute',
		alias: 'addProductCategoryAttribute',
		response: apiResponse(productCategoryAttribute),
		parameters: [
			{
				name: 'productCategoryId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
			{ name: 'data', type: 'Body', required: true, schema: productCategoryAttributeCreation },
		],
		errors,
	},
	{
		method: 'get',
		path: '/:productCategoryId/import-file',
		alias: 'downloadProductCategoryImportFile',
		response: z.any(),
		parameters: [
			{
				name: 'productCategoryId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:productCategoryId/attribute/:attributeId',
		alias: 'deleteProductCategoryAttribute',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'productCategoryId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
			{
				name: 'attributeId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
			{
				name: 'type',
				type: 'Body',
				schema: productCategoryAttributeDeletion,
			},
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:productCategoryId/attribute-sequence',
		alias: 'updateProductCategoryAttributeSequence',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'productCategoryId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
			{
				name: 'productCategoryAttributeType',
				type: 'Body',
				required: true,
				schema: productCategoryAttributeUpdateSequence,
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/:productCategoryId/grade',
		alias: 'getProductCategoryGrades',
		response: apiResponse(z.array(productCategoryGrade)),
		parameters: [
			{
				name: 'productCategoryId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
		],
		errors,
	},

	{
		method: 'post',
		path: '/:productCategoryId/grade',
		alias: 'handleProductCategoryGrade',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'productCategoryId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
			{
				name: 'body',
				type: 'Body',
				required: true,
				schema: z.object({ gradeId: z.string().uuid(), maxCosmeticDefects: z.number().min(0) }),
			},
		],
		errors,
	},
]);
