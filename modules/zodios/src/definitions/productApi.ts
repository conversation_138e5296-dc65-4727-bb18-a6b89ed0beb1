import { PRODUCT_TYPES, WARRANTY_TYPES } from '@pocitarna-nx-2023/config';
import { makeApi } from '@zodios/core';
import { z } from 'zod';
import {
	attributeValuesUpdatesBody,
	attributeValueUpdate,
	attributeValueWithType,
	ecommerceOrder,
	errors,
	listProps,
	primitive,
	product,
	productCosmeticDefect,
	productCreate,
	productPrice,
	productTestUpdate,
	productUpdate,
	productWarranties,
	serviceCase,
	warranty,
	warrantyClaim,
	warrantyCreate,
	warrantyUpdate,
} from '../entities';
import { bulk, bulkNoData } from '../entities/bulk';
import { apiPagedResponse, apiResponse, singleOrArray } from '../utils/wrapper';

export const productApi = makeApi([
	{
		method: 'post',
		path: '/',
		alias: 'createProduct',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'product',
				type: 'Body',
				schema: z.object({
					product: productCreate,
					productPrice: z.number(),
					attributeValues: z.array(attributeValueUpdate),
				}),
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/',
		alias: 'getAllProducts',
		response: apiPagedResponse(product),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/tested',
		alias: 'getTestedProducts',
		response: apiPagedResponse(product),
		parameters: [
			...listProps,
			{ name: 'autofill', type: 'Query', schema: z.boolean().optional() },
			{ name: 'attributeValue', type: 'Query', schema: z.string().optional() },
		],
		errors,
	},
	{
		method: 'get',
		path: '/:productId',
		alias: 'getProduct',
		response: apiResponse(product),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/code/:productCode',
		alias: 'getProductByCode',
		response: apiResponse(z.object({ id: z.string().uuid() })),
		parameters: [
			{
				name: 'productCode',
				type: 'Path',
				required: true,
				schema: z.string(),
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/:productId/prices',
		alias: 'getAllProductPrices',
		response: apiResponse(z.array(productPrice)),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:productId/warranties',
		alias: 'getProductWarranties',
		response: apiResponse(productWarranties),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:productId/order',
		alias: 'getProductOrder',
		response: apiResponse(z.union([ecommerceOrder, z.null()])),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'post',
		path: '/:productId/warranties',
		alias: 'createProductWarranty',
		response: apiResponse(warranty),
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: warrantyCreate,
			},
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:productId/warranties/:warrantyId',
		alias: 'updateProductWarranty',
		response: apiResponse(warranty),
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{ name: 'warrantyId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: warrantyUpdate,
			},
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:productId/warranties/:warrantyId',
		alias: 'deleteProductWarranty',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{ name: 'warrantyId', type: 'Path', schema: z.string().uuid() },
		],
		errors,
	},
	{
		method: 'get',
		path: '/:productId/attribute-value',
		alias: 'getProductAttributeValues',
		response: apiResponse(z.array(attributeValueWithType)),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/bulk/attribute-values',
		alias: 'bulkAssignAttributeValues',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'body',
				type: 'Body',
				schema: z.object({ productIds: z.array(z.string().uuid()), attributeValues: z.array(attributeValueUpdate) }),
			},
		],
		errors,
	},
	{
		method: 'put',
		path: '/:productId/attribute-value',
		alias: 'updateProductAttributeValues',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: z.object({
					attributeValues: z.array(
						z.object({
							attributeId: z.string().uuid(),
							attributeValueId: z.string().optional(),
							isFix: z.boolean().default(false),
							value: primitive.optional(),
						}),
					),
				}),
			},
		],
		errors,
	},
	{
		method: 'post',
		path: '/:productId/attribute-value',
		alias: 'setProductAttributeValue',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: singleOrArray(
					z.object({
						attributeId: z.string().uuid(),
						attributeValueId: z.string().optional(),
						value: primitive.optional(),
						autofill: z.boolean().default(false),
						lock: z.boolean().default(false),
						isFix: z.boolean().default(false),
					}),
				),
			},
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:productId/attribute-value-autofill',
		alias: 'removeProductAttributeValueAutofill',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'delete',
		path: '/:productId/attribute-value/:attributeValueId',
		alias: 'removeProductAttributeValue',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{ name: 'attributeValueId', type: 'Path', schema: z.string() },
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:productId/bulk/attribute-values',
		alias: 'assignAttributeValuesToProduct',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{ name: 'productAttributeValuesUpdates', type: 'Body', schema: attributeValuesUpdatesBody },
		],
		errors,
	},
	{
		method: 'post',
		path: '/:productId/attribute-value-loader',
		alias: 'loadAttributeValuesFromReader',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: z.object({
					fileIds: z.array(z.string().uuid()),
					forceAppendSn: z.boolean().default(false),
					forceModelOverride: z.boolean().default(false),
				}),
			},
		],
		errors: [
			...errors,
			{ status: 500, description: 'SN mismatch', schema: z.object({ sn: z.object({ new: z.string(), current: z.string() }) }) },
			{ status: 409, description: 'SN conflict', schema: z.object({ sn: z.string(), conflictingProductId: z.string().uuid() }) },
			{
				status: 300,
				description: 'Model conflict',
				schema: z.object({
					readerModel: z.string(),
					productModel: z.string(),
					matchingProducts: z.array(
						z.object({
							id: z.string().uuid(),
							code: z.number().nullable(),
						}),
					),
				}),
			},
		],
	},
	{
		method: 'post',
		path: '/attribute-value-loader',
		alias: 'loadAttributeValuesFromCLI',
		response: apiResponse(z.string()),
		parameters: [{ name: 'body', type: 'Body', schema: z.object({ fileIds: z.array(z.string().uuid()) }) }],
		errors,
	},
	{
		method: 'patch',
		path: '/:productId',
		alias: 'updateProduct',
		response: apiResponse(product.omit({ code: true, warehousePosition: true })), // FIXME - code is there but without the code property
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{ name: 'product', type: 'Body', schema: productUpdate },
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:productId/files',
		alias: 'addFilesToProduct',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{ name: 'files', type: 'Body', schema: z.array(z.string()) },
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:productId/test',
		alias: 'finishProductTest',
		response: apiResponse(product.pick({ status: true })),
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: z.object({
					preventWarrantyClaim: z.boolean().default(false),
					gradeId: z.string().uuid().optional(),
					productType: z.enum(PRODUCT_TYPES).optional(),
					clearCategoryCosmeticAreas: z.record(z.string().uuid(), z.boolean()).optional(),
				}),
			},
		],
		errors,
	},
	{
		method: 'patch',
		path: '/bulk/test',
		alias: 'bulkFinishProductTest',
		response: apiResponse(
			z.object({ data: z.array(z.tuple([z.string().uuid(), z.string().uuid(), z.string().uuid()])), errors: z.array(z.string()) }),
		),
		parameters: [
			{
				name: 'body',
				type: 'Body',
				schema: z.array(z.string().uuid()),
			},
		],
		errors,
	},
	{
		method: 'put',
		path: '/:productId/test',
		alias: 'saveProductTest',
		response: apiResponse(product.pick({ status: true })),
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: z.object({
					gradeId: z.string().uuid().optional(),
					productType: z.enum(PRODUCT_TYPES).optional(),
					clearCategoryCosmeticAreas: z.record(z.string().uuid(), z.boolean()).optional(),
				}),
			},
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:productId/stock/attribute-values',
		alias: 'editProductAttributeValues',
		response: apiResponse(product),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'post',
		path: '/:productId/template',
		alias: 'createProductTemplate',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: z.object({ attributeId: z.string().uuid(), conditionalAttributeIds: z.array(z.string().uuid()) }),
			},
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:productId',
		alias: 'deleteProduct',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/bulk',
		alias: 'bulkUpdateProduct',
		response: apiResponse(z.boolean()),
		parameters: bulk(productUpdate),
		errors,
	},
	{
		method: 'delete',
		path: '/bulk',
		alias: 'bulkDeleteProducts',
		response: apiResponse(z.boolean()),
		parameters: bulkNoData(),
		errors,
	},
	{
		method: 'patch',
		path: '/bulk/product-test',
		alias: 'bulkUpdateProductTests',
		response: apiResponse(z.boolean()),
		parameters: bulk(productTestUpdate),
		errors,
	},
	{
		method: 'patch',
		path: '/bulk/product-test/printInfo',
		alias: 'bulkUpdateProductTestPrintInfo',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'body',
				type: 'Body',
				schema: z.array(
					z.object({
						productId: z.string().uuid(),
						productEnvelopeId: z.string().uuid(),
					}),
				),
			},
		],
		errors,
	},
	{
		method: 'patch',
		path: '/product-test/:productId',
		alias: 'updateProductTest',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{ name: 'productTest', type: 'Body', schema: productTestUpdate },
		],
		errors,
	},
	{
		method: 'get',
		path: '/:productId/service-case',
		alias: 'getProductServiceCase',
		response: apiResponse(serviceCase),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:productId/warranty-claim',
		alias: 'getProductWarrantyClaim',
		response: apiResponse(warrantyClaim),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:productId/cosmetic-defects',
		alias: 'getProductOwnCosmeticDefects',
		response: apiResponse(z.array(productCosmeticDefect)),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/bulk/warranty',
		alias: 'bulkUpdateProductWarranty',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'body',
				type: 'Body',
				schema: z.object({
					productIds: z.array(z.string().uuid()),
					data: z.object({ expiredAt: z.coerce.date(), type: z.enum(WARRANTY_TYPES) }),
				}),
			},
		],
		errors,
	},
]);
