import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { ecommerceOrder, errors, listProps } from '../../entities';
import { apiResponse } from '../../utils/wrapper';

export const publicEcommerceOrderApi = makeApi([
	{
		method: 'get',
		path: '/',
		alias: 'getPublicEcommerceOrders',
		response: apiResponse(z.array(ecommerceOrder)),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/:ecommerceOrderId',
		alias: 'getPublicEcommerceOrder',
		response: apiResponse(ecommerceOrder),
		parameters: [
			{
				name: 'ecommerceOrderId',
				type: 'Path',
				required: true,
				schema: z.string().uuid(),
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/code/:ecommerceOrderCode',
		alias: 'getPublicEcommerceOrderByCode',
		response: apiResponse(ecommerceOrder),
		parameters: [
			{
				name: 'ecommerceOrderCode',
				type: 'Path',
				required: true,
				schema: z.string(),
			},
		],
		errors,
	},
]);
