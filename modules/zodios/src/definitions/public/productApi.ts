import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, listProps, product, productCosmeticDefect, relatedFile } from '../../entities';
import { apiResponse } from '../../utils/wrapper';

export const publicProductApi = makeApi([
	{
		method: 'get',
		path: '/:productId',
		alias: 'getPublicProduct',
		response: apiResponse(product),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},

	{
		method: 'get',
		path: '/:productId/cosmetic-defects',
		alias: 'getPublicProductCosmeticDefects',
		response: apiResponse(z.array(productCosmeticDefect)),
		parameters: [...listProps, { name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},

	{
		method: 'get',
		path: '/:productId/files',
		alias: 'getPublicProductFiles',
		response: apiResponse(z.array(relatedFile)),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
