import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { cosmeticArea, cosmeticAreaCreate, errors, listProps } from '../entities';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';

export const cosmeticAreaApi = makeApi([
	{
		method: 'post',
		path: '/',
		alias: 'createCosmeticArea',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'grade', type: 'Body', schema: cosmeticAreaCreate }],
		errors,
	},
	{
		method: 'get',
		path: '/',
		alias: 'getCosmeticAreas',
		response: apiPagedResponse(cosmeticArea),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/:cosmeticAreaId',
		alias: 'getCosmeticArea',
		response: apiResponse(cosmeticArea),
		parameters: [{ name: 'cosmeticAreaId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/:cosmeticAreaId',
		alias: 'updateCosmeticArea',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'cosmeticAreaId', type: 'Path', schema: z.string().uuid() },
			{ name: 'grade', type: 'Body', schema: cosmeticAreaCreate.partial() },
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:cosmeticAreaId',
		alias: 'deleteCosmeticArea',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'cosmeticAreaId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
