import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, listProps, relatedDefect, relatedFile, warrantyClaim } from '../../entities';
import { apiResponse, historical } from '../../utils/wrapper';

export const warrantyClaimHistoryApi = makeApi([
	{
		method: 'get',
		path: '/:warrantyClaimId',
		alias: 'getWarrantyClaimHistory',
		response: apiResponse(historical({ warrantyClaim, fileWarrantyClaim: relatedFile, productDefect: relatedDefect })),
		parameters: [...listProps, { name: 'warrantyClaimId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
