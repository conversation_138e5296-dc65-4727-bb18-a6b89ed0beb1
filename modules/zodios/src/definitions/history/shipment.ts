import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, listProps, shipment } from '../../entities';
import { apiResponse, historical } from '../../utils/wrapper';

export const shipmentHistoryApi = makeApi([
	{
		method: 'get',
		path: '/:shipmentId',
		alias: 'getShipmentHistory',
		response: apiResponse(historical({ shipment })),
		parameters: [...listProps, { name: 'shipmentId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
