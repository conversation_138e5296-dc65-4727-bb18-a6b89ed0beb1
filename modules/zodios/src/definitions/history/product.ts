import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, product, productAttributeValue, relatedFile, relatedProductCosmeticDefect } from '../../entities';
import { apiResponse, historical } from '../../utils/wrapper';

export const productHistoryApi = makeApi([
	{
		method: 'get',
		path: '/:productId',
		alias: 'getProductHistory',
		response: apiResponse(
			historical({ product, productAttributeValue, fileProduct: relatedFile, productCosmeticDefect: relatedProductCosmeticDefect }),
		),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
