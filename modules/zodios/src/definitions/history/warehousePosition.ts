import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, listProps, product, warehousePosition } from '../../entities';
import { apiResponse, historical } from '../../utils/wrapper';

export const warehousePositionHistoryApi = makeApi([
	{
		method: 'get',
		path: '/:warehousePositionId',
		alias: 'getWarehousePositionHistory',
		response: apiResponse(historical({ warehousePosition, warehousePositionProduct: product })),
		parameters: [...listProps, { name: 'warehousePositionId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
