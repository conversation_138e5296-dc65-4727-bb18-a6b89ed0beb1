import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, listProps, relatedDefect, relatedFile, serviceCase } from '../../entities';
import { apiResponse, historical } from '../../utils/wrapper';

export const serviceCaseHistoryApi = makeApi([
	{
		method: 'get',
		path: '/:serviceCaseId',
		alias: 'getServiceCaseHistory',
		response: apiResponse(historical({ serviceCase, fileServiceCase: relatedFile, productDefect: relatedDefect })),
		parameters: [...listProps, { name: 'serviceCaseId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
