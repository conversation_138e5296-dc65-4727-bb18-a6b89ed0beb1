import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, listProps, serviceCenter } from '../../entities';
import { apiResponse, historical } from '../../utils/wrapper';

export const serviceCenterHistoryApi = makeApi([
	{
		method: 'get',
		path: '/:serviceCenterId',
		alias: 'getServiceCenterHistory',
		response: apiResponse(historical({ serviceCenter })),
		parameters: [...listProps, { name: 'serviceCenterId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
