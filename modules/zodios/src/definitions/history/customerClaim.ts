import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { customerClaim, errors, listProps, relatedDefect } from '../../entities';
import { apiResponse, historical } from '../../utils/wrapper';

export const customerClaimHistoryApi = makeApi([
	{
		method: 'get',
		path: '/:customerClaimId',
		alias: 'getCustomerClaimHistory',
		response: apiResponse(historical({ customerClaim, productDefect: relatedDefect })),
		parameters: [...listProps, { name: 'customerClaimId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
