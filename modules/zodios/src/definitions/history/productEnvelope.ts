import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, listProps, productEnvelope } from '../../entities';
import { apiResponse, historical } from '../../utils/wrapper';

export const productEnvelopeHistoryApi = makeApi([
	{
		method: 'get',
		path: '/:productEnvelopeId',
		alias: 'getProductEnvelopeHistory',
		response: apiResponse(historical({ productEnvelope })),
		parameters: [...listProps, { name: 'productEnvelopeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
