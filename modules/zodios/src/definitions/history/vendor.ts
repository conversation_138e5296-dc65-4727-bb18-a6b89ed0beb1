import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, listProps, relatedFile, vendor } from '../../entities';
import { apiResponse, historical } from '../../utils/wrapper';

export const vendorHistoryApi = makeApi([
	{
		method: 'get',
		path: '/:vendorId',
		alias: 'getVendorHistory',
		response: apiResponse(historical({ vendor, fileVendor: relatedFile })),
		parameters: [...listProps, { name: 'vendorId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
