import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { batch, errors, listProps, relatedFile } from '../../entities';
import { apiResponse, historical } from '../../utils/wrapper';

export const batchHistoryApi = makeApi([
	{
		method: 'get',
		path: '/:batchId',
		alias: 'getBatchHistory',
		response: apiResponse(historical({ batch, fileBatch: relatedFile })),
		parameters: [...listProps, { name: 'batchId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
