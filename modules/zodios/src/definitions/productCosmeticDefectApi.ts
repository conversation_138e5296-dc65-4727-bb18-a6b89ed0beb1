import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, listProps, productCosmeticDefect } from '../entities';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';

export const productCosmeticDefectApi = makeApi([
	{
		method: 'get',
		path: '/',
		alias: 'getProductCosmeticDefects',
		response: apiPagedResponse(productCosmeticDefect),
		parameters: listProps,
		errors,
	},
	{
		method: 'post',
		path: '/product/:productId/cosmeticArea/:cosmeticAreaId',
		alias: 'addCosmeticDefectsToProductArea',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{ name: 'cosmeticAreaId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: z.object({
					cosmeticDefectId: z.string().uuid(),
					isFix: z.boolean().optional(),
				}),
			},
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:productCosmeticDefectId/files',
		alias: 'addFilesToProductCosmeticDefect',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productCosmeticDefectId', type: 'Path', schema: z.string().uuid() },
			{ name: 'files', type: 'Body', schema: z.array(z.string()) },
		],
		errors,
	},
	{
		method: 'delete',
		path: '/product/:productId/cosmeticDefect/:cosmeticDefectId',
		alias: 'deleteProductCosmeticDefect',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{ name: 'cosmeticDefectId', type: 'Path', schema: z.string().uuid() },
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:productCosmeticDefectId/files/:fileId',
		alias: 'deleteProductCosmeticDefectFile',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productCosmeticDefectId', type: 'Path', schema: z.string().uuid() },
			{ name: 'fileId', type: 'Path', schema: z.string().uuid() },
		],
		errors,
	},
]);
