import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { customerClaim, customerClaimCreate, customerClaimUpdate, errors, listProps } from '../entities';
import { code } from '../entities/code';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';

export const customerClaimApi = makeApi([
	{
		method: 'post',
		path: '/',
		alias: 'createCustomerClaim',
		response: apiResponse(z.string().uuid()),
		parameters: [{ name: 'customerClaim', type: 'Body', schema: customerClaimCreate }],
		errors,
	},
	{
		method: 'get',
		path: '/',
		alias: 'getCustomerClaims',
		response: apiPagedResponse(customerClaim),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/code/:customerClaimCodeId',
		alias: 'getCustomerClaimCode',
		response: apiResponse(code),
		parameters: [{ name: 'customerClaimCodeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:customerClaimId/protocol',
		alias: 'getCustomerClaimPdfProtocolLink',
		response: apiResponse(z.string()),
		parameters: [{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:customerClaimId',
		alias: 'getCustomerClaim',
		response: apiResponse(customerClaim),
		parameters: [{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/:customerClaimId',
		alias: 'updateCustomerClaim',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: customerClaimUpdate,
			},
		],
		errors,
	},
]);
