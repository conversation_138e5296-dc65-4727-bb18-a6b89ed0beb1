import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, listProps, notificationType } from '../entities';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';

export const notificationTypeApi = makeApi([
	{
		method: 'get',
		path: '/',
		alias: 'getNotificationTypes',
		response: apiPagedResponse(notificationType),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/:notificationTypeId',
		alias: 'getNotificationType',
		response: apiResponse(notificationType),
		parameters: [{ name: 'notificationTypeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
