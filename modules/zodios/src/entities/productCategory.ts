import { NO_NEGATIVE_VALUE, REQUIRED_FIELD } from '@pocitarna-nx-2023/config';
import { z } from 'zod';
import { relationAssignment } from '../utils/relationAssignment';
import { grade } from './grade';
import { recyclingFee } from './recyclingFee';
import { shoptetCategory } from './shoptetCategory';

export const productCategoryCreate = z.object({
	name: z.string({ required_error: REQUIRED_FIELD }).min(1, { message: REQUIRED_FIELD }),
	margin: z.coerce.number({ required_error: REQUIRED_FIELD }).min(0, { message: NO_NEGATIVE_VALUE }),
	codePrefix: z.string({ required_error: REQUIRED_FIELD }).min(1, { message: REQUIRED_FIELD }).toUpperCase(),
	minimumTestPhotos: z.coerce.number({ required_error: REQUIRED_FIELD }).min(0, { message: NO_NEGATIVE_VALUE }),
	parent: relationAssignment.optional(),
	shoptetCategory: relationAssignment.optional(),
	recyclingFee: relationAssignment.optional(),
});

export const productCategoryUpdate = productCategoryCreate.partial();

export const baseProductCategory = z.object({
	id: z.string().uuid(),
	createdAt: z.coerce.date(),
	name: z.string(),
	margin: z.number(),
	codePrefix: z.string(),
	shoptetCategory: shoptetCategory.nullish(),
	shoptetCategoryId: z.string().uuid().nullish(),
	recyclingFee: recyclingFee.nullish(),
	recyclingFeeId: z.string().uuid().nullish(),
	minimumTestPhotos: z.number(),
});

type ProductCategory = z.infer<typeof baseProductCategory> & {
	parent?: ProductCategory | null;
	children?: ProductCategory[] | null;
};

export const productCategory: z.ZodType<ProductCategory> = baseProductCategory.extend({
	parent: z.lazy(() => productCategory.nullish().default(null)),
	children: z.lazy(() => productCategory.array().nullish().default(null)),
});

export const productCategoryGrade = z.object({
	id: z.string().uuid(),
	productCategoryId: z.string().uuid(),
	gradeId: z.string().uuid(),
	grade,
	maxCosmeticDefects: z.number().min(1),
});
