import { z } from 'zod';
import { relationAssignmentArray } from '../utils/relationAssignment';

export const productCosmeticDefect = z.object({
	id: z.string().uuid(),
	cosmeticDefectId: z.string().uuid(),
	productId: z.string().uuid(),
	cosmeticDefect: z.object({
		id: z.string().uuid(),
		name: z.string().min(1),
		pictureRequired: z.boolean().default(false),
		cosmeticAreaCosmeticDefects: z.array(z.object({ id: z.string().uuid(), cosmeticAreaId: z.string().uuid() })),
		grade: z.object({ id: z.string().uuid(), name: z.string().min(1) }),
	}),
	cosmeticAreaId: z.string().uuid(),
	files: relationAssignmentArray,
});

export const relatedProductCosmeticDefect = z.object({
	id: z.string().uuid(),
	cosmeticDefect: z.object({
		id: z.string().uuid(),
		name: z.string(),
	}),
	cosmeticArea: z.object({ id: z.string().uuid(), name: z.string() }),
});
