import { z } from 'zod';
import { relationAssignment } from '../utils/relationAssignment';
import { cosmeticArea } from './cosmeticArea';
import { grade } from './grade';

const cosmeticAreaCosmeticDefect = z.object({
	id: z.string().uuid(),
	cosmeticAreaId: z.string().uuid(),
	cosmeticArea,
});

const productCategoryCosmeticDefect = z.object({
	id: z.string().uuid(),
	productCategoryId: z.string().uuid(),
});

export const cosmeticDefect = z.object({
	id: z.string().uuid(),
	name: z.string().min(1),
	pictureRequired: z.boolean().default(false),
	cosmeticAreaCosmeticDefects: z.array(cosmeticAreaCosmeticDefect).nullish(),
	productCategoryCosmeticDefects: z.array(productCategoryCosmeticDefect).nullish(),
	grade: grade,
	gradeId: z.string().uuid(),
});

export const cosmeticDefectCreate = z.object({
	name: z.string(),
	pictureRequired: z.boolean(),
	productCategories: z.array(z.string().uuid()).default([]),
	cosmeticAreas: z.array(z.string().uuid()).default([]),
	grade: relationAssignment,
});
