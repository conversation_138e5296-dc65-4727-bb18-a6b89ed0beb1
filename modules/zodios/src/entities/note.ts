import { z } from 'zod';
import { relationAssignment } from '../utils/relationAssignment';
import { user } from './user';

export const note = z.object({
	id: z.string().uuid(),
	content: z.string(),
	createdBy: user.nullish().default(null),
	createdById: z.string().uuid().nullish().default(null),
	createdAt: z.coerce.date(),
});

export const noteCreation = z.object({
	content: z.string(),
	warrantyClaim: relationAssignment.optional(),
	serviceCase: relationAssignment.optional(),
	customerClaim: relationAssignment.optional(),
});

export type Note = z.infer<typeof note>;
