import { z } from 'zod';
import { zodDecimal } from '../utils/zodDecimal';
import { attributeValue } from './attribute';

const category = z.object({
	id: z.string(),
	name: z.string(),
});

const testingCategoryStat = z.object({
	categoryId: z.string(),
	total: z.number(),
	fixes: z.number(),
});

const tester = z.object({
	id: z.string(),
	name: z.string(),
	totalTests: z.number(),
	totalFixes: z.number(),
	categoryStats: z.array(testingCategoryStat),
	dailyAverage: z.number(),
});

const testingTotals = z.object({
	byCategory: z.array(testingCategoryStat),
	total: z.number(),
	fixes: z.number(),
	dailyAverage: z.number(),
});

export const testingAnalytics = z.object({
	categories: z.array(category),
	testers: z.array(tester),
	totals: testingTotals,
});

export const testingFixesAnalytics = z.object({
	fixedAttributes: z.array(z.tuple([z.string(), z.number()])),
	total: z.number(),
});

const defectStats = z.object({
	straightToStock: z.number(),
	afterTestingWithServiceCase: z.number(),
	afterTestingWithWarrantyClaim: z.number(),
});

const defectTypeStats = z.object({
	name: z.string(),
	serviceCases: z.number(),
	warrantyClaims: z.number(),
});

const byDefectType = z.array(defectTypeStats);

const vendorCategoryStat = z.object({
	categoryId: z.string(),
	total: z.number(),
	defects: z.number(),
	defectStats,
});

const totalByCategory = z.object({
	categoryId: z.string(),
	total: z.number(),
	defects: z.number(),
	defectStats,
	byDefectType,
});

const vendor = z.object({
	id: z.string(),
	name: z.string(),
	totalTests: z.number(),
	totalDefects: z.number(),
	categoryStats: z.array(vendorCategoryStat),
	defectStats,
	byDefectType,
});

const vendorTotals = z.object({
	byCategory: z.array(totalByCategory),
	total: z.number(),
	defects: z.number(),
	defectStats,
	byDefectType,
});

export const vendorAnalytics = z.object({
	categories: z.array(category),
	vendors: z.array(vendor),
	totals: vendorTotals,
});

export const closedBatchAnalytics = z.object({
	testedProducts: z.number(),
	straightToStock: z.number(),
	withServiceCase: z.number(),
	withWarrantyClaim: z.number(),
});

export const productAnalytics = z.array(
	z.object({
		category: z.string(),
		data: z.array(
			z.object({
				identifier: z.string(),
				count: z.number(),
				manufacturer: z.string(),
				model: z.string(),
				caseSize: z.string().optional(),
			}),
		),
	}),
);

const StockAttributeValueSchema = z.object({
	attributeId: z.string(),
	matches: z.array(attributeValue),
});

export type StockAttributeValue = z.infer<typeof StockAttributeValueSchema>;

export const agingStockAnalytics = z.array(
	z.object({
		category: z.string(),
		data: z.array(
			z.object({
				envelopeId: z.string(),
				envelopeCode: z.string(),
				envelopeName: z.string(),
				attributesOverview: z.object({
					brandStockAttributeValues: z.array(StockAttributeValueSchema),
					modelStockAttributeValues: z.array(StockAttributeValueSchema),
					otherStockAttributeValues: z.array(StockAttributeValueSchema),
				}),
				averageBuyPrice: zodDecimal(),
				envelopeSalePrice: zodDecimal(),
				daysWithoutMovement: z.number().nullish().default(null),
				tempo: z.number().nullish().default(null),
				stockAmount: z.number(),
			}),
		),
	}),
);
