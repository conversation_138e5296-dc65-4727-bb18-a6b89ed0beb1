import { z } from 'zod';

export const notification = z.object({
	id: z.string().uuid(),
	createdAt: z.coerce.date(),
	notificationCode: z.string(),
	title: z.string(),
	body: z.string(),
	priority: z.number(),
	href: z.string(),
	viewUntil: z.coerce.date().nullable(),
	viewedAt: z.coerce.date().nullable(),
	refiredAt: z.coerce.date().nullable(),
	userId: z.string().uuid(),
});

export const notificationUpdate = z.object({
	viewedAt: z.coerce.date().nullable(),
});
