import { z } from 'zod';
import { user } from './user';

export const outage = z.object({
	id: z.string().uuid(),
	createdAt: z.coerce.date(),
	createdBy: user.nullish().default(null),
	createdById: z.string().uuid().nullish().default(null),
	endedAt: z.coerce.date().nullish().default(null),
	endedBy: user.nullish().default(null),
	endedById: z.string().uuid().nullish().default(null),
	status: z.enum(['ok', 'outage']),
});
