import { PRODUCT_ATTRIBUTE_VALUE_TYPE, PRODUCT_STATUS_NAMES, PRODUCT_TYPES } from '@pocitarna-nx-2023/config';
import { z } from 'zod';
import { relationAssignment } from '../utils/relationAssignment';
import { attributeValue } from './attribute';
import { batch } from './batch';
import { code } from './code';
import { zDate } from './date';
import { grade } from './grade';
import { productCategory } from './productCategory';
import { productDefect } from './productDefect';
import { productEnvelope } from './productEnvelope';
import { productPrice } from './productPrice';
import { productTask } from './productTask';
import { productTest } from './productTest';
import { user } from './user';
import { warehousePosition } from './warehousePosition';

export const productCode = z.object({
	id: z.string().uuid(),
	code: z.number(),
	batch: batch.nullish().default(null),
	batchId: z.string().uuid().nullish().default(null),
});

export const productUpdate = z
	.object({
		status: z.enum(PRODUCT_STATUS_NAMES),
		code: relationAssignment,
		productCategory: relationAssignment,
		warehousePosition: relationAssignment,
		grade: relationAssignment,
		productEnvelope: relationAssignment,
		sn: z.string(),
		type: z.enum(PRODUCT_TYPES),
	})
	.partial();

export const productCreate = z.object({
	sn: z.string(),
	batch: relationAssignment,
	productCategory: relationAssignment,
});

export const productAttributeValue = z.object({
	id: z.string().uuid(),
	type: z.enum(PRODUCT_ATTRIBUTE_VALUE_TYPE),
	autofill: z.boolean(),
	lock: z.boolean(),
	attributeValue,
	attributeValueId: z.string().uuid(),
	productId: z.string().uuid(),
});

export const product = z.object({
	id: z.string().uuid(),
	createdAt: zDate,
	sn: z.string(),
	pastSn: z.string(),
	status: z.enum(PRODUCT_STATUS_NAMES),
	productTest: productTest.nullish().default(null),
	productCategory: productCategory.nullish().default(null),
	productCategoryId: z.string().uuid().nullish().default(null),
	code: code.nullish().default(null),
	codeId: z.string().uuid().nullish().default(null),
	productDefects: productDefect.array().optional().default([]),
	productEnvelope: productEnvelope.nullish().default(null),
	productEnvelopeId: z.string().uuid().nullish().default(null),
	productEnvelopeAssignedAt: zDate.nullish().default(null),
	productEnvelopeAssignedBy: user.nullish().default(null),
	productEnvelopeAssignedById: z.string().uuid().nullish().default(null),
	productTasks: productTask.array().optional().default([]),
	productPrice: productPrice.array().optional().default([]),
	attributeValues: productAttributeValue.array().optional().default([]),
	batch: batch.nullish().default(null),
	batchId: z.string().uuid().nullish().default(null),
	warehousePosition: warehousePosition.nullish().default(null),
	warehousePositionId: z.string().uuid().nullish().default(null),
	soldAt: zDate.nullish().default(null),
	pickedAt: zDate.nullish().default(null),
	pickedBy: user.nullish().default(null),
	pickedById: z.string().uuid().nullish().default(null),
	grade: grade.nullish().default(null),
	gradeId: z.string().uuid().nullish().default(null),
	type: z.enum(PRODUCT_TYPES),
});
