import {
	CUSTOMER_CLAIM_DELIVERY_METHODS,
	CUSTOMER_CLAIM_HANDLING_METHODS,
	CUSTOMER_CLAIM_STATUS_NAMES,
	REQUIRED_FIELD,
} from '@pocitarna-nx-2023/config';
import { z } from 'zod';
import { address, addressCreate } from './address';
import { code } from './code';
import { contact, customerContactCreate } from './contact';
import { zDate } from './date';
import { ecommerceOrderItem } from './ecommerceOrderItem';
import { note } from './note';
import { user } from './user';

export const customerClaimMessage = z.object({
	id: z.string().uuid(),
	createdAt: z.coerce.date(),
	message: z.string(),
	contactId: z.string().uuid().nullish().default(null),
	userId: z.string().uuid().nullish().default(null),
	customerClaimId: z.string().uuid(),
});

export const customerClaim = z.object({
	id: z.string().uuid(),
	createdAt: z.coerce.date(),
	createdBy: user.nullish().default(null),
	createdById: z.string().uuid().nullish().default(null),
	status: z.enum(CUSTOMER_CLAIM_STATUS_NAMES),
	code,
	codeId: z.string().uuid(),
	notes: z.array(note).default([]),
	handlingMethod: z.enum(CUSTOMER_CLAIM_HANDLING_METHODS),
	customerDeliveryMethod: z.enum(CUSTOMER_CLAIM_DELIVERY_METHODS),
	cmpDeliveryMethod: z.enum(CUSTOMER_CLAIM_DELIVERY_METHODS),
	address: address,
	addressId: z.string().uuid(),
	contact: contact,
	contactId: z.string().uuid(),
	ecommerceOrderItem: ecommerceOrderItem.extend({
		ecommerceOrder: z.object({
			id: z.string().uuid(),
			code: z.string(),
			createdAt: zDate,
			placedAt: zDate,
		}),
	}),
	ecommerceOrderItemId: z.string().uuid(),
	messages: z.array(customerClaimMessage).default([]),
	receivedAt: zDate.nullable().default(null),
});

export const customerClaimUpdate = z.object({
	status: z.enum(CUSTOMER_CLAIM_STATUS_NAMES),
	productId: z.string().uuid(),
	receivedAt: zDate.nullable().default(null),
});

export const customerClaimCreate = z.object({
	message: z.string().min(1, { message: REQUIRED_FIELD }),
	handlingMethod: z.enum(CUSTOMER_CLAIM_HANDLING_METHODS),
	customerDeliveryMethod: z.enum(CUSTOMER_CLAIM_DELIVERY_METHODS),
	cmpDeliveryMethod: z.enum(CUSTOMER_CLAIM_DELIVERY_METHODS).optional(),
	address: addressCreate,
	contact: customerContactCreate,
	ecommerceOrderItem: z.object({ id: z.string().uuid() }),
	files: z.array(z.string()),
});
