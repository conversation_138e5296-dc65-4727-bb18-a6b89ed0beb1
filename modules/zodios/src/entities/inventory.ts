import {
	BASE_INVENTORY_ITEM_STATUS_NAMES,
	ENVELOPE_TYPES,
	INVENTORY_ITEM_STATUS_NAMES,
	INVENTORY_STATUS_NAMES,
	PRODUCT_STATUS_NAMES,
	SERVICE_TASK_RESOLUTION_SCOPE,
} from '@pocitarna-nx-2023/config';
import { z } from 'zod';
import { zodDecimal } from '../utils/zodDecimal';
import { code } from './code';
import { customerClaimCreate } from './customerClaim';
import { productEnvelope } from './productEnvelope';
import { user } from './user';

export const inventoryItem = z.object({
	id: z.string().uuid(),
	inventoryId: z.string().uuid(),
	scannedAt: z.coerce.date().nullable(),
	scannedBy: user.nullish().default(null),
	scannedById: z.string().uuid().nullish().default(null),
	status: z.enum(PRODUCT_STATUS_NAMES),
	inventoryStatus: z.enum(INVENTORY_ITEM_STATUS_NAMES),
	code: z.string(),
	sn: z.string().nullish().default(null),
	buyPrice: zodDecimal(),
	productId: z.string().uuid(),
	batchId: z.string().uuid().nullish().default(null),
	productEnvelope: productEnvelope.nullish().default(null),
	productEnvelopeId: z.string().uuid().nullish().default(null),
	productEnvelopeCode: z.string().nullish().default(null),
	productEnvelopeName: z.string().nullish().default(null),
	productEnvelopeType: z.enum(ENVELOPE_TYPES).nullish().default(null),
	envelopeProductsExpectedCount: z.number().nullish().default(null),
	envelopeProductsRealCount: z.number().nullish().default(null),
	envelopeProductsCountWasConfirmed: z.boolean().nullish().default(null),
	ecommerceOrderId: z.string().uuid().nullish().default(null),
	ecommerceOrderCode: z.string().nullish().default(null),
	ecommerceOrderItemId: z.string().uuid().nullish().default(null),
	serviceCaseId: z.string().uuid().nullish().default(null),
	serviceCaseCode: z.number().nullish().default(null),
	warrantyClaimId: z.string().uuid().nullish().default(null),
	warrantyClaimCode: z.number().nullish().default(null),
	customerClaimId: z.string().uuid().nullish().default(null),
	customerClaimCode: z.number().nullish().default(null),
	preScanWarehousePositionId: z.string().uuid().nullish().default(null),
	preScanWarehousePositionName: z.string().nullish().default(null),
	postScanWarehousePositionId: z.string().uuid().nullish().default(null),
	postScanWarehousePositionName: z.string().nullish().default(null),
});

export const inventory = z.object({
	id: z.string().uuid(),
	name: z.string().default(''),
	createdAt: z.coerce.date(),
	finishedAt: z.coerce.date().nullable(),
	status: z.enum(INVENTORY_STATUS_NAMES),
	createdBy: user.nullish().default(null),
	createdById: z.string().uuid().nullish().default(null),
	finishedBy: user.nullish().default(null),
	finishedById: z.string().uuid().nullish().default(null),
	inventoryItems: z.array(inventoryItem).default([]),
	code: code.nullish().default(null),
	codeId: z.string().uuid().nullish().default(null),
	untestedPrice: zodDecimal(),
	forSalePrice: zodDecimal(),
	servicePrice: zodDecimal(),
	deadPrice: zodDecimal(),
});

export const inventoryUpdate = z.object({
	finishedAt: z.coerce.date().nullable(),
	status: z.enum(INVENTORY_STATUS_NAMES),
	name: z.string().default(''),
});

export const inventoryItemScan = z.object({
	pcn: z.string(),
	status: z.enum(BASE_INVENTORY_ITEM_STATUS_NAMES),
	error: z.string().optional(),
});

export type InventoryItemScan = z.infer<typeof inventoryItemScan>;

const actionTypeEnum = z.enum(['SWAP_PRODUCT_IN_ORDER', 'ADD_PRODUCT_TO_ORDER', 'REMOVE_PRODUCT_FROM_ORDER'] as const);

const inventoryItemUpdateBodyUnion = z.discriminatedUnion('actionType', [
	z.object({
		actionType: z.literal('SWAP_PRODUCT_IN_ORDER'),
		productToSwapOut: z.string().uuid(),
		productToSwapIn: z.string().uuid(),
		orderToAddTo: z.string().uuid(),
	}),
	z.object({
		actionType: z.literal('ADD_PRODUCT_TO_ORDER'),
		productToAdd: z.string().uuid(),
		productEnvelopeId: z.string().uuid(),
		orderToAddTo: z.string().uuid(),
	}),
	z.object({
		actionType: z.literal('REMOVE_PRODUCT_FROM_ORDER'),
		productToDelete: z.string().uuid(),
	}),
	z.object({
		actionType: z.literal('CREATE_SERVICE_CASE'),
		serviceCaseId: z.string().uuid(),
	}),
	customerClaimCreate.extend({
		actionType: z.literal('CREATE_CUSTOMER_CLAIM'),
		ecommerceOrderItemId: z.string().uuid(),
	}),
	z.object({
		actionType: z.literal('CREATE_WARRANTY_CLAIM'),
		productId: z.string().uuid(),
		productDefectId: z.string().uuid(),
	}),
	z.object({
		actionType: z.literal('RESOLVE_OPEN_TASK'),
		productId: z.string().uuid(),
		productDefectsIds: z.array(z.string().uuid()),
		price: zodDecimal(),
		serviceTaskTypeId: z.string().uuid(),
		attributeValues: z.array(z.object({ attributeId: z.string().uuid(), attributeValueId: z.string().uuid() })),
		warrantyClaimId: z.string().uuid().nullish().default(null),
		serviceCaseId: z.string().uuid().nullish().default(null),
		customerClaimId: z.string().uuid().nullish().default(null),
		resolutionScope: z.enum(SERVICE_TASK_RESOLUTION_SCOPE).optional(),
	}),
	z.object({
		actionType: z.literal('CHANGE_PRODUCT_STATUS'),
		status: z.enum(PRODUCT_STATUS_NAMES),
		productId: z.string().uuid(),
	}),
	z.object({
		actionType: z.literal('MARK_PRODUCT_AS_OK'),
		actionDescription: z.string(),
	}),
	z.object({
		actionType: z.literal('EDIT_AMOUNT_ENVELOPE_PRODUCT_COUNT'),
		productEnvelopeId: z.string().uuid(),
		productsAmount: z.number(),
	}),
	z.object({
		actionType: z.literal('CONFIRM_AMOUNT_ENVELOPE_PRODUCT_COUNT'),
		productEnvelopeId: z.string().uuid(),
	}),
]);

export const inventoryItemUpdateBody = z.preprocess((input) => {
	if (typeof input === 'object' && input !== null && 'actionType' in input) {
		const actionType = (input as any).actionType;
		if (actionTypeEnum.options.includes(actionType)) {
			return input;
		}
	}
	throw new Error('Invalid warranty claim status');
}, inventoryItemUpdateBodyUnion);

export type InventoryItemUpdateBody = z.infer<typeof inventoryItemUpdateBody>;
