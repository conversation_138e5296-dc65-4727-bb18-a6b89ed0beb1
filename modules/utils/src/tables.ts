import type { WorkSheet } from 'xlsx';
import * as XLSX from 'xlsx';

export const filterToMatchHeader = (data: Record<string, unknown>[], header: string[]) => {
	return data.map((row) => {
		const filteredRow: Record<string, unknown> = {};
		for (const key of header) {
			filteredRow[key] = row[key];
		}
		return filteredRow;
	});
};

export const formatSheetForEmail = (sheet: WorkSheet) => {
	let trMatches = 0;
	return XLSX.utils
		.sheet_to_html(sheet)
		.replace(/(id|data-v|data-t)=".*?"/gi, '')
		.replace(/.*?(<table>.*?<\/table>).*/, '$1')
		.replace(
			/<table/g,
			'<table style="border-collapse:collapse;font-family:sans-serif;font-size:12px;line-height:1;white-space:nowrap"',
		)
		.replace(/<tr/g, () => {
			trMatches++;
			if (trMatches === 1) {
				return '<tr style="background:#eec;border:2px solid #000;font-weight:bold"';
			} else if (trMatches % 2 === 1) {
				return '<tr style="background-color:#eee"';
			} else {
				return '<tr';
			}
		})
		.replace(/<td/g, '<td style="border:1px solid #000;padding:2px;vertical-align:middle"');
};
