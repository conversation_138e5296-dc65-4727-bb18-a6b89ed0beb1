const pemToArrayBuffer = (pem: string) => {
	const b64 = pem.replace(/-----[^-]+-----/g, '').replace(/\s+/g, '');
	const binary = atob(b64);
	const buf = new ArrayBuffer(binary.length);
	const bytes = new Uint8Array(buf);
	for (let i = 0; i < binary.length; i++) bytes[i] = binary.charCodeAt(i);
	return buf;
};

const arrayBufferToBase64 = (buf: ArrayBuffer) => {
	const bytes = new Uint8Array(buf);
	let binary = '';
	for (let i = 0; i < bytes.length; i++) binary += String.fromCharCode(bytes[i]);
	return btoa(binary);
};

export const importPrivateKeyPkcs8Sha1 = async (pemPkcs8: string) => {
	const keyData = pemToArrayBuffer(pemPkcs8);
	return crypto.subtle.importKey('pkcs8', keyData, { name: 'RSASSA-PKCS1-v1_5', hash: 'SHA-1' }, false, ['sign']);
};

export const signSha1RsaPkcs1v15 = async (privateKey: CryptoKey, data: Uint8Array | string) => {
	const bytes: Uint8Array =
		typeof data === 'string' ? new TextEncoder().encode(data) : new Uint8Array(data.buffer, data.byteOffset, data.byteLength);
	const signature = await crypto.subtle.sign(
		{ name: 'RSASSA-PKCS1-v1_5' },
		privateKey,
		bytes as BufferSource, // explicit, though Uint8Array already satisfies BufferSource
	);
	return arrayBufferToBase64(signature);
};
