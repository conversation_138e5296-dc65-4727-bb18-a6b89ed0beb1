import { equals } from 'rambdax';
import { type Primitive } from './types';

export const DISK_ATTRIBUTE_REGEX = /Pevný disk \d/;
export const DISK_CAPACITY_REGEX = /Pevný disk \d - kapacita/;

type WithAttributeDisplayName = {
	attribute: { displayName: string };
	value: Primitive;
};

export const checkIfDiskCapacitiesMatch = <T extends WithAttributeDisplayName>(
	productDiskAttributeValues: T[],
	serviceFilterFn: (item: T) => boolean,
	importFilterFn: (item: T) => boolean,
	resolvedFilterFn: (item: T) => boolean,
) => {
	const [productDiskCapacityAttributes, productDiskTypeAttributes] = splitAttributesByDisplayName(
		productDiskAttributeValues,
		(item) => item.attribute.displayName,
		DISK_CAPACITY_REGEX,
	);

	const [serviceCapacities, serviceTypes] = [productDiskCapacityAttributes, productDiskTypeAttributes].map((list) =>
		list.filter(serviceFilterFn),
	);

	const [importCapacities, importTypes] = [productDiskCapacityAttributes, productDiskTypeAttributes].map((list) =>
		list.filter(importFilterFn),
	);

	const [resolvedCapacities, resolvedTypes] = [productDiskCapacityAttributes, productDiskTypeAttributes].map((list) =>
		list.filter(resolvedFilterFn),
	);

	const serviceCapacityBreakDown = getDiskCapacityBreakdown(serviceCapacities, serviceTypes);
	const importCapacityBreakDown = getDiskCapacityBreakdown(importCapacities, importTypes);
	const resolvedCapacityBreakDown = getDiskCapacityBreakdown(resolvedCapacities, resolvedTypes);

	const compareAgainst = serviceCapacityBreakDown ?? importCapacityBreakDown;

	return equals(compareAgainst, resolvedCapacityBreakDown);
};

export const splitAttributesByDisplayName = <T>(
	items: T[],
	getDisplayName: (item: T) => string,
	regex = DISK_ATTRIBUTE_REGEX,
): [T[], T[]] => {
	return items.reduce<[T[], T[]]>(
		(acc, curr) => {
			const index = getDisplayName(curr).match(regex) ? 0 : 1;
			acc[index].push(curr);
			return acc;
		},
		[[], []],
	);
};

const extractDiskNumber = (label: string) => {
	const match = label.match(/Pevný disk (\d+)/);
	return match ? match[1] : null;
};

const getDiskCapacityBreakdown = <T extends WithAttributeDisplayName>(
	productDiskCapacityAttributeValues: T[],
	productDiskTypeAttributeValues: T[],
) => {
	if (productDiskCapacityAttributeValues.length === 0 || productDiskTypeAttributeValues.length === 0) return null;

	return productDiskTypeAttributeValues
		.map((entry) => {
			return { ...entry, value: ['NVMe', 'SATA'].includes(entry.value as string) ? 'SSD' : entry.value };
		})
		.reduce<Record<string, number>>((acc, curr) => {
			const diskType = curr.value;
			const diskNumber = extractDiskNumber(curr.attribute.displayName);
			const matchingCapacity = diskNumber
				? productDiskCapacityAttributeValues.find((item) => item.attribute.displayName.includes(diskNumber))?.value
				: null;

			if (typeof matchingCapacity === 'number' && typeof diskType === 'string') {
				if (acc[diskType]) {
					acc[diskType] = acc[diskType] + matchingCapacity;
				} else {
					acc[diskType] = matchingCapacity;
				}
			}

			return acc;
		}, {});
};
