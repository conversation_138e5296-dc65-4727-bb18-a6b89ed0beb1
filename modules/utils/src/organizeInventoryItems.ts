import type { ProductStatus } from '@pocitarna-nx-2023/config';
import { getProductStatusesBelow } from './statusManagement';

export const organizeInventoryItems = <T extends { status: ProductStatus }>(inventoryItems: T[]) => {
	const untestedItems = inventoryItems.filter((item) => getProductStatusesBelow('TESTED').includes(item.status));
	const forSaleItems = inventoryItems.filter((item) => ['TESTED', 'STOCK', 'FOR_SALE', 'RESERVED'].includes(item.status));
	const serviceItems = inventoryItems.filter((item) => ['SERVICE', 'WARRANTY_CLAIM'].includes(item.status));
	const deadItems = inventoryItems.filter((item) => ['AUTOPSY', 'DEAD'].includes(item.status));

	return { untestedItems, forSaleItems, serviceItems, deadItems } as const;
};
