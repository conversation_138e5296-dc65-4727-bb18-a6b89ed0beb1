{"name": "sse-client", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "modules/sse-client/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/modules/sse-client", "main": "modules/sse-client/src/index.ts", "tsConfig": "modules/sse-client/tsconfig.lib.json", "assets": []}}, "lint": {}, "typecheck": {}}}