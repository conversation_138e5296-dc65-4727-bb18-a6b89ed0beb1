import { API_ORIGIN, SSE_ENDPOINT } from '@pocitarna-nx-2023/config';
import { type EventSource } from 'eventsource';
import { useCallback, useEffect, useMemo, useState } from 'react';

export const createUseSSE = (ES: typeof EventSource) => (handlers: [string, (event: MessageEvent) => void][]) => {
	const [isConnected, setConnected] = useState(false);
	const cachedHandlers = useMemo(() => handlers, []);
	const handleError = useCallback(() => setConnected(false), []);
	const handleOpen = useCallback(() => setConnected(true), []);

	useEffect(() => {
		const eventSource = new ES(`${API_ORIGIN}${SSE_ENDPOINT}`);
		cachedHandlers.forEach(([name, handler]) => eventSource.addEventListener(name, handler));
		eventSource.addEventListener('open', handleOpen);
		eventSource.addEventListener('error', handleError);

		return () => {
			cachedHandlers.forEach(([name, handler]) => eventSource.removeEventListener(name, handler));
			eventSource.removeEventListener('open', handleOpen);
			eventSource.removeEventListener('error', handleError);
			eventSource.close();
			setConnected(false);
		};
	}, [cachedHandlers, handleError, handleOpen]);

	return isConnected;
};
