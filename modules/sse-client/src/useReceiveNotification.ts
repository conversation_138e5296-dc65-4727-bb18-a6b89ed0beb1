import { PRIORITY_VALUES } from '@pocitarna-nx-2023/config';
import { apiClient, apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useCallback } from 'react';
import { toast } from 'sonner';
import type { SSENotification } from './types';

const priorityMethodMap: Record<number, 'info' | 'warning' | 'error'> = {
	[PRIORITY_VALUES.LOW]: 'info',
	[PRIORITY_VALUES.MEDIUM]: 'warning',
	[PRIORITY_VALUES.HIGH]: 'error',
};

type Props = {
	userId?: string;
	onNavigate?: (href: string) => void;
};

export const useReceiveNotification = ({ userId, onNavigate }: Props = {}) => {
	const { invalidate: invalidateNotifications } = apiHooks.useGetNotifications(undefined, { enabled: false });

	return useCallback(
		({ data }: MessageEvent) => {
			const notification = JSON.parse(data) as SSENotification;
			if (notification.userId !== userId) return;

			toast[priorityMethodMap[notification.priority]](notification.title, {
				id: notification.id,
				description: notification.body,
				position: 'top-right',
				duration: 30000,
				...(notification.href
					? {
							action: {
								label: 'Zobrazit',
								onClick: async () => {
									await apiClient.updateNotification(
										{ viewedAt: new Date() },
										{ params: { notificationId: notification.id } },
									);
									await invalidateNotifications();
									onNavigate?.(notification.href as string);
								},
							},
						}
					: undefined),
			});
		},
		[invalidateNotifications, onNavigate, userId],
	);
};
