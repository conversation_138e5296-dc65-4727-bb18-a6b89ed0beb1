// FIXME
/* eslint-disable unused-imports/no-unused-vars */
import { PRIORITY_VALUES } from '@pocitarna-nx-2023/config';
import { useCallback } from 'react';

const priorityMethodMap: Record<number, 'info' | 'warning' | 'error'> = {
	[PRIORITY_VALUES.LOW]: 'info',
	[PRIORITY_VALUES.MEDIUM]: 'warning',
	[PRIORITY_VALUES.HIGH]: 'error',
};

type Props = {
	userId?: string;
	onNavigate?: (href: string) => void;
};

export const useReceiveNotification = ({ userId, onNavigate }: Props = {}) => {
	return useCallback(({ data }: MessageEvent) => {
		// const notification = JSON.parse(data) as SSENotification;
		// if (notification.userId !== userId) return;
	}, []);
};
