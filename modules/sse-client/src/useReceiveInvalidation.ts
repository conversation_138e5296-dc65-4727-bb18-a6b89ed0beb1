import { useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import type { SSEInvalidation } from './types';

export const useReceiveInvalidation = () => {
	const queryClient = useQueryClient();

	return useCallback(
		({ data }: MessageEvent) => {
			const queryKey = JSON.parse(data) as SSEInvalidation;
			queryClient.invalidateQueries(queryKey);
		},
		[queryClient],
	);
};
