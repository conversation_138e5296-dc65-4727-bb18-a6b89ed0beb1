#!/usr/bin/env node
import 'source-map-support/register';
import { APP_NAME, AWS_ACCOUNT, AWS_REGION, STAGE } from '@pocitarna-nx-2023/config';
import { App, Aspects, type StackProps } from 'aws-cdk-lib';
import { AwsSolutionsChecks } from 'cdk-nag';
import { Api } from '../lib/api';
import { Cluster } from '../lib/cluster';
import { CurrencyRates } from '../lib/currencyRates';
import { Database } from '../lib/database';
import { GlobalResources } from '../lib/globalResources';
import { Housekeeping } from '../lib/housekeeping';
import { LoadBalancer } from '../lib/loadBalancer';
import { Mailer } from '../lib/mailer';
import { Network } from '../lib/network';
import { Resources } from '../lib/resources';
import { Shoptet } from '../lib/shoptet';
import { Storage } from '../lib/storage';
import { Web } from '../lib/web';

const stage = STAGE;

console.warn(AWS_ACCOUNT[STAGE], AWS_REGION, STAGE);

const commonProps: StackProps = {
	env: {
		account: AWS_ACCOUNT[STAGE],
		region: AWS_REGION,
	},
	tags: { STAGE, APP: APP_NAME },
};

const app = new App();

const {
	hostedZone,
	databaseCredentials,
	databaseLayer,
	dockerCredentials,
	nextAuthCredentials,
	keyPair,
	shoptetExportsDeadLetterQueue,
	shoptetExportsQueue,
	shoptetStockDrainQueue,
	orderSyncQueue,
	orderUpdateQueue,
	orderItemStatusUpdateQueue,
	productPricesSyncDeadLetterQueue,
	productPricesSyncQueue,
	amountEnvelopeWarehousePositionDeadLetterQueue,
	amountEnvelopeWarehousePositionQueue,
	shoptetCredentials,
	shoptetCredentialsSK,
	feedbackTopic,
	certificate: regionalCertificate,
} = new Resources(app, 'Resources', {
	...commonProps,
	stage,
});

const { certificate } = new GlobalResources(app, 'GlobalResources', {
	...commonProps,
	env: {
		...commonProps.env,
		region: 'us-east-1',
	},
	crossRegionReferences: true,
	stage,
	hostedZone,
});

const { vpc } = new Network(app, 'Network', { ...commonProps, stage, keyPair, hostedZone });

const { emailBucket, filesBucket, sessionTable } = new Storage(app, 'Storage', { ...commonProps, stage, vpc });

new Database(app, 'Database', { ...commonProps, stage, vpc, databaseCredentials, hostedZone });

const { cluster } = new Cluster(app, 'Cluster', { ...commonProps, stage, vpc, keyPair });

const { service: apiService } = new Api(app, 'Api', {
	...commonProps,
	stage,
	cluster,
	databaseCredentials,
	dockerCredentials,
	hostedZone,
	nextAuthCredentials,
	emailBucket,
	filesBucket,
	sessionTable,
	shoptetExportsQueue,
	shoptetStockDrainQueue,
	orderSyncQueue,
	orderUpdateQueue,
	orderItemStatusUpdateQueue,
	productPricesSyncQueue,
});

const { service: webService } = new Web(app, 'Web', { ...commonProps, stage, cluster, dockerCredentials, nextAuthCredentials });

new CurrencyRates(app, 'CurrencyRates', { ...commonProps, stage, vpc, databaseCredentials, databaseLayer });

new Shoptet(app, 'Shoptet', {
	...commonProps,
	stage,
	vpc,
	databaseCredentials,
	databaseLayer,
	shoptetExportsDeadLetterQueue,
	shoptetExportsQueue,
	shoptetStockDrainQueue,
	orderSyncQueue,
	orderUpdateQueue,
	orderItemStatusUpdateQueue,
	shoptetCredentials,
	shoptetCredentialsSK,
	emailBucket,
});

new Housekeeping(app, 'Housekeeping', {
	...commonProps,
	stage,
	vpc,
	databaseCredentials,
	databaseLayer,
	productPricesSyncDeadLetterQueue,
	productPricesSyncQueue,
	amountEnvelopeWarehousePositionDeadLetterQueue,
	amountEnvelopeWarehousePositionQueue,
	emailBucket,
	filesBucket,
});

new Mailer(app, 'Mailer', { ...commonProps, stage, vpc, databaseCredentials, databaseLayer, emailBucket, feedbackTopic });

new LoadBalancer(app, 'LoadBalancer', {
	...commonProps,
	crossRegionReferences: true,
	stage,
	vpc,
	certificate,
	regionalCertificate,
	hostedZone,
	apiService,
	webService,
});

Aspects.of(app).add(new AwsSolutionsChecks({ verbose: true }));
