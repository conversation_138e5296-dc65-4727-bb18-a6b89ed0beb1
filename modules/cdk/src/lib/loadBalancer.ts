import { DOMAIN_NAME, type Stage } from '@pocitarna-nx-2023/config';
import { Duration, Stack, type StackProps } from 'aws-cdk-lib';
import { type Certificate } from 'aws-cdk-lib/aws-certificatemanager';
import {
	AllowedMethods,
	type BehaviorOptions,
	CachePolicy,
	Distribution,
	GeoRestriction,
	OriginProtocolPolicy,
	OriginRequestPolicy,
	PriceClass,
	SecurityPolicyProtocol,
	ViewerProtocolPolicy,
} from 'aws-cdk-lib/aws-cloudfront';
import { LoadBalancerV2Origin } from 'aws-cdk-lib/aws-cloudfront-origins';
import { SecurityGroup, SubnetType, type Vpc } from 'aws-cdk-lib/aws-ec2';
import { type Ec2Service } from 'aws-cdk-lib/aws-ecs';
import {
	ApplicationLoadBalancer,
	ApplicationProtocol,
	ApplicationTargetGroup,
	IpAddressType,
	ListenerAction,
	ListenerCondition,
	TargetGroupLoadBalancingAlgorithmType,
} from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import { ARecord, type HostedZone, RecordTarget } from 'aws-cdk-lib/aws-route53';
import { CloudFrontTarget } from 'aws-cdk-lib/aws-route53-targets';
import { NagSuppressions } from 'cdk-nag';
import type { Construct } from 'constructs';

type Props = StackProps & {
	vpc: Vpc;
	stage: Stage;
	certificate: Certificate;
	regionalCertificate: Certificate;
	hostedZone: HostedZone;
	apiService: Ec2Service;
	webService: Ec2Service;
};

export class LoadBalancer extends Stack {
	constructor(scope: Construct, id: string, props: Props) {
		super(scope, id, props);
		const { vpc, stage, certificate, regionalCertificate, hostedZone, apiService, webService } = props;

		const apiDomains = stage === 'prod' ? [`api.${stage}.${DOMAIN_NAME}`, `api.${DOMAIN_NAME}`] : [`api.${stage}.${DOMAIN_NAME}`];
		const apexDomains =
			stage === 'prod'
				? [`${stage}.${DOMAIN_NAME}`, `www.${stage}.${DOMAIN_NAME}`, `${DOMAIN_NAME}`, `www.${DOMAIN_NAME}`]
				: [`${stage}.${DOMAIN_NAME}`, `www.${stage}.${DOMAIN_NAME}`];
		const webDomains = stage === 'prod' ? [`cmp.${stage}.${DOMAIN_NAME}`, `cmp.${DOMAIN_NAME}`] : [`cmp.${stage}.${DOMAIN_NAME}`];

		const securityGroup = new SecurityGroup(this, 'ALBSecurityGroup', { vpc });
		// securityGroup.addIngressRule(Peer.prefixList('pl-a3a144ca'), Port.tcp(443));

		const loadBalancer = new ApplicationLoadBalancer(this, 'ApplicationLoadBalancer', {
			vpc,
			securityGroup,
			vpcSubnets: { subnetType: SubnetType.PUBLIC },
			ipAddressType: IpAddressType.IPV4,
			internetFacing: true,
			http2Enabled: true,
			idleTimeout: Duration.seconds(180),
		});
		const loadBalancerOrigin = new LoadBalancerV2Origin(loadBalancer, {
			protocolPolicy: OriginProtocolPolicy.HTTPS_ONLY,
			keepaliveTimeout: Duration.seconds(60),
			connectionTimeout: Duration.seconds(10),
			readTimeout: Duration.seconds(stage === 'prod' ? 180 : 120), // On prod we requested quota increase
		});
		const geoRestriction = GeoRestriction.allowlist('CZ', 'SK', 'DE');

		const listener = loadBalancer.addListener('HttpListener', {
			port: 443,
			certificates: [regionalCertificate],
			protocol: ApplicationProtocol.HTTPS,
			open: true,
		});
		listener.addAction('HttpDefaultAction', {
			action: ListenerAction.redirect({
				protocol: 'HTTPS',
				port: '443',
				host: 'www.pocitarna.cz',
				permanent: false,
			}),
		});

		const commonDistributionBehaviorSettings: BehaviorOptions = {
			origin: loadBalancerOrigin,
			viewerProtocolPolicy: ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
			allowedMethods: AllowedMethods.ALLOW_ALL,
			cachePolicy: CachePolicy.CACHING_DISABLED,
			compress: true,
			originRequestPolicy: OriginRequestPolicy.ALL_VIEWER,
		};

		/// ===== API =====

		const apiTargetGroup = new ApplicationTargetGroup(this, 'ApiServiceTargetGroup', {
			vpc,
			port: 80,
			protocol: ApplicationProtocol.HTTP,
			targets: [apiService],
			loadBalancingAlgorithmType: TargetGroupLoadBalancingAlgorithmType.LEAST_OUTSTANDING_REQUESTS,
			deregistrationDelay: Duration.seconds(5),
			healthCheck: {
				unhealthyThresholdCount: 5,
				healthyThresholdCount: 2,
				timeout: Duration.seconds(4),
				interval: Duration.seconds(5),
			},
		});

		listener.addAction('ApiAction', {
			action: ListenerAction.forward([apiTargetGroup]),
			conditions: [ListenerCondition.hostHeaders(apiDomains)],
			priority: 10,
		});

		const apiDistribution = new Distribution(this, 'ApiDistribution', {
			defaultBehavior: commonDistributionBehaviorSettings,
			minimumProtocolVersion: SecurityPolicyProtocol.TLS_V1_2_2021,
			priceClass: PriceClass.PRICE_CLASS_100,
			certificate,
			domainNames: apiDomains,
			geoRestriction,
		});

		new ARecord(this, 'ApiDnsRecord', {
			zone: hostedZone,
			target: RecordTarget.fromAlias(new CloudFrontTarget(apiDistribution)),
			recordName: 'api',
		});

		// ===== WEB =====

		const webTargetGroup = new ApplicationTargetGroup(this, 'WebServiceTargetGroup', {
			vpc,
			port: 80,
			protocol: ApplicationProtocol.HTTP,
			targets: [webService],
			loadBalancingAlgorithmType: TargetGroupLoadBalancingAlgorithmType.LEAST_OUTSTANDING_REQUESTS,
			deregistrationDelay: Duration.seconds(5),
			healthCheck: {
				path: '/healthcheck',
				unhealthyThresholdCount: 5,
				healthyThresholdCount: 2,
				timeout: Duration.seconds(4),
				interval: Duration.seconds(5),
			},
		});

		listener.addAction('WebRedirectAction', {
			action: ListenerAction.redirect({
				host: 'www.pocitarna.cz',
				protocol: 'HTTPS',
				port: '443',
				permanent: true,
			}),
			conditions: [ListenerCondition.hostHeaders(apexDomains)],
			priority: 100,
		});

		listener.addAction('WebAction', {
			action: ListenerAction.forward([webTargetGroup]),
			conditions: [ListenerCondition.hostHeaders(webDomains)],
			priority: 5,
		});

		const webDistribution = new Distribution(this, 'WebDistribution', {
			defaultBehavior: {
				...commonDistributionBehaviorSettings,
				cachePolicy: CachePolicy.USE_ORIGIN_CACHE_CONTROL_HEADERS_QUERY_STRINGS,
			},
			additionalBehaviors: { '/api': { cachePolicy: CachePolicy.CACHING_DISABLED, origin: loadBalancerOrigin } },
			minimumProtocolVersion: SecurityPolicyProtocol.TLS_V1_2_2021,
			priceClass: PriceClass.PRICE_CLASS_100,
			certificate,
			domainNames: [...apexDomains, ...webDomains],
			geoRestriction,
		});

		new ARecord(this, 'ApexDnsRecord', {
			zone: hostedZone,
			target: RecordTarget.fromAlias(new CloudFrontTarget(webDistribution)),
		});

		new ARecord(this, 'CmpDnsRecord', {
			zone: hostedZone,
			target: RecordTarget.fromAlias(new CloudFrontTarget(webDistribution)),
			recordName: 'cmp',
		});

		NagSuppressions.addResourceSuppressions(loadBalancer, [
			{ id: 'AwsSolutions-ELB2', reason: 'No logs for traffic as it is used only internally' },
		]);
		NagSuppressions.addResourceSuppressions(securityGroup, [{ id: 'AwsSolutions-EC23', reason: 'Allowing only ports 80 and 443' }]);
		NagSuppressions.addResourceSuppressions(
			[apiDistribution, webDistribution],
			[
				{ id: 'AwsSolutions-CFR3', reason: 'Not necessary right now' },
				{ id: 'AwsSolutions-CFR5', reason: 'Using just http to communicate with ALB' },
			],
		);
	}
}
