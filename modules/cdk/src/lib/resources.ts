import { APP_NAME, DOMAIN_NAME, GITLAB_REGISTRY_PASS, GITLAB_REGISTRY_USER, GITLAB_URL, type Stage } from '@pocitarna-nx-2023/config';
import { CfnOutput, Duration, RemovalPolicy, SecretValue, Stack, type StackProps } from 'aws-cdk-lib';
import { Certificate, CertificateValidation } from 'aws-cdk-lib/aws-certificatemanager';
import { KeyPair } from 'aws-cdk-lib/aws-ec2';
import { ManagedPolicy, OpenIdConnectProvider, Role, WebIdentityPrincipal } from 'aws-cdk-lib/aws-iam';
import { Architecture, Code, LayerVersion, Runtime } from 'aws-cdk-lib/aws-lambda';
import { HostedZone, type IHostedZone, MxRecord, TxtRecord } from 'aws-cdk-lib/aws-route53';
import { Secret } from 'aws-cdk-lib/aws-secretsmanager';
import {
	ConfigurationSet,
	DkimIdentity,
	EasyDkimSigningKeyLength,
	EmailIdentity,
	EmailSendingEvent,
	EventDestination,
	Identity,
} from 'aws-cdk-lib/aws-ses';
import { Topic } from 'aws-cdk-lib/aws-sns';
import { Queue } from 'aws-cdk-lib/aws-sqs';
import { NagSuppressions } from 'cdk-nag';
import type { Construct } from 'constructs';

type Props = StackProps & {
	stage: Stage;
};

export class Resources extends Stack {
	public readonly hostedZone: HostedZone;
	public readonly keyPair: KeyPair;
	public readonly certificate: Certificate;
	public readonly deploymentRole: Role;
	public readonly databaseCredentials: Secret;
	public readonly databaseLayer: LayerVersion;
	public readonly dockerCredentials: Secret;
	public readonly nextAuthCredentials: Secret;
	public readonly shoptetCredentials: Secret;
	public readonly shoptetCredentialsSK: Secret;
	public readonly shoptetExportsDeadLetterQueue: Queue;
	public readonly shoptetExportsQueue: Queue;
	public readonly shoptetStockDrainQueue: Queue;
	public readonly orderSyncQueue: Queue;
	public readonly orderUpdateQueue: Queue;
	public readonly orderItemStatusUpdateQueue: Queue;
	public readonly productPricesSyncDeadLetterQueue: Queue;
	public readonly productPricesSyncQueue: Queue;
	public readonly amountEnvelopeWarehousePositionDeadLetterQueue: Queue;
	public readonly amountEnvelopeWarehousePositionQueue: Queue;
	public readonly emailIdentity: EmailIdentity;
	public readonly feedbackTopic: Topic;

	constructor(scope: Construct, id: string, props: Props) {
		super(scope, id, props);
		const { stage } = props;

		this.hostedZone = this.createHostedZone(stage);
		this.keyPair = this.createSshKey(stage);
		this.certificate = this.createCertificate(this.hostedZone);
		this.deploymentRole = this.createDeploymentRole();
		this.databaseCredentials = this.createDatabaseCredentials();
		this.databaseLayer = this.createDatabaseLayer(stage);
		this.dockerCredentials = this.createDockerCredentials();
		this.nextAuthCredentials = this.createNextAuthCredentials();
		this.shoptetCredentials = this.createShoptetCredentials(stage);
		this.shoptetCredentialsSK = this.createShoptetCredentialsSK();
		this.shoptetExportsDeadLetterQueue = this.createShoptetExportsDeadLetterQueue();
		this.shoptetExportsQueue = this.createShoptetExportsQueue(this.shoptetExportsDeadLetterQueue);
		this.shoptetStockDrainQueue = this.createShoptetStockDrainQueue();
		this.orderSyncQueue = this.createOrderSyncQueue();
		this.orderUpdateQueue = this.createOrderUpdateQueue();
		this.orderItemStatusUpdateQueue = this.createOrderItemStatusUpdateQueue();
		this.productPricesSyncDeadLetterQueue = this.createProductPricesSyncDeadLetterQueue();
		this.productPricesSyncQueue = this.createProductPricesSyncQueue(this.productPricesSyncDeadLetterQueue);
		this.amountEnvelopeWarehousePositionDeadLetterQueue = this.createAmountEnvelopeWarehousePositionDeadLetterQueue();
		this.amountEnvelopeWarehousePositionQueue = this.createAmountEnvelopeWarehousePositionQueue(
			this.amountEnvelopeWarehousePositionDeadLetterQueue,
		);
		this.feedbackTopic = this.createFeedbackTopic();
		this.emailIdentity = this.createEmailIdentity(stage, this.hostedZone, this.feedbackTopic);

		NagSuppressions.addStackSuppressions(this, [{ id: 'AwsSolutions-SMG4', reason: 'Not rotating secrets at this time' }]);
	}

	private createHostedZone(stage: string) {
		const hostedZone = new HostedZone(this, 'HostedZone', {
			zoneName: `${stage}.${DOMAIN_NAME}`,
		});

		new TxtRecord(this, 'SPFRecord', {
			zone: hostedZone,
			values: ['"v=spf1 include:amazonses.com ~all"'],
		});

		new TxtRecord(this, 'DMARCRecord', {
			recordName: '_dmarc',
			zone: hostedZone,
			values: ['"v=DMARC1; p=none;"'],
		});

		return hostedZone;
	}

	private createSshKey(stage: string) {
		const key = new KeyPair(this, 'KeyPair', {
			keyPairName: APP_NAME,
		});

		new CfnOutput(this, 'DownloadKeyCommand', {
			value: `aws --profile pocitarna-${stage} ssm get-parameter --with-decryption --name /ec2/keypair/${key.keyPairId} --query Parameter.Value --output text > ~/.ssh/${key.keyPairName}.pem && chmod 400 ~/.ssh/${key.keyPairName}.pem`,
		});

		return key;
	}

	private createCertificate(hostedZone: IHostedZone) {
		return new Certificate(this, `${DOMAIN_NAME}-RegionalCertificate`, {
			domainName: hostedZone.zoneName,
			subjectAlternativeNames: [`*.${hostedZone.zoneName}`],
			validation: CertificateValidation.fromDns(hostedZone),
		});
	}

	private createDeploymentRole() {
		const identityProvider = new OpenIdConnectProvider(this, 'OpenIdConnectProvider', {
			url: GITLAB_URL,
			clientIds: [GITLAB_URL],
		});

		const deploymentRole = new Role(this, 'DeploymentRole', {
			assumedBy: new WebIdentityPrincipal(identityProvider.openIdConnectProviderArn, {
				StringEquals: {
					[`gitlab.superkoders.com:sub`]: [
						`project_path:sk/${APP_NAME}:ref_type:branch:ref:master`,
						`project_path:sk/${APP_NAME}:ref_type:branch:ref:development`,
					],
				},
			}),
			managedPolicies: [ManagedPolicy.fromAwsManagedPolicyName('AdministratorAccess')],
			maxSessionDuration: Duration.hours(1),
		});

		new CfnOutput(this, 'DeploymentRoleARN', { value: deploymentRole.roleArn });

		NagSuppressions.addResourceSuppressions(deploymentRole, [
			{ id: 'AwsSolutions-IAM4', reason: 'Not sure what rights are necessary for deployment. Only used by CI in self hosted Gitlab' },
		]);

		return deploymentRole;
	}

	private createDatabaseCredentials() {
		return new Secret(this, 'DatabaseCredentials', {
			generateSecretString: {
				includeSpace: false,
				excludePunctuation: true,
				passwordLength: 32,
			},
		});
	}

	private createDatabaseLayer(stage: Stage) {
		const stacks = this.node.tryGetContext('aws:cdk:bundling-stacks');

		const layerName = `DatabaseLayer-${stage}-2025-09-04`;

		if (stage === 'prod') {
			this.exportValue('arn:aws:lambda:eu-central-1:992382396734:layer:DatabaseLayer-prod-2025-07-16:1', {
				name: 'Resources:ExportsOutputRefDatabaseLayerprod2025071612C7EC9E6D788424',
			});
		} else if (stage === 'staging') {
			this.exportValue('arn:aws:lambda:eu-central-1:637423493253:layer:DatabaseLayer-staging-2025-07-16:1', {
				name: 'Resources:ExportsOutputRefDatabaseLayerstaging2025071664601286D5CE60D8',
			});
		}

		return new LayerVersion(this, layerName, {
			layerVersionName: layerName,
			compatibleRuntimes: [Runtime.NODEJS_22_X, Runtime.NODEJS_20_X, Runtime.NODEJS_18_X],
			compatibleArchitectures: [Architecture.ARM_64],
			code:
				stacks?.includes('Resources') || (stacks?.includes('**') && stage === 'dev') // This is stupid hack to avoid rebuilding the docker image everytime lambda gets deployed
					? Code.fromDockerBuild('../..', {
							platform: 'linux/arm64',
							file: 'modules/database/layer/Dockerfile',
						})
					: Code.fromCustomCommand('../../tmp/database', ['mkdir', '-p', '../../tmp/database/node_modules']),
			removalPolicy: RemovalPolicy.RETAIN,
		});
	}

	private createDockerCredentials() {
		return new Secret(this, 'DockerCredentials', {
			secretStringValue: SecretValue.unsafePlainText(
				JSON.stringify({ username: GITLAB_REGISTRY_USER, password: GITLAB_REGISTRY_PASS }),
			),
		});
	}

	private createNextAuthCredentials() {
		return new Secret(this, 'NextAuthCredentials', {
			generateSecretString: {
				includeSpace: false,
				excludePunctuation: true,
				passwordLength: 64,
			},
		});
	}

	private createShoptetCredentials(stage: Stage) {
		return new Secret(this, 'ShoptetCredentials', {
			secretStringValue: SecretValue.unsafePlainText(
				stage === 'prod' ? '64530-p-399024-svoyd6c5l4xjrbdmkwniyavpiyj6yx9b' : '697094-p-452303-o8qxd0twf94iib18omxhgooaxx7joky8',
			),
		});
	}

	private createShoptetCredentialsSK() {
		return new Secret(this, 'ShoptetCredentialsSK', {
			secretStringValue: SecretValue.unsafePlainText('343501-p-522764-kv5wpamokusg6oksq0t3xx3qzersqzmv'),
		});
	}

	private createShoptetExportsDeadLetterQueue() {
		return new Queue(this, 'ShoptetExportsDeadLetterQueue', {
			retentionPeriod: Duration.days(14),
			visibilityTimeout: Duration.minutes(15),
			enforceSSL: true,
		});
	}

	private createShoptetExportsQueue(deadLetterQueue: Queue) {
		return new Queue(this, 'ShoptetExportsQueue', {
			receiveMessageWaitTime: Duration.seconds(20),
			retentionPeriod: Duration.hours(1),
			visibilityTimeout: Duration.minutes(15),
			enforceSSL: true,
			deadLetterQueue: {
				queue: deadLetterQueue,
				maxReceiveCount: 3,
			},
		});
	}

	private createShoptetStockDrainQueue() {
		const queue = new Queue(this, 'ShoptetStockDrainQueue', {
			receiveMessageWaitTime: Duration.seconds(20),
			retentionPeriod: Duration.hours(1),
			visibilityTimeout: Duration.minutes(15),
			enforceSSL: true,
		});

		NagSuppressions.addResourceSuppressions(queue, [{ id: 'AwsSolutions-SQS3', reason: 'Do not need DLQ here' }]);

		return queue;
	}

	private createOrderSyncQueue() {
		const queue = new Queue(this, 'OrderSyncQueue', {
			receiveMessageWaitTime: Duration.seconds(20),
			retentionPeriod: Duration.hours(1),
			visibilityTimeout: Duration.minutes(15),
			enforceSSL: true,
		});

		NagSuppressions.addResourceSuppressions(queue, [{ id: 'AwsSolutions-SQS3', reason: 'Do not need DLQ here' }]);

		return queue;
	}

	private createOrderUpdateQueue() {
		const queue = new Queue(this, 'OrderUpdateQueue', {
			receiveMessageWaitTime: Duration.seconds(20),
			retentionPeriod: Duration.hours(1),
			visibilityTimeout: Duration.minutes(15),
			enforceSSL: true,
		});

		NagSuppressions.addResourceSuppressions(queue, [{ id: 'AwsSolutions-SQS3', reason: 'Do not need DLQ here' }]);

		return queue;
	}

	private createOrderItemStatusUpdateQueue() {
		const queue = new Queue(this, 'OrderItemStatusUpdateQueue', {
			receiveMessageWaitTime: Duration.seconds(20),
			retentionPeriod: Duration.hours(1),
			visibilityTimeout: Duration.minutes(15),
			enforceSSL: true,
		});

		NagSuppressions.addResourceSuppressions(queue, [{ id: 'AwsSolutions-SQS3', reason: 'Do not need DLQ here' }]);

		return queue;
	}

	private createProductPricesSyncDeadLetterQueue() {
		return new Queue(this, 'ProductPricesSyncDeadLetterQueue', {
			retentionPeriod: Duration.days(14),
			visibilityTimeout: Duration.minutes(15),
			enforceSSL: true,
		});
	}

	private createProductPricesSyncQueue(deadLetterQueue: Queue) {
		return new Queue(this, 'ProductPricesSyncQueue', {
			receiveMessageWaitTime: Duration.seconds(20),
			retentionPeriod: Duration.hours(1),
			visibilityTimeout: Duration.minutes(15),
			enforceSSL: true,
			deadLetterQueue: {
				queue: deadLetterQueue,
				maxReceiveCount: 3,
			},
		});
	}

	private createAmountEnvelopeWarehousePositionDeadLetterQueue() {
		return new Queue(this, 'AmountEnvelopeWarehousePositionDeadLetterQueue', {
			retentionPeriod: Duration.days(14),
			visibilityTimeout: Duration.minutes(15),
			enforceSSL: true,
		});
	}

	private createAmountEnvelopeWarehousePositionQueue(deadLetterQueue: Queue) {
		return new Queue(this, 'AmountEnvelopeWarehousePositionQueue', {
			receiveMessageWaitTime: Duration.seconds(20),
			retentionPeriod: Duration.hours(1),
			visibilityTimeout: Duration.minutes(15),
			enforceSSL: true,
			deadLetterQueue: {
				queue: deadLetterQueue,
				maxReceiveCount: 1,
			},
		});
	}

	private createFeedbackTopic() {
		const topic = new Topic(this, 'FeedbackTopic', {
			enforceSSL: true,
		});

		NagSuppressions.addResourceSuppressions(topic, [{ id: 'AwsSolutions-SNS2', reason: 'Do not want to deal with encryption atm' }]);

		return topic;
	}

	private createEmailIdentity(stage: Stage, hostedZone: HostedZone, feedbackTopic: Topic) {
		const dkimIdentity = DkimIdentity.easyDkim(EasyDkimSigningKeyLength.RSA_2048_BIT);

		const configurationSet = new ConfigurationSet(this, 'ConfigurationSet');
		configurationSet.addEventDestination('Feedback', {
			events: [EmailSendingEvent.BOUNCE, EmailSendingEvent.COMPLAINT, EmailSendingEvent.REJECT],
			destination: EventDestination.snsTopic(feedbackTopic),
		});

		const identity = new EmailIdentity(this, 'EmailIdentity', {
			identity: Identity.domain(`${stage}.${DOMAIN_NAME}`),
			dkimIdentity,
			configurationSet,
			dkimSigning: true,
			feedbackForwarding: false,
		});

		dkimIdentity.bind(identity, hostedZone);

		new MxRecord(this, 'EmailIdentityMX', {
			zone: hostedZone,
			values: [{ priority: 10, hostName: `inbound-smtp.${this.region}.amazonaws.com` }],
		});

		return identity;
	}
}
