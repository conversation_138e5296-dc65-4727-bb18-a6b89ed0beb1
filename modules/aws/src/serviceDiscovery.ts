import { networkInterfaces } from 'node:os';
import { ListInstancesCommand, ListServicesCommand, ServiceDiscoveryClient } from '@aws-sdk/client-servicediscovery';
import type { ServiceSummary } from '@aws-sdk/client-servicediscovery/dist-types/models/models_0';
import { IS_DEV, serviceDiscoveryCredentials } from '@pocitarna-nx-2023/config';

// Note: This API is not supported locally

const serviceDiscoveryClient = new ServiceDiscoveryClient(serviceDiscoveryCredentials);

// Service will be the same while this is running, so it's safe to cache it
const serviceCache: { api?: ServiceSummary; web?: ServiceSummary } = {};

const findService = async (type: 'api' | 'web') => {
	if (IS_DEV) return;
	if (serviceCache[type]) return serviceCache[type];

	const command = new ListServicesCommand();
	const response = await serviceDiscoveryClient.send(command);
	const service = response.Services?.find((service) => service.Name === type);
	serviceCache[type] = service;
	return service;
};

export const listInstances = async (...args: Parameters<typeof findService>) => {
	if (IS_DEV) return [];

	const service = await findService(...args);
	if (!service) return [];

	const command = new ListInstancesCommand({ ServiceId: service.Id });
	const response = await serviceDiscoveryClient.send(command);
	return response.Instances ?? [];
};

export const getCurrentIPAddresses = () => {
	const networks = networkInterfaces();
	const ipAddresses: string[] = [];

	for (const network of Object.values(networks)) {
		if (!network) continue;
		for (const net of network) {
			ipAddresses.push(net.address);
		}
	}

	return ipAddresses;
};
