import {
	type CustomerClaim,
	CustomerClaimController,
	FileController,
	FileProductDefectController,
	listAll,
	ProductDefectController,
} from '@pocitarna-nx-2023/database';
import { renderToBuffer } from '@react-pdf/renderer';
import { CustomerClaimPdfProtocol } from './components/CustomerClaimPdfProtocol';

export const generateCustomerClaimPdfProtocol = async (customerClaimId: CustomerClaim['id']) => {
	const customerClaim = await new CustomerClaimController().resolveRecord(customerClaimId);

	if (!customerClaim) throw new Error('Customer claim not found');

	const [relatedProductDefects] = await new ProductDefectController().list(
		listAll({ filter: { customerClaimId: { eq: customerClaimId } } }),
	);
	const [productDefectsImages] = await new FileProductDefectController().list(
		listAll({ filter: { productDefectId: { eq: relatedProductDefects.map((defect) => defect.id) }, 'file.mime': { eq: '%image%' } } }),
	);

	const base64Images = await Promise.all(
		productDefectsImages.map(async (fileRelation) => {
			const file = await new FileController().downloadFile(fileRelation.file.id);
			const base64 = file ? Buffer.from(file).toString('base64') : null;
			if (!base64) return null;
			return `data:${fileRelation.file.mime};base64,${base64}`;
		}),
	);

	// TODO: Cosmetic defects when merged

	const existingFile = await new CustomerClaimController().getPdfProtocolFile(customerClaimId);

	if (existingFile) {
		await new CustomerClaimController().deleteFile(customerClaimId, existingFile.fileId);
	}

	const pdfBuffer = await renderToBuffer(
		<CustomerClaimPdfProtocol customerClaim={customerClaim} base64Images={base64Images.filter(Boolean) as string[]} />,
	);
	const pdfUint8Array = new Uint8Array(pdfBuffer);
	const savedFile = await new FileController().saveFile(
		pdfUint8Array,
		new CustomerClaimController().getPdfProtocolFileName(customerClaimId),
	);
	await new CustomerClaimController().addFiles(customerClaimId, [savedFile.id]);

	return await new FileController().getFileDownloadLink(savedFile.id);
};
