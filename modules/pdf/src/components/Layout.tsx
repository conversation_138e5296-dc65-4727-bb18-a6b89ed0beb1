import { Document, Font, Page } from '@react-pdf/renderer';
import path from 'path';
import { type FC, type PropsWithChildren, type ReactNode } from 'react';
import { tw } from './tailwind';
import { Header } from './ui/Header';
import { View } from './ui/View';

Font.register({
	family: 'NotoSans',
	src: path.resolve(process.cwd(), 'modules/pdf/fonts/NotoSans-Regular.ttf'),
	fontWeight: 'normal',
	fontStyle: 'normal',
});

type Props = {
	footer?: ReactNode;
};

export const Layout: FC<PropsWithChildren<Props>> = ({ footer, children }) => {
	return (
		<Document>
			<Page size="A4" style={tw('relative font-[NotoSans] text-[11px] p-[40px] pt-[60px]')}>
				<View className="absolute top-0 left-0 right-0 h-[40px]">
					<Header />
				</View>
				{children}

				{footer && (
					<View className="absolute bottom-0 left-0 right-0 h-[20px] text-[9px] text-[hsl(240,3.8%,45.1%)] px-[40px]">
						{footer}
					</View>
				)}
			</Page>
		</Document>
	);
};
