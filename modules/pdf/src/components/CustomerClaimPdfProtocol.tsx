import { CustomerClaimHandlingMethodMessage, CustomerDeliveryMethodMessage } from '@pocitarna-nx-2023/config';
import { type CustomerClaim } from '@pocitarna-nx-2023/database';
import { formatCustomerClaimCode, formatDate, formatDateTime } from '@pocitarna-nx-2023/utils';
import { type FC } from 'react';
import { Layout } from './Layout';
import { Image } from './ui/Image';
import { Section } from './ui/Section';
import { Table, TableCell, TableHeader, TableRow } from './ui/Table';
import { Text } from './ui/Text';
import { Muted, SubTitle, Title } from './ui/Typography';

type Props = {
	customerClaim: CustomerClaim;
	base64Images: string[];
};

export const CustomerClaimPdfProtocol: FC<Props> = ({ customerClaim, base64Images }) => {
	const order = customerClaim.ecommerceOrderItem.ecommerceOrder;

	return (
		<Layout footer={<Text>Protokol vytvořen dne: {formatDateTime(new Date())}</Text>}>
			<Title>
				Reklamace čislo {formatCustomerClaimCode(customerClaim.code)} <Muted>{formatDate(customerClaim.createdAt)}</Muted>
			</Title>

			{/* Order */}
			<Section>
				<SubTitle>Objednávka</SubTitle>
				<Table>
					<TableHeader>
						<TableCell>Číslo</TableCell>
						<TableCell>Datum objednávky</TableCell>
					</TableHeader>

					<TableRow>
						<TableCell>{order.code}</TableCell>
						<TableCell>{formatDateTime(order.placedAt)}</TableCell>
					</TableRow>
				</Table>
			</Section>

			{/* Customer claim */}
			<Section>
				<SubTitle>Hlášení reklamace</SubTitle>
				<Table>
					<TableHeader>
						<TableCell>Popis závady</TableCell>
						<TableCell>Preferovaný způsob vyřízení reklamace</TableCell>
						<TableCell>Způsob doručení</TableCell>
					</TableHeader>

					<TableRow>
						<TableCell>{customerClaim.messages.map((message) => message.message).join('\n')}</TableCell>
						<TableCell>{CustomerClaimHandlingMethodMessage[customerClaim.handlingMethod]}</TableCell>
						<TableCell>{CustomerDeliveryMethodMessage[customerClaim.customerDeliveryMethod]}</TableCell>
					</TableRow>
				</Table>
			</Section>

			<Section>
				<SubTitle>Stav zboží při převzetí</SubTitle>
				{base64Images.map((base64Image) => (
					<Image key={base64Image} src={base64Image} />
				))}
			</Section>
		</Layout>
	);
};
