import { type FC, type PropsWithChildren } from 'react';
import { Text } from './Text';
import { View } from './View';

export const Table: FC<PropsWithChildren> = ({ children }) => {
	return <View className="w-full mb-[15px] border border-[#000]">{children}</View>;
};

export const TableHeader: FC<PropsWithChildren> = ({ children }) => {
	return <View className="flex-row border-b border-[#000] bg-[#e0e0e0]">{children}</View>;
};

export const TableRow: FC<PropsWithChildren> = ({ children }) => {
	return <View className="flex-row border-b border-[#000]">{children}</View>;
};

export const TableCell: FC<PropsWithChildren> = ({ children }) => {
	return <Text className="flex-1 p-[8px] border-r border-[#000]">{children}</Text>;
};
