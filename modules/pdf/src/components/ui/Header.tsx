import { Path, Svg } from '@react-pdf/renderer';
import { type FC } from 'react';
import { tw } from '../tailwind';
import { View } from './View';

export const Header: FC = () => {
	return (
		<View className="flex-row items-center justify-between bg-[hsl(179,100%,34%)] px-[16px] py-[12px]">
			<Svg style={tw('w-[80px] h-[20px]')} viewBox="0 0 3675.2 798.4" fill="white">
				<Path
					d="M929.2,359.4c0,106.6-86.4,192.9-192.9,192.9c-51.3,0-97.9-20-132.5-52.7c12.3-10.9,25.7-22.3,40.2-34.3
		c4.7-3.9,9-7.9,13-12c21.3,18,48.8,28.8,78.8,28.8c67.5,0,122.1-54.7,122.1-122.1c0-67.5-54.7-122.1-122.1-122.1
		c-26,0-50.1,8.1-69.9,22c-10.3-19.9-23.4-39.9-38.8-59.5c31-21.4,68.6-33.8,109.2-33.8C842.8,166.4,929.2,252.8,929.2,359.4z"
				/>
				<Path
					d="M2348.6,265.8l-28.6-15.8c-2.6-1.4-4.8-3.2-6.5-5.1c-2.9-2.8-4.5-6.3-4.5-9.5c-0.2-2.1,0.2-4.2,1.4-6
		l19.4-32.1l10.5-18.2h-41.1c-6.4,0-11.5,3.8-11.5,8.4v23.1c-20.6-16.8-44.8-29.4-71.3-36.5c-15.9-4.3-32.7-6.6-50-6.6
		c-3.2,0-6.4,0.1-9.5,0.2c-101.8,5-182.7,89.1-182.7,192c0,106.2,86.1,192.3,192.3,192.3c46,0,88.2-16.1,121.3-43.1v34.6
		c0,4.7,5.1,8.4,11.5,8.4h47.9c6.3,0,11.5-3.8,11.5-8.4V268.9C2355.3,268.6,2351.8,267.6,2348.6,265.8z M2165.8,482
		c-67.2,0-121.7-54.5-121.7-121.7s54.5-121.7,121.7-121.7s121.7,54.5,121.7,121.7S2233,482,2165.8,482z"
				/>
				<Path
					d="M354.5,232.1c-66.4,0-120.1,53.8-120.1,120.1c0,66.4,53.8,120.1,120.1,120.1c66.4,0,120.2-53.8,120.2-120.1
		C474.7,285.9,420.9,232.1,354.5,232.1z"
				/>
				<Path
					d="M392.7,46.3C179.4-88.3,0,88.8,0,344.3c0,255.5,259.6,557.9,470.3,419.1c94.6-62.3-163.2-48.1,155.7-328.8
		C742.7,332,574.1,160.7,392.7,46.3z M355.1,541.5c-46.3,0-88.8-16.6-121.7-44.2v122.3c0,5.8-4.9,10.5-10.9,10.5h-45.4
		c-6,0-10.9-4.7-10.9-10.5V176.3c0-5.8,4.9-10.5,10.9-10.5h45.4c6,0,10.9,4.7,10.9,10.5v29.8c33-27.6,75.4-44.2,121.7-44.2
		c104.8,0,189.8,85,189.8,189.8C544.8,456.6,459.9,541.5,355.1,541.5z"
				/>
				<Path
					d="M1290.7,446.4l49.3,53.8c-35.3,32.2-82.4,51.9-134,51.9c-109.9,0-199-89.1-199-199
		c0-74.1,40.5-138.8,100.7-173c5.2-2.9,10.5-5.7,15.9-8.1c4.2-1.9,8.5-3.7,12.9-5.3c25.2,24.7,82.5,63.5,158.5,8.5
		c23.5,11.8,44.4,28.1,61.5,47.8l-54.4,49.9c-23.1-27.6-57.8-45.1-96.6-45.1c-69.5,0-126,56.4-126,126c0,69.6,56.4,126,126,126
		C1238.4,479.6,1268.3,467,1290.7,446.4z"
				/>
				<Path
					d="M2702.5,178.6v49.2c0,6.5-1.8,11.8-4.1,11.8h-83.9c-2.2,0.3-4.5,0.7-6.7,1.2c0,0-0.1,0-0.1,0
		c-13.6,2.8-27,7.9-39.6,15.4c-34.6,20.8-55.2,55.5-58.8,92.3v195.7c0,3.7-5.3,6.8-11.8,6.8h-49.1c-6.5,0-11.8-3-11.8-6.8V359.6
		c0-32.5,8.4-64.6,24.4-93.2c2.8-5,5.9-10,9.2-14.8c0.1-0.2,0.3-0.4,0.4-0.6c13.5-19.5,31-36.9,52-50.9c2.5-1.7,5.1-3.3,7.8-4.9
		c23.5-14.1,48.8-22.8,74.4-26.5c2-0.3,4-0.5,6-0.8c6.9-0.8,13.9-1.2,20.8-1.2h66.5C2700.7,166.9,2702.5,172.1,2702.5,178.6z"
				/>
				<Path
					d="M2780.9,175.6v367c0,4.8,5.2,8.7,11.7,8.7h48.6c6.4,0,11.7-3.9,11.7-8.7V240h55.2c2.2,0.3,4.4,0.7,6.6,1.2
		c0,0,0.1,0,0.1,0c13.5,2.8,26.8,7.9,39.2,15.4c34.3,20.8,54.7,55.5,58.3,92.2v195.5c0,3.7,5.2,6.8,11.7,6.8h48.6
		c6.4,0,11.7-3,11.7-6.8V359.8c0-32.5-8.3-64.6-24.2-93.1c-2.8-5-5.8-10-9.1-14.8c-0.1-0.2-0.3-0.4-0.4-0.6
		c-13.4-19.5-30.7-36.9-51.5-50.8c-2.5-1.7-5.1-3.3-7.7-4.9c-23.3-14.1-48.3-22.8-73.7-26.4c-2-0.3-4-0.5-6-0.8
		c-6.9-0.8-13.8-1.2-20.6-1.2H2844c-0.9-0.2-1.9-0.3-2.9-0.3h-48.6C2786.1,167,2780.9,170.8,2780.9,175.6z"
				/>
				<Path
					d="M1799.7,545c0,3.8-5.4,6.9-12,6.9h-50c-6.6,0-12-3.1-12-6.9V254.7c0-3.8,5.4-6.9,12-6.9h50
		c6.6,0,12,3.1,12,6.9V545z"
				/>
				<Path
					d="M1645.7,245.5c0,8.5,59.4-53.6,127.5-31.9l127.6,47.2c9.6,0,17.3-6.9,17.3-15.3v-63.8
		c0-8.5-7.8-15.3-17.3-15.3H1663c-9.6,0-17.3,6.9-17.3,15.3V245.5z"
				/>
				<Path d="M3540.8,369.1V381c0.1-1.9,0.1-3.9,0.1-5.8C3540.9,373.1,3540.9,371.1,3540.8,369.1z" />
				<Path
					d="M3549.8,185.7c0-4.7-5.2-8.5-11.5-8.5h-48.2c-6.4,0-11.6,3.8-11.6,8.5V209c-20.8-16.9-45.1-29.6-71.7-36.7
		c-16-4.3-32.9-6.6-50.3-6.6c-3.2,0-6.4,0.1-9.6,0.2c-102.3,5-183.7,89.5-183.7,193.1c0,106.8,86.5,193.3,193.3,193.3
		c46.2,0,88.7-16.2,122-43.3v34.7c0,4.7,5.2,8.5,11.6,8.5h38.2l-29.7-91.9l51.3,29.4V185.7z M3356,482
		c-67.6,0-122.4-54.8-122.4-122.4c0-67.6,54.8-122.4,122.4-122.4c67.6,0,122.4,54.8,122.4,122.4C3478.4,427.2,3423.6,482,3356,482z"
				/>
				<Path
					d="M3665.6,647.6c1.6,1.9,2.6,3.8,2.3,4l-14.3,11.8c-0.3,0.3-1.9-1.1-3.5-3l-56-67.8l-16.7,62.3l-23.3-72.2
		l-29.7-91.9l51.3,29.4l99.7,57.2l-63.3,5.2L3665.6,647.6z"
				/>
				<Path
					d="M1297.1,156.1c-76,55-133.3,16.2-158.5-8.5c21.6-8.1,45-12.5,69.5-12.5c27.9,0,54.4,5.7,78.5,16.1
		c1.9,0.8,3.8,1.6,5.6,2.5C1293.8,154.5,1295.5,155.3,1297.1,156.1z"
				/>
				<Path d="M2347.7,373.7v11.9c0.1-1.9,0.1-3.9,0.1-5.8C2347.8,377.8,2347.7,375.7,2347.7,373.7z" />
				<Path
					d="M1492.6,265.8l-28.6-15.8c-9.2-5.1-13.5-14.3-9.6-20.7l1.2-1.9c0.1-0.4,0.3-0.7,0.5-1.1l34.9-60.4h-44.1
		c-6.6,0-12,3.9-12,8.7v368.7c0,4.8,5.4,8.7,12,8.7h50c6.6,0,12-3.9,12-8.7V268.4C1504,269.6,1498.1,268.8,1492.6,265.8z"
				/>
				<Path
					d="M1565.1,165.8l-45.3,78.4l-5.5,9.5c-3,5.1-11,6-18.1,1.9l-21.9-12.7c-7-4.1-10.3-11.5-7.4-16.6l34.9-60.4
		l15.9-27.5c3-5.1,11-6,18-1.9l21.9,12.7C1564.7,153.3,1568,160.7,1565.1,165.8z"
				/>
				<Path
					d="M2420.1,166.5l-49.1,84.9l-1.7,2.9c-3,5.1-11,6-18.1,1.9l-21.9-12.7c-7-4-10.3-11.5-7.4-16.6l30.6-52.9
		l20.2-34.9c3-5.1,11-6,18.1-1.9l21.9,12.7C2419.7,154,2423,161.4,2420.1,166.5z"
				/>
			</Svg>
		</View>
	);
};
