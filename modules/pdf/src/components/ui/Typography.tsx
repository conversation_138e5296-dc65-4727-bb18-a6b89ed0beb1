import { type FC, type <PERSON>psWithChildren } from 'react';
import { Text } from './Text';

export const Title: FC<PropsWithChildren> = ({ children }) => {
	return <Text className="text-[15px] font-bold m-[10px]">{children}</Text>;
};

export const SubTitle: FC<PropsWithChildren> = ({ children }) => {
	return <Text className="text-[12px] font-bold m-[8px]">{children}</Text>;
};

export const Muted: FC<PropsWithChildren> = ({ children }) => {
	return <Text className="text-xs text-muted-foreground">{children}</Text>;
};
