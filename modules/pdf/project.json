{"name": "pdf", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "modules/pdf/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/modules/pdf", "main": "modules/pdf/src/index.ts", "tsConfig": "modules/pdf/tsconfig.lib.json", "assets": []}}, "lint": {}, "typecheck": {}}}