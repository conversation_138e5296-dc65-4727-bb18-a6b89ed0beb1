import { base64ToArrayBuffer } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useMemo } from 'react';
import { read, utils } from 'xlsx';
import type { ExcelSlide } from 'yet-another-react-lightbox';

type Props = {
	slide: ExcelSlide;
};

const styles =
	'<style>*{box-sizing:border-box}body{margin:0;padding:0;position:relative}table{border-collapse:collapse;font-family:sans-serif;font-size:12px;line-height:1;white-space:nowrap}tr:nth-child(2n+1){background-color:#eee}tr:first-child{position:sticky;top:0;background:#eec;border:2px solid #000}td{border:1px solid #000;padding:2px;vertical-align:middle}</style>';

export const LightboxExcelSlide: FC<Props> = ({ slide }) => {
	const urlParts = slide.src.split('/');
	const { data, isLoading, isError } = apiHooks.useDownloadBase64({ params: { fileId: urlParts[urlParts.length - 2] } }); // FIXME - not in UI component

	const sheet = useMemo(() => {
		if (!data) return;
		const workbook = read(base64ToArrayBuffer(data));
		const sheet = workbook.Sheets[workbook.SheetNames[0]];
		const html = utils.sheet_to_html(sheet);
		return html.replace('</head>', `${styles}</head>`);
	}, [data]);

	return (
		<div className="max-w-screen-xl w-full h-full max-h-[90vh] bg-white flex items-center justify-center">
			{isLoading ? (
				'Načítám...'
			) : isError ? (
				'Nepodařilo se načíst'
			) : (
				<iframe title="Preview" className="w-full h-full" srcDoc={sheet} />
			)}
		</div>
	);
};
