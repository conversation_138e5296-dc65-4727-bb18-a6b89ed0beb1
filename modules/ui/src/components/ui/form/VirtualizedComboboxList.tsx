import { uniquesBy } from '@pocitarna-nx-2023/utils';
import { useVirtualizer } from '@tanstack/react-virtual';
import { type ReactNode, useMemo, useRef } from 'react';
import { type ControllerRenderProps, type FieldValues, type Path } from 'react-hook-form';
import { CommandItem, CommandList } from '../command';
import { Icon } from '../icon';
import { type ComboBoxItemType } from './ComboboxControl';

type Props<T extends FieldValues> = {
	items: ComboBoxItemType[];
	buttonClassName?: string;
	onSelect?: (value: string) => void;
	onScroll?: (e: React.UIEvent<HTMLDivElement, UIEvent>) => void;
	setOpen: (status: boolean) => void;
	field: ControllerRenderProps<T, Path<T>>;
};

export const VirtualizedComboboxList = <T extends FieldValues>({ items, onSelect, onScroll, setOpen, field }: Props<T>): ReactNode => {
	const listRef = useRef<HTMLDivElement>(null);
	const uniqueItems = useMemo(() => uniquesBy(items, 'value'), [items]);

	const virtualizer = useVirtualizer({
		count: uniqueItems.length,
		getScrollElement: () => listRef.current,
		estimateSize: () => 28,
		overscan: 5,
	});

	const virtualItems = virtualizer.getVirtualItems();

	return (
		<CommandList ref={listRef} onScroll={onScroll}>
			<div
				style={{
					height: `${virtualizer.getTotalSize()}px`,
					width: '100%',
					position: 'relative',
				}}
			>
				{virtualItems.map((virtualRow) => {
					const item = uniqueItems[virtualRow.index];
					return (
						<CommandItem
							key={item.value}
							value={typeof item.label === 'string' ? item.label : item.value}
							style={{
								position: 'absolute',
								top: 0,
								left: 0,
								width: '100%',
								height: `${virtualRow.size}px`,
								transform: `translateY(${virtualRow.start}px)`,
							}}
							onSelect={() => {
								field.onChange(item.value);
								onSelect?.(item.value);
								setOpen(false);
							}}
						>
							{item.label}
							{item.value === field.value && <Icon name="check" className="ml-auto h-3 w-3" />}
						</CommandItem>
					);
				})}
			</div>
		</CommandList>
	);
};
