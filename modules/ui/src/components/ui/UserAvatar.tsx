import { NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { getInitials } from '@pocitarna-nx-2023/utils';
import { type ComponentProps, type FC } from 'react';
import { cn } from '../../utils/styles';
import { Avatar, AvatarFallback, AvatarImage } from './avatar';
import { Tooltip } from './tooltip';

type Props = {
	user?: { image: string | null; name: string; deletedAt: Date | null } | null;
} & ComponentProps<typeof Avatar>;

export const UserAvatar: FC<Props> = ({ user, ...props }) => {
	const isDeleted = !!user?.deletedAt;

	return (
		<Tooltip tooltip={`${user?.name ?? NOT_AVAILABLE}${user?.deletedAt ? ' (smazáno)' : ''}`}>
			<Avatar className={cn(isDeleted && 'grayscale opacity-70')} {...props}>
				{user?.image && <AvatarImage src={user.image} />}
				<AvatarFallback>{getInitials(user?.name ?? NOT_AVAILABLE)}</AvatarFallback>
			</Avatar>
		</Tooltip>
	);
};

UserAvatar.displayName = 'UserAvatar';
