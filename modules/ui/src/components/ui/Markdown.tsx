import type { FC, HTMLProps } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import sanitizeHtml, { defaults as sanitizeHtmlDefaults } from 'sanitize-html';
import { cn } from '../../utils/styles';

type Props = { value: string } & HTMLProps<HTMLDivElement>;

export const Markdown: FC<Props> = ({ value, ...props }) => {
	const sanitizedContent = sanitizeHtml(value, {
		allowedTags: sanitizeHtmlDefaults.allowedTags.concat(['span']),
		allowedAttributes: {
			...sanitizeHtmlDefaults.allowedAttributes,
			span: ['style'],
		},
		allowedStyles: {
			span: {
				color: [/^.*$/],
			},
		},
	});

	return (
		<div {...props} className={cn('[&_a]:text-primary [&_a]:underline [&_a]:cursor-pointer [&_a:hover]:no-underline', props.className)}>
			<ReactMarkdown remarkPlugins={[remarkGfm]} rehypePlugins={[rehypeRaw]}>
				{sanitizedContent}
			</ReactMarkdown>
		</div>
	);
};
