import type { GenericSlide } from 'yet-another-react-lightbox';

declare module 'yet-another-react-lightbox' {
	// eslint-disable-next-line @typescript-eslint/consistent-type-definitions
	export interface PdfSlide extends GenericSlide {
		type: 'pdf';
		src: string;
	}

	// eslint-disable-next-line @typescript-eslint/consistent-type-definitions
	export interface ExcelSlide extends GenericSlide {
		type: 'xlsx';
		src: string;
	}

	// eslint-disable-next-line @typescript-eslint/consistent-type-definitions
	interface SlideTypes {
		pdf: PdfSlide;
		xlsx: ExcelSlide;
	}

	// eslint-disable-next-line @typescript-eslint/consistent-type-definitions
	interface Labels {
		Previous?: string;
		Next?: string;
		Close?: string;
		RotateLeft?: string;
		RotateRight?: string;
		Save?: string;
	}
}
