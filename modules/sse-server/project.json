{"name": "sse-server", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "modules/sse-server/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/modules/sse-server", "main": "modules/sse-server/src/index.ts", "tsConfig": "modules/sse-server/tsconfig.lib.json", "assets": []}}, "lint": {}, "typecheck": {}}}