import type { EventBuffer } from 'better-sse';

export const getDataFromBuffer = (buffer: EventBuffer) =>
	buffer
		.read()
		.split('\n\n')
		.reduce<{ eventName: string; data: unknown[] }[]>((acc, event) => {
			const { eventName, data } = event.split('\n').reduce<{ eventName?: string; data?: unknown }>((acc, line) => {
				if (line.startsWith('data:')) {
					acc.data = JSON.parse(line.slice(5).trim());
				} else if (line.startsWith('event:')) {
					acc.eventName = line.slice(6).trim();
				}
				return acc;
			}, {});

			if (!eventName || !data) return acc;

			const foundEvent = acc.find((item) => item.eventName === eventName);
			if (foundEvent) {
				foundEvent.data.push(data);
			} else {
				acc.push({ eventName, data: [data] });
			}
			return acc;
		}, []);
