import type { <PERSON>quest<PERSON><PERSON><PERSON> } from 'express';
import { sseBuffer, sseChannel } from '.';
import { getData<PERSON>romBuffer } from './getDataFromBuffer';
import { notifyOthers } from './notifyOthers';

export const sseMiddleware: RequestHandler = async (_req, res, next) => {
	res.on('close', async () => {
		const data = getDataFromBuffer(sseBuffer);
		if (data.length === 0) return next();

		sseBuffer.clear();

		await Promise.all([
			...data.map(({ eventName, data }) =>
				Promise.all(sseChannel.activeSessions.map((session) => session.iterate(data, { eventName }))),
			),
			notifyOthers(data),
		]);
	});

	next();
};
