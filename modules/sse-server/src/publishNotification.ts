import { SSE_MESSAGE } from '@pocitarna-nx-2023/config';
import { sseBuffer } from '.';
import { publishInvalidation } from './publishInvalidation';

// Using generic, since I can't import from database (circular dependency issues)
export function publishNotification<T extends { id: string }>(...notifications: T[]) {
	notifications.forEach((notification) => sseBuffer.push(notification, SSE_MESSAGE.NOTIFICATION));
	publishInvalidation('getNotifications');
}
