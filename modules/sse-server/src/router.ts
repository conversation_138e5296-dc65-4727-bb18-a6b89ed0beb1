import { createSession } from 'better-sse';
import { Router } from 'express';
import { sseChannel } from '.';
import type { getDataFromBuffer } from './getDataFromBuffer';

export const sseRouter = () => {
	const router = Router();

	router.get('/', async (req, res) => {
		const session = await createSession(req, res);
		session.push('Connected to SSE');
		sseChannel.register(session);
	});

	router.post('/sync', async (req, res) => {
		const data = req.body as ReturnType<typeof getDataFromBuffer>;
		await Promise.all(
			data.map(({ eventName, data }) =>
				Promise.all(sseChannel.activeSessions.map((session) => session.iterate(data, { eventName }))),
			),
		);
		res.status(200).json();
	});

	return router;
};
