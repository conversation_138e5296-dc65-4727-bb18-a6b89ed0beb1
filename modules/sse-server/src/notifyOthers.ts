import { getCurrentIPAddresses, listInstances } from '@pocitarna-nx-2023/aws';
import { IS_DEV, SSE_ENDPOINT } from '@pocitarna-nx-2023/config';
import axios from 'axios';
import type { getDataFromBuffer } from './getDataFromBuffer';

export const notifyOthers = async (data: ReturnType<typeof getDataFromBuffer>) => {
	if (IS_DEV) return; // Unsupported locally

	const instances = await listInstances('api');
	const currentIps = getCurrentIPAddresses();
	const instanceAddresses = instances
		.map((instance) => [instance.Attributes?.['AWS_INSTANCE_IPV4'], instance.Attributes?.['AWS_INSTANCE_PORT']] as const)
		.filter(([ip, port]) => ip && port && !currentIps.includes(ip));

	await Promise.all(
		instanceAddresses.map(([ip, port]) =>
			axios({
				url: `http://${ip}:${port}${SSE_ENDPOINT}/sync`,
				method: 'POST',
				data,
			}),
		),
	).catch((error) => console.error(error));
};
