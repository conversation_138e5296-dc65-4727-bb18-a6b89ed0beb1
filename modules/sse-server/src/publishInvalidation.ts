import { SSE_MESSAGE } from '@pocitarna-nx-2023/config';
import { api, type ApiAliases } from '@pocitarna-nx-2023/zodios';
import { sseBuffer } from '.';

// TODO - allow to specify also the params of the query keys

export const publishInvalidation = (...aliases: ApiAliases[]) => {
	aliases.forEach((alias) => {
		const endpoint = api.find((endpoint) => endpoint.alias === alias);
		if (endpoint) {
			const queryKey = [{ api: 'api', path: endpoint.path }] as const;
			sseBuffer.push(queryKey, SSE_MESSAGE.INVALIDATION);
		}
	});
};
